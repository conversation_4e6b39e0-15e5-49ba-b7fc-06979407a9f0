// lib/client-services/admin/topics.client.ts
import axios from 'axios'
import { SuperGroup, Group, TMCQGroup, TMCQSubGroup } from '@/types/admin-types'
import { LoginRefresh } from '../../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Create auth headers with JW<PERSON> token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class AdminTopicsClientService {
  /**
   * Get super groups for chapter-based papers
   */
  static async getSuperGroups(): Promise<SuperGroup[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/test/progression/super-groups`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get groups for a super group
   */
  static async getGroups(superGroupId: string): Promise<Group[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/test/progression/groups/${superGroupId}`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error(
        `Error fetching groups for super group ${superGroupId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get TMCQ groups
   */
  static async getTMCQGroups(): Promise<TMCQGroup[]> {
    try {
      // Use v3 API to match Angular implementation
      const response = await axios.get(
        `${API_BASE_URL}/v3/admin/tmcq/group`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching TMCQ groups:', error)
      return []
    }
  }

  /**
   * Get TMCQ sub groups
   */
  static async getTMCQSubGroups(groupId: string): Promise<TMCQSubGroup[]> {
    try {
      // Use v3 API to match Angular implementation
      const response = await axios.get(
        `${API_BASE_URL}/v3/admin/tmcq/subgroup/${groupId}`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error(
        `Error fetching TMCQ sub groups for group ${groupId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get verbal practice groups
   */
  static async getVerbalPracticeGroups(): Promise<any[]> {
    try {
      const response = await axios.get(
        `${V2_BASE_URL}/verbal/practice/groups`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching verbal practice groups:', error)
      return []
    }
  }

  /**
   * Get technical practice groups
   */
  static async getTechnicalPracticeGroups(): Promise<any[]> {
    try {
      const response = await axios.get(
        `${V2_BASE_URL}/technical/practice/groups`,
        createAuthHeaders()
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching technical practice groups:', error)
      return []
    }
  }
}
