// lib/client/chapter-practice-client.ts
import axios from 'axios'
import { Group, ChapterPaper } from '@/types/chapter-practice-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  groupUrl: `${API_BASE_URL}/test/progression/practice/groups`,
  groupPaperUrl: `${API_BASE_URL}/test/progression/practice/group`
}

/**
 * Get JWT token
 */
const getJwtToken = (): string => {
  // Use the LoginRefresh helper from the auth library
  return LoginRefresh.getAuthToken() || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Get groups for a super group
 */
export async function getGroups(superGrpId: string): Promise<Group[]> {
  const token = getJwtToken()

  try {
    const response = await axios.get(
      `${ENDPOINTS.groupUrl}/${superGrpId}`,
      createAuthHeaders(token)
    )
    return response.data
  } catch (error) {
    console.error('Error fetching groups:', error)
    throw error
  }
}

/**
 * Get papers for a group
 */
export async function getPapersOfAGroup(
  groupId: string
): Promise<ChapterPaper[]> {
  const token = getJwtToken()

  try {
    const response = await axios.get(
      `${ENDPOINTS.groupPaperUrl}/${groupId}`,
      createAuthHeaders(token)
    )
    return response.data
  } catch (error) {
    console.error('Error fetching papers:', error)
    throw error
  }
}
