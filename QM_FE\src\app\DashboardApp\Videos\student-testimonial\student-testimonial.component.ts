import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { StreamingService } from 'src/app/Services/streaming.service';

import { StudentVideoList } from '../../../Models/Streaming/StudentVideoList';

declare let gtag: Function;

@Component({
  selector: 'app-student-testimonial',
  templateUrl: './student-testimonial.component.html',
  styleUrls: ['./student-testimonial.component.scss']
})
export class StudentTestimonialComponent implements OnInit {

  public showIndicator = false;

  public videoList: StudentVideoList[];

  constructor(public router: Router,
    public activatedRoute: ActivatedRoute,
    public streamingService: StreamingService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.streamingService.getTestimonyVideoList().subscribe(response => {
      that.videoList = response;

      for (const video of this.videoList) {
        video.thumbnail = 'https://quantmasters.in/' + video.thumbnail;
      }

      that.showIndicator = false;
    }, error => {

    });
  }

  startStream(videoId: string, videoType: number) {
    this.router.navigate(['stream', videoId, videoType], { relativeTo: this.activatedRoute });
  }
}
