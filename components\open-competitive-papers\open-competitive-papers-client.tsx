'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { <PERSON>, FileTex<PERSON>, ArrowR<PERSON>, <PERSON> } from 'lucide-react'
import { ChapterPaper } from '@/types/open-competitive-paper-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface OpenCompetitivePapersClientProps {
  initialPapers: ChapterPaper[]
}

export default function OpenCompetitivePapersClient({
  initialPapers
}: OpenCompetitivePapersClientProps) {
  const router = useRouter()
  const papers = initialPapers
  const [isLoading, setIsLoading] = useState(false)

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    paperLim: string
  ) => {
    setIsLoading(true)

    // Convert time limit to number for navigation
    const timeLimNumber = parseInt(paperLim) || 0

    router.push(
      `/dashboard/test/8/${paperId}/${encodeURIComponent(paperName)}/${timeLimNumber}`
    )
  }

  const navigateToPlans = () => {
    router.push('/plans')
  }

  const formatTimeLimit = (timeLim: string): string => {
    const timeMs = parseInt(timeLim)
    if (!timeMs) return 'No Time Limit'

    const minutes = Math.floor(timeMs / 60000)
    if (minutes >= 60) {
      const hours = Math.floor(minutes / 60)
      const remainingMinutes = minutes % 60
      return remainingMinutes > 0
        ? `${hours}h ${remainingMinutes}m`
        : `${hours}h`
    }
    return `${minutes} Minutes`
  }

  if (isLoading) {
    return (
      <div className="text-center py-20">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-4 text-gray-600">Starting your test...</p>
      </div>
    )
  }

  return (
    <div className="paper-wrap">
      {papers.length === 0 ? (
        <div className="text-center py-20">
          <Trophy className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 mb-2">
            No Competitive Papers Available
          </h3>
          <p className="text-gray-600 mb-6">
            Check back later for new competitive examination papers.
          </p>
          <Button onClick={navigateToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {papers.map((paper) => (
            <Card
              key={paper.paper_id}
              className="h-full flex flex-col hover:shadow-lg transition-shadow duration-200"
            >
              <CardHeader>
                <CardTitle className="text-lg leading-tight">
                  {paper.paper_name}
                </CardTitle>

                <CardDescription className="flex items-center space-x-2 text-sm">
                  <Clock className="h-4 w-4" />
                  <span>{formatTimeLimit(paper.time_lim)}</span>
                </CardDescription>
              </CardHeader>

              <CardContent className="flex-grow space-y-3">
                <div className="flex items-center space-x-1">
                  <FileText className="h-4 w-4" />
                  <span>No. of Questions: {paper.no_of_ques}</span>
                </div>
              </CardContent>

              <CardFooter className="mt-auto">
                <Button
                  onClick={() =>
                    handleBeginTest(
                      paper.paper_id,
                      paper.paper_name,
                      paper.time_lim
                    )
                  }
                  className="w-full"
                  disabled={paper.status === '0'}
                >
                  {paper.status === '0' ? (
                    'Currently Unavailable'
                  ) : (
                    <>
                      Begin Test
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
