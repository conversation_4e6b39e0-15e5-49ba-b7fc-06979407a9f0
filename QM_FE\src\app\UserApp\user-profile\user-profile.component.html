  <app-nav></app-nav>
  <div class="Main-containt">
    <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
    <div class="edit-profile">
      <p>Edit-Profile</p>
    </div>
    <div class="left-sect">
      <div class="left-bar">
        <div class="user-options">
          <div class="option-link" [class.selected]="selectedView[0]" (click)="selectView(1)">Profile Details</div>
          <!-- <div class="option-link" [class.selected]="selectedView[1]" (click)="selectView(2)">Email</div> -->
          <div class="option-link" [class.selected]="selectedView[2]" (click)="selectView(3)">Password</div>
        </div>
      </div>
      <div class="detail-bar" *ngIf="selectedView[0]">
        <div class="user-avatar--wrap">
          <img class="profile-avatar" title="Avatar"
            src="{{ imagePath ? imagePath : '../../../assets/push-icons/avatar-def.png' }}" />
          <form (ngSubmit)="userAvatarSelect.click()" enctype="multipart/form-data">
            <input style="display: none" type="file" (change)="fileSelect($event)" #userAvatarSelect />
            <button type="submit" title="Edit Profile Avatar">
              <img src="../../../assets/push-icons/edit.svg" />
            </button>
          </form>
        </div>
        <form #userDetailForm="ngForm" appPasswordEqualValidator (ngSubmit)="updateUserDetails(userDetailForm.valid)"
          autocomplete="off">
          <div class="form-row--1">
            <div class="form-elem">
              <input type="text" name="firstname" placeholder="First Name"
                [class.is-inv--input]="firstname.invalid && firstname.touched || (firstname.errors?.required && userDetailForm.submitted)"
                [(ngModel)]="userInfo.f_name" #firstname="ngModel" required pattern="[a-zA-Z][a-zA-Z ]*" />
              <small class="form-error--text"
                *ngIf="firstname.errors?.required && firstname.touched || (firstname.errors?.required && userDetailForm.submitted)">
                First Name is Required
              </small>
              <small class="form-error--text" *ngIf="firstname.errors?.pattern && firstname.touched">
                First Name can only contain letters
              </small>
            </div>
            <div class="form-elem">
              <input type="text" name="lastname" placeholder="Last Name"
                [class.is-inv--input]="lastname.invalid && lastname.touched || (lastname.errors?.required && userDetailForm.submitted)"
                [(ngModel)]="userInfo.l_name" #lastname="ngModel" pattern="[a-zA-Z]+|^$" />
              <small class="form-error--text" *ngIf="lastname.errors?.pattern && lastname.touched">
                Last Name can only contain letters
              </small>
            </div>
          </div>
          <div class="form-row--1">
            <div class="form-elem">
              <input type="text" name="dob" #dp="bsDatepicker" bsDatepicker placeholder="Date of Birth" autocomplete="off"
                [bsConfig]="bsConfig" [bsValue]="bsValue" [(ngModel)]="userInfo.dob" #dob="ngModel"
                [class.is-inv--input]="dob.invalid && dob.touched || (dob.errors?.required && userDetailForm.submitted)"
                required />
              <svg id="Icon_Event_Rounded" data-name="Icon / Event / Rounded" xmlns="http://www.w3.org/2000/svg"
                width="24" height="24" viewBox="0 0 24 24">
                <g id="Icon_Event_Rounded-2" data-name="Icon / Event / Rounded" opacity="0.5">
                  <rect id="Box" width="24" height="24" fill="none" />
                  <path id="Path_2017" data-name="Path 2017"
                    d="M879.9,18h-12a1.075,1.075,0,0,1-1-1V7h14V17A1,1,0,0,1,879.9,18Zm-2-17V2h-8V1a1,1,0,0,0-2,0V2h-1a2.006,2.006,0,0,0-2,2V18a2.006,2.006,0,0,0,2,2h14a2.006,2.006,0,0,0,2-2V4a2.006,2.006,0,0,0-2-2h-1V1a1,1,0,0,0-2,0Zm0,10h-3a1.075,1.075,0,0,0-1,1v3a1.075,1.075,0,0,0,1,1h3a1.075,1.075,0,0,0,1-1V12A1,1,0,0,0,877.9,11Z"
                    transform="translate(-862 2)" />
                </g>
              </svg>
              <div class="form-fill--bar"
                [class.is-inv]="dob.invalid && dob.touched || (dob.errors?.required && userDetailForm.submitted)">
              </div>
              <small class="form-error--text"
                *ngIf="dob.errors?.required && dob.touched || (dob.errors?.required && userDetailForm.submitted)">
                Date of Birth is Required
              </small>
            </div>
            <div class="form-elem">
              <input type="number" name="phoneNum"
                [class.is-inv--input]="phoneNum.invalid && phoneNum.touched || (phoneNum.errors?.required && userDetailForm.submitted)"
                placeholder="Mobile Number" [(ngModel)]="userInfo.phone_no" #phoneNum="ngModel" required
                pattern="[0-9]{10}" />
              <div class="form-fill--bar"
                [class.is-inv]="phoneNum.invalid && phoneNum.touched || (phoneNum.errors?.required && userDetailForm.submitted)">
              </div>
              <small class="form-error--text" *ngIf="phoneNum.errors?.pattern && phoneNum.touched">
                Mobile number must contain 10 digits
              </small>
              <small class="form-error--text" *ngIf="phoneNum.errors?.required && phoneNum.touched">
                Please enter your phone number
              </small>
            </div>
          </div>
          <div class="form-row--2">
            <div class="form-elem">
              <input type="email" name="email"
                [class.is-inv--input]="email.invalid && email.touched || (email.errors?.required && userDetailForm.submitted)"
                placeholder="Email" aria-describedby="emailHelpBlock" [(ngModel)]="userInfo.email" #email="ngModel"
                required email disabled />
              <div class="form-fill--bar"
                [class.is-inv]="email.invalid && email.touched || (email.errors?.required && userDetailForm.submitted)">
              </div>
              <small class="form-error--text"
                *ngIf="email.errors?.required && email.touched || (email.errors?.required && userDetailForm.submitted)">
                Email is Required
              </small>
              <small class="form-error--text" *ngIf="email.errors?.email && email.touched">
                Email is Invalid
              </small>
            </div>
          </div>
          <div class="form-row--1">
            <div class="form-elem">
              <input type="text" name="inst"
                [class.is-inv--input]="instname.invalid && instname.touched  || (instname.errors?.required && userDetailForm.submitted)"
                placeholder="Your College or School Name" [(ngModel)]="userInfo.inst_name" #instname="ngModel" required />
              <div class="form-fill--bar"
                [class.is-inv]="instname.invalid && instname.touched  || (instname.errors?.required && userDetailForm.submitted)">
              </div>
              <small class="from-text text-danger"
                *ngIf="instname.errors?.required && instname.touched  || (instname.errors?.required && userDetailForm.submitted)">
                Please select your College
              </small>
            </div>
            <div class="form-elem">
              <select name="course" class="custom-select form-control"
                [class.is-inv--input]="course.invalid && course.touched || (course.errors?.required && userDetailForm.submitted)"
                [(ngModel)]="userInfo.qual" #course="ngModel" (change)="filterSelectedCourse($event.target.value)" required>
                <option [ngValue]="null">Course</option>
                <option *ngFor="let course of courses.data">{{ course.bName }}</option>
              </select>
              <div class="form-fill--bar"
                [class.is-inv]="course.invalid && course.touched || (course.errors?.required && userDetailForm.submitted)">
              </div>
              <small class="form-error--text"
                *ngIf="course.errors?.required && course.touched || (course.errors?.required && userDetailForm.submitted)">
                Please select your latest course
              </small>
            </div>
          </div>
          <div class="form-row--3">
            <div class="form-elem">
              <input type="text" name="usn" [class.is-inv--input]="usnCtrl.touched && usnCtrl.invalid"
                [(ngModel)]="userInfo.usn" placeholder="USN" #usnCtrl="ngModel" />
              <div class="form-fill--bar" [class.is-inv]="usnCtrl.invalid && usnCtrl.touched"></div>
              <small class="form-error--text" *ngIf="usnCtrl.touched && usnCtrl.errors?.pattern">USN format is wrong,
                please use only caps for letters.
              </small>
            </div>
            <div class="form-elem">
              <select name="section" class="custom-select form-control"
                [(ngModel)]="userInfo.section">
                <option [ngValue]="null">Section *</option>
                <option>A</option>
                <option>B</option>
                <option>C</option>
                <option>D</option>
                <option>E</option>
                <option>F</option>
              </select>
            </div>
            <div class="form-elem">
              <select name="branch" class="custom-select form-control"
                [class.is-inv--input]="branch.invalid && branch.touched || (branch.errors?.required && userDetailForm.submitted)"
                [(ngModel)]="userInfo.branch" #branch="ngModel">
                <option [ngValue]="null">Branch</option>
                <option *ngFor="let bch of branches.subName">{{ bch }}</option>
                <option [ngValue]="Other">Other</option>
              </select>
            </div>
            <div class="form-elem">
              <select name="yop" class="custom-select form-control"
                [class.is-inv--input]="yop.invalid && yop.touched || (yop.errors?.required && userDetailForm.submitted)"
                [(ngModel)]="userInfo.yop" #yop="ngModel" required>
                <option [ngValue]="null">Year of Passing</option>
                <option>2028</option>
                <option>2027</option>
                <option>2026</option>
                <option>2025</option>
                <option>2024</option>
                <option>2023</option>
                <option>2022</option>
                <option>2021</option>
                <option>2020</option>
                <option>2019</option>
                <option>2018</option>
                <option>2017</option>
                <option>2016</option>
                <option>2015</option>
                <option>2014</option>
                <option>2013</option>
                <option>2012</option>
                <option>2011</option>
                <option>2010</option>
                <option>2009</option>
              </select>
              <div class="form-fill--bar"
                [class.is-inv]="yop.invalid && yop.touched || (yop?.errors?.required && userDetailForm.submitted)"></div>
              <small class="form-error--text"
                *ngIf="yop?.errors?.required && yop.touched || (yop?.errors?.required && userDetailForm.submitted)">
                Please select your Year of Passing
              </small>
            </div>
          </div>
          <!-- <div class="form-row--2">
          <div class="form-elem elem-custom-cta">
            <input class="hidden-xs-up" name="subscriber" id="cbx" type="checkbox" checked
              [(ngModel)]="userInfo.subscribed" #subscriber="ngModel" />
            <label class="cbx" for="cbx"></label><label class="lbl" for="cbx">Sign me up for email updates about <i>bank
                exams, government exams, company interview dates and much more</i></label>
          </div>
        </div> -->
          <div class="form-row--1">
            <button type="submit" value="submit" class="custom-btn register-btn">
              Save Changes
            </button>
          </div>
          <small class="form-error--text form-error-last" *ngIf="userDetailForm.invalid && userDetailForm.submitted">
            Please fill all the details first.
          </small>
        </form>
      </div>
      <div class="detail-bar" *ngIf="selectedView[1]">Email Update</div>
      <!-- Password Changes -->
      <div class="detail-bar" *ngIf="selectedView[2]">
        <p>To change your password, please enter your current password.</p>
        <small>You're passwords are secure as they come, but its still recommended to change your password once every 3 months.</small>
        <small>Ensure that your password is at least 8 characters long and contains at least one capital letter.</small>
        <form #userNewPassword="ngForm" (ngSubmit)="updateUserNewPassword(userNewPassword.valid)" appPasswordEqualValidator autocomplete="off">
          <div class="form-row--2--btn">
            <div class="form-elem-input">
              <input type="password" name="currentPass" [disabled]="disableFields" required
                [class.is-inv--input]="currentPass.invalid && currentPass.touched || (currentPass.errors?.required && userNewPassword.submitted)"
                placeholder="Current Password *" [(ngModel)]="newPassword.currentPass" #currentPass="ngModel" />
              <div class="form-fill--bar"
                [class.is-inv]="currentPass.invalid && currentPass.touched || (currentPass.errors?.required && userNewPassword.submitted)">
              </div>
              <small class="form-error--text"
                *ngIf="currentPass.errors?.required && currentPass.touched || (currentPass.errors?.required && userNewPassword.submitted)">
                Your current password is required
              </small>
              <small class="form-error--text" *ngIf="currentPass.errors?.currentPass && currentPass.touched">
                Your current password is required
              </small>
            </div>
            <div class="form-elem-btn">
              <button type="button" class="cust-btn-small" (click)="checkCurrentPassword()" [disabled]="disableFields">
                Check
              </button>
            </div>
          </div>
          <div class="form-row--1" *ngIf="visibleFields">
            <div class="form-elem">
              <input type="password" name="password" class="form-input"
                [class.is-inv--input]="password.invalid && password.touched || (password.pristine && userNewPassword.submitted)"
                placeholder="Password *" aria-describedby="passwordHelpBlock" [(ngModel)]="newPassword.password"
                #password="ngModel" required minlength="8" appPasswordValidator />
              <div class="form-fill--bar"
                [class.is-inv]="password.invalid && password.touched || (password.pristine && userNewPassword.submitted)">
              </div>
              <small class="form-error--text" *ngIf="password.errors?.minlength && password.touched">
                Password Must be at least 8 characters long
              </small>
              <small class="form-error--text"
                *ngIf="password.errors?.passwordCaps && password.touched && !password.errors?.required">
                Please include one Capital Letter in your password
              </small>
              <small class="form-error--text"
                *ngIf="userNewPassword.errors?.notEqual && confPass.touched && !confPass.errors?.required">
                Passwords do not match
              </small>
              <small class="form-error--text"
                *ngIf="password.errors?.required && password.touched || (password.pristine && userNewPassword.submitted)">
                Password is Required
              </small>
            </div>
          </div>
          <div class="form-row--1" *ngIf="visibleFields">
            <div class="form-elem">
              <input type="password" name="conf_password" class="form-input"
                [class.is-inv--input]="confPass.invalid && confPass.touched || (confPass.pristine && userNewPassword.submitted)"
                placeholder="Confirm Password *" [(ngModel)]="newPassword.conf_password" #confPass="ngModel" required
                appPasswordValidator />
              <div class="form-fill--bar"
                [class.is-inv]="confPass.invalid && confPass.touched || (confPass.pristine && userNewPassword.submitted)">
              </div>
              <small class="form-error--text"
                *ngIf="userNewPassword.errors?.notEqual && confPass.touched && !confPass.errors?.required">
                Passwords do not match
              </small>
              <small class="form-error--text"
                *ngIf="confPass.errors?.required && confPass.touched || (confPass.pristine && userNewPassword.submitted)">
                You have to confirm your password
              </small>
            </div>
          </div>
          <div class="form-row--1" *ngIf="visibleFields">
            <button type="submit" value="submit" class="custom-btn register-btn">
              Save Your Password
            </button>
          </div>
          <small class="form-error--text form-error-last" *ngIf="newPassword.invalid && userNewPassword.submitted">
            Please fill all the details first.
          </small>
        </form>
      </div>
      <!-- Password Changes -->
    </div>
    <ng-template #successTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-success">Details Updated</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Your details have been updated successfully</p>
      </div>
    </ng-template>
    <ng-template #imgSuccessTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-success">Image Updated</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Your profile image has been updated successfully</p>
      </div>
    </ng-template>
    <ng-template #errorTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-danger">{{errorShowText}}</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Please try again</p>
      </div>
    </ng-template>
    <ng-template #imgErrorTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-danger">Oops, something went wrong!</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>{{ imgUploadError }}</p>
      </div>
    </ng-template>
    <ng-template #imgUpdlTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-info">Change Profile Image</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <p>Only .png, jpg and .jpeg images are supported</p>
        <p>Max file size is 10MB</p>
        <button class="btn btn-primary mr-4" (click)="uploadFile()">Upload</button>
        <button class="btn btn-warning" (click)="cancelUpload()">Cancel</button>
      </div>
    </ng-template>
  </div>