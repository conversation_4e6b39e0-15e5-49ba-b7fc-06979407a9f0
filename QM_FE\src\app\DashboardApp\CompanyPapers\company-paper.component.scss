.chapter-wrap {
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;
  padding: 10px;

  .header {
    display: flex;
    height: 2.5rem;
    
    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .logo-wrap {
    margin: 0.5rem;
    padding: 1rem;
    height: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;

    .logo {
      margin: auto;
      width: 10rem;
    }
  }

  .form-wrap {
    position: relative;
    width: 100%;
    margin-bottom: 1em;

    .locked-resource {
      height: 100%;
      width: 70%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      margin: auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: rgba(146, 135, 135, 0.85);

      img {
        height: 20%;
      }

      p {
        font-weight: bold;
        font-size: 20px;
        color: #fff;
        text-align: center;
      }
    }

    .item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.5rem;
      text-align: center;

      label {
        width: 20rem;
      }

      select {
        display: block;
        height: 50px;
        width: 20rem;
        padding: 3px 5px;
        border: solid 1.5px #707070;
        border-radius: 5px;
        transition: all 0.3s ease;
      }

      .papers {
        width: 80%;
        margin-top: 2em;

        .paper {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 60px;
          width: 100%;
          padding: 1em;
          margin-top: 1em;
          color: #fff;
          background-color: #0b6fb1;

          h6, p {
            margin: 0;
          }
        }
      }
    }

    button {
      cursor: pointer;
      margin: 1rem;
      width: 10rem;
      padding: 0.3rem;
      background-color: #e88224;
      border: none;
      border-radius: 5px;
      color: white;
      transition: all 0.4s ease-out;

      &:hover {
        background-color: #38A3E9;
        color: #000;
        transition: all 0.3s ease-in;
      }
    }
  }
}

.copy-content {
  text-align: right;

  p {
    color: #707070;
    margin: 0;
  }
}

@media (max-width: 440px) {
  .chapter-wrap{

    .header {
      height: 3.5rem;
    }
    
    .logo-wrap{
      grid-template-columns: 1fr 1fr;

      .logo{
        width: 6rem;
      }
    }
   .form-wrap {
     display: flex;
     flex-direction: column;

     .locked-resource {
      height: 100%;
      width: 90%;

      img {
        height: 20%;
      }

      p {
        font-weight: bold;
        font-size: 20px;
        color: #fff;
        text-align: center;
      }
    }
    .item {
      display: flex;
      flex-direction: column;

      select {
        display: block;
        max-width: 70vw;
      }

      .papers {
        max-width: 80%;
        margin-top: 0;

        .paper {
          flex-direction: column;
          height: initial;

          h6 {
            font-size: 1.2rem;
            margin-bottom: 0.5em;
          }
        }
      }
    }
  }



  }
}