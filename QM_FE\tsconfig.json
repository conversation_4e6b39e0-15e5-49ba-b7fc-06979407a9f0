{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2015", "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es5", "typeRoots": ["node_modules/@types"], "lib": ["es2018", "dom"], "plugins": [{"name": "typescript-tslint-plugin"}], "resolveJsonModule": true, "esModuleInterop": true, "paths": {"jdoodle-swagger/*": ["projects/jdoodle-swagger/*"]}}}