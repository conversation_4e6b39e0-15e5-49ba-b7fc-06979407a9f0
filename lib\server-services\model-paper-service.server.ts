// lib/server-services/tests-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { Paper } from '@/types/model-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  papersUrl: `${API_BASE_URL}/test/question/ret/papers`,
  questionsUrl: `${API_BASE_URL}/test/question/ret`,
  submitUrl: `${API_BASE_URL}/test/answer/submit`,
  indResultsAllUrl: `${API_BASE_URL}/view/results/ind/all`,
  indResultsUrl: `${API_BASE_URL}/view/results/ind/bypaper`,
  allResultsUrl: `${API_BASE_URL}/view/results/all`,
  uploadExplUrl: `${API_BASE_URL}/test/model/paper/`,
  openResultsUrl: `${API_BASE_URL}/v2/test/open`,
  createPaperUrl: `${API_BASE_URL}/admin/paper/upload/test`,
  createQuestionUrl: `${API_BASE_URL}/admin/paper/upload/question`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ModelPapersServerTestsService {
  /**
   * Get all papers
   */
  static async getPapers(): Promise<Paper[]> {
    // Check authentication
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.papersUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching papers:', error)
      return []
    }
  }

  /**
   * Get questions for a specific paper
   */
  static async getQuestions(paperId: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.questionsUrl}/${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching questions for paper ${paperId}:`, error)
      throw error
    }
  }

  /**
   * Get individual results for a user
   */
  static async getIndividualResults(email: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const data = { email }

    try {
      const response = await axios.post(
        ENDPOINTS.indResultsAllUrl,
        data,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching individual results:', error)
      throw error
    }
  }

  /**
   * Get individual result for a specific paper
   */
  static async getIndividualResult(
    email: string,
    paperId: string
  ): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const data = { email, paper_id: paperId }

    try {
      const response = await axios.post(
        ENDPOINTS.indResultsUrl,
        data,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching individual result:', error)
      throw error
    }
  }

  /**
   * Get all results
   */
  static async getAllResults(): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.allResultsUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching all results:', error)
      throw error
    }
  }

  /**
   * Get all open results for a user
   */
  static async getAllOpenResults(email: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.openResultsUrl}/papers/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders(token))
      return response.data
    } catch (error) {
      console.error('Error fetching open results:', error)
      throw error
    }
  }

  /**
   * Get open paper section-wise marks
   */
  static async getOpenPaperSectionWiseMarks(
    email: string,
    answerId: string
  ): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.openResultsUrl}/papers/${email}/section/${answerId}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders(token))
      return response.data
    } catch (error) {
      console.error('Error fetching section-wise marks:', error)
      throw error
    }
  }

  /**
   * Get all open company results
   */
  static async getAllOpenCompanyResults(email: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.openResultsUrl}/company/papers/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders(token))
      return response.data
    } catch (error) {
      console.error('Error fetching company results:', error)
      throw error
    }
  }

  /**
   * Get all weekly competitive results
   */
  static async getAllWeeklyCompetitiveResults(email: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const url = `${ENDPOINTS.openResultsUrl}/competitive/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders(token))
      return response.data
    } catch (error) {
      console.error('Error fetching competitive results:', error)
      throw error
    }
  }
}
