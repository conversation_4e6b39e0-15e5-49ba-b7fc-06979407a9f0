import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpHeaders, HttpClient } from '@angular/common/http';

import { ApiAuthService } from '../Services/api-auth.service';

@Injectable({
  providedIn: 'root'
})
export class OpenTestsService {

  private papersUrl    = 'https://api.quantmasters.in/test/open/papers';
  private questionsUrl = 'https://api.quantmasters.in/test/open/paper';
  private submitUrl    = 'https://api.quantmasters.in/test/open/submit';

  private submitSectionUrl = 'https://api.quantmasters.in/v2/test/open';

  private trialPapersUrl    = 'https://api.quantmasters.in/test/sample/papers';
  private trialQuestionsUrl = 'https://api.quantmasters.in/test/sample/paper/new/';

  private freeCompanyPapersUrl    = 'https://api.quantmasters.in/test/sample/company/papers';

  private openCompetitiveUrl       = 'https://api.quantmasters.in/v2/test/open/competitive/papers';
  private openCompetitiveQuesUrl   = 'https://api.quantmasters.in/v2/test/open/competitive/paper/';
  private openCompetitiveSubmitUrl = 'https://api.quantmasters.in/v2/test/open/competitive/submit/marks';

  private createOpenQuestion = 'https://api.quantmasters.in/admin/paper/open/update';

  private JwtToken: string;

  constructor(private http: HttpClient,
              private apiAuthService: ApiAuthService) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getPapers(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.papersUrl, httpOps);
  }

  getQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.questionsUrl + `/${paper_id}`, httpOps);
  }

  addExplanation(paperId: string, quesNo: string, explanation: string): Observable<string> {
    this.setSecurityToken();

    const url = this.questionsUrl + '/' + paperId + '/explanation';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const reqBody = {
      question_no: quesNo,
      explanation
    };

    return this.http.post<string>(url, reqBody, httpOps);
  }

  submitAnswers(email: string, paper_id: string, marks: number, ans_date: string): Observable<string> {
    const data = {
      email: email,
      paper_id: paper_id,
      marks: marks,
      answered_on: ans_date
    };

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.submitUrl, data, httpOps);
  }

  submitSectionAnswers(email: string, paper_id: string, answer_id: string, data: any): Observable<string> {

    const url = this.submitSectionUrl + '/' + paper_id + '/submit/' + answer_id + '/' + email + '/sections';

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, data, httpOps);
  }

  getTrialPapers(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.trialPapersUrl, httpOps);
  }

  getFreeCompanyPapers(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.freeCompanyPapersUrl, httpOps);
  }

  getTrialQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.trialQuestionsUrl + `${paper_id}`, httpOps);
  }

  getOpenCompetitivePapers(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.openCompetitiveUrl, httpOps);
  }

  etOpenCompetitivePapersOverriden(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(`${this.openCompetitiveUrl}/true`, httpOps);
  }

  getOpenCompetitiveQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.openCompetitiveQuesUrl + `${paper_id}`, httpOps);
  }

  submitOpenCompetitiveAnswers(email: string, paper_id: string, marks: number): Observable<string> {

    const data = {
      email: email,
      paper_id: paper_id,
      marks: marks
    };

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.openCompetitiveSubmitUrl, data, httpOps);
  }

  // Create question for paper
  postPaperQuestionDetails(QuestionDetails: object, paper_id: string): Observable<any> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/admin/paper/open/update/' + paper_id + '/question', QuestionDetails);
    const url = `${this.createOpenQuestion}/${paper_id}/question`;

    return this.http.post<object>(url, protectedBody, httpOps);
  }

  deleteQuestion(paper_id: string, ques_no: string): Observable<string> {

    this.setSecurityToken();

    const url = `${this.createOpenQuestion}/question/${paper_id}/${ques_no}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.delete<string>(url, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
