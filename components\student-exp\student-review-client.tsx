'use client'

import { useState, useEffect, useMemo } from 'react'
import { SelectedVideoType, StudentVideoListType } from '@/types/video-types'
import VideoCard from '@/components/student-exp/video-card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'

import { StreamingService } from '@/lib/client-services/streaming.service'
import VideoStreamClient from './video-stream-client'

const VIDEOS_PER_PAGE = 6

interface StudentReviewClientProps {
  initialVideoList: StudentVideoListType[]
  error?: string
}

export default function StudentReviewClient({
  initialVideoList = [],
  error
}: StudentReviewClientProps) {
  const videoList = initialVideoList
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const [selectedVideo, setSelectedVideo] = useState<SelectedVideoType | null>(
    null
  )
  const [videoLink, setVideoLink] = useState<string | null>(null)
  const [videoError, setVideoError] = useState<string | undefined>(undefined)

  // Calculate displayed videos and check if there are more to load
  const displayedVideos = useMemo(() => {
    return videoList.slice(0, currentPage * VIDEOS_PER_PAGE)
  }, [videoList, currentPage])

  const hasMoreVideos = useMemo(() => {
    return displayedVideos.length < videoList.length
  }, [displayedVideos.length, videoList.length])

  // Google Analytics tracking
  useEffect(() => {
    if (window.gtag) {
      window.gtag('config', 'UA-163800914-1', { page_path: '/student-review' })
    }
  }, [])

  // Fetch video link when a video is selected and modal is opened
  useEffect(() => {
    const fetchVideoLink = async (): Promise<void> => {
      if (!selectedVideo || !isModalOpen) return

      try {
        setIsLoading(true)
        setVideoError(undefined)

        let fetchMethod
        switch (selectedVideo.type) {
          case '1':
            fetchMethod = StreamingService.getTestimonyVideoLink
            break
          case '2':
          case '6':
            fetchMethod = StreamingService.getReviewVideoLink
            break
          default:
            fetchMethod = StreamingService.getVideoLink
            break
        }

        const link = await fetchMethod(selectedVideo.id)
        setVideoLink(link)

        // Track video view in analytics
        if (window.gtag) {
          window.gtag('event', 'video_view', {
            video_id: selectedVideo.id,
            video_type: selectedVideo.type
          })
        }
      } catch (err) {
        console.error('Error fetching video link:', err)
        setVideoError('Failed to load video')
      } finally {
        setIsLoading(false)
      }
    }

    fetchVideoLink()
  }, [selectedVideo, isModalOpen])

  // Handle starting a stream - now just sets state for modal
  const startStream = (
    videoId: string,
    videoType: string,
    videoName: string
  ): void => {
    setSelectedVideo({ id: videoId, type: videoType, name: videoName })
    setIsModalOpen(true)
  }

  // Load more videos
  const handleLoadMore = (): void => setCurrentPage((prev) => prev + 1)

  // Reset modal state when closing
  const handleModalClose = (): void => {
    setIsModalOpen(false)
    setTimeout(() => {
      setSelectedVideo(null)
      setVideoLink(null)
      setVideoError(undefined)
    }, 300) // Small delay to avoid visual glitches
  }

  return (
    <div>
      {error ? (
        <Alert variant="destructive" className="my-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading && videoList.length === 0
              ? Array(VIDEOS_PER_PAGE)
                  .fill(0)
                  .map((_, index) => (
                    <div key={index} className="space-y-3">
                      <Skeleton className="h-[180px] w-full rounded-md" />
                      <Skeleton className="h-6 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-10 w-full" />
                    </div>
                  ))
              : displayedVideos.map((video) => (
                  <VideoCard
                    key={video.video_id}
                    video={video}
                    startStream={startStream}
                  />
                ))}
          </div>

          {videoList.length > 0 && (
            <div className="mt-8 text-center">
              <Button
                onClick={handleLoadMore}
                disabled={!hasMoreVideos}
                variant="outline"
                className="px-6"
              >
                {hasMoreVideos ? 'Show More' : 'No More Videos'}
              </Button>
            </div>
          )}
        </>
      )}

      {/* Video Stream Modal */}
      {selectedVideo && (
        <VideoStreamClient
          isModalOpen={isModalOpen}
          handleModalClose={handleModalClose}
          selectedVideo={selectedVideo}
          isLoading={isLoading}
          videoLink={videoLink}
          videoError={videoError}
        />
      )}

      <div className="text-center text-sm text-gray-500 mt-12">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
