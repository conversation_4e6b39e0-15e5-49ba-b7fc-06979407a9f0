// lib/server-services/admin/topics.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { SuperGroup, Group, TMCQGroup, TMCQSubGroup } from '@/types/admin-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value
  return !!(token && email)
}

export class AdminTopicsServerService {
  /**
   * Get super groups for chapter-based papers
   */
  static async getSuperGroups(): Promise<SuperGroup[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${API_BASE_URL}/test/progression/super-groups`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get groups for a super group
   */
  static async getGroups(superGroupId: string): Promise<Group[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${API_BASE_URL}/test/progression/groups/${superGroupId}`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error(
        `Error fetching groups for super group ${superGroupId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get TMCQ groups
   */
  static async getTMCQGroups(): Promise<TMCQGroup[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      // Use v3 API to match Angular implementation
      const response = await axios.get(
        `${API_BASE_URL}/v3/admin/tmcq/group`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching TMCQ groups:', error)
      return []
    }
  }

  /**
   * Get TMCQ sub groups
   */
  static async getTMCQSubGroups(groupId: string): Promise<TMCQSubGroup[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      // Use v3 API to match Angular implementation
      const response = await axios.get(
        `${API_BASE_URL}/v3/admin/tmcq/subgroup/${groupId}`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error(
        `Error fetching TMCQ sub groups for group ${groupId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get verbal practice groups
   */
  static async getVerbalPracticeGroups(): Promise<any[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${V2_BASE_URL}/verbal/practice/groups`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching verbal practice groups:', error)
      return []
    }
  }

  /**
   * Get technical practice groups
   */
  static async getTechnicalPracticeGroups(): Promise<any[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${V2_BASE_URL}/technical/practice/groups`,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching technical practice groups:', error)
      return []
    }
  }
}
