import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { NotesService } from 'src/app/Services/Blog/notes.service';
import { UserService } from '../../Services/user.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import * as $ from 'jquery';

import { Notes } from '../models/Notes';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

declare let fbq: Function;
declare let gtag: Function;

@Component({
  selector: 'app-notes-detail',
  templateUrl: './notes-detail.component.html',
  styleUrls: ['./notes-detail.component.scss']
})
export class NotesDetailComponent implements OnInit {

  @ViewChild('messageTemplate') public messageTemplate: TemplateRef<any>;

  public noteId: string;
  public showIndicator: boolean;
  public noteToRead: Notes;

  public embedVideoUrl: SafeResourceUrl;
  public shareNoteUrl: string;

  public showCopyAlert = false;
  public copyAlert = {
    type: 'info',
    msg: 'Url Copied!',
    timeout: 2000,
    isOpen: false
  };

  // public prevNoteId = '';
  // public prevNoteName = '';
  // public nextNoteId = '';
  // public nextNoteName = '';
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };

  public myComment: string;
  public myCommentBody = { 'text': '', 'email': '' };
  public myReplyComment: string;
  public myCommentReplyBody = { 'text': '', 'email': '' };
  public message: string; // Added by Gururaj.
  public messageHeader: string;
  public notesCommentList = [];
  public userImage: string;
  public isCollapsed = [];
  public isCollapsedReplies = [];
  public commentReply = [];
  public email = sessionStorage.getItem('QMail');
  public messageModalRef: BsModalRef;
  public oUserAvatar = {};
  public sAWSSignledURl = '';
  constructor(private router: Router,
    private route: ActivatedRoute,
    private notesService: NotesService,
    public userService: UserService,
    private _sanitizationService: DomSanitizer,
    public modalService: BsModalService) {

    this.router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });

        fbq('track', 'PageView', {
          value: 0.1,
          currency: 'INR',
        });
      }
    });
  }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.route.paramMap.subscribe(params => {

      if (!this.noteId) {
        this.noteId = params.get('noteId');
      }

      if (!this.noteId && localStorage.getItem('QNotesData') !== null) {
        const notesData = JSON.parse(localStorage.getItem('QNotesData'));

        this.noteId = notesData.thisNoteId;
      }

      this.noteToRead = new Notes();

      this.shareNoteUrl = window.location.href + '/' + this.noteId;

      this.notesService.getNotesForReading(this.noteId).subscribe(response => {
        const resp = JSON.parse(JSON.stringify(response));

        this.noteToRead = resp;

        this.embedVideoUrl = this.noteToRead.notes_video ?
          this._sanitizationService.bypassSecurityTrustResourceUrl('https://www.youtube.com/embed/' + this.noteToRead.notes_video) : '';
        this.noteToRead.posted_on = new Date(this.noteToRead.posted_on).toLocaleDateString();
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
      });
    });

    $('body').bind('cut copy select', (e) => e.preventDefault());
    $('body').on('contextmenu', (e) => false);
    $('body').on('selectstart', (e) => false);


    // To get the current user profile avatar
    this.fnGetUserAvatar(this.email, 'CurrentUser', '', '', function () {
      that.showIndicator = true;
      // To get the list of comments for this note
      that.fnGetComments(that.noteId);
    });
  }

  fnGetUserAvatar(mailId, sType, iIndex, jIndex, fnSuccess) {
    this.showIndicator = true;
    this.userService.getUserProfileAvatar(mailId).subscribe(response2 => {
      const resp = JSON.parse(JSON.stringify(response2));
      if (sType === 'CurrentUser') {
        this.userImage = resp.path;
        this.oUserAvatar[mailId] = resp.path;
        return fnSuccess();
      } else if (sType === 'OtherUser') {
        this.oUserAvatar[mailId] = resp.path;
        if ((iIndex || iIndex === 0) && (jIndex || jIndex === 0)) {
          this.notesCommentList[iIndex].replies[jIndex].user_avatar = resp.path;
        } else if ((iIndex || iIndex === 0) && jIndex === '') {
          this.notesCommentList[iIndex].user_avatar = resp.path;
        }
        // return resp.path;
      }
    }, error => {
      this.showIndicator = false;
    });
  }
  shareOnWhatsApp() {

    const urlText = 'https://wa.me/?text=Hey!,%20Checkout%20this%20article%20from%20Quant%20Masters:%0A' +
      this.noteToRead.notes_name + '%0A%40%0A' + this.shareNoteUrl;

    window.open(urlText, '__blank');
  }

  shareOnLinkedIn() {

    const urlText = `https://www.linkedin.com/sharing/share-offsite/?url=${this.shareNoteUrl}`;

    window.open(urlText, '__blank');
  }

  shareOnFacebook() {

    const urlText = `https://www.facebook.com/sharer/sharer.php?u=${this.shareNoteUrl}`;

    window.open(urlText, '__blank');
  }

  shareOnTwitter() {

    const urlText = `https://twitter.com/home?status=${this.shareNoteUrl}`;

    window.open(urlText, '__blank');
  }

  shareDirectLink() {

    const that = this;
    // const selBox = document.createElement('textarea');
    // selBox.style.position = 'fixed';
    // selBox.style.left = '0';
    // selBox.style.top = '0';
    // selBox.style.opacity = '0';
    // selBox.value = this.shareNoteUrl;
    // document.body.appendChild(selBox);
    // selBox.focus();
    // selBox.select();
    // document.execCommand('copy');
    // document.body.removeChild(selBox);

    navigator['clipboard'].writeText(this.shareNoteUrl).then(() => {
      that.showCopyAlert = true;
    }, () => {});
  }

  onClosed() {
    this.showCopyAlert = false;
  }

  // takeToPreviousNote() {

  //   if (this.prevNoteId !== '') {
  //     this.router.navigate(['notes/detail', this.prevNoteId]);
  //   }
  // }

  takeToNotesHome() {

    this.router.navigate(['notes']);
  }

  // takeToNextNote() {

  //   if (this.nextNoteId !== '') {
  //     this.router.navigateByUrl(this.router.url.replace('noteId', this.nextNoteId));
  //   }
  // }

  fnGetComments(noteId) {
    this.notesService.getNotesComments(this.noteId).subscribe(response => {
      const responseParsed = JSON.parse(JSON.stringify(response));
      this.notesCommentList = responseParsed;

      for (let i = 0; i < responseParsed.length; i++) {
        this.isCollapsed.push(true);
        const date = new Date(responseParsed[i].created_at).toDateString();
        this.notesCommentList[i].created_at = date;

        if (!responseParsed[i].user_avatar) {
          if (this.oUserAvatar[responseParsed[i].email]) {
            responseParsed[i].user_avatar = this.oUserAvatar[responseParsed[i].email];
          } else {
            this.fnGetUserAvatar(responseParsed[i].email, 'OtherUser', i, '', '');
          }
        } else {
          if (this.oUserAvatar[responseParsed[i].email]) {
            responseParsed[i].user_avatar = this.oUserAvatar[responseParsed[i].email];
          } else {
            this.fnGetUserAvatar(responseParsed[i].email, 'OtherUser', i, '', '');
          }
        }
        if (responseParsed[i].replies.length > 0) {
          for (let j = 0; j < responseParsed[i].replies.length; j++) {
            this.isCollapsedReplies.push(true);
            const replyDate = new Date(responseParsed[i].replies[j].created_at).toDateString();
            this.notesCommentList[i].replies[j].created_at = replyDate;

            if (!responseParsed[i].replies[j].user_avatar) {
              if (this.oUserAvatar[responseParsed[i].replies[j].email]) {
                responseParsed[i].replies[j].user_avatar = this.oUserAvatar[responseParsed[i].replies[j].email];
              } else {
                this.fnGetUserAvatar(responseParsed[i].replies[j].email, 'OtherUser', i, j, '');
              }
            } else {
              if (this.oUserAvatar[responseParsed[i].replies[j].email]) {
                responseParsed[i].replies[j].user_avatar = this.oUserAvatar[responseParsed[i].replies[j].email];
              } else {
                this.fnGetUserAvatar(responseParsed[i].replies[j].email, 'OtherUser', i, j, '');
              }
            }
          }
        }
      }

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }
  fnOnClickClearComment() {
    this.myComment = '';
  }
  fnOnClickConfrimComment(comment) {
    if (comment) {
      this.myCommentBody.text = comment;
      this.myCommentBody.email = sessionStorage.getItem('QMail');
      this.showIndicator = true;
      this.notesService.fnPostNoteComment(this.noteId, this.myCommentBody).subscribe(response => {
        // this.showIndicator = false;
        this.messageHeader = 'Done';
        this.message = 'Thank you for your valuable Feedback..!';
        this.openMessageModal(this.messageTemplate);
        this.myComment = '';
        this.fnGetComments(this.noteId);
      }, error => {
        this.showIndicator = false;
        this.messageHeader = 'Failed';
        this.message = 'We Sorry, Failed to added your valuable comments..!';
        this.openMessageModal(this.messageTemplate);
      });
    } else {
      this.messageHeader = 'Remember';
      this.message = 'Please provide your comment before submit..!';
      this.openMessageModal(this.messageTemplate);
    }
  }
  fnOnClickConfrimCommentReply(commentId, comment, i) {
    this.myCommentReplyBody = { 'text': '', 'email': '' };
    if (comment) {
      this.myCommentReplyBody.text = comment;
      this.myCommentReplyBody.email = sessionStorage.getItem('QMail');
      this.showIndicator = true;
      this.notesService.fnPostNoteCommentreply(this.noteId, commentId, this.myCommentReplyBody).subscribe(response => {
        this.messageHeader = 'Done';
        this.message = 'Thank you for your valuable response..!';
        this.openMessageModal(this.messageTemplate);
        this.myComment = '';
        this.commentReply[i] = '';
        this.isCollapsed[i] = true;
        this.fnGetComments(this.noteId);
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
        this.messageHeader = 'Failed';
        this.message = 'We Sorry, Failed to added your valuable response..!';
        this.openMessageModal(this.messageTemplate);
      });
    } else {
      this.messageHeader = 'Remember';
      this.message = 'Please provide your response before submit..!';
      this.openMessageModal(this.messageTemplate);
    }

  }

  fnOnClickClearReply(index) {
    this.isCollapsed[index] = true;
    this.commentReply[index] = '';
  }
  openMessageModal(ShowMessage: TemplateRef<any>) {
    this.messageModalRef = this.modalService.show(ShowMessage, this.config);
  }
}
