import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { JobPost } from '../../Models/Dashboard/JobPost';
import { JobDetail } from '../..//Models/Dashboard/JobDetail';

@Injectable({
  providedIn: 'root'
})
export class PlacementPostsService {

  private JwtToken: string;
  private submitUrl = 'https://api.quantmasters.in/admin/placements/post';
  private editUrl = 'https://api.quantmasters.in/admin/placements/post/edit';
  private deleteUrl = 'https://api.quantmasters.in/admin/placements/post/';
  private postAdmUrl = 'https://api.quantmasters.in/admin/placements/post/list';
  private postsUrl = 'https://api.quantmasters.in/placements/post/list';
  private imgListUrl = 'https://api.quantmasters.in/admin/placements/companies';
  private imdUpdlUrl = 'https://api.quantmasters.in/admin/placements/post/';

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getAllPosts(): Observable<JobPost[]> {

    return this.http.get<JobPost[]>(this.postsUrl);
  }

  getAllPostsAdmin(): Observable<JobPost[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<JobPost[]>(this.postAdmUrl, httpOps);
  }

  createPost(newPost: JobPost): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.submitUrl + '/new', newPost, httpOps);
  }

  createDetail(newDetail: JobDetail): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.submitUrl + '/detail/new', newDetail, httpOps);
  }

  modifyPost(post: JobPost): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.submitUrl + '/edit', post, httpOps);
  }

  modifyPostDetail(postDetail: JobDetail): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.submitUrl + '/detail/edit', postDetail, httpOps);
  }

  deletePost(id: number): Observable<string> {
    this.setSecurityToken();

    const url = this.deleteUrl + id;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.delete<string>(url, httpOps);
  }

  getImages(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.imgListUrl, httpOps);
  }

  setImage(id: number, selectedFile: File): Observable<string> {
    this.setSecurityToken();

    const getAvatarUrl = this.imdUpdlUrl + id + '/edit/image';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const uploadData = new FormData();
    uploadData.append('image_upload', selectedFile);

    return this.http.post<string>(getAvatarUrl, uploadData, httpOpts);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
