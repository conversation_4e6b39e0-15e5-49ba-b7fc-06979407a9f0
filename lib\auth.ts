const API_BASE_URL = 'https://api.quantmasters.in'

export class AuthService {
  private static getToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('QMA_TOK') || null
    }
    return null
  }

  private static async fetchWithHeaders(
    url: string,
    options: RequestInit = {}
  ) {
    const headers = new Headers(options.headers || {})

    if (!headers.has('Content-Type')) {
      headers.append('Content-Type', 'application/json')
    }

    const token = this.getToken()
    if (token) {
      headers.append('Authorization', `Bearer ${token}`)
    }

    const response = await fetch(url, {
      ...options,
      headers
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || 'Request failed')
    }

    return response.json()
  }

  static async loginUser(user: { email: string; passwd: string }) {
    const response = await this.fetchWithHeaders(`${API_BASE_URL}/user/login`, {
      method: 'POST',
      body: JSON.stringify(user)
    })

    return response
  }

  static async refreshUserLogin(email: string) {
    return this.fetchWithHeaders(`${API_BASE_URL}/login/${email}/refresh`, {
      method: 'GET'
    })
  }

  static async checkUserLogin(email: string) {
    return this.fetchWithHeaders(
      `${API_BASE_URL}/user/manage/${email}/perm/check`,
      {
        method: 'GET'
      }
    )
  }

  static async checkAdminLogin(email: string) {
    return this.fetchWithHeaders(
      `${API_BASE_URL}/admin/manage/${email}/perm/check`,
      {
        method: 'GET'
      }
    )
  }

  static async logoutUser(email: string) {
    return this.fetchWithHeaders(`${API_BASE_URL}/user/logout`, {
      method: 'POST',
      body: JSON.stringify({ email })
    })
  }

  static async refreshLogin(email: string) {
    return this.fetchWithHeaders(`${API_BASE_URL}/user/${email}/refresh`, {
      method: 'GET'
    })
  }
}
