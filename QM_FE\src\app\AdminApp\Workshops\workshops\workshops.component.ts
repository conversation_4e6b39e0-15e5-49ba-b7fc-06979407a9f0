import { Component, OnInit } from '@angular/core';

import * as XLSX from 'xlsx';

import { AdminViewService } from '../../../Services/admin-view.service';

import { Workshop } from '../../../Models/Workshop';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Component({
  selector: 'app-workshops',
  templateUrl: './workshops.component.html',
  styleUrls: ['./workshops.component.scss']
})
export class WorkshopsComponent implements OnInit {

  public allRecords: Workshop[];
  public displayRecords: Workshop[];

  public numResults: number;
  public numFilteredResults: number;

  public showExportOpts = false;

  public showIndicator = false;

  constructor(private adminViewService: AdminViewService) { }

  ngOnInit() {

    this.showIndicator = true;
    this.adminViewService.getWorkshopsRegistrations().subscribe(response => {
      this.allRecords = response;

      this.allRecords.forEach((result: Workshop) => {
        const date = new Date(result.created_at);
        result.selected = false;
        result.display_date = date.toLocaleString();
      });

      this.allRecords = this.allRecords.sort((x, y) => {
        const aDate = new Date(x.created_at),
              bDate = new Date(y.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });

      this.numResults = this.allRecords.length;
      this.numFilteredResults = 0;
      this.displayRecords = this.allRecords.slice(0, 15);

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  public exportAllData() {

    const exportRecords = this.allRecords;

    exportRecords.map(x => {
      delete x.workshop_id;
      delete x.created_at;
      delete x.selected;

      return x;
    });

    this.exportAsExcelFile(exportRecords, 'All-Data');
  }

  public exportSelectedData() {
    const exportRecords = this.allRecords.filter(x => x.selected);

    exportRecords.map(x => {
      delete x.workshop_id;
      delete x.created_at;
      delete x.selected;

      return x;
    });

    this.exportAsExcelFile(exportRecords, 'Selected-Data');
  }

  public toExportFileName(excelFileName: string): string {
    return `${excelFileName}_export_${new Date().getTime()}.xlsx`;
  }

  public exportAsExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    const workbook: XLSX.WorkBook = {Sheets: {'data': worksheet}, SheetNames: ['data']};
    XLSX.writeFile(workbook, this.toExportFileName(excelFileName));
  }

  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayRecords = this.allRecords.slice(startItem, endItem);
  }
}
