import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ApiAuthService } from '.././api-auth.service';

import { Groups } from '../../Models/Blog/Notes';

@Injectable({
  providedIn: 'root'
})
export class NotesService {

  private BaseUrl = 'https://api.quantmasters.in';
  private notesBaseUrl = 'https://api.quantmasters.in/v2/notes';
  private newNoteBaseUrl = 'https://api.quantmasters.in/v3/notes';
  private JwtToken: string;

  constructor(private http: HttpClient,
    private apiAuthService: ApiAuthService) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  // Get call to fetch all group related records
  getSuperGroups(): Observable<any> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const superGroupUrl = this.notesBaseUrl + '/groups';

    return this.http.get<string>(superGroupUrl, httpOps);
  }

  // Post the super group and group
  postSuperGropu(NewGroup: Groups): Observable<any> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const NewGroup_Details = {
      super_id: '',
      super_name: NewGroup.super_name,
      group_id: '',
      group_name: NewGroup.group_name
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/admin/notes/group', NewGroup_Details);

    const superGroupUrl = this.BaseUrl + '/v2/admin/notes/group';
    return this.http.post<any>(superGroupUrl, protectedBody, httpOps);
  }

  // Put call to add group to super group
  putSuperGropu(NewGroup: Groups): Observable<any> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const NewGroup_Details = {
      super_id: NewGroup.super_id,
      super_name: NewGroup.super_name,
      group_id: '',
      group_name: NewGroup.group_name
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('PUT', '/v2/admin/notes/group', NewGroup_Details);

    const Url = this.BaseUrl + '/v2/admin/notes/group';
    return this.http.put<Groups>(Url, protectedBody, httpOps);
  }

  // Get Call
  // To get call the notes list for related group
  getAllNotesListForGroup(groupId): Observable<any> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const Url = this.notesBaseUrl + '/' + groupId;

    return this.http.get<any>(Url, httpOps);
  }

  // Post Call
  // To add new note to related group
  postNewNoteForGroup(note): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/admin/notes', note);
    const Url = this.BaseUrl + '/v2/admin/notes';
    return this.http.post<string>(Url, protectedBody, httpOps);
  }

  // Put Call
  // To add new note to related group
  putNewNoteForGroup(note): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('PUT', '/v2/admin/notes', note);
    const Url = this.BaseUrl + '/v2/admin/notes';
    return this.http.put<string>(Url, protectedBody, httpOps);
  }

  // Get Call
  // To get call the note detail
  getNoteDetail(noteId): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const Url = this.newNoteBaseUrl + '/detail/' + noteId;

    return this.http.get<string>(Url, httpOps);
  }

  // Delete Call
  // To Delete the Note
  deleteNote(noteId): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const Url = this.BaseUrl + '/v2/admin/notes/' + noteId;
    return this.http.delete<string>(Url, httpOps);
  }

  getNotesOfaGroup(groupId: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const notesUrl = this.newNoteBaseUrl + '/' + groupId;

    return this.http.get<string>(notesUrl, httpOps);
  }

  getNotesForReading(noteId: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const detailUrl = this.newNoteBaseUrl + '/detail/' + noteId;

    return this.http.get<string>(detailUrl, httpOps);
  }

  uploadImage(imgPath: File): Observable<any> {

    const uploadData = new FormData();
    uploadData.append('image_upload', imgPath);

    const detailUrl = this.BaseUrl + '/v2/admin/notes/image';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    return this.http.post<any>(detailUrl, uploadData, httpOps);
  }

  // Comment part services
  getNotesComments(noteId: string): Observable<string> {
    const sURL = this.notesBaseUrl + '/' + noteId + '/comments';
    return this.http.get<string>(sURL);
  }

  // Post Call
  // To add new note to related group
  fnPostNoteComment(noteId, myCommentBody): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const sUrl = this.notesBaseUrl + '/' + noteId + '/comment';
    return this.http.post<string>(sUrl, myCommentBody, httpOps);
  }

  // Post Call
  // To add new note to related group
  fnPostNoteCommentreply(noteId, commentId, myCommentBody): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const sUrl = this.notesBaseUrl + '/' + noteId + '/comment/' + commentId + '/reply';
    return this.http.post<string>(sUrl, myCommentBody, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
