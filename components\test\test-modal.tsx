'use client'

import React from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

interface WarnModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
}

export const WarnModal: React.FC<WarnModalProps> = ({
  isOpen,
  onClose,
  onConfirm
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-success">Wait a minute!</DialogTitle>
        </DialogHeader>
        <div className="text-center py-4">
          <p>Looks like you&apos;ve not answered all the questions.</p>
          <p>Do you want to submit anyway?</p>
        </div>
        <DialogFooter className="flex justify-center gap-4">
          <Button variant="default" onClick={onConfirm}>
            Yes
          </Button>
          <Button variant="outline" onClick={onClose}>
            No
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface MarksModalProps {
  isOpen: boolean
  onClose: () => void
  marks: number
  totalMarks: number
  sectionTrack?: { section_name: string; marks: number }[]
}

export const MarksModal: React.FC<MarksModalProps> = ({
  isOpen,
  onClose,
  marks,
  totalMarks,
  sectionTrack = []
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-success">Answers Submitted!</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <p className="mb-4">
            You&apos;ve scored <strong>{marks}</strong> out of{' '}
            <strong>{totalMarks}</strong> in this test.
          </p>

          {sectionTrack.length > 0 && (
            <>
              <p className="font-medium mb-2">Section Wise Breakdown</p>
              <div className="border rounded-md overflow-hidden">
                <div className="grid grid-cols-2 bg-gray-100 p-2 font-medium">
                  <p>Section</p>
                  <p className="text-right">Marks</p>
                </div>
                {sectionTrack.map((section, index) => (
                  <div key={index} className="grid grid-cols-2 p-2 border-t">
                    <p>{section.section_name}</p>
                    <p className="text-right font-bold">{section.marks}</p>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button variant="default" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

interface ConfirmStartModalProps {
  isOpen: boolean
  onClose: () => void
  onStart: () => void
  paperLim: number
  showAns: boolean
  negMarks: boolean
}

export const ConfirmStartModal: React.FC<ConfirmStartModalProps> = ({
  isOpen,
  onClose,
  onStart,
  paperLim,
  showAns,
  negMarks
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Before we begin...</DialogTitle>
        </DialogHeader>
        <div className="py-4 text-center">
          <p className="mb-2">You have {paperLim} mins to finish this test.</p>

          {!showAns && (
            <p className="mb-2">Answers are not displayed for this paper.</p>
          )}

          {negMarks && (
            <>
              <p className="font-bold mb-2">Marking Scheme:</p>
              <p className="mb-1">+2 for every Correct Answer</p>
              <p className="mb-3">-1 for every Wrong Answer</p>
            </>
          )}

          <p>Press begin to start</p>
        </div>
        <DialogFooter className="flex justify-center gap-4">
          <Button variant="default" onClick={onStart}>
            Begin
          </Button>
          <Button variant="destructive" onClick={onClose}>
            No, not yet
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
