'use client'

import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { COMPANY_DATA } from '@/types/company-paper-types'
import { Lock } from 'lucide-react'

interface CompanyTestsListProps {
  initialLockedResource?: boolean
  hasAccess?: boolean
}

export default function CompanyTestsList({
  initialLockedResource = true
}: CompanyTestsListProps) {
  const router = useRouter()
  const lockedResource = initialLockedResource

  const takeToLanding = () => {
    router.push('/placement/training/live')
  }

  const takeToPapersList = (companyType: number) => {
    if (lockedResource) {
      return
    }
    router.push(`/dashboard/company-papers/list/${companyType}`)
  }

  return (
    <div className="company-papers--wrap">
      <div className="company-papers--list grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {COMPANY_DATA.map((company) => (
          <div
            key={company.id}
            className="company-papers--card relative bg-white rounded-lg border shadow-sm hover:shadow-lg transition-shadow duration-300 cursor-pointer overflow-hidden"
            onClick={() => takeToPapersList(company.id)}
          >
            <div className="p-6 flex flex-col items-center text-center">
              <div className="relative w-50 h-20 mb-4">
                <Image
                  src={company.logo}
                  alt={company.alt}
                  fill
                  className="object-contain"
                  sizes="80px"
                />
              </div>
              <p className="text-sm font-medium text-gray-800 leading-tight">
                {company.name}
              </p>
            </div>

            {lockedResource && (
              <div
                className="locked-resource absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer"
                onClick={(e) => {
                  e.stopPropagation()
                  takeToLanding()
                }}
                title="Premium Feature"
              >
                <div className="text-center text-white">
                  <Lock className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-xs">Premium</p>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
