import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { JdoodleService } from '../Services/jdoodle.service';
import { CompilerService } from '../Services/compiler.service';

import { InlineResponse200 } from '../Model/InlineResponse200';

@Component({
  selector: 'app-online-compiler',
  templateUrl: './online-compiler.component.html',
  styleUrls: ['./online-compiler.component.scss']
})
export class OnlineCompilerComponent implements OnInit {

  @ViewChild('errTemplate') public errTemplate: TemplateRef<any>;

  public selectedLang: string;
  public selectedTheme: string;
  public editorOptions = {
    theme: 'vs-dark',
    language: 'cpp',
    tabSize: 2,
    padding: { top: 100 } };
  public code = '/**\n * Sample starter code\n * @copyright Quantmasters Training Services\n */\n\n' +
                '#include <stdio.h>\n\nint main(void) {\n\n  printf(\"Hello World!\"); \n\n  return 0; \n}';

  public inputEditorOptions = {
    theme: 'vs-dark',
    langage: 'plaintext',
    padding: { top: 10 },
    minimap: { enabled: false },
    lineNumbers: 'off',
    cursorStyle: 'block',
    cursorBlinking: 'blink',
  };
  public inputText = '';

  public showIndicator: boolean;
  public op: InlineResponse200;
  public compilerTodayCount: number;
  public compilerStateError: boolean;
  public runDisabled: boolean;

  public modalRef: BsModalRef;
  public modalConfig = {
    backdrop: true,
    ignoreBackdropClick: false,
    keyboard: false
  };

  onCodeChanged(value) {
    console.log('CODE', value);
  }

  constructor(private route: ActivatedRoute,
              private modalService: BsModalService,
              private executeService: JdoodleService,
              private compilerService: CompilerService) { }

  ngOnInit() {
    this.showIndicator = true;
    this.route.paramMap.subscribe(params => {
      this.selectedLang = params.get('lang');
      this.editorOptions.language = this.selectedLang;

      if (this.selectedLang === 'python2' || this.selectedLang === 'python3') {
        this.editorOptions.language = 'python';
      }

      this.selctSwitchedCode();
    });

    this.compilerStateError = false;

    this.op = {
      cpuTime: 0,
      memory: 0,
      output: '',
      statusCode: 0
    };

    this.selectedTheme = 'vs-dark';
    this.runDisabled = false;

    const email = sessionStorage.getItem('QMail');
    this.compilerService.getCountForClient(email).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.compilerTodayCount = parseInt(resp.compiler_count, 10);

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  handleLangChange() {
    const text = (<any>window).monaco.editor.createModel(this.code);

    // console.log(text.getLanguageIdentifier());
    (<any>window).monaco.editor.setModelLanguage(text, this.selectedLang);
    // (<any>window).monaco.editor.colorize(this.code, text.getLanguageIdentifier().id);

    this.selctSwitchedCode();
    // console.log(text.getLanguageIdentifier());

    const hrefPieces = window.location.href.split('/');
    hrefPieces[hrefPieces.length - 1] = this.selectedLang;
    window.location.href = hrefPieces.join('/');
  }

  handleThemeChange() {
    (<any>window).monaco.editor.setTheme(this.selectedTheme);
  }

  executeCode() {
    this.showIndicator = true;
    const exec = {
      clientId: '8343bc4e4455cb9c88a49aa89c0d5ef',
      clientSecret: '8aa44dcf5a76e4e9172486c5cf901157373d2d4914daf98aaa93098977f65206',
      script: this.code,
      stdin: this.inputText,
      language: this.selectedLang,
      versionIndex: '0'
    };

    const email = sessionStorage.getItem('QMail');
    this.compilerService.setCountForClient(email).subscribe(iResponse => {
      const resp = JSON.parse(JSON.stringify(iResponse));

      if (parseInt(resp.status, 10) === 101) {
        this.compilerTodayCount = parseInt(resp.count, 10);

        this.executeService.executePost(exec).subscribe(response => {
          this.op = response;

          try {
            this.compilerStateError = this.op.output.match(/jdoodle./).length > 0 ? true : false;
          } catch (e) {
            this.compilerStateError = false;
          }

          this.showIndicator = false;
        }, error => {
          console.log('error :>> ', error);
          this.showIndicator = false;
        });
      } else {
        this.compilerTodayCount = parseInt(resp.count, 10);
        this.openModal(this.errTemplate);
        this.showIndicator = false;
      }
    }, error => {
      console.log('error :>> ', error);
      this.showIndicator = false;
    });
  }

  selctSwitchedCode() {
    switch (this.selectedLang) {
      case 'c':
        this.code = '/**\n * Sample starter code\n * @copyright Quantmasters Training Services\n */\n' +
        '#include <stdio.h>\n\nint main(void) {\n\n  printf(\"Hello World!\"); \n\n  return 0; \n}';
        break;
      case 'cpp':
        this.code = '/**\n * Sample starter code\n * @copyright Quantmasters Training Services\n */\n' +
        '#include <iostream>\n\nusing namespace std;\n\nint main() {\n\n  cout<<\"Hello World!\"<<endl; \n\n  return 0; \n}';
        break;
      case 'python2':
        this.code = '# Sample starter code\n# @copyright Quantmasters Training Services\n\n' +
        'print(\'look ma, no semi-colons\')';
        break;
      case 'python3':
        this.code = '"""\nSample starter code\n@copyright Quantmasters Training Services\n"""\n\nif __name__ == \'__main__\':\n' +
        '  print(\'look ma, no semi-colons in v3!\')';
        break;
      case 'java':
        this.code = '/**\n * Sample starter code\n * @copyright Quantmasters Training Services\n */\n\n' +
        'package in.quantmasters.awesome;\n\npublic class FunStuff {\n\n  public static void main(String []args) {\n' +
        '    System.out.println(\"please work, please\");\n  }\n}';
    }
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }
}
