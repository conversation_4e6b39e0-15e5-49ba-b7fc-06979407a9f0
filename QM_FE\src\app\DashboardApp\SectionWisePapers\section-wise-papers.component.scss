.competitive-wrap {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .page-header {
    display: flex;
    height: 2.5rem;
    margin-bottom: 2em;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .data-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .group-wrap {
      width: 80%;
      height: 50px;

      .group {
        display: inline-block;
        width: 50%;
        height: 100%;
        padding: 0.6em;
        text-align: center;
        cursor: pointer;
        color: #fff;
        background-color: #0B6FB1;
        transition: all 0.4s ease-out;

        &:hover {
          background-color: #e27723;
        transition: all 0.4s ease-out;
        }
      }

      .is-selected {
        background-color: #e27723;
      }
    }

    .paper-wrap {
      width: 80%;
      margin-top: 3em;
      padding: 10px;

      .paper {
        position: relative;
        display: grid;
        grid-template-columns: 30% 30% 20% 20%;
        align-items: center;
        padding: 0.5em;
        color: #fff;
        background-color: #38A3E9;
        margin-bottom: 1em;

        .locked-resource {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
          width: 100%;
          height: 57px;
          background-color: rgba(146, 135, 135, 0.85);

          img {
            display: block;
            height: 100%;
            margin: auto;
          }
        }

        h6, p {
          margin: 0;
        }

        button {
          justify-self: end;
          padding: 0.5em 1em;
          color: #fff;
          cursor: pointer;
          background-color: #E88224;
          border: none;
          border-radius: 5px;
          transition: all 0.4s ease-out;

          &:hover {
            background-color: #0B6FB1;
            transition: all 0.2s ease-in;
          }
        }
      }
    }
  }
}

.copy-content {
    text-align: right;
  
    p {
      color: #707070;
      margin: 0;
    }
}

@media (max-width: 440px) {
  .competitive-wrap {
    .page-header {
      height: 3.5rem;
    }

    .data-wrap {
      .group-wrap{ 
        display: grid;
        grid-template-columns: 1fr 1fr;
        justify-items: center;
        height: 100%;

        .group {
          height: 100%;
          width: 100%;
        }
      }

      .paper-wrap {
        .paper {
          grid-template-columns: 1fr;
          grid-gap: 0.5em;

          .locked-resource {
            height: 100%;

            img {
              height: 50%;
              margin-top: 1.8em;
            }
          }
        }
      }
    }
  } 
}
