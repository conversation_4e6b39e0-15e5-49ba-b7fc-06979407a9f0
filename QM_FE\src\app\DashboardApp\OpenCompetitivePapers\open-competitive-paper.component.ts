import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { OpenTestsService } from '../../Services/open-tests.service';

import { ChapterPaper } from '../../Models/Dashboard/Chapters/ChapterPaper';

declare let gtag: Function;

@Component({
  selector: 'app-open-competitive-paper',
  templateUrl: './open-competitive-paper.component.html',
  styleUrls: ['./open-competitive-paper.component.scss']
})
export class OpenCompetitivePaperComponent implements OnInit {

  public papers: ChapterPaper[];

  public showIndicator = false;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public openTestsService: OpenTestsService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    const that = this;

    this.showIndicator = true;
    this.openTestsService.getOpenCompetitivePapers().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      that.papers = respText;
      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '8', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

}
