// app/dashboard/afcat-papers/page.tsx
import { Metadata } from 'next'
import { ServerAfcatPapersService } from '@/lib/server-services/afcat-papers-service.server'
import AfcatPapersClient from '@/components/afcat-papers/afcat-papers-client'

export const metadata: Metadata = {
  title: 'AFCAT Papers | Quant Masters',
  description: 'Practice AFCAT papers for comprehensive test preparation'
}

export default async function AfcatPapersPage() {
  // Fetch AFCAT papers on the server
  const papers = await ServerAfcatPapersService.getAfcatPapers()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center mb-2">
            AFCAT Practice Papers
          </h1>
          <p className="text-center text-gray-600">
            Crack AFACT tests without a hitch
          </p>
        </div>
      </div>

      {/* Pass the fetched papers to the client component */}
      <AfcatPapersClient initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
