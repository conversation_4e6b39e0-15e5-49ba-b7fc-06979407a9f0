<app-nav></app-nav>
<app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
<div class="notes-home-wrap">
  <h3 class="greeting-msg" [@greetingMovements]="appReady">What are we reading today?</h3>
  <div class="super-groups" [@superGrpMvmts]="displaySupGroups && displaySupGroups.length">
    <div class="group-1" [@scootSuperGroups]="scootSuperGroups" *ngFor="let supGrp of displaySupGroups">
      <p class="super-group" (click)="onSelectSuperGroup(supGrp.super_id)" (hover)="focusSuperGroups()">{{ supGrp.super_name }}</p>
    </div>
  </div>
  <div class="groups" [@slectGroupMvmts]="displayGroups && displayGroups.length">
    <div class="group-2" [@scootGroups]="scootGroups" *ngFor="let grp of displayGroups">
      <p class="group" (click)="onSelectGroup(grp.group_id)">{{ grp.group_name }}</p>
    </div>
  </div>
  <div class="notes-wrap" [@selectNotesMvmts]="notesOfGroup && notesOfGroup.length" >
    <div class="notes-list" *ngFor="let notes of notesOfGroup; let i = index">
      <div class="notes-item">
        <div class="locked-resource" (click)="takeToPlans()" *ngIf="notes.public == 0" title="Premium Feature">
          <img src="../../../assets/icons/lock.svg"/>
        </div>
        <p class="note_node">{{ notes.notes_name }}</p>
        <p class="note_node">Posted On: {{ notes.posted_on }}</p>
        <button class="note_node read-btn" (click)="takeToNotesDetail(notes.notes_id, i)">Start Reading</button>
      </div>
    </div>
  </div>
</div>
<app-footer></app-footer>
