import { Component, OnInit, ViewChild, TemplateRef, ChangeDetectorRef, ViewEncapsulation } from '@angular/core';
import { NotesService } from '../../Services/Blog/notes.service';
import { Subscription, combineLatest } from 'rxjs';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Router, NavigationEnd } from '@angular/router';

import { Groups } from '../../Models/Blog/Notes';

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class NotesComponent implements OnInit {

  @ViewChild('AddSuperGroup') public template: TemplateRef<any>;
  @ViewChild('AddGroup') public templateGroup: TemplateRef<any>;
  @ViewChild('AddNewNote') public NoteGroup: TemplateRef<any>;
  @ViewChild('successTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('successNoteTemplate') public sNotesTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public eTemplate: TemplateRef<any>;
  @ViewChild('warningTemplate') public wTemplate: TemplateRef<any>;
  @ViewChild('imgUpdlTemplate') public updlTemplate: TemplateRef<any>;
  @ViewChild('validationTemplate') public validationTemplate: TemplateRef<any>;
  @ViewChild('advSettingsTemplate') public advSettingsTemplate: TemplateRef<any>;

  constructor(public router: Router,
    private notesService: NotesService,
    public modalService: BsModalService,
    public changeDetection: ChangeDetectorRef) { }

  public modalRef: BsModalRef;
  public subscriptions: Subscription[] = [];

  public showIndicator = false;
  public allGroups = [];
  public superGroup = [];
  public groups = [];
  public NotesList = [];
  // public NoteDetails: Groups = {
  //   super_name: '',
  //   super_id: null,
  //   group_id: null,
  //   group_name: '',
  //   notes_content: '',
  //   notes_id: '',
  //   notes_name: '',
  // };
  public NoteDetails;
  public NoteDescInDatailSec: '';
  public errMsg = '';
  public sel_super_grp_id = null;
  public sel_grp_id = null;
  public sel_note_id = null;
  public config = {
    backdrop: true,
    class: 'create-modal',
    isAnimated: 'true'
  };
  public NewGroup: Groups = {
    super_name: '',
    super_id: null,
    group_id: null,
    group_name: '',
    notes_id: '',
    notes_content: '',
    notes_name: ''
  };
  public NewNoteDesc: '';
  public visibleSelectNote = false;
  public AllGroupsById: {};
  public markdownText: string;
  public warningtext;

  public selectedImage: File;
  public sSuccessMsg = 'Data Added Successfully!';

  public validationGood = false;
  public validationTitle = '';
  public validationMsg = '';

  public showManageSuperGroups = false;
  public selectedSupGroup: boolean[];

  public noteVideo = '';

  ngOnInit() {
    this.getAllGroups();
  }

  getAllGroups() {
    const that = this;

    this.showIndicator = true;
    this.notesService.getSuperGroups().subscribe(response => {
      this.allGroups = response;
      this.superGroup = this.getUniqueSuperGroup(response);
      this.AllGroupsById = this.getAllGroupsByID(this.superGroup);

      that.selectedSupGroup = [];
      this.superGroup.map(x => {
        that.selectedSupGroup.push(false);
      });

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  selectSuperGroup() {
    let subGroup = [];
    this.sel_grp_id = null;
    this.sel_note_id = null;
    if (!this.sel_super_grp_id) {
      return;
    }
    this.groups = [];
    this.NotesList = [];
    const sel_super_grp_id = this.sel_super_grp_id;
    subGroup = this.allGroups.filter(function (oVal, i) {
      return oVal.super_id === sel_super_grp_id;
    });
    this.groups = subGroup;
  }

  selectGroup() {
    this.showIndicator = true;
    this.sel_note_id = null;
    if (!this.sel_grp_id) {
      this.showIndicator = false;
      return;
    }
    this.getNotesForGroups(this.sel_grp_id);
  }

  getNotesForGroups(groupId) {
    this.notesService.getAllNotesListForGroup(groupId).subscribe(response => {
      this.NotesList = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  selectNote() {
    const that = this;
    const NoteId = this.sel_note_id;
    if (!this.sel_super_grp_id || !this.sel_grp_id && NoteId) {
      this.sel_note_id = null;
      this.warningtext = 'Please select supergroup and group, Before adding Notes!';
      this.openModal(this.wTemplate);
      return;
    }
    if (NoteId === 'AddNewNote') {
      this.openModal(this.NoteGroup);
    } else if (NoteId) {
      this.markdownText = 'Enter Your Content Here...';
      this.showIndicator = true;
      this.notesService.getNoteDetail(NoteId).subscribe(response => {
        // that.NoteDetails.notes_name = null;
        this.NoteDetails = response;
        this.NoteDescInDatailSec = response['notes_name'];
        this.noteVideo = response['notes_video'];
        this.markdownText = response['notes_content'];
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
        this.modalRef.hide();
        this.openModal(this.eTemplate);
        this.errMsg = 'Looks like something went wrong. Please try again later.';
      });
    }
  }

  getUniqueSuperGroup(All) {
    const superGroup = this.superGroup;
    All.forEach(function (item) {
      // this.AllGroupsById[item.super_id] = item;
      let found;
      found = superGroup.findIndex(function (oVal, i) {
        return oVal.super_id === item.super_id;
      });
      if (found < 0) {
        superGroup.push(item);
      }
    });
    // const new_superGroup = [].concat({ 'super_name': '--Please choose an option--' }, superGroup);
    return superGroup;
  }

  getAllGroupsByID(All) {
    const AllGroups = {};
    All.forEach(function (item) {
      AllGroups[item.super_id] = item;
    });
    return AllGroups;

  }

  openAddNewSuperGroup() {
    this.NewGroup.super_name = '';
    this.NewGroup.group_name = '';
    this.openModal(this.template);
  }

  openAddNewGroup() {
    this.NewGroup.super_id = null;
    this.NewGroup.super_name = '';
    this.NewGroup.group_name = '';
    this.openModal(this.templateGroup);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  onOpenAdditionalSettings() {

    this.openModal(this.advSettingsTemplate);
  }

  closeModel(template: TemplateRef<any>) {
    this.sSuccessMsg = 'Data Added Successfully!';
    this.modalRef.hide();
  }
  closeWarningTemplate() {
    this.sel_note_id = null;
    this.modalRef.hide();
  }

  selectSuperGroupForSetting(idx: number) {
    this.selectedSupGroup.fill(false);

    this.selectedSupGroup[idx] = true;
  }

  SubmitSuperGroup(isValid: boolean) {
    this.showIndicator = true;

    if (!isValid) {
      this.showIndicator = false;
      return;
    }
    this.showIndicator = true;
    this.notesService.postSuperGropu(this.NewGroup).subscribe(response => {
      this.NewGroup.super_name = '';
      this.NewGroup.group_name = '';
      this.modalRef.hide();
      this.openModal(this.sTemplate);
      this.getAllGroups();
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.modalRef.hide();
      this.openModal(this.eTemplate);
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  SubmitGroup(isValid: boolean) {
    this.showIndicator = true;
    if (!isValid) {
      this.showIndicator = false;
      return;
    }
    this.showIndicator = true;
    this.NewGroup.super_name = this.AllGroupsById[this.NewGroup.super_id].super_name;
    this.notesService.putSuperGropu(this.NewGroup).subscribe(response => {
      this.NewGroup.super_name = '';
      this.NewGroup.group_name = '';
      this.modalRef.hide();
      this.openModal(this.sTemplate);
      this.getAllGroups();
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.modalRef.hide();
      this.openModal(this.eTemplate);
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  SubmitNewNote(isValid: boolean) {
    this.showIndicator = true;
    if (!isValid) {
      this.showIndicator = false;
      return;
    }
    const NewNoteContent = {
      'group_id': this.sel_grp_id,
      'notes_name': this.NewNoteDesc,
      'notes_id': '',
      'notes_content': '',
      'posted_on': ''
    };
    this.notesService.postNewNoteForGroup(NewNoteContent).subscribe(response => {
      this.NewNoteDesc = '';
      this.sel_note_id = null;
      this.getNotesForGroups(NewNoteContent.group_id);
      this.modalRef.hide();
      this.openModal(this.sTemplate);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.modalRef.hide();
      this.openModal(this.eTemplate);
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  SubmitNoteDetail(isValid: boolean) {
    this.showIndicator = true;
    if (!isValid) {
      this.showIndicator = false;
      return;
    }
    // if(!this.NoteDescInDatailSec){

    // }
    const NewNoteContent = {
      'group_id': this.NoteDetails.group_id,
      'notes_name': this.NoteDescInDatailSec,
      'notes_id': this.NoteDetails.notes_id,
      'notes_video': this.noteVideo,
      'notes_content': this.markdownText
    };
    this.notesService.putNewNoteForGroup(NewNoteContent).subscribe(response => {
      this.showIndicator = false;
      this.openModal(this.sNotesTemplate);
    }, error => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }

  validateNotesContent() {
    const notesText = this.markdownText;

    const errors = [];
    for (const i of notesText) {
      try {
        btoa(i);
      } catch (ex) {
        errors.push(i);
      }
    }

    if (errors.length === 0) {
      this.validationGood = true;
      this.validationTitle = 'All Good!';
      this.validationMsg = 'No invalid characters found';
    } else {
      this.validationGood = false;
      this.validationTitle = 'Problems exist!';
      this.validationMsg = 'Invalid Characters found: ' + errors.toString();
    }

    this.openModal(this.validationTemplate);
  }

  fileSelect(event: any) {
    this.selectedImage = event.target.files[0];

    this.openModal(this.updlTemplate);
  }

  uploadFile() {
    this.showIndicator = true;

    this.notesService.uploadImage(this.selectedImage)
      .subscribe(response => {
        if (response.url) {
          this.markdownText = this.markdownText + '\n\n' +
            '![enter image description here](' + response.url + ' \"enter image title here\")';
        }
        this.showIndicator = false;
        this.closeModel(this.updlTemplate);
        this.sSuccessMsg = 'Image Uploded Successfully. Hence,' +
          'image is added to bottom of the content area!\nPlease do rearrage as needed.';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
      });
  }

  deleteSelectedNote() {
    this.showIndicator = true;
    if (!this.NoteDetails.notes_id) {
      this.showIndicator = false;
      this.warningtext = 'Please select supergroup, group and note Before deleting Notes!';
      this.openModal(this.wTemplate);
      return;
    }
    this.notesService.deleteNote(this.NoteDetails.notes_id).subscribe(response => {
      this.sSuccessMsg = 'Note Deleted Successfully';
      this.openModal(this.sTemplate);
      this.sel_note_id = null;
      this.getNotesForGroups(this.sel_grp_id);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
      this.errMsg = 'Looks like something went wrong. Please try again later.';
    });
  }
}
