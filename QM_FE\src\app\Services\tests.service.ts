import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { Paper } from '../Models/Paper';

@Injectable({
  providedIn: 'root'
})
export class TestsService {
  private paperUrl         = 'https://api.quantmasters.in/test/question/ret/papers';
  private questionUrl      = 'https://api.quantmasters.in/test/question/ret';
  private submitUrl        = 'https://api.quantmasters.in/test/answer/submit';
  private indResultsAllUrl = 'https://api.quantmasters.in/view/results/ind/all';
  private indResultsUrl    = 'https://api.quantmasters.in/view/results/ind/bypaper';

  private allResultsUrl    = 'https://api.quantmasters.in/view/results/all';

  private uploadExplUrl    = 'https://api.quantmasters.in/test/model/paper/';

  private openResultsUrl   = 'https://api.quantmasters.in/v2/test/open';

  private createPaperUrl    = 'https://api.quantmasters.in/admin/paper/upload/test';
  private createQuestionUrl = 'https://api.quantmasters.in/admin/paper/upload/question';


  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getPapers(): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.paperUrl, httpOps);
  }

  getQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.questionUrl + `/${paper_id}`, httpOps);
  }

  submitAnswers(email: string, paper_id: string, marks: number, ans_date: string): Observable<string> {
    const data = {
      email: email,
      paper_id: paper_id,
      marks: marks,
      answered_on: ans_date
    };

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.submitUrl, data, httpOps);
  }

  getIndividualResults(email: string): Observable<string> {

    const data = {
      email: email
    };

    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.indResultsAllUrl, data, httpOpts);
  }

  getIndividualResult(email: string, paperId: string): Observable<string> {

    const data = {
      email   : email,
      paper_id: paperId
    };

    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.indResultsUrl, data, httpOpts);
  }

  getAllResults(): Observable<string> {

    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.allResultsUrl, httpOpts);
  }

  getAllOpenResults(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.openResultsUrl + `/papers/${email}/marks`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  getOpenPaperSectionWiseMarks(email: string, answerId: string): Observable<string> {

    this.setSecurityToken();

    const url = this.openResultsUrl + `/papers/${email}/section/${answerId}/marks`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  getAllOpenCompanyResults(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.openResultsUrl + `/company/papers/${email}/marks`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  getAllWeeklyCompetitiveResults(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.openResultsUrl + `/competitive/${email}/marks`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  addExplanation(paperId: string, quesNo: string, explanation: string): Observable<string> {
    this.setSecurityToken();

    const url = this.uploadExplUrl + paperId + '/explanation';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const reqBody = {
      question_no: quesNo,
      explanation
    };

    return this.http.post<string>(url, reqBody, httpOps);
  }

  updatePaperDetails(paper: Paper): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.createPaperUrl, paper, httpOps);
  }

  updateQuestionDetails(question: Object): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.createQuestionUrl, question, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
