.company-papers--wrap {
  display: grid;
  place-items: center;
  margin-bottom: 15em;

  .paper-heading {
    font-size: 26px;
    font-weight: 700;
    margin-top: 2em;
    text-align: center;
  }

  .papers-wrap {
    width: 70%;
    padding: 10px;
    margin-top: 1em;

    .paper {
      .oth-papers {
        position: relative;
        display: grid;
        grid-template-columns: 30% 30% 20% 20%;
        align-items: center;
        padding: 0.5em;
        color: #fff;
        background-color: #38A3E9;
        margin-bottom: 1em;

        h6, p {
          margin: 0;
        }

        button {
          justify-self: end;
          padding: 0.5em 1em;
          color: #fff;
          cursor: pointer;
          background-color: #E88224;
          border: none;
          border-radius: 5px;
          transition: all 0.4s ease-out;

          &:hover {
            background-color: #0B6FB1;
            transition: all 0.2s ease-in;
          }
        }
      }
    }

    .tcs-digital-2022 {
      margin-top: 3em;

      .tcs-folder--1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0.5em;

        button {
          background-color: #0B6FB1;
          border: none;
          border-radius: 50%;
          outline: none;
          padding: 3px;

          svg {
            transform: rotate(0deg);
            transition: all 0.4s ease-in;
          }
        }

        .is-open {
          svg {
            transform: rotate(180deg);
            transition: all 0.3s ease-out;
          }
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .company-papers--wrap {
    .papers-wrap {
      width: 95%;

      .paper {
        .oth-papers {
          padding: 20px;
          grid-template-columns: 1fr;
          gap: 0.7rem;

          .locked-resource {
            height: 100%;

            img {
              height: 50%;
            }
          }
        }
      }
    }
  }
}