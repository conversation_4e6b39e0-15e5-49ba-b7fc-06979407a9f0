'use client'

import React from 'react'
import {
  Question,
  CompetitiveQuestion,
  CompanyQuestion,
  PaperAdditionData
} from '@/types/test-types'
import ReactMarkdown from 'react-markdown'
import rehypeRaw from 'rehype-raw'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronUp } from 'lucide-react'
import Image from 'next/image'

interface QuestionDisplayProps {
  question: Question | CompetitiveQuestion | CompanyQuestion
  index: number
  additionData?: PaperAdditionData[]
  bPaperSubmitted: boolean
  showAns: boolean
  isCollapsed: boolean[]
  toggleCollapse: (index: number) => void
  handleOptionSelect: (questionNo: string, optionIndex: number) => void
  showAllExpl: boolean
  testType: number
  negMarks: boolean
  questionTexts?: string[]
}

const QuestionDisplay: React.FC<QuestionDisplayProps> = ({
  question,
  index,
  additionData = [],
  bPaperSubmitted,
  showAns,
  isCollapsed,
  toggleCollapse,
  handleOptionSelect,
  showAllExpl,
  testType,
  negMarks,
  questionTexts = ['A. ', 'B. ', 'C. ', 'D. ', 'E. ']
}) => {
  const questionNo = question.question_no || (question as Question).ques_no
  const isDisplayOnlyMode = testType === 6
  const relevantAdditionData = additionData.filter(
    (data) => data.question_no === parseInt(questionNo, 10)
  )

  // For questions with origin (like competitive questions)
  const questionWithOrigin =
    'question_origin' in question
      ? `**${questionNo})** ${question.question} **(${question.question_origin})**`
      : `**${questionNo})** ${question.question}`

  const renderMarkdown = (content: string) => {
    return <ReactMarkdown rehypePlugins={[rehypeRaw]}>{content}</ReactMarkdown>
  }

  return (
    <div
      id={`qm-q--${questionNo}`}
      className="question mb-8 p-4 bg-white rounded-md shadow"
    >
      {/* Display instruction text from addition data */}
      {relevantAdditionData.map((data, i) => (
        <div key={i} className="instr-text mb-4">
          {renderMarkdown(data.value)}
        </div>
      ))}

      {/* Question text */}
      {renderMarkdown(questionWithOrigin)}

      {/* Options */}
      <div className="options-wrap mt-4 space-y-2">
        {question.options.map((opt, idx) => (
          <div
            key={idx}
            className={`option-wrap--2 p-3 rounded-md border ${
              bPaperSubmitted && showAns
                ? question.selected_ans === idx + 1 &&
                  question.selected_ans !== parseInt(question.correct_opt, 10)
                  ? 'border-red-500 bg-red-50'
                  : parseInt(question.correct_opt, 10) === idx + 1
                    ? 'border-green-500 bg-green-50'
                    : 'border-gray-200'
                : 'border-gray-200'
            }`}
          >
            {isDisplayOnlyMode ? (
              <div>
                <span className="font-bold">{questionTexts[idx]}</span>
                {renderMarkdown(opt)}
                {!isCollapsed[index] &&
                  parseInt(question.correct_opt, 10) === idx + 1 && (
                    <small className="block mt-1 text-green-600 font-medium">
                      Correct Option
                    </small>
                  )}
              </div>
            ) : (
              <div className="flex items-start gap-2">
                <input
                  type="radio"
                  name={questionNo}
                  value={opt}
                  disabled={bPaperSubmitted}
                  checked={question.selected_ans === idx + 1}
                  onChange={() => handleOptionSelect(questionNo, idx + 1)}
                  className="mt-1 h-4 w-4"
                />
                <div className="flex-1">
                  {renderMarkdown(opt)}
                  {bPaperSubmitted &&
                    showAns &&
                    parseInt(question.correct_opt, 10) === idx + 1 && (
                      <small className="block mt-1 text-green-600 font-medium">
                        Correct Option
                      </small>
                    )}
                  {bPaperSubmitted &&
                    showAns &&
                    question.selected_ans === idx + 1 &&
                    question.selected_ans !==
                      parseInt(question.correct_opt, 10) && (
                      <small className="block mt-1 text-red-600 font-medium">
                        Selected Option is wrong {negMarks && '(-1)'}
                      </small>
                    )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Explanation button and content */}
      {(bPaperSubmitted || showAllExpl) && showAns && (
        <>
          <Button
            variant="outline"
            onClick={() => toggleCollapse(index)}
            className={`mt-4 ${!isCollapsed[index] ? 'bg-gray-100' : ''}`}
          >
            {isCollapsed[index] ? (
              <span className="flex items-center gap-1">
                Show Explanation <ChevronDown className="h-4 w-4" />
              </span>
            ) : (
              <span className="flex items-center gap-1">
                Hide Explanation <ChevronUp className="h-4 w-4" />
              </span>
            )}
          </Button>

          <div
            className={`qn-explanation mt-4 ${isCollapsed[index] ? 'hidden' : 'block'}`}
          >
            {(question as Question).img_expln === '1' ? (
              <Image
                src={question.explanation}
                alt="Image Explanation"
                className="max-w-full h-auto"
                width={500}
                height={500}
              />
            ) : (
              renderMarkdown(
                question.explanation || 'Explanation Not Available'
              )
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default QuestionDisplay
