import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { CompetitiveService } from '../../Services/Dashboard/competitive.service';

import { CompetitiveGroup } from '../../Models/Dashboard/Competitive/CompetitiveGroup';
import { CompetitivePapers } from '../../Models/Dashboard/Competitive/CompetitivePapers';

declare let gtag: Function;

@Component({
  selector: 'app-competitive-paper',
  templateUrl: './competitive-paper.component.html',
  styleUrls: ['./competitive-paper.component.scss']
})
export class CompetitivePaperComponent implements OnInit {

  public groups: CompetitiveGroup[];
  public papers: CompetitivePapers[];
  public papersForDisplay: CompetitivePapers[];

  public showPapers = false;
  public selectedGroup = [];

  public showIndicator = false;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public competitiveSerive: CompetitiveService) {
    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    const that = this;

    this.showIndicator = true;
    this.competitiveSerive.getGroups().subscribe(response => {
      that.groups = response;

      for (let i = 0; i < that.groups.length; i++) {
        that.selectedGroup.push(false);
      }

      that.competitiveSerive.getAllPapers().subscribe(response2 => {
        that.papers = response2;

        that.selectedGroup[0] = true;
        that.getPapers(that.groups[0].group_id, 0);

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
      });
    }, error => {
      that.showIndicator = false;
    });
  }

  getPapers(groupId: string, groupIdx: number) {
    this.showIndicator = true;
    this.showPapers = true;

    this.selectedGroup.fill(false);

    this.selectedGroup[groupIdx] = true;
    this.papersForDisplay = this.papers.filter(paper => paper.group_id === groupId);
    this.showIndicator = false;
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '2', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans() {
    this.router.navigate(['/plans']);
  }
}
