import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { NotesService } from '../../Services/Blog/notes.service';

import { NotesGroup } from '../../../app/NotesApp/models/NotesGroup';
import { Notes } from '../../../app/NotesApp/models/Notes';

@Component({
  selector: 'app-tech-notes',
  templateUrl: './tech-notes.component.html',
  styleUrls: ['./tech-notes.component.scss']
})
export class TechNotesComponent implements OnInit {

  constructor(private notesService: NotesService,
              private router: Router,
              private activatedRoute: ActivatedRoute) { }

  public showIndicator: boolean;
  
  public superGroups: [{ super_id: string, super_name: string, super_seq: number }];

  public groups: NotesGroup[];
  public displayGroups: NotesGroup[];
  public notesOfGroup: Notes[];

  public selectedGroup;
  public selectedSubGroup;

  ngOnInit() {
    this.showIndicator = true;
    this.notesService.getSuperGroups().subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      for (const obj of respObj) {
        if (!this.superGroups) {
          this.superGroups = [{ super_id: '', super_name: '', super_seq: 0 }];
          this.superGroups.pop();

          const pushObj = {
            super_id: obj.super_id,
            super_name: obj.super_name,
            super_seq: obj.super_seq
          };

          this.superGroups.push(pushObj);
        } else if (!this.superGroups.find(x => x.super_id === obj.super_id)) {
          this.superGroups.push(obj);
        }
      }

      this.superGroups.sort((a, b) => {
        if (a.super_seq < b.super_seq) {
          return 0;
        } else if (a.super_seq > b.super_seq) {
          return 1;
        } else {
          return -1;
        }
      });

      this.groups = respObj;


      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  onSelectSuperGroup(supId: string, supName: string) {
    this.displayGroups = this.groups.filter(x => x.super_id === supId);
    this.selectedGroup = supName;
    this.selectedSubGroup= '';
    this.notesOfGroup = [];
    this.displayGroups.sort((a, b) => {
      if (a.group_seq < b.group_seq) {
        return 0;
      } else if (a.group_seq > b.group_seq) {
        return 1;
      } else {
        return -1;
      }
    });
  }

  getSubGroup(subId: string, Name: String){
    this.selectedSubGroup = Name;
    this.showIndicator = true;
    this.notesService.getNotesOfaGroup(subId).subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      this.notesOfGroup = respObj;
      for (const obj of this.notesOfGroup) {
        obj.posted_on = new Date(obj.posted_on).toLocaleDateString();
      }

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }
  takeToNotesDetail(noteId: string, idx: number) {

    const currNote = this.notesOfGroup[idx].notes_name.replace(/ /g, '-');
    const nextNote = idx !== this.notesOfGroup.length - 1 ? this.notesOfGroup[idx + 1] : new Notes();
    const prevNote = idx !== 0 ? this.notesOfGroup[idx - 1] : new Notes();

    const notedData = {
      thisNoteId: this.notesOfGroup[idx].notes_id,
      selectedSupGroup: this.selectedSubGroup,
      selectedGroup: this.selectedGroup,
      nextNoteId: nextNote.notes_id || '',
      nextNoteName: nextNote.notes_name || '',
      prevNoteId: prevNote.notes_id || '',
      prevNoteName: prevNote.notes_name || ''
    };

    localStorage.setItem('QNotesData', JSON.stringify(notedData));

    this.router.navigate(['/notes/read', currNote], { relativeTo: this.activatedRoute });
  }
}
