import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { style, transition, trigger, query, animate, stagger, state } from '@angular/animations';
@Component({
  selector: 'app-links',
  templateUrl: './links.component.html',
  styleUrls: ['./links.component.scss']
})
export class LinksComponent implements OnInit {

  constructor( private router: Router) { }
  public selectItemMvmts = 'normal';
  ngOnInit() {
  }

  onClick(actionFrom){
    if(actionFrom == 'program') {
      window.open('https://quantmasters.in/placement/training/live', '_blank')
    } else if(actionFrom == 'jobs') {
      window.open('https://www.placements.quantmasters.in/home', '_blank')
    } else if(actionFrom == 'youtube') {
      window.open('https://www.youtube.com/channel/UCnQweeTKKj0gUZeDG1_PDtQ', '_blank')
    } else if(actionFrom == 'telegram') {
      window.open('https://t.me/quant_masters', '_blank')
    }
  }
}
