<div class="practice-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Practice Papers</p>
    </div>
  </div>
  <div class="data-wrap">
    <h4>{{ group.group_name }} - Practice Papers</h4>
    <div class="qm-tmcq--papers" *ngIf="papers && papers.length > 0">
      <div class="qm-paper" *ngFor="let paper of papers">
        <h6>{{ paper.paper_name }}</h6>
        <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
        <p>No. of Questions: {{ paper.no_of_ques }}</p>
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>
  </div>
</div>