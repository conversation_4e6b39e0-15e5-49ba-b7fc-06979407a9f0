'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function Footer() {
  const pathname = usePathname()

  // Check if current route is in an admin or dashboard area
  const isSpecialRoute =
    pathname.includes('/admin') ||
    pathname.includes('/dashboard') ||
    pathname.includes('/technical')

  // Don't show footer for special routes that have their own layout
  if (isSpecialRoute) return null

  return (
    <footer className="p-6 my-6 mx-4 bg-white rounded-2xl shadow-lg  md:flex md:items-center md:justify-between">
      <ul className="flex flex-wrap items-center mb-6 md:mb-0">
        <li>
          <Link
            href="/"
            className="mr-4 text-sm font-normal text-gray-500 hover:underline md:mr-6"
          >
            Home
          </Link>
        </li>
        <li>
          <Link
            href="/achievers"
            className="mr-4 text-sm font-normal text-gray-500 hover:underline md:mr-6"
          >
            Achievers
          </Link>
        </li>
        <li>
          <Link
            href="/privacy-policy"
            className="mr-4 text-sm font-normal text-gray-500 hover:underline md:mr-6"
          >
            Privacy Policy
          </Link>
        </li>
        <li>
          <Link
            href="/contact"
            className="text-sm font-normal text-gray-500 hover:underline"
          >
            Contact
          </Link>
        </li>
      </ul>

      <div className="flex space-x-6 sm:justify-center">
        <p className="text-sm text-gray-500">
          © {new Date().getFullYear()} QuantMasters. All rights reserved.
        </p>
      </div>
    </footer>
  )
}
