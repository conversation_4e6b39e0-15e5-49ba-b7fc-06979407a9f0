'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Clock, FileText, ArrowRight, Users } from 'lucide-react'
import {
  CompetitiveGroup,
  CompetitivePapers
} from '@/types/competitive-paper-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface CompetitivePapersClientProps {
  initialGroups: CompetitiveGroup[]
  initialPapers: CompetitivePapers[]
}

export default function CompetitivePapersClient({
  initialGroups,
  initialPapers
}: CompetitivePapersClientProps) {
  const router = useRouter()
  const [groups] = useState<CompetitiveGroup[]>(initialGroups)
  const [papers] = useState<CompetitivePapers[]>(initialPapers)
  const [papersForDisplay, setPapersForDisplay] = useState<CompetitivePapers[]>(
    []
  )
  const [selectedGroupIndex, setSelectedGroupIndex] = useState<number>(0)
  const [showPapers, setShowPapers] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  // Initialize with first group's papers on component mount
  useEffect(() => {
    if (groups.length > 0 && papers.length > 0) {
      getPapers(groups[0].group_id, 0)
    }
  }, [groups, papers])

  const getPapers = (groupId: string, groupIdx: number) => {
    setIsLoading(true)
    setSelectedGroupIndex(groupIdx)

    // Filter papers by group_id
    const filteredPapers = papers.filter((paper) => paper.group_id === groupId)
    setPapersForDisplay(filteredPapers)
    setShowPapers(true)
    setIsLoading(false)
  }

  const beginTest = (paperId: string, paperName: string, paperLim: string) => {
    const timeLimit = parseInt(paperLim) || 0
    router.push(
      `/dashboard/test/2/${paperId}/${encodeURIComponent(paperName)}/${timeLimit}`
    )
  }

  const takeToPlans = () => {
    router.push('/plans')
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading papers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="competitive-papers-wrap">
      {groups.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-gray-600 mb-4">
            No competitive groups available at the moment.
          </p>
          <Button onClick={takeToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Groups Navigation */}
          <div className="groups-section">
            <h2 className="text-md font-semibold mb-4 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Categories
            </h2>
            <div className="flex flex-wrap gap-3">
              {groups.map((group, index) => (
                <Button
                  key={group.group_id}
                  variant={selectedGroupIndex === index ? 'default' : 'outline'}
                  onClick={() => getPapers(group.group_id, index)}
                  className="transition-all duration-200"
                >
                  {group.group_name}
                </Button>
              ))}
            </div>
          </div>

          {/* Papers Display */}
          {showPapers && (
            <div className="papers-section">
              <h3 className="text-lg font-medium mb-4">
                Papers in {groups[selectedGroupIndex]?.group_name}
              </h3>

              {papersForDisplay.length === 0 ? (
                <div className="text-center py-10">
                  <p className="text-gray-600 mb-4">
                    No papers available in this category.
                  </p>
                  <Button onClick={takeToPlans} variant="outline">
                    Explore Other Plans
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {papersForDisplay.map((paper) => (
                    <Card
                      key={paper.paper_id}
                      className="h-full flex flex-col hover:shadow-lg transition-shadow"
                    >
                      <CardHeader>
                        <CardTitle className="text-lg">
                          {paper.paper_name}
                        </CardTitle>
                        <CardDescription className="flex items-center space-x-1">
                          <Clock className="h-4 w-4" />
                          <span>
                            {paper.time_lim
                              ? `${Math.floor(parseInt(paper.time_lim) / 60000)} Minutes`
                              : 'No Time Limit'}
                          </span>
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center space-x-1">
                          <FileText className="h-4 w-4" />
                          <span>No. of Questions: {paper.no_of_ques}</span>
                        </div>
                      </CardContent>
                      <CardFooter className="mt-auto">
                        <Button
                          onClick={() =>
                            beginTest(
                              paper.paper_id,
                              paper.paper_name,
                              paper.time_lim
                            )
                          }
                          className="w-full"
                          // disabled={paper.status !== 'active'}
                        >
                          Begin Test
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
