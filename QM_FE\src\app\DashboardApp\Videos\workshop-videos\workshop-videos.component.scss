.videos-wrap {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .page-header {
    display: flex;
    height: 2.5rem;
    margin-bottom: 2em;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .video-header {
    display: flex;
    flex-direction: row;
    width: 90%;
    margin: auto;
    margin-bottom: 1rem;
    color: #fff;

    div {
      cursor: pointer;
      width: 100%;
    }

    .is-selected {
      background-color: #e27723;
      transition: all 0.3s ease;
    }

    p {
        margin: auto;
        padding: 0.8rem;
        width: 100%;
        text-align: center;
        background-color: #0b6fb1;
        border-right: 1px solid;
        &:hover {
            background-color: #E88224;
            transition: all 0.3s ease;
        }
    }

    .active {
        background-color: #e27723;
    }
      
  }

  .video-group--list {
    width: 90%;
    margin: auto;
    padding: 10px;

    .video-group {
      width: 100%;
      height: 100%;
      margin-bottom: 3em;

      .group-name--bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        padding: 1em;
        color: #fff;
        background-color: #38A3E9;

        h5 {
          margin: 0;
        }

        span {
          margin-right: 1em;
        }

        button {
          svg {
            transform: rotate(0deg);
            transition: all 0.4s ease-in;
          }
        }

        .is-open {
          svg {
            transform: rotate(180deg);
            transition: all 0.3s ease-out;
          }
        }
      }
    }

    button {
      cursor: pointer;
      border: none;
      background-color: rgba(0, 0, 0, 0.0);
    }
  }

  .videos {
    width: 100%;
    padding: 1em;
    background-color: #E4F9F5;
  
    .video-group-wrap {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 1em;
  
      .video {
        width: 100%;
        background-color: #fff;
        margin-bottom: 25px;
  
        .video-name--bar {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5em;
          background-color: #eeeeee;
  
          h6, p {
            margin: 0;
          }
        }
  
        .video-content {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          padding: 1em;
          background-color: #fff;
          position: relative;
  
          .thmb-wrap {
            width: 100%;
            height: 270px;
            background-color: #fff;

            iframe {
              padding-top: 1rem;
            }
  
            img {
              width: 100%;
              height: 100%;
            } 
          }
        }
  
        div {
          display: flex;
          align-items: flex-start;
          justify-content: center;
  
          p, button {
            margin-bottom: 0;
            margin-left: 15px;
          }
  
          button {
            margin-top: 2em;
            padding: 0.5em 1em;
            color: #000;
            background-color: #eeeeee;
            border: 1px solid #0B6FB1;
            border-radius: 5px;
            transition: all 0.4s ease-out;
  
            &:hover {
              background-color: #E88224;
              transition: all 0.2s ease-in;
            }
          }
        }
      }
    }
  }
}

.copy-content {
  text-align: right;
  margin-top: 1em;

  p {
    color: #707070;
    margin: 0;
  }
}

@media (max-width: 440px) {
  .videos-wrap {
    .page-header {
      height: 3.5rem;
    }

    .sub-groups {
      overflow-x: scroll;
    }
    
    .videos {
      .video-group-wrap {
        grid-template-columns: 1fr;

        iframe {
          width: 100%;
          margin-bottom: 2rem;
        }

        .video {

          .video-content {
            padding: 0;
            padding-bottom: 10px;

            .thmb-wrap {
              height: 150px;
            }

            div {
              flex-direction: column;
              align-items: center;

              button {
                margin-left: 0;
              }
            }
          }
        }
      }
    }
  }
}