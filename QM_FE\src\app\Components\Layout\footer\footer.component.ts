import { Component, OnInit, TemplateRef, ViewChild, OnDestroy } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { UserService } from '../../../Services/user.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss']
})
export class FooterComponent implements OnInit, OnDestroy {

  public modalRef: BsModalRef;
  public newEmail: string;
  public subscribeError: string;

  @ViewChild('successTemplate') public successTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public errorTemplate: TemplateRef<any>;

  constructor(public modalService: BsModalService,
              public userService: UserService) { }

  ngOnInit() {
  }

  addSubscriber(isValid: boolean) {
    if (!isValid) {
      return;
    }

    this.userService.addNewsLetterSubscriber(this.newEmail).subscribe(response => {
      const respJson = JSON.parse(JSON.stringify(response));

      if (respJson.text === 'Subscriber Added') {
        this.openModal(this.successTemplate);
      }
    }, error => {
      const errJson = JSON.parse(JSON.stringify(error));

      if (errJson.err === 'Email already there') {
        this.subscribeError = 'You\'re already a subscriber';
      } else {
        this.subscribeError = 'Something went wrong, please try again';
      }

      this.openModal(this.errorTemplate);
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  ngOnDestroy(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}
