'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Clock, FileText, ArrowRight } from 'lucide-react'
import { ChapterPaper } from '@/types/paper-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface FreePapersClientProps {
  initialPapers: ChapterPaper[]
}

export default function FreePapersClient({
  initialPapers
}: FreePapersClientProps) {
  const router = useRouter()
  const papers = initialPapers
  const [isLoading, setIsLoading] = useState(false)

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    paperLim: number
  ) => {
    setIsLoading(true)
    router.push(`/dashboard/test/5/${paperId}/${paperName}/${paperLim}`)
  }

  const navigateToPlans = () => {
    router.push('/plans')
  }

  if (isLoading) {
    return <div className="text-center py-10">Please wait...</div>
  }

  return (
    <div className="paper-wrap">
      {papers.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-gray-600">
            No trial papers available at the moment.
          </p>
          <Button onClick={navigateToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {papers.map((paper) => (
            <Card key={paper.paper_id} className="h-full flex flex-col">
              <CardHeader>
                <CardTitle>{paper.paper_name}</CardTitle>
                <CardDescription className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    {paper.time_lim
                      ? `${paper.time_lim / 60000} Minutes`
                      : 'No Time Limit'}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-1">
                  <FileText className="h-4 w-4" />
                  <span>No. of Questions: {paper.no_of_ques}</span>
                </div>
              </CardContent>
              <CardFooter className="mt-auto">
                <Button
                  onClick={() =>
                    handleBeginTest(
                      paper.paper_id,
                      paper.paper_name,
                      paper.time_lim
                    )
                  }
                  className="w-full"
                >
                  Begin Test
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
