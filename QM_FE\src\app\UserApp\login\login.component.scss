.login-wrap {
    display            : flex;
    flex-direction     : row;
    justify-content    : center;
    align-items        : center;
    // background         : url("./../../../assets/bgs/login-bg.png");
    background-position: cover;
    background-repeat  : no-repeat;
    background-size    : cover;
    min-height         : 100vh;
    padding            : 1em;
    min-width          : 60vw;
    .card {
        max-width: 1000px;
    }

    h5 {
        color : #041840;
        font  : 700 normal 35px 'PT Serif', serif;
        margin: 0;
    }
}
.login-content{
    min-width          : 70vw;
    min-height         : 65vh;
    display            : flex;
    flex-direction     : row;
    background-color: #fff;
    margin-bottom   : 1rem;
    box-shadow      : 3px 3px 15px rgba(0, 0, 0, 0.16);
}
.login{
    width          : 50%;
    height         : 100%;
    padding        : 2em;
    padding-left   : 1em;
    flex-direction     : column;

}
.sect-text--1 {
    font-size: 30px;
    font-weight: lighter;
    margin: 0.5em 0 0 0;
}

.sect-text--2 {
    font-size:12px;
    font-weight: lighter;
}
.login-guy{
    width          : 50%;
    height         : 100%;
    padding        : 2em;
    background-color: #e0f3f4;
    
    svg {
        width : 100%;
        height: 100%;
    }
}
form {
    width: 100%;
    padding-top: 2em;

    .is-inv {
        border-color: #dc3545;
    }

    small {
        color: #052D74;

        a:hover {
            text-decoration: none;
        }
    }

    .form-group--1 {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // align-items: center;
        width: 100%;

        & .form-elem {
            width: 100%;
        }

        & .form-elem:last-child {
            margin-top: 2em;
        }
        
        p {
            a {
                cursor: pointer;
                color: #128C7E;
            }
        }
    }

    input {
        width: 100%;
        border-radius: 0;
        padding: 5px;
        font-size: 13px;
  
        &:focus + .form-fill--bar:before {
            width: 100%;
            transition: all 0.3s ease-out;
        }
    }

    .form-fill--bar {
        width: 100%;
        height: 3px;
        background-color: rgba(0, 0, 0, 0.35);
        border-radius: 3px;
  
        &:before {
            display: block;
            content: "";
            height: 3px;
            width: 0;
            background-color: #000;
            transition: all 0.3s ease-in;
        }
    }

    .custom-btn {
        width: 100%;
        height: 40px;
        background-color: rgb(232, 130, 36);
        margin: 2em 0 1em 0;
        border: none;
        color: #fff;
        padding: 8px;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
      }
  
      .custom-btn:active:after {
        transition: 0s;
        opacity: 0.7;
        clip-path: circle(0% at 0% 0%);
      }
  
      .custom-btn::after {
        content: "";
        display: block;
        position: relative;
        top: -32px;
        left: -8px;
        height: 40px;
        width: 150px;
        background-color:rgb(255, 238, 192);
        opacity: 0;
        clip-path: circle(150% at 0% 0%);
        transition: all 0.4s ease-in;
      }

      .form-error--text {
        font-size: 80%;
        color: #dc3545;
    }
  
    .is-inv {
        background-color: #dc3545;
    }
  
    .is-inv--input {
        background-color: rgb(255, 200, 194);
    }
}

.neg-space {
    position: absolute;
    top: 80%;
    left: -65px;
}

.loginText {
    width           : 100%;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius   : 5px 0 0 5px;
    color           : white;
    padding         : 3em;

    p {
        text-align: justify;
    }
}

@media (max-width: 440px) {

    .login-content,
    .login-content{
        display       : flex;
        flex-direction: column;
        align-items   : center;
    }

     .login-wrap{
        background-position: cover;
        background-repeat  : no-repeat;
        background-size    : cover;
        min-height         : 100vh;
        padding            : 1em;
        min-width          : 100vw;
     }

     .login{
        width          : 100%;
        height         : 100%;
        padding        : 2em;
        padding-left   : 1em;
        direction      : flex;
        flex-direction : column;
    }

    .login-guy{
        width          : 100%;
        height         : 100%;
        padding        : 2em;
        background-color: #e0f3f4;
        svg {
            height: 100%;
            width: 100%
        }
    }
    .sect-text--1{
        font-size   : 28px;
        font-weight : lighter;
        margin      : 0.5em 0 0 0;
    }
    .neg-space {
        position: absolute;
        top: 80%;
        left: -200px;
    }

    

}