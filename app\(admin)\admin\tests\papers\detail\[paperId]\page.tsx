// app/(admin)/admin/tests/papers/detail/[paperId]/page.tsx
import { notFound } from 'next/navigation'
import { AdminPapersServerService } from '@/lib/server-services/admin/papers.server'
import PaperDetailClient from '@/components/admin/tests/paper-detail-client'
import AdminBreadcrumb from '@/components/admin/admin-breadcrumb'

interface PageProps {
  params: Promise<{ paperId: string }>
  searchParams: Promise<{
    type: string
    name: string
    show_ans: string
    time_lim: string
    no_of_ques: string
    status: string
    once_ans: string
    neg_marks: string
    rand_ques: string
  }>
}

export default async function PaperDetailPage({
  params,
  searchParams
}: PageProps) {
  const { paperId } = await params
  const searchParamsData = await searchParams
  const paperType = parseInt(searchParamsData.type)

  if (!paperId || isNaN(paperType)) {
    notFound()
  }

  // Get paper details and questions
  const [paperDetails, questions] = await Promise.all([
    AdminPapersServerService.getPaperDetails(paperId),
    AdminPapersServerService.getPaperQuestions(paperId, paperType)
  ])

  // Build paper data from search params if server data is not available
  const paperData = paperDetails || {
    paper_id: paperId,
    paper_name: searchParamsData.name || 'Unknown Paper',
    no_of_ques: searchParamsData.no_of_ques || '0',
    time_lim: searchParamsData.time_lim || '0',
    status: searchParamsData.status || '0',
    show_ans: searchParamsData.show_ans || '0',
    once_ans: searchParamsData.once_ans || '0',
    neg_marks: searchParamsData.neg_marks || '0',
    rand_ques: searchParamsData.rand_ques || '0'
  }

  return (
    <div className="px-4 py-6">
      <AdminBreadcrumb
        items={[
          { label: 'Admin Console', href: '/admin' },
          { label: 'Tests & Papers', href: '/admin/tests/papers' },
          { label: paperData.paper_name, isCurrentPage: true }
        ]}
      />

      <PaperDetailClient
        paperId={paperId}
        initialPaperDetails={paperData}
        initialQuestions={questions}
        paperType={paperType}
      />
    </div>
  )
}
