import { Component, OnInit, ViewChild, TemplateRef, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';

import { LoginService } from '../../Services/login.service';
import { AdminViewService } from '../../Services/admin-view.service';

@Component({
  selector: 'app-upload-console',
  templateUrl: './upload-console.component.html',
  styleUrls: ['./upload-console.component.scss']
})
export class UploadConsoleComponent implements OnInit, OnDestroy
{
  @ViewChild('unauthTemplate')      public uATemplate: TemplateRef<any>;
  @ViewChild('resEmptyTemplate')    public resTemplate: TemplateRef<any>;
  @ViewChild(BsDatepickerDirective) public datepicker: BsDatepickerDirective;

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };

  public pageDisable = true;
  public adminName: string;

  public isOpen      = false;
  public isOpenOpts1 = false;
  public showContent = false;
  public testTypes   = [
    'Model Paper',
    'Module Paper'
  ];

  public bsValue  = new Date();
  public bsConfig = {
    dateInputFormat: 'DD/MM/YYYY',
    containerClass: 'theme-dark-blue',
  };

  public selectedDate: Date;
  public userList: Array<any>;

  constructor(public router: Router,
              public modalService: BsModalService,
              public adminViewService: AdminViewService,
              public loginService: LoginService) { }

  ngOnInit() {
    localStorage.removeItem('QPage');

    setTimeout(() => {
      if (sessionStorage.getItem('logged') === null) {
        this.openModal(this.uATemplate);
        localStorage.setItem('QPage', 'AC');
      } else {
        const email = sessionStorage.getItem('QMail');
        this.loginService.checkAdminLogin(email).subscribe(response => {
          const repText = JSON.parse(JSON.stringify(response));

          if (repText.msg === '821f069c-12dd-3d3d-9462-d6e84564b659') {
            this.adminName   = sessionStorage.getItem('QUser');
            this.pageDisable = false;

            return;
          } else {
            this.router.navigate(['/home']);
          }

          this.router.navigate(['/home']);
        }, error => {
          this.router.navigate(['/home']);
        });
      }
    }, 0);
  }

  toggleSidebar() {
    this.isOpen = !this.isOpen;
  }

  showOpts(num: Number) {
    switch (num) {
      case 0:
        this.showContent = !this.showContent;
        break;

      case 1:
        this.isOpenOpts1 = !this.isOpenOpts1;
        break;
    }
  }

  /**
   * @description Function to get the list of registered students
   * of a particular date
   *
   * <AUTHOR> Sunil <<EMAIL>>
   */
  viewRegistrations() {
    const that = this;

    if (typeof this.selectedDate !== 'undefined') {
      let month = '' + (this.selectedDate.getMonth() + 1),
          day   = '' + (this.selectedDate.getDate());

      const year  = '' + (this.selectedDate.getFullYear());

      day   = day.length < 2 ? '0' + day : day;
      month = month.length < 2 ? '0' + month : month;

      const formattedDate = [year, month, day].join('-');

      this.adminViewService.getRegisteredList(formattedDate).subscribe(response => {
        const respText = JSON.parse(JSON.stringify(response));

        that.userList = respText;

        if (that.userList.length <= 0) {
          that.openModal(that.resTemplate);
        }
      });
    }
  }

  logout() {
    this.loginService.logoutUser(sessionStorage.getItem('QMail')).subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      if (respText.text === 'Logged Out') {
        sessionStorage.clear();
        this.closeModal();
        this.router.navigate(['/user/login']);
      }
    }, error => {});
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  closeModal() {
    if (typeof this.modalRef !== 'undefined') {
      this.modalRef.hide();
    }
  }

  ngOnDestroy() {
    this.closeModal();
  }
}
