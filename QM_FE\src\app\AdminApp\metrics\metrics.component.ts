import { Component, OnInit, ViewChild } from '@angular/core';
import { IMediaStream } from 'src/app/DashboardApp/Videos/video-stream/video-stream.component';
import { VgAPI, VgFullscreenAPI, BitrateOption } from 'videogular2/compiled/core';

import { StreamingService } from '../../Services/streaming.service';
import { VgHLS } from 'videogular2/compiled/src/streaming/vg-hls/vg-hls';
import { HttpHeaders } from '@angular/common/http';

@Component({
  selector: 'app-metrics',
  templateUrl: './metrics.component.html',
  styleUrls: ['./metrics.component.scss']
})
export class MetricsComponent implements OnInit {

  @ViewChild(VgHLS) vgHls: VgHLS;

  public api: VgAPI;
  public fullscreenApi: VgFullscreenAPI;
  public stream: IMediaStream = {
    type: 'hls',
    label: 'HLS: Streaming',
    source: ''
  };
  public hlsHeaders = {};

  public bitRates: BitrateOption[];
  public bitRateLabels = ['Auto', 'Low', 'Medium', 'High'];

  constructor(private streamingService: StreamingService) { }

  ngOnInit() {
    const that = this;
    this.streamingService.getTestVideoLink().subscribe(response => {

      that.api.pause();
      const respJson = JSON.parse(JSON.stringify(response.body));

      that.stream.source = respJson.url;
      that.api.play();
    }, error => {
      console.log('error :>> ', error);
    });
  }

  onPlayerReady(api: VgAPI) {
    this.api = api;
    this.fullscreenApi = api.fsAPI;
  }

  getRates(option: BitrateOption[]) {
    this.bitRates = [];

    for (const idx in option) {
      if (Object.prototype.hasOwnProperty.call(option, idx)) {
        option[idx].label = this.bitRateLabels[idx];

        this.bitRates.push(option[idx]);
      }
    }
  }

  setRates(option: BitrateOption) {
    this.vgHls.setBitrate(option);
  }

  setCFHeaders() {

    this.streamingService.getTestVideoSign().subscribe(response => {

      const cookies = [
        {'Set-Cookie': 'CloudFront-Expires=' + Date.now() + 300 + '; Domain=d3sbsgul7mrj3l.cloudfront.net; Secure; HttpOnly'},
        {'Set-Cookie': 'CloudFront-Signature=' + response + '; Domain=d3sbsgul7mrj3l.cloudfront.net; Secure; HttpOnly'},
        {'Set-Cookie': 'CloudFront-Key-Pair-Id=AKIAUTME5MDJ33NGWN7G; Domain=d3sbsgul7mrj3l.cloudfront.net; Secure; HttpOnly'}
      ];

      this.hlsHeaders = cookies;
    });
  }
}
