// components/admin/tests/paper-type-detail-client.tsx
'use client'

import { useState, useCallback, useMemo, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import {
  PaperType,
  Paper,
  FilterState,
  getPaperTypeConfig,
  Subtopic
} from '@/types/admin-types'
import { Group } from '@/types/chapter-paper-types'
import { TMCQSubGroup } from '@/types/technical-mcq-types'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import { AdminTopicsClientService } from '@/lib/client-services/admin/topics.client'
import TopicSubtopicSelectors from './topic-subtopic-selectors'
import PapersList from './papers-list'
import CreatePaperModal from './create-paper-modal'
import LoadingIndicator from '@/components/shared/indicator'

interface PaperTypeDetailClientProps {
  paperType: PaperType
  initialTopic?: string
  initialSubtopic?: string
}

// Separate loading states for different operations
interface LoadingStates {
  topics: boolean
  subtopics: boolean
  papers: boolean
  actions: boolean
}

export default function PaperTypeDetailClient({
  paperType,
  initialTopic = '',
  initialSubtopic = ''
}: PaperTypeDetailClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize state
  const [papers, setPapers] = useState<Paper[]>([])
  const [topics, setTopics] = useState<any[]>([])
  const [subtopics, setSubtopics] = useState<any[]>([])
  const [selectedTopic, setSelectedTopic] = useState<string>(initialTopic)
  const [selectedSubtopic, setSelectedSubtopic] =
    useState<string>(initialSubtopic)

  // Granular loading states
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    topics: false,
    subtopics: false,
    papers: false,
    actions: false
  })

  const [showCreateModal, setShowCreateModal] = useState(false)

  // Helper to update specific loading state
  const setLoadingState = useCallback(
    (key: keyof LoadingStates, value: boolean) => {
      setLoadingStates((prev) => ({ ...prev, [key]: value }))
    },
    []
  )

  const config = useMemo(() => getPaperTypeConfig(paperType.id), [paperType.id])

  /**
   * Update URL with current filter state
   */
  const updateURL = useCallback(
    (newFilters: Partial<FilterState>) => {
      const params = new URLSearchParams(searchParams)

      Object.entries(newFilters).forEach(([key, value]) => {
        if (value) {
          params.set(key, value)
        } else {
          params.delete(key)
        }
      })

      const typeName = paperType.name.toLowerCase().replace(/\s+/g, '-')
      router.push(`/admin/tests/papers/type/${typeName}?${params.toString()}`)
    },
    [router, searchParams, paperType.name]
  )

  /**
   * Load topics on component mount
   */
  useEffect(() => {
    const loadInitialData = async () => {
      // Store paper type in sessionStorage for navigation
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('paperType', paperType.id.toString())
        sessionStorage.setItem('paperState', 'Edit')
      }

      if (config.hasTopicDropdown) {
        setLoadingState('topics', true)
        try {
          let topicsData = []

          switch (paperType.id) {
            case 1: // Chapter-Wise Papers
            case 6: // Chapter-Wise Practice
              topicsData = await AdminTopicsClientService.getSuperGroups()
              break
            case 11: // Technical MCQs
              topicsData = await AdminTopicsClientService.getTMCQGroups()
              break
            case 13: // Verbal Practice
              topicsData =
                await AdminTopicsClientService.getVerbalPracticeGroups()
              break
            case 14: // Technical Practice
              topicsData =
                await AdminTopicsClientService.getTechnicalPracticeGroups()
              break
          }

          setTopics(topicsData)

          // If we have an initial topic, load its data
          if (initialTopic) {
            await handleTopicChange(initialTopic)
          }
        } catch (error) {
          console.error('Error loading topics:', error)
          toast.error('Failed to load topics')
        } finally {
          setLoadingState('topics', false)
        }
      } else {
        // Load papers directly if no topics required
        setLoadingState('papers', true)
        try {
          const papersData = await AdminPapersClientService.getPapersByType(
            paperType.id
          )
          setPapers(papersData)
        } catch (error) {
          console.error('Error loading papers:', error)
          toast.error('Failed to load papers')
        } finally {
          setLoadingState('papers', false)
        }
      }
    }

    loadInitialData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paperType.id, config.hasTopicDropdown, initialTopic])

  /**
   * Handle topic change
   */
  const handleTopicChange = useCallback(
    async (topicId: string) => {
      if (selectedTopic === topicId) return

      setLoadingState('subtopics', true)
      setSelectedTopic(topicId)
      setSelectedSubtopic('')
      setSubtopics([])
      setPapers([])

      try {
        // Store topic in sessionStorage
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('QPaperGroup', topicId)
        }

        if (config.hasSubtopicDropdown && topicId) {
          // Load subtopics
          let subtopicsData: (Group | TMCQSubGroup | Subtopic)[] = []

          switch (paperType.id) {
            case 1: // Chapter-Wise Papers
            case 6: // Chapter-Wise Practice
              subtopicsData = await AdminTopicsClientService.getGroups(topicId)
              break
            case 11: // Technical MCQs
              subtopicsData =
                await AdminTopicsClientService.getTMCQSubGroups(topicId)
              break
          }

          setSubtopics(subtopicsData)
        } else if (topicId) {
          // Load papers directly
          setLoadingState('papers', true)
          setLoadingState('subtopics', false)
          const papersData = await AdminPapersClientService.getPapersByType(
            paperType.id,
            topicId
          )
          setPapers(papersData)
          setLoadingState('papers', false)
        }

        updateURL({
          topic: topicId,
          subtopic: ''
        })
      } catch (error) {
        console.error('Error loading topic data:', error)
        toast.error('Failed to load topic data')
      } finally {
        setLoadingState('subtopics', false)
      }
    },
    [
      selectedTopic,
      config.hasSubtopicDropdown,
      paperType.id,
      updateURL,
      setLoadingState
    ]
  )

  /**
   * Handle subtopic change
   */
  const handleSubtopicChange = useCallback(
    async (subtopicId: string) => {
      if (!selectedTopic || selectedSubtopic === subtopicId) return

      setLoadingState('papers', true)
      setSelectedSubtopic(subtopicId)

      try {
        // Store subtopic in sessionStorage
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('QPaperSubGroup', subtopicId)
        }

        const papersData = await AdminPapersClientService.getPapersByType(
          paperType.id,
          selectedTopic,
          subtopicId
        )
        setPapers(papersData)

        updateURL({
          topic: selectedTopic,
          subtopic: subtopicId
        })
      } catch (error) {
        console.error('Error loading subtopic data:', error)
        toast.error('Failed to load papers')
      } finally {
        setLoadingState('papers', false)
      }
    },
    [selectedTopic, selectedSubtopic, paperType.id, updateURL, setLoadingState]
  )

  /**
   * Handle paper edit
   */
  const handleEditPaper = useCallback(
    (paper: Paper) => {
      // Build query params for paper detail page
      const params = new URLSearchParams({
        type: paperType.id.toString(),
        name: paper.paper_name,
        show_ans: paper.show_ans || '0',
        time_lim: paper.time_lim || '0',
        no_of_ques: paper.no_of_ques || '0',
        status: paper.status || '0',
        once_ans: paper.once_ans || '0',
        neg_marks: paper.neg_marks || '0',
        rand_ques: paper.rand_ques || '0'
      })

      router.push(
        `/admin/tests/papers/detail/${paper.paper_id}?${params.toString()}`
      )
    },
    [paperType.id, router]
  )

  /**
   * Handle paper deletion
   */
  const handleDeletePaper = useCallback(
    async (paperId: string) => {
      const confirmed = window.confirm(
        'Are you sure you want to delete this paper? This action cannot be undone.'
      )
      if (!confirmed) return

      try {
        setLoadingState('actions', true)
        await AdminPapersClientService.deletePaper(paperId, paperType.id)

        // Refresh papers list
        setPapers((current) => current.filter((p) => p.paper_id !== paperId))
        toast.success('Paper deleted successfully')
      } catch (error) {
        console.error('Error deleting paper:', error)
        toast.error('Failed to delete paper')
      } finally {
        setLoadingState('actions', false)
      }
    },
    [paperType.id, setLoadingState]
  )

  /**
   * Handle paper status toggle
   */
  const handleToggleStatus = useCallback(
    async (paperId: string, newStatus: string) => {
      try {
        setLoadingState('actions', true)
        await AdminPapersClientService.togglePaperStatus(
          paperId,
          newStatus,
          paperType.id
        )

        // Update papers list
        setPapers((current) =>
          current.map((p) =>
            p.paper_id === paperId
              ? { ...p, status: newStatus === '1' ? '0' : '1' }
              : p
          )
        )

        toast.success(
          `Paper ${newStatus === '1' ? 'hidden' : 'shown'} successfully`
        )
      } catch (error) {
        console.error('Error toggling paper status:', error)
        toast.error('Failed to update paper status')
      } finally {
        setLoadingState('actions', false)
      }
    },
    [paperType.id, setLoadingState]
  )

  /**
   * Handle refresh cache for weekly competitive papers
   */
  const handleRefreshCache = useCallback(async () => {
    try {
      setLoadingState('actions', true)
      await AdminPapersClientService.refreshCompetitiveCache()
      toast.success('Cache refreshed successfully')
    } catch (error) {
      console.error('Error refreshing cache:', error)
      toast.error('Failed to refresh cache')
    } finally {
      setLoadingState('actions', false)
    }
  }, [setLoadingState])

  /**
   * Handle copy list URL for practice papers
   */
  const handleCopyListUrl = useCallback(() => {
    if (!selectedTopic) {
      toast.error('Please select a topic first')
      return
    }

    const baseUrl =
      paperType.id === 13
        ? 'https://verbal-notes.quantmasters.in'
        : 'https://tech-notes.quantmasters.in'

    const listUrl = `${baseUrl}/papers/${selectedTopic}`
    navigator.clipboard.writeText(listUrl)
    toast.success('URL copied to clipboard!')
  }, [paperType.id, selectedTopic])

  /**
   * Handle paper creation success
   */
  const handlePaperCreated = useCallback((newPaper: Paper) => {
    setPapers((current) => [newPaper, ...current])
    setShowCreateModal(false)
    toast.success('Paper created successfully')
  }, [])

  // Check if any loading state is active
  const isAnyLoading = Object.values(loadingStates).some((state) => state)

  return (
    <div className="space-y-6">
      {/* Topic/Subtopic Selectors */}
      {config.hasTopicDropdown && (
        <div className="bg-white rounded-lg shadow p-6 relative">
          {loadingStates.topics && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Loading topics..." />
            </div>
          )}
          {loadingStates.subtopics && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Loading subtopics..." />
            </div>
          )}
          <TopicSubtopicSelectors
            paperType={paperType.id}
            topics={topics}
            subtopics={subtopics}
            selectedTopic={selectedTopic}
            selectedSubtopic={selectedSubtopic}
            onTopicChange={handleTopicChange}
            onSubtopicChange={handleSubtopicChange}
            onTopicsUpdate={setTopics}
            onSubtopicsUpdate={setSubtopics}
          />
        </div>
      )}

      {/* Papers List */}
      <div className="bg-white rounded-lg shadow p-6 relative">
        {loadingStates.papers && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
            <LoadingIndicator isLoading={true} text="Loading papers..." />
          </div>
        )}
        {loadingStates.actions && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
            <LoadingIndicator isLoading={true} text="Processing..." />
          </div>
        )}

        <div className="flex justify-between items-center mb-6">
          <h2 className="text-lg font-semibold">Papers</h2>
          {config.canCreate && (
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              disabled={isAnyLoading}
            >
              Create New Paper
            </button>
          )}
        </div>

        <PapersList
          papers={papers}
          paperType={paperType.id}
          onEdit={handleEditPaper}
          onDelete={handleDeletePaper}
          onToggleStatus={handleToggleStatus}
          onRefreshCache={handleRefreshCache}
          onCopyUrl={handleCopyListUrl}
        />
      </div>

      {/* Create Paper Modal */}
      <CreatePaperModal
        isOpen={showCreateModal}
        paperType={paperType.id}
        selectedTopic={selectedTopic}
        selectedSubtopic={selectedSubtopic}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handlePaperCreated}
      />
    </div>
  )
}
