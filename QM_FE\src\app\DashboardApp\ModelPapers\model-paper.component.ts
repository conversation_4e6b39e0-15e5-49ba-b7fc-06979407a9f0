import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { TestsService } from '../../Services/tests.service';
import { Paper } from '../../Models/Paper';

declare let gtag: Function;

@Component({
  selector: 'app-model-paper',
  templateUrl: './model-paper.component.html',
  styleUrls: ['./model-paper.component.scss']
})
export class ModelPaperComponent implements OnInit {

  public papers: Array<Paper> = [];
  selectedTest = null;

  public showIndicator = false;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              private testsService: TestsService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    const that = this;

    this.showIndicator = true;
    this.testsService.getPapers().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      that.papers = resp;
      for (const idx in that.papers) {
        if (parseInt(that.papers[idx].public, 10) === 1) {
          that.selectedTest = that.papers[idx];
        }
      }

      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '4', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans(isPublic: boolean) {
    if (!isPublic) {
      this.router.navigate(['/plans']);
    }
  }
}
