// lib/cookies.ts
import Cookies from 'js-cookie'

const COOKIE_EXPIRATION = 7

export interface AuthResponse {
  text: string
  user: string
  key: string
  sess: string
  token: string
  email: string
  complete: string
  subscribed: string
}

export const LoginRefresh = {
  fnSetCookiesAndSession: (
    data: AuthResponse,
    email: string,
    userName: string,
    complete: string,
    key: string
  ) => {
    // Set cookies (including the token!)
    Cookies.set('QMA_USR', email, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_KEY', key, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_NAME', userName, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_COMPLETE', complete, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_TOK', data.token, { expires: COOKIE_EXPIRATION }) // Add token to cookies
    Cookies.set('QMA_SESS', data.sess, { expires: COOKIE_EXPIRATION }) // Add session to cookies
    Cookies.set('QMA_SUBSCRIBED', data.subscribed || '0', {
      expires: COOKIE_EXPIRATION
    })

    // Set localStorage and sessionStorage items
    if (typeof window !== 'undefined') {
      localStorage.setItem('QMA_USR', email)
      localStorage.setItem('QMA_NAME', userName)
      localStorage.setItem('QMA_TOK', data.token)
      localStorage.setItem('QMA_SESS', data.sess)
      localStorage.setItem('QMA_KEY', key)
      localStorage.setItem('QMA_COMPLETE', complete)
      localStorage.setItem('QMA_SUBSCRIBED', data.subscribed || '0')

      sessionStorage.setItem('QMA_USR', email)
      sessionStorage.setItem('QMA_NAME', userName)
      sessionStorage.setItem('QMA_TOK', data.token)
      sessionStorage.setItem('QMA_SESS', data.sess)
      sessionStorage.setItem('QMA_KEY', key)
      sessionStorage.setItem('QMA_COMPLETE', complete)
      sessionStorage.setItem('QMA_SUBSCRIBED', data.subscribed || '0')
    }

    return true
  },

  fnClearCookiesAndSession: () => {
    // Clear cookies
    Cookies.remove('QMA_USR')
    Cookies.remove('QMA_KEY')
    Cookies.remove('QMA_NAME')
    Cookies.remove('QMA_COMPLETE')
    Cookies.remove('QMA_TOK') // Add token removal
    Cookies.remove('QMA_SESS') // Add session removal
    Cookies.remove('QMA_SUBSCRIBED')

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('QMA_USR')
      localStorage.removeItem('QMA_NAME')
      localStorage.removeItem('QMA_TOK')
      localStorage.removeItem('QMA_SESS')
      localStorage.removeItem('QMA_KEY')
      localStorage.removeItem('QMA_COMPLETE')
      localStorage.removeItem('QMA_SUBSCRIBED')
    }

    return true
  },

  fnCheckUserLoginStatus: (): boolean => {
    if (typeof window !== 'undefined') {
      // Check both cookie and localStorage for better reliability
      const tokenCookie = Cookies.get('QMA_TOK')
      const emailCookie = Cookies.get('QMA_USR')

      // If cookies exist, use them
      if (tokenCookie && emailCookie) {
        return true
      }

      // Fallback to localStorage
      const tokenStorage = localStorage.getItem('QMA_TOK')
      const emailStorage = localStorage.getItem('QMA_USR')

      return !!(tokenStorage && emailStorage)
    }
    return false
  },

  // Helper to get token from either cookie or localStorage
  getAuthToken: (): string | null => {
    if (typeof window !== 'undefined') {
      // First try cookie
      const tokenCookie = Cookies.get('QMA_TOK')
      if (tokenCookie) return tokenCookie

      // Fallback to localStorage
      return localStorage.getItem('QMA_TOK')
    }
    return null
  },

  // Sync cookies from localStorage if needed
  syncFromLocalStorage: (): void => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('QMA_TOK')
      const email = localStorage.getItem('QMA_USR')

      if (token && email && !Cookies.get('QMA_TOK')) {
        // If we have auth in localStorage but not in cookies, restore cookies
        Cookies.set('QMA_TOK', token, { expires: COOKIE_EXPIRATION })
        Cookies.set('QMA_USR', email, { expires: COOKIE_EXPIRATION })

        // Also sync other values if available
        const name = localStorage.getItem('QMA_NAME')
        const key = localStorage.getItem('QMA_KEY')
        const complete = localStorage.getItem('QMA_COMPLETE')
        const sess = localStorage.getItem('QMA_SESS')
        const subscribed = localStorage.getItem('QMA_SUBSCRIBED')

        if (name) Cookies.set('QMA_NAME', name, { expires: COOKIE_EXPIRATION })
        if (key) Cookies.set('QMA_KEY', key, { expires: COOKIE_EXPIRATION })
        if (complete)
          Cookies.set('QMA_COMPLETE', complete, { expires: COOKIE_EXPIRATION })
        if (sess) Cookies.set('QMA_SESS', sess, { expires: COOKIE_EXPIRATION })
        if (subscribed)
          Cookies.set('QMA_SUBSCRIBED', subscribed, {
            expires: COOKIE_EXPIRATION
          })
      }
    }
  },

  // Helper to get email from either cookie or localStorage
  getUserEmail: (): string | null => {
    if (typeof window !== 'undefined') {
      // First try cookie
      const emailCookie = Cookies.get('QMA_USR')
      if (emailCookie) return emailCookie

      // Fallback to localStorage/sessionStorage
      return (
        localStorage.getItem('QMA_USR') ||
        sessionStorage.getItem('QMail') ||
        sessionStorage.getItem('QMA_USR')
      )
    }
    return null
  },

  // Helper to get auth key from storage
  getKey: (): string | null => {
    if (typeof window !== 'undefined') {
      // First try cookie
      const keyCookie = Cookies.get('QMA_KEY')
      if (keyCookie) return keyCookie

      // Fallback to localStorage/sessionStorage
      return (
        localStorage.getItem('QMA_KEY') || sessionStorage.getItem('QMA_KEY')
      )
    }
    return null
  },

  // Helper to get session ID from storage
  getSessionId: (): string | null => {
    if (typeof window !== 'undefined') {
      // First try cookie
      const sessionCookie = Cookies.get('QMA_SESS')
      if (sessionCookie) return sessionCookie

      // Fallback to localStorage/sessionStorage
      return (
        localStorage.getItem('QMA_SESS') || sessionStorage.getItem('QMSESS_ID')
      )
    }
    return null
  }
}
