'use client'

import { Lock } from 'lucide-react'
import { ChapterPaper } from '@/types/chapter-practice-types'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'

interface PaperListProps {
  papers: ChapterPaper[]
  beginTest: (paperId: string, paperName: string, timeLim: string) => void
  takeToPlans: () => void
}

export default function PaperList({
  papers,
  beginTest,
  takeToPlans
}: PaperListProps) {
  return (
    <div className="p-4 space-y-3">
      {papers.length === 0 ? (
        <p className="text-center text-gray-500 py-4">No papers available</p>
      ) : (
        papers.map((paper) => (
          <Card
            key={paper.paper_id}
            className="overflow-hidden border-gray-200"
          >
            <CardContent className="p-4 flex justify-between items-center">
              <div className="flex-1">
                <div className="flex items-center space-x-2">
                  {paper.public === '0' && (
                    <div
                      onClick={takeToPlans}
                      className="cursor-pointer"
                      title="Premium Feature"
                    >
                      <Lock className="text-yellow-500" size={18} />
                    </div>
                  )}
                  <h4 className="font-medium">{paper.paper_name}</h4>
                </div>
                <div className="mt-1 text-sm text-gray-600">
                  <p>
                    {parseInt(paper.time_lim) === 0
                      ? 'No Time Limit'
                      : `${parseInt(paper.time_lim) / (1000 * 60)} Minutes`}
                  </p>
                  <p>Number of questions: {paper.no_of_ques}</p>
                </div>
              </div>
              <Button
                onClick={() =>
                  beginTest(paper.paper_id, paper.paper_name, paper.time_lim)
                }
                className="ml-4"
              >
                Begin Test
              </Button>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  )
}
