import { Metadata } from 'next'
import { Mail, Phone, MapPin, Send } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Contact Us | QuantMasters',
  description:
    'Get in touch with QuantMasters team. We are here to help you with your career journey.'
}

export default function ContactUs() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-sky-50 to-blue-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-12 mt-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Contact Us</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Have questions or need assistance? We're here to help you succeed in
            your career journey.
          </p>
        </div>

        {/* Main Content */}
        <div className="grid md:grid-cols-2 gap-12 items-start">
          {/* Contact Information */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Get in Touch
            </h2>

            <div className="space-y-6">
              {/* Email */}
              <div className="flex items-start space-x-4">
                <div className="bg-sky-100 p-3 rounded-lg">
                  <Mail className="h-6 w-6 text-sky-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    Email Support
                  </h3>
                  <p className="text-gray-600 mb-2">
                    Reach out to us for any queries or support
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-sky-600 hover:text-sky-700 font-medium text-lg"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              {/* Response Time */}
              <div className="flex items-start space-x-4">
                <div className="bg-green-100 p-3 rounded-lg">
                  <Send className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    Response Time
                  </h3>
                  <p className="text-gray-600">
                    We typically respond within 24 hours during business days
                  </p>
                </div>
              </div>

              {/* Support Hours */}
              <div className="flex items-start space-x-4">
                <div className="bg-purple-100 p-3 rounded-lg">
                  <MapPin className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">
                    Support Hours
                  </h3>
                  <p className="text-gray-600">
                    Monday - Friday: 9:00 AM - 6:00 PM IST
                    <br />
                    Saturday: 10:00 AM - 4:00 PM IST
                  </p>
                </div>
              </div>
            </div>

            {/* CTA Button */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <a
                href="mailto:<EMAIL>?subject=Support Request&body=Hi QuantMasters Team,%0D%0A%0D%0AI need help with:%0D%0A%0D%0A"
                className="w-full bg-sky-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-sky-700 transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <Mail className="h-5 w-5" />
                <span>Send us an email</span>
              </a>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Frequently Asked Questions
            </h2>

            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  How can I access the practice tests?
                </h3>
                <p className="text-gray-600">
                  After logging in, you can access various practice tests from
                  your dashboard including chapter-wise tests, model papers, and
                  competitive exam papers.
                </p>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  What types of exams do you cover?
                </h3>
                <p className="text-gray-600">
                  We cover a wide range of competitive exams including banking,
                  SSC, railway, defense (AFCAT), and technical MCQ papers.
                </p>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  How do I track my progress?
                </h3>
                <p className="text-gray-600">
                  Your progress is automatically tracked and displayed in your
                  dashboard with detailed analytics and performance insights.
                </p>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Can I download study materials?
                </h3>
                <p className="text-gray-600">
                  Yes, you can access and download notes and study materials
                  from the notes section of your dashboard.
                </p>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500 text-center">
                Can't find what you're looking for?{' '}
                <a
                  href="mailto:<EMAIL>"
                  className="text-sky-600 hover:text-sky-700 font-medium"
                >
                  Contact us directly
                </a>
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Message */}
        <div className="mt-12 text-center bg-white rounded-2xl shadow-lg p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-3">
            Ready to Start Your Journey?
          </h3>
          <p className="text-gray-600 mb-6">
            Join thousands of students who have successfully advanced their
            careers with QuantMasters
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/user/login"
              className="bg-sky-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-sky-700 transition-colors duration-200"
            >
              Get Started
            </a>
            <a
              href="mailto:<EMAIL>"
              className="border-2 border-sky-600 text-sky-600 px-8 py-3 rounded-lg font-medium hover:bg-sky-50 transition-colors duration-200"
            >
              Ask a Question
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
