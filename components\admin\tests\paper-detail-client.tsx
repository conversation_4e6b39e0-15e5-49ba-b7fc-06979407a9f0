// components/admin/tests/paper-detail-client.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeft, Save, FileText, Settings } from 'lucide-react'
import { Paper, getPaperTypeConfig } from '@/types/admin-types'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { toast } from 'sonner'
import LoadingIndicator from '@/components/shared/indicator'
import QuestionManager from './question-manager'

interface PaperDetailClientProps {
  paperId: string
  initialPaperDetails: Paper
  initialQuestions: any[]
  paperType: number
}

export default function PaperDetailClient({
  paperId,
  initialPaperDetails,
  initialQuestions,
  paperType
}: PaperDetailClientProps) {
  const router = useRouter()
  const [paperDetails, setPaperDetails] = useState<Paper>(initialPaperDetails)
  const [questions, setQuestions] = useState<any[]>(initialQuestions)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('details')

  const config = getPaperTypeConfig(paperType)

  const handleSavePaper = async () => {
    setLoading(true)
    try {
      await AdminPapersClientService.updatePaper(
        paperId,
        paperDetails,
        paperType
      )
      toast.success('Paper updated successfully')
    } catch (error) {
      console.error('Error updating paper:', error)
      toast.error('Failed to update paper')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof Paper, value: string | number) => {
    setPaperDetails((prev) => ({ ...prev, [field]: value }))
  }

  const formatTime = (timeInMs: string | number) => {
    const timeMs = typeof timeInMs === 'string' ? parseInt(timeInMs) : timeInMs
    if (timeMs === 0) return 0
    return Math.round(timeMs / 60000)
  }

  const handleTimeChange = (minutes: number) => {
    handleInputChange('time_lim', (minutes * 60000).toString())
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" onClick={() => router.back()} className="p-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{paperDetails.paper_name}</h1>
            <p className="text-gray-600">{config.name} - Paper Details</p>
          </div>
        </div>
        <Button onClick={handleSavePaper} disabled={loading}>
          {loading ? (
            <LoadingIndicator isLoading={loading} />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          Save Changes
        </Button>
      </div>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Paper Details</TabsTrigger>
          <TabsTrigger value="questions">
            Questions ({questions.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="paper_name">Paper Name</Label>
                  <Input
                    id="paper_name"
                    value={paperDetails.paper_name}
                    onChange={(e) =>
                      handleInputChange('paper_name', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="no_of_ques">Number of Questions</Label>
                  <Input
                    id="no_of_ques"
                    type="number"
                    min="0"
                    value={paperDetails.no_of_ques}
                    onChange={(e) =>
                      handleInputChange('no_of_ques', e.target.value)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="time_lim">Time Limit (minutes)</Label>
                  <Input
                    id="time_lim"
                    type="number"
                    min="0"
                    value={formatTime(paperDetails.time_lim)}
                    onChange={(e) =>
                      handleTimeChange(parseInt(e.target.value) || 0)
                    }
                    placeholder="0 for no time limit"
                  />
                </div>

                {config.hasStatusToggle && (
                  <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                      id="status"
                      value={paperDetails.status}
                      onChange={(e) =>
                        handleInputChange('status', e.target.value)
                      }
                      className="w-full p-2 border rounded-md"
                    >
                      <option value="1">Shown</option>
                      <option value="0">Hidden</option>
                    </select>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Paper Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Paper Settings</span>
              </CardTitle>
              <CardDescription>
                Configure how students interact with this paper
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show_ans"
                    checked={paperDetails.show_ans === '1'}
                    onCheckedChange={(checked) =>
                      handleInputChange('show_ans', checked ? '1' : '0')
                    }
                  />
                  <Label htmlFor="show_ans">
                    Show answers after completion
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="once_ans"
                    checked={paperDetails.once_ans === '1'}
                    onCheckedChange={(checked) =>
                      handleInputChange('once_ans', checked ? '1' : '0')
                    }
                  />
                  <Label htmlFor="once_ans">Allow only one attempt</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="neg_marks"
                    checked={paperDetails.neg_marks === '1'}
                    onCheckedChange={(checked) =>
                      handleInputChange('neg_marks', checked ? '1' : '0')
                    }
                  />
                  <Label htmlFor="neg_marks">Enable negative marking</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="rand_ques"
                    checked={paperDetails.rand_ques === '1'}
                    onCheckedChange={(checked) =>
                      handleInputChange('rand_ques', checked ? '1' : '0')
                    }
                  />
                  <Label htmlFor="rand_ques">Randomize questions</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Paper Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {paperDetails.no_of_ques}
                  </div>
                  <div className="text-sm text-gray-600">Questions</div>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {formatTime(paperDetails.time_lim) || 'No Limit'}
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatTime(paperDetails.time_lim)
                      ? 'Minutes'
                      : 'Time Limit'}
                  </div>
                </div>
                <div className="p-4 bg-sky-50 rounded-lg">
                  <div className="text-2xl font-bold text-sky-600">
                    {paperDetails.status === '1' ? 'Active' : 'Hidden'}
                  </div>
                  <div className="text-sm text-gray-600">Status</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-6">
          <QuestionManager
            paperId={paperId}
            paperType={paperType}
            questions={questions}
            onQuestionsUpdate={setQuestions}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
