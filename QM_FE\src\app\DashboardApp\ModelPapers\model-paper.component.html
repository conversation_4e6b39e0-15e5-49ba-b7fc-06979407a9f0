<div class="model-paper-list-container">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Custom compiled aptitude tests covering a wide variety of concepts</p>
    </div>
  </div>
  <div class="content">
    <div class="paper-wrap">
      <div class="paper" *ngFor="let paper of papers">
        <div class="locked-resource" (click)="takeToPlans()" *ngIf="paper.public == 0" title="Premium Feature">
          <img src="../../../assets/icons/lock.svg"/>
        </div>
        <h6>{{ paper.paper_name }}</h6>
        <p>{{paper.paper_desc}}</p>
        <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
        <!-- <p>No. of Questions: {{ paper.no_of_ques }}</p> -->
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>
    <!-- <table>
      <thead>
        <tr>
          <th>Paper Name</th>
          <th>Description</th>
          <th>Time Limit(Minutes)</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let paper of papers" (click)="paper.public == 1 ? selectedTest = paper : selectedTest = null; takeToPlans(paper.public)" [class.active]="selectedTest.paper_id === paper.paper_id" [class.disabled]="paper.public == 0">
          <td>{{paper.paper_name}}</td>
          <td>{{paper.paper_desc}}</td>
          <td>{{paper.time_lim > 0 ? paper.time_lim : "No TimeLimit"}}</td>
        </tr>
      </tbody>
    </table>
    <div class="footer">
      <button (click)="beginTest(selectedTest.paper_id, selectedTest.paper_name, selectedTest.time_lim)">Begin Test</button>
    </div> -->

  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>