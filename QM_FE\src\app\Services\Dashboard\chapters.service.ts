import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { SuperGroup } from '../../Models/Dashboard/Chapters/SuperGroup';
import { Group } from 'src/app/Models/Dashboard/Chapters/Group';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';

@Injectable({
  providedIn: 'root'
})
export class ChaptersService {

  private superGroupUrl = 'https://api.quantmasters.in/test/progression/super-groups';
  private groupUrl      = 'https://api.quantmasters.in/test/progression/groups';
  private groupPaperUrl = 'https://api.quantmasters.in/test/progression/group';
  private submitUrl     = 'https://api.quantmasters.in/test/progression/submit/marks';

  private imgUrl = 'https://api/quantmasters.in/test/progression/paper/';

  private adminPaperUrl = 'https://api.quantmasters.in/admin/paper/progression/upload/test';
  private adminQuesUrl  = 'https://api.quantmasters.in/admin/paper/progression/upload/test/question';
  private adminQuesGetUrl = 'https://api.quantmasters.in/admin/paper/progression/paper';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getSuperGroups(): Observable<SuperGroup[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<SuperGroup[]>(this.superGroupUrl, httpOps);
  }

  getGroups(superGrpId: string): Observable<Group[]> {
    this.setSecurityToken();

    const groupUrl = this.groupUrl + '/' + superGrpId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<Group[]>(groupUrl, httpOps);
  }

  getPapersOfAGroup(groupId: string): Observable<ChapterPaper[]> {
    this.setSecurityToken();

    const groupPaperUrl = this.groupPaperUrl + '/' + groupId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<ChapterPaper[]>(groupPaperUrl, httpOps);
  }

  getImageExplanation(paperId: string, quesNo: number) {

    this.setSecurityToken();

    const imageUrl = this.imgUrl + '/' + paperId + '/' + quesNo + '/explanation';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get(imageUrl, httpOps);
  }

  submitMarks(email: string, paperId: string, marks: number): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const data = {
      email: email,
      paper_id: paperId,
      marks: marks
    };

    return this.http.post<string>(this.submitUrl, data, httpOps);
  }

  updatePaper(paper: ChapterPaper): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.adminPaperUrl, paper, httpOps);
  }

  updateQuestion(question: Object): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.adminQuesUrl, question, httpOps);
  }

  getQuestionsForAdmin(paperId: String): Observable<string> {

    this.setSecurityToken();

    const questionUrl = this.adminQuesGetUrl + '/' + paperId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(questionUrl, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
