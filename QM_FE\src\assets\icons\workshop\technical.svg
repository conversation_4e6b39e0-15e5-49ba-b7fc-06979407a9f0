<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="373.982" height="294.786" viewBox="0 0 373.982 294.786"><defs><radialGradient id="a" cx="0.5" cy="0.5" r="0.478" gradientTransform="translate(-0.049) scale(1.098 1)" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#ffc8af"/><stop offset="1" stop-color="#ffbb9c"/></radialGradient><radialGradient id="b" cx="0.506" cy="0.49" r="0.451" gradientTransform="matrix(1.232, -0.21, 0.265, 0.978, -0.31, 0.174)" xlink:href="#a"/><radialGradient id="c" cx="0.5" cy="0.5" r="0.531" gradientTransform="translate(0.054) scale(0.891 1)" xlink:href="#a"/><radialGradient id="d" cx="0.5" cy="0.5" r="0.511" gradientTransform="translate(0.021) scale(0.958 1)" xlink:href="#a"/><radialGradient id="e" cx="1.47" cy="-0.006" r="4.15" gradientTransform="translate(0.306) scale(0.387 1)" xlink:href="#a"/><radialGradient id="f" cx="0.183" cy="0.438" r="1.813" gradientTransform="translate(0.33) scale(0.34 1)" xlink:href="#a"/><linearGradient id="g" x1="4.513" y1="-3.241" x2="3.024" y2="-2.458" gradientUnits="objectBoundingBox"><stop offset="0" stop-color="#606bf1"/><stop offset="1" stop-color="#58479f"/></linearGradient><linearGradient id="h" x1="8.927" y1="-5.136" x2="7.575" y2="-4.342" xlink:href="#g"/><linearGradient id="i" x1="6.918" y1="-3.36" x2="5.26" y2="-2.587" xlink:href="#g"/></defs><g transform="translate(-29.745 -79.775)"><g transform="translate(81.211 79.775)"><g transform="translate(26.884 16.749)"><rect width="84.469" height="78.841" fill="#112b46"/><g style="mix-blend-mode:screen;isolation:isolate"><rect width="84.469" height="8.338" fill="#1d262d"/></g><g transform="translate(63.153 2.735)"><path d="M196.225,103.805a1.529,1.529,0,1,1-1.529-1.528A1.529,1.529,0,0,1,196.225,103.805Z" transform="translate(-193.168 -102.277)" fill="#606bf1"/><g transform="translate(6.382)"><path d="M203.6,103.805a1.528,1.528,0,1,1-1.528-1.528A1.528,1.528,0,0,1,203.6,103.805Z" transform="translate(-200.539 -102.277)" fill="#fbd15b"/></g><g transform="translate(12.308 0.161)"><path d="M210.439,103.991a1.528,1.528,0,1,1-1.528-1.528A1.528,1.528,0,0,1,210.439,103.991Z" transform="translate(-207.383 -102.463)" fill="#ff8578"/></g></g><g transform="translate(15.02 16.089)"><rect width="11.964" height="1.006" fill="#ff8578"/><g transform="translate(14.042)"><rect width="11.963" height="1.006" fill="#fbd15b"/></g><g transform="translate(3.701 5.417)"><rect width="18.552" height="1.006" fill="#606bf1"/></g><g transform="translate(25.32 5.417)"><rect width="9.43" height="1.006" fill="#ff8578"/></g><g transform="translate(10.136 10.833)"><rect width="8.148" height="1.006" fill="#fbd15b"/></g><g transform="translate(22.993 10.833)"><rect width="27.03" height="1.006" fill="#fbd15b"/></g><g transform="translate(16.465 16.25)"><rect width="30.892" height="1.006" fill="#606bf1"/></g><g transform="translate(8.741 21.667)"><rect width="9.507" height="1.006" fill="#ff8578"/></g><g transform="translate(22.316 21.667)"><rect width="9.507" height="1.006" fill="#606bf1"/></g><g transform="translate(0 28.102)"><g transform="translate(0 21.667)"><rect width="13.831" height="1.006" fill="#ff8578"/></g><g transform="translate(18.675 21.667)"><rect width="13.831" height="1.006" fill="#fbd15b"/></g><g transform="translate(36.847 21.667)"><rect width="13.831" height="1.006" fill="#ff8578"/></g><g transform="translate(3.701 16.25)"><rect width="27.03" height="1.006" fill="#606bf1"/></g><g transform="translate(34.945 16.25)"><rect width="27.03" height="1.006" fill="#ff8578"/></g><g transform="translate(10.136 10.833)"><rect width="26.823" height="1.006" fill="#ff8578"/></g><g transform="translate(39.298 10.833)"><rect width="7.466" height="1.006" fill="#fbd15b"/></g><g transform="translate(8.384 5.417)"><rect width="10.098" height="1.006" fill="#fbd15b"/></g><g transform="translate(23.845 5.417)"><rect width="10.098" height="1.006" fill="#606bf1"/></g><g transform="translate(40.437 5.417)"><rect width="10.098" height="1.006" fill="#ff8578"/></g><g transform="translate(20.089)"><rect width="14.159" height="1.006" fill="#fbd15b"/></g><g transform="translate(0.589)"><rect width="14.159" height="1.006" fill="#606bf1"/></g></g></g><g transform="translate(7.19 15.124)"><g transform="translate(0)"><rect width="1.287" height="1.287" fill="#f9fbfc"/></g><g transform="translate(0 5.225)"><path d="M129.823,164.62h-1.288V162h1.288Zm0-6.562h-1.288v-2.624h1.288Zm0-6.563h-1.288V148.87h1.288Zm0-6.562h-1.288v-2.625h1.288Zm0-6.562h-1.288v-2.625h1.288Zm0-6.562h-1.288v-2.625h1.288Zm0-6.563h-1.288v-2.624h1.288Z" transform="translate(-128.535 -122.619)" fill="#f9fbfc"/></g><g transform="translate(0 51.164)"><rect width="1.287" height="1.287" fill="#f9fbfc"/></g></g></g><g transform="translate(129.528)"><rect width="69.555" height="42.874" fill="#ff8578"/><g transform="translate(16.006 8.429)"><path d="M268.27,99.311l-8.276,3.532,8.276,3.48-1.083,2.5-9.925-4.433v-3.068l9.925-4.46Z" transform="translate(-257.262 -90.495)" fill="#f9fbfc"/><path d="M282.685,90.153l-6.006,23.072-2.862-.67,6.033-23.046Z" transform="translate(-259.483 -89.509)" fill="#f9fbfc"/><path d="M298.941,101.322v3.068l-9.925,4.433-1.081-2.5,8.249-3.48-8.249-3.532,1.081-2.449Z" transform="translate(-261.376 -90.495)" fill="#f9fbfc"/></g></g><g transform="translate(196.694 83.059)"><rect width="39.617" height="28.224" fill="#606bf1"/><g transform="translate(11.09 4.232)"><path d="M334.637,182.449c-1.275,0-1.7.344-1.7,1.294v4.248a2.167,2.167,0,0,1-1.841,2.488,2.126,2.126,0,0,1,1.841,2.447v4.248c0,.951.445,1.294,1.7,1.294v1.862c-3.216,0-4.127-1.032-4.127-3.176v-4.025c0-1.153-.3-1.6-1.355-1.6v-2.124c1.052,0,1.355-.465,1.355-1.618v-4.025c0-2.165.911-3.176,4.127-3.176Z" transform="translate(-329.155 -180.588)" fill="#f1f2f2"/><path d="M347.089,183.764v4.025c0,1.153.3,1.618,1.376,1.618v2.124c-1.073,0-1.376.445-1.376,1.6v4.025c0,2.144-.91,3.176-4.127,3.176v-1.862c1.255,0,1.7-.344,1.7-1.294v-4.248a2.125,2.125,0,0,1,1.841-2.447,2.167,2.167,0,0,1-1.841-2.488v-4.248c0-.951-.425-1.294-1.7-1.294v-1.861C346.179,180.588,347.089,181.6,347.089,183.764Z" transform="translate(-331.007 -180.588)" fill="#f1f2f2"/></g></g><g transform="translate(0 6.149)"><rect width="44.445" height="29.727" fill="#fbd15b"/><g transform="translate(13.139 5.777)"><path d="M105.928,96.3a7.295,7.295,0,0,0,9.761,10.6l4.664,4.665a.505.505,0,0,0,.713,0l1.31-1.311a.5.5,0,0,0,0-.713l-4.664-4.665A7.3,7.3,0,0,0,105.928,96.3Zm9.363,8.182a5.158,5.158,0,1,1,0-7.294A5.164,5.164,0,0,1,115.29,104.487Z" transform="translate(-104.357 -93.548)" fill="#f9fbfc"/></g></g><g transform="translate(0.131 52.362)"><g transform="translate(133.144)"><path d="M286.525,191.036c-.1.982-1.439,11.233-11.981,11.233-11.067,0-9.213-10.957-9.213-10.957l-.014-.068a11.032,11.032,0,0,1,3.472-.166v-5.188l12.561-7.027h.456v12.1A22.83,22.83,0,0,1,286.525,191.036Z" transform="translate(-246.069 -145.428)" fill="url(#a)"/><g transform="translate(7.614 5.72)"><path d="M278.638,151.411a14.354,14.354,0,0,1,3.775,7c1.207,5.322.071,10.952-1.473,16.081a8.081,8.081,0,0,1-1.106,2.5,8.9,8.9,0,0,1-1.377,1.391q-3.352,2.918-6.7,5.844c-1.511,1.323-10.814,2.424-12.589,1.155-6.842-4.891-8.158-19.065-6.789-26.31C254.653,147.051,270.1,142.754,278.638,151.411Z" transform="translate(-251.897 -146.854)" fill="url(#b)"/></g><path d="M278.337,169.588c.091-1.157,1.281-2.424,2.475-1.673,1.236.778.946,2.832.433,3.963-.4.887-1.746,1.857-2.656,1.033-.763-.692.423-1.507.461-2.138.036-.568-1.1-.975-.636-1.583" transform="translate(-247.825 -143.929)" fill="#f7b094"/><path d="M278.586,175.126a3.533,3.533,0,0,0,3.894-2.314,13.3,13.3,0,0,0,.477-3.72,8.915,8.915,0,0,1,.3,4.1c-.323,2.53-1.21,4.94-3.2,4.972S278.586,175.126,278.586,175.126Z" transform="translate(-247.855 -144.117)" fill="#f7b094"/><path d="M272.3,169.457a.438.438,0,0,1-.139.042c-.772.108-1.081-.826-1.195-1.41a26.029,26.029,0,0,1-.274-4.128,3.109,3.109,0,0,0-.125-1.1c-.435-.994-1.59-1.639-1.943-2.665-.41-1.193.748-2.384.288-3.689-.7-2-3.273-2.462-5.131-2.439a58.346,58.346,0,0,0-8.259.977c-2.9.45-6.618.879-9.178-.959-3.085-2.214-4.374-7.985-2.071-11.145A7.476,7.476,0,0,1,250,140.25c2.21-.05,4.077,1.267,6.105,1.93,1.615.528,3.177-1.017,4.686-1.319,1.9-.379,2.7,1.692,4.382,1.749,1.63.055,3.23-1.218,4.816-1.507,3.16-.578,2.974,2.6,5.095,3.682,1.319.674,3.021.266,4.354.951,1.013.52,1.155,1.435,1.376,2.471a24.37,24.37,0,0,0,.5,2.78,6.289,6.289,0,0,0,1.41,1.847,6.78,6.78,0,0,1,1.174,2.235,13.924,13.924,0,0,1,.584,5.678,38.587,38.587,0,0,1-2.375,9.339c-.692,1.952-.949,3.982-2.013,5.828-.286.5-1.555,1.946-2.027.866a1.582,1.582,0,0,1-.068-.746c.14-2.011,2.013-14.968-2.9-13.411a3.479,3.479,0,0,0-2.106,2.162,8.421,8.421,0,0,0-.433,3.585c.009.177.013.354.006.531A.6.6,0,0,1,272.3,169.457Z" transform="translate(-243.103 -140.248)" fill="#3d3d7a"/></g><path d="M269.291,190.777a16.9,16.9,0,0,0,9.264-5.306,10.172,10.172,0,0,1-9.6,7.823Z" transform="translate(-113.427 -146.314)" fill="#f7b094"/><g transform="translate(127.562 145.745)"><path d="M1.626,0H6.363A1.626,1.626,0,0,1,7.989,1.626V78.059a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V1.626A1.626,1.626,0,0,1,1.626,0Z" transform="translate(45.452 78.059) rotate(180)" fill="#0a2033"/><path d="M6.314,0H37.622a6.314,6.314,0,0,1,6.314,6.314v.353a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V6.314A6.314,6.314,0,0,1,6.314,0Z" transform="translate(63.426 7.762) rotate(180)" fill="#30475e"/><g transform="translate(0 63.018)"><path d="M1.626,0H6.363A1.626,1.626,0,0,1,7.989,1.626V19.17a0,0,0,0,1,0,0H0a0,0,0,0,1,0,0V1.626A1.626,1.626,0,0,1,1.626,0Z" transform="translate(45.452 25.123) rotate(180)" fill="#0a2033"/><path d="M236.657,404.241a4.676,4.676,0,1,0,4.676-4.676A4.676,4.676,0,0,0,236.657,404.241Z" transform="translate(-236.657 -383.793)" fill="#0a2033"/><path d="M321.617,404.241a4.676,4.676,0,1,0,4.676-4.676A4.675,4.675,0,0,0,321.617,404.241Z" transform="translate(-248.053 -383.793)" fill="#0a2033"/><rect width="16.072" height="14.045" transform="translate(49.494 14.045) rotate(180)" fill="#30475e"/><path d="M0,0H82.915a0,0,0,0,1,0,0V.858a5.751,5.751,0,0,1-5.751,5.751H5.751A5.751,5.751,0,0,1,0,.858V0A0,0,0,0,1,0,0Z" transform="translate(82.915 17.349) rotate(180)" fill="#fbd15b"/></g><rect width="2.771" height="52.427" transform="translate(38.687 9.338)" fill="#30475e"/></g><path d="M312.031,244.4a3.259,3.259,0,0,0-3.365,3.441c.549,9.516-1.469,32.73-10.332,40.651-10.837,9.684-41.553,8.364-41.553,8.364s-5.072,6.226,5.073,7.609,38.736.461,45.652-6c6.7-6.249,10.671-22.622,7.675-51.119a3.28,3.28,0,0,0-3.151-2.951Z" transform="translate(-111.624 -154.218)" fill="#fbd15b"/><g transform="translate(79.031 54.297)"><path d="M252.589,379.9l3.652,8.328-11.192,4.911-3.666-8.356Z" transform="translate(-188.759 -226.69)" fill="url(#c)"/><path d="M301.273,273c-.692,1.384-50.419.028-55.115.457,0,0-30.435,14.526-34.806,28.124a14.857,14.857,0,0,0-.429,5.34c1.217,19.147,23.02,64.993,23.02,64.993l3.348-1.466,11.205-4.883,1.591-.692s-17.293-51.532-13.834-54.3,42.65-9.684,56.249-13.364S301.273,273,301.273,273Z" transform="translate(-184.667 -212.351)" fill="#56548c"/><path d="M203.155,382.373l.874,12.512H191.814l-.874-12.539Z" transform="translate(-181.993 -227.018)" fill="url(#d)"/><path d="M241.85,273.031l-.443.194s-50.951,7.152-59.251,21.443c-5.4,9.3,5.048,77.226,5.048,77.226l18.026.042s-2.7-56.585-1.8-59.74c1.01-3.6,2.739-4.9,3.431-5.271a14.857,14.857,0,0,1,.429-5.34c4.371-13.6,34.806-28.124,34.806-28.124A.868.868,0,0,1,241.85,273.031Z" transform="translate(-180.608 -212.355)" fill="#3d3d7a"/><path d="M300.434,218.661l-3.418.47a167.021,167.021,0,0,1-2.863,18.427l-42.568,16.158L251,256.8l-.526-.042c.014.636.014,1.259.042,1.84a30.622,30.622,0,0,0,.706,7.138.875.875,0,0,0,.249.429c1.156.943,16.212-.885,32.487,5.091,14.116,5.183,22.028,10.845,22.394,10.085C310.793,272.121,302.717,235.926,300.434,218.661Z" transform="translate(-189.98 -205.063)" fill="#ff8578"/><path d="M295.993,239.845c-1.134,5.451-4.067,16.3-9.822,17.749-7.511,1.909-22.037,1.812-35.65,1.079-.028-.581-.028-1.2-.042-1.84l.526.042.581-3.085,42.568-16.158a167.021,167.021,0,0,0,2.863-18.427,1.2,1.2,0,0,0,.014.152C297.114,220.782,297.695,231.794,295.993,239.845Z" transform="translate(-189.98 -205.135)" fill="#f4675d"/><path d="M252.038,202.956c-.761,1.383-1.854,16.186-2.449,30.878l-8.7-8.121C244.581,218.561,252.038,202.956,252.038,202.956Z" transform="translate(-188.693 -202.956)" fill="#f4675d"/><path d="M242.363,246.686c-7.346,1.273-14.1,2.366-14.1,2.366l-.014.359c-8.176-6.53-20.613-25.025-20.613-25.025s3.721-4.469,4.648-5.645c.139-.18,20.42,13.779,21.457,13.654.263-.027,1.3-1.854,2.684-4.565l8.7,8.121c-.139,3.5-.249,6.986-.318,10.293C244,246.395,243.179,246.533,242.363,246.686Z" transform="translate(-184.233 -205.073)" fill="#f7b094"/><path d="M228,253.346l-.18,6.059H205.69s2.2-8.647,8.189-8.647,8.3,3.168,8.3,3.168Z" transform="translate(-183.972 -209.368)" fill="url(#e)"/><path d="M209.941,217.616l.165.139c-.926,1.176-4.648,5.645-4.648,5.645l-2.31-1.633a5.743,5.743,0,0,1-5.506-.069c-3.085-1.674-1.176-5.671-1.017-6.144-.6.169-4.295,3.156-4.876,2.838s-1.01-1.37,3.126-5.451,9.739,1.162,9.906,1.162Z" transform="translate(-182.053 -204.087)" fill="#f7b094"/><path d="M199.129,222.148a1.688,1.688,0,0,0,.694,2.4,15.8,15.8,0,0,0,6,1.122c1.325-.128,2.379-1.982,2.379-1.982l-4.013-3.766h-3.209Z" transform="translate(-183.058 -205.231)" fill="#f7b094"/><path d="M198.757,222.956c.238-.82,2.517-.383,3.929-.35,1.041.025,1.611-.248,1.933-1.04s-1.19-2.056-1.487-1.66.545.954.57,1.48-1.487.7-2.65.7a28.345,28.345,0,0,0-3.173.3Z" transform="translate(-182.924 -205.219)" fill="#e8987c"/></g><path d="M289.412,248.372l-21.678-7.678c-1.577,2.947-3.265,5.16-4.939,5.6-2.532.664-8.646,1.812-14.816,2.892-.817.152-1.633.29-2.449.442-7.346,1.273-14.1,2.366-14.1,2.366l-.014.359v.236l-.166,5.464v1.386a153.2,153.2,0,0,1,16.643.274c12.99.927,29.743,1.481,35.291-1.134,2.6-1.231,4.676-5.271,6.308-10.251Z" transform="translate(-108.37 -153.721)" fill="url(#f)"/><path d="M273.268,240.694l21.678,7.678-.982,2.9-21.456-9.2Z" transform="translate(-113.903 -153.721)" fill="#f7b094"/><path d="M270.325,234.286c-1.577,2.947-3.265,5.16-4.939,5.6-2.532.664-8.646,1.812-14.816,2.892.069-3.307.179-6.793.318-10.293.595-14.692,1.688-29.495,2.449-30.878,1.245-2.311,4.648-7.332,10.016-8.48l.014.068s-1.854,10.957,9.213,10.957c10.542,0,11.884-10.251,11.981-11.233,5.423.609,13.737,3.32,14.885,14.221.236,2.159.567,4.925,1,8.066l-3.417.47s-1.384,15.356-4.939,26.244l-.083.042Z" transform="translate(-110.961 -147.314)" fill="#ff8578"/><path d="M273.126,235.14l8.656-20.955-6.377,21.8Z" transform="translate(-113.986 -150.166)" fill="#6d4b35" style="mix-blend-mode:screen;isolation:isolate"/><path d="M284.887,192.922h-4.718s-6.713,7.371-11.029,7.371-1.989-7.33-1.989-7.33h-2.1a11.9,11.9,0,0,0-11.823,10.551c-.078.688-.15,1.341-.211,1.932-.462,4.459,17.6,7.7,17.6,7.7l9.3-.52,5.648-11.02,1.126-4.513Z" transform="translate(-111.287 -147.314)" fill="#ff8578"/><g transform="translate(116.659 215.1)"><path d="M224.081,405.261c.421,2.255,1.983,3.857,4.237,3.436l30.046-5.6a1.4,1.4,0,0,0,1.12-1.633l-.5-2.666s-31.6,5.53-34.92,6.451Z" transform="translate(-224.065 -390.027)" fill="#f1f2f2"/><path d="M233.02,396.338c.313-.272.615-.541.928-.813.6-.539,1.2-1.065,1.766-1.573.3-.269.6-.539.882-.792.549-.492,1.066-.952,1.529-1.365.333-.3.644-.585.924-.838l1.227-1.1a.68.68,0,0,1,.894-.018c1.235,1.038,5.227,3.412,12.458-1.062a.694.694,0,0,1,.984.256l4.373,8.4s-31.6,5.53-34.92,6.451C225.157,403.206,229.191,399.715,233.02,396.338Z" transform="translate(-224.065 -388.669)" fill="#3d3d7a"/></g><g transform="translate(69.342 218.641)"><path d="M169.43,405.8c.056,2.292,1.343,4.122,3.635,4.067l30.556-.746a1.4,1.4,0,0,0,1.365-1.434l-.067-2.71s-32.078.424-35.5.8Z" transform="translate(-169.418 -394.397)" fill="#f1f2f2"/><path d="M179.46,398.113c.352-.218.694-.436,1.046-.655.681-.436,1.35-.86,1.995-1.271.34-.218.681-.436,1-.642.62-.4,1.2-.771,1.727-1.1l1.045-.68,1.387-.89a.678.678,0,0,1,.885.124c1.055,1.222,4.618,4.2,12.469.937a.694.694,0,0,1,.931.41l2.978,8.994s-32.078.424-35.5.8C170.6,403.64,175.142,400.837,179.46,398.113Z" transform="translate(-169.418 -392.759)" fill="#3d3d7a"/></g><g transform="translate(31.3 62.199)"><path d="M141.8,255.6l-16.318-43.522H171.94L188.258,255.6Z" transform="translate(-125.483 -212.083)" fill="#abb5ba"/><path d="M198.327,255.6h-2.872l-16.318-43.522,2.608.116,15.093,39.394Z" transform="translate(-132.679 -212.083)" fill="#8495a0"/><path d="M128.369,256.914H186.86" transform="translate(-125.87 -218.096)" fill="none"/><g transform="translate(64.119 40.778)"><path d="M200.162,261.921h30.7l-.083-2.744H199.534" transform="translate(-199.534 -259.177)" fill="#8495a0"/></g></g><g transform="translate(0 105.721)"><g transform="translate(219.438 8.3)"><rect width="7.391" height="121.424" fill="#8495a0"/></g><g transform="translate(179.319 8.3)"><rect width="7.391" height="121.424" fill="#abb5ba"/></g><g transform="translate(50.791 8.3)"><rect width="7.391" height="121.424" fill="#8495a0"/></g><g transform="translate(10.673 8.3)"><rect width="7.391" height="121.424" fill="#abb5ba"/></g><rect width="237.818" height="8.762" rx="4.149" fill="#141b56"/></g><path d="M134.441,217.366l.795,2.119h9.255l-.795-2.119Z" transform="translate(-95.384 -150.592)" fill="#8495a0"/><path d="M240.785,229.238l-.664,1.283,9.366,9.679v-2.842Z" transform="translate(-109.559 -152.185)" fill="#e8987c"/></g></g><g transform="translate(29.745 255.78)"><rect width="373.983" height="8.539" transform="translate(0 110.241)" fill="#a5b7c6"/><g transform="translate(262.85)"><g transform="translate(32.336 13.618)"><path d="M390.162,332.059c-1.486,7.208-1.038,19.487-10.575,21.432-2.567.524-5.048-1.256-6.541-3.3-1.953-2.669-2.421-8.32-2.387-11.552.028-2.561.9-5.418,1.182-8,.3-2.791.551-5.587.876-8.376,1.173-10.051,6.424-18.475,15.305-23.492.688,3.3,2.255,6.091,3.636,9.26a32.786,32.786,0,0,1,1.608,4.481c.727,2.68-.152,5.388-.8,7.956C391.5,324.264,390.953,328.219,390.162,332.059Z" transform="translate(-370.657 -298.773)" fill="url(#g)"/><path d="M378.24,354.29c.068-.6.143-1.209.223-1.813a7.757,7.757,0,0,0,3.283-1.146,17.434,17.434,0,0,0,4.307-3.411c.07-.07-.048-.184-.122-.122a19.4,19.4,0,0,1-4.194,2.87,11.8,11.8,0,0,1-3.186,1.186q.151-1.082.331-2.16a9.073,9.073,0,0,0,3.278-1.52,37.467,37.467,0,0,0,4.7-3.587.017.017,0,0,0-.022-.027c-2.328,1.691-4.922,4.1-7.868,4.625.156-.9.321-1.8.51-2.691.01-.047.02-.093.03-.139,3.409-.862,5.715-3.536,8.325-5.7.042-.036,0-.107-.048-.073-2.571,2-4.986,4.275-8.163,5.243.275-1.276.562-2.548.85-3.82a18.2,18.2,0,0,0,7.7-5.462c.036-.042-.023-.1-.062-.061a23.5,23.5,0,0,1-7.518,4.978c.286-1.259.568-2.517.836-3.779a28.323,28.323,0,0,0,3.97-1.919,10.867,10.867,0,0,0,3.089-3.133.061.061,0,0,0-.1-.065c-1.727,2.481-4.328,3.253-6.843,4.558.245-1.176.474-2.356.678-3.542,2.8-1.543,5.847-2.973,7.435-5.93.016-.029-.025-.058-.043-.029-1.682,2.716-4.623,3.887-7.292,5.366.035-.211.073-.422.106-.634.172-1.1.332-2.2.49-3.3a26.9,26.9,0,0,0,4.594-2.5,8.745,8.745,0,0,0,2.8-2.385.061.061,0,0,0-.1-.07c-1.5,2.153-4.794,3.4-7.226,4.476q.236-1.669.473-3.339c2.7-1.632,5.871-2.535,7.531-5.477a.079.079,0,0,0-.133-.083c-1.719,2.687-4.746,3.515-7.324,5.04q.149-1.034.3-2.067a16.293,16.293,0,0,0,7.837-6.143.131.131,0,0,0-.22-.14,16.352,16.352,0,0,1-7.531,5.736c.172-1.116.352-2.23.551-3.343a.292.292,0,0,0,.043-.1,16.7,16.7,0,0,0,3.73-2,10.2,10.2,0,0,0,3.508-3.2c.094-.152-.155-.294-.242-.137-1.294,2.309-4.365,3.857-6.907,4.739.074-.405.148-.811.229-1.217.12-.606.243-1.211.367-1.817,1.041-.719,2.362-1.062,3.407-1.822a19.282,19.282,0,0,0,2.48-2.26c.078-.081-.037-.2-.119-.126a16.408,16.408,0,0,1-3.424,2.63c-.718.384-1.536.6-2.231,1.024.2-.99.406-1.979.6-2.971l.036-.008.037.008a.186.186,0,0,0,.171-.053,7.735,7.735,0,0,0,3.449-2.614.026.026,0,0,0-.036-.037,9.444,9.444,0,0,1-3.469,2.34.1.1,0,0,0-.014-.006l-.1-.016q.234-1.211.437-2.424a.185.185,0,0,0,.167-.208,14.9,14.9,0,0,0,2.072-1.774.052.052,0,0,0-.071-.076,12.091,12.091,0,0,1-2.069,1.461,50.01,50.01,0,0,0,.563-5.053c.009-.158-.229-.159-.243,0-.148,1.635-.386,3.254-.667,4.865a15.032,15.032,0,0,1-2.127-3.251c-.025-.061-.121-.036-.1.029a9.276,9.276,0,0,0,1.035,2.012,12.237,12.237,0,0,0,1.1,1.768q-.236,1.3-.506,2.59a4.775,4.775,0,0,1-2.6-1.02,4.726,4.726,0,0,1-1.6-3.253c-.011-.156-.257-.155-.243,0a5.382,5.382,0,0,0,1.385,3.168,5.064,5.064,0,0,0,2.988,1.457q-.312,1.492-.642,2.982a23.2,23.2,0,0,1-2.44-1.812,33.218,33.218,0,0,1-3.082-3.327c-.109-.113-.287.05-.179.165a35.041,35.041,0,0,0,2.87,3.233,16.536,16.536,0,0,0,2.753,2.1l-.035.158c-.011.01-.022.017-.033.028a.16.16,0,0,0-.023.223c-.21.951-.418,1.9-.619,2.856-.013.06-.023.12-.035.18-2.218-1.016-5.533-2.78-5.826-5.354a.174.174,0,1,0-.338.074c.656,2.912,3.857,4.2,6.062,5.79q-.326,1.617-.6,3.244a13.128,13.128,0,0,1-4-2.059c-1.094-.87-2.642-1.92-3.134-3.268-.016-.042-.078-.015-.064.028.456,1.339,1.845,2.26,2.855,3.174a10.683,10.683,0,0,0,4.243,2.609.292.292,0,0,0,0,.145q-.169,1.031-.327,2.064c-2.694-.207-6.463-2.253-7.84-4.29-.007-.01-.02,0-.014.01a8.84,8.84,0,0,0,3.959,3.252A10.926,10.926,0,0,0,382.85,325c-.193,1.275-.38,2.551-.571,3.825-1.169-.436-2.407-.644-3.563-1.107a8.823,8.823,0,0,1-4.045-3.444c-.061-.092-.214-.015-.153.081,1.865,2.965,4.548,3.834,7.691,4.945q-.236,1.569-.494,3.135c-2.572-.494-5.9-2.063-7.2-4.3-.1-.164-.357-.028-.261.138a8.34,8.34,0,0,0,3.683,3.067,11.322,11.322,0,0,0,3.714,1.49c-.006.034-.011.068-.017.1-.254,1.5-.551,3-.872,4.487-2.873-.722-4.73-2.851-6.819-4.844-.145-.139-.372.074-.228.215,2.127,2.095,4,4.291,6.952,5.065q-.4,1.818-.84,3.627a13.275,13.275,0,0,1-6.452-4.416c-.113-.141-.309.05-.2.191a14.29,14.29,0,0,0,6.543,4.693q-.477,1.94-.978,3.876a9.528,9.528,0,0,1-6-3.946c-.036-.047-.117.014-.082.062,1.375,1.914,3.457,4.124,5.96,4.355q-.16.618-.319,1.236c-.064.249-.132.5-.2.754a14.262,14.262,0,0,1-5.219-2.516c-.044-.035-.094.032-.053.069a13.609,13.609,0,0,0,5.169,2.826c-.211.775-.434,1.572-.654,2.384a10.6,10.6,0,0,0-1.44-.613,10.358,10.358,0,0,1-2.374-1.374c-.075-.045-.16.061-.085.111a10.776,10.776,0,0,0,2.05,1.385c.609.229,1.183.521,1.771.783-.864,3.221-1.628,6.644-1.213,9.736.092.686,1.242.868,1.419.138A55,55,0,0,0,378.24,354.29Z" transform="translate(-370.924 -299.33)" fill="#747ded"/></g><g transform="translate(0 15.567)"><path d="M354.973,330.3c1.7,7.162,7.284,18.105-.54,23.893-2.108,1.559-5.107.992-7.322-.227-2.9-1.6-5.706-6.521-7.039-9.465-1.057-2.334-1.47-5.292-2.306-7.753-.9-2.658-1.859-5.3-2.741-7.963-3.179-9.608-1.973-19.46,3.96-27.757,2.016,2.7,4.615,4.571,7.2,6.86a32.759,32.759,0,0,1,3.349,3.383c1.79,2.124,2.136,4.95,2.629,7.552C352.9,322.666,354.07,326.483,354.973,330.3Z" transform="translate(-333.312 -301.024)" fill="url(#h)"/><path d="M353.316,355.681q-.291-.866-.564-1.739a7.753,7.753,0,0,0,2.492-2.424,17.456,17.456,0,0,0,2.465-4.91c.033-.093-.121-.146-.162-.058a19.43,19.43,0,0,1-2.592,4.373,11.789,11.789,0,0,1-2.387,2.419q-.321-1.044-.613-2.1a9.048,9.048,0,0,0,2.33-2.761,37.549,37.549,0,0,0,2.746-5.235.018.018,0,0,0-.031-.016c-1.4,2.516-2.734,5.794-5.181,7.515-.239-.882-.468-1.766-.674-2.655-.011-.047-.021-.093-.031-.139,2.726-2.221,3.689-5.618,5.142-8.679.023-.05-.048-.1-.074-.046-1.488,2.895-2.716,5.98-5.188,8.2q-.432-1.911-.842-3.823a18.178,18.178,0,0,0,4.679-8.2.044.044,0,0,0-.082-.029,23.487,23.487,0,0,1-4.715,7.685c-.273-1.261-.547-2.521-.837-3.779a28.1,28.1,0,0,0,2.79-3.414,10.85,10.85,0,0,0,1.479-4.144.061.061,0,0,0-.12-.016c-.518,2.977-2.551,4.775-4.28,7.02q-.41-1.755-.88-3.5c1.889-2.582,4.046-5.163,4.238-8.514,0-.033-.048-.042-.052-.008-.378,3.173-2.55,5.474-4.346,7.942-.057-.206-.112-.413-.171-.619q-.464-1.605-.951-3.2a26.9,26.9,0,0,0,3.111-4.2,8.757,8.757,0,0,0,1.532-3.344.061.061,0,0,0-.12-.022c-.455,2.587-2.913,5.1-4.662,7.108q-.49-1.614-.981-3.226c1.757-2.619,4.253-4.777,4.516-8.144a.079.079,0,0,0-.156-.019c-.424,3.161-2.818,5.19-4.512,7.661q-.3-1-.6-2a16.287,16.287,0,0,0,4.512-8.877.131.131,0,0,0-.259-.035,16.341,16.341,0,0,1-4.406,8.379c-.314-1.084-.622-2.171-.911-3.263a.32.32,0,0,0,0-.107,16.7,16.7,0,0,0,2.535-3.391,10.2,10.2,0,0,0,1.831-4.378.14.14,0,0,0-.278-.023c-.2,2.64-2.329,5.339-4.262,7.212-.1-.4-.208-.8-.307-1.2q-.22-.9-.433-1.8c.64-1.091,1.693-1.96,2.32-3.089a19.2,19.2,0,0,0,1.294-3.1.087.087,0,0,0-.161-.064,16.348,16.348,0,0,1-1.994,3.828c-.488.652-1.138,1.2-1.59,1.871-.234-.983-.468-1.966-.71-2.946.01-.007.019-.016.029-.023l.037-.009a.184.184,0,0,0,.132-.12,7.726,7.726,0,0,0,2.024-3.825.026.026,0,0,0-.049-.018,9.45,9.45,0,0,1-2.157,3.586.09.09,0,0,1-.016,0l-.1.029q-.3-1.195-.627-2.382a.185.185,0,0,0,.064-.259,14.794,14.794,0,0,0,1.129-2.482.052.052,0,0,0-.1-.04,12.088,12.088,0,0,1-1.26,2.2,50.079,50.079,0,0,0-1.623-4.819c-.058-.146-.274-.048-.223.1.556,1.545,1.024,3.113,1.449,4.692a15.039,15.039,0,0,1-3.3-2.049c-.048-.045-.125.018-.078.068a9.308,9.308,0,0,0,1.787,1.388,12.348,12.348,0,0,0,1.74,1.14c.223.85.433,1.7.636,2.56a4.776,4.776,0,0,1-2.791.175,4.724,4.724,0,0,1-2.824-2.275c-.076-.136-.3-.031-.22.1a5.389,5.389,0,0,0,2.592,2.289,5.068,5.068,0,0,0,3.324.06q.347,1.485.677,2.973a23.266,23.266,0,0,1-2.977-.612,33.371,33.371,0,0,1-4.2-1.715.122.122,0,0,0-.094.224,34.793,34.793,0,0,0,3.967,1.72,16.482,16.482,0,0,0,3.382.741l.035.157c-.005.014-.013.026-.018.04a.16.16,0,0,0,.074.21c.211.952.423,1.9.644,2.851.013.059.029.118.043.177-2.439.016-6.189-.184-7.542-2.393-.129-.21-.431.005-.274.209,1.823,2.363,5.271,2.182,7.939,2.691q.388,1.6.825,3.193a13.132,13.132,0,0,1-4.493-.179c-1.359-.327-3.2-.626-4.22-1.64-.032-.031-.077.02-.047.052.979,1.023,2.627,1.271,3.928,1.673a10.673,10.673,0,0,0,4.947.575.3.3,0,0,0,.057.133q.282,1.005.574,2.009c-2.529.95-6.809.686-8.918-.58-.01-.006-.017.009-.008.015a8.838,8.838,0,0,0,4.961,1.278,10.946,10.946,0,0,0,4.066-.364c.363,1.236.732,2.472,1.1,3.708-1.243.1-2.454.432-3.7.5a8.826,8.826,0,0,1-5.121-1.416c-.094-.057-.2.077-.1.139,2.942,1.9,5.742,1.558,9.06,1.237q.448,1.522.876,3.05c-2.54.639-6.219.62-8.342-.856-.156-.108-.335.125-.178.235A8.343,8.343,0,0,0,343.355,335a11.314,11.314,0,0,0,4-.216c.009.033.019.067.028.1.4,1.469.765,2.949,1.1,4.435-2.909.558-5.492-.588-8.226-1.513-.19-.064-.307.223-.116.291,2.812,1,5.434,2.2,8.44,1.657q.407,1.817.77,3.643a13.282,13.282,0,0,1-7.714-1.28c-.161-.081-.259.176-.1.258a14.274,14.274,0,0,0,7.911,1.494c.259,1.307.506,2.618.749,3.927a9.527,9.527,0,0,1-7.1-1.045c-.052-.027-.1.062-.048.092,2.055,1.154,4.875,2.28,7.242,1.433q.117.627.232,1.255c.047.252.093.509.138.768a14.278,14.278,0,0,1-5.794-.078c-.055-.014-.071.068-.018.085a13.6,13.6,0,0,0,5.879.38c.135.792.269,1.608.413,2.437a10.429,10.429,0,0,0-1.564.052,10.389,10.389,0,0,1-2.733-.243c-.087-.01-.12.123-.029.136a10.742,10.742,0,0,0,2.442.39c.649-.049,1.293-.026,1.936-.038.576,3.285,1.327,6.711,3.009,9.339.372.583,1.492.261,1.346-.474A55.1,55.1,0,0,0,353.316,355.681Z" transform="translate(-333.557 -301.613)" fill="#747ded"/></g><g transform="translate(13.914)"><path d="M373.657,322.334c.4,9.1,4.663,23.7-6.19,28.921-2.922,1.407-6.438.023-8.847-1.972-3.154-2.611-5.427-9.248-6.366-13.136-.745-3.082-.563-6.774-1.009-9.958-.481-3.441-1.033-6.872-1.488-10.317-1.639-12.415,2.107-24.114,11.242-32.827,1.825,3.75,4.552,6.626,7.169,10.008a40.577,40.577,0,0,1,3.289,4.889c1.684,3,1.451,6.512,1.447,9.789C372.9,312.577,373.442,317.488,373.657,322.334Z" transform="translate(-349.382 -283.045)" fill="url(#i)"/><path d="M365.93,352.765q-.153-1.118-.283-2.243a9.606,9.606,0,0,0,3.59-2.37,21.619,21.619,0,0,0,4.135-5.4c.062-.106-.113-.206-.184-.109a24.033,24.033,0,0,1-4.162,4.716,14.632,14.632,0,0,1-3.463,2.39q-.148-1.346-.258-2.693a11.2,11.2,0,0,0,3.471-2.818,46.52,46.52,0,0,0,4.55-5.728.022.022,0,0,0-.034-.027c-2.282,2.735-4.664,6.41-8.039,7.936-.087-1.127-.16-2.256-.2-3.384,0-.059,0-.117-.006-.176,3.829-2.069,5.786-5.976,8.262-9.361.04-.055-.036-.127-.08-.073-2.479,3.175-4.686,6.641-8.2,8.766-.056-1.615-.1-3.229-.138-4.841a22.51,22.51,0,0,0,7.587-8.89.054.054,0,0,0-.093-.054,29.062,29.062,0,0,1-7.511,8.252c-.039-1.6-.081-3.192-.143-4.787a34.739,34.739,0,0,0,4.182-3.505,13.443,13.443,0,0,0,2.757-4.7.075.075,0,0,0-.143-.048c-1.318,3.5-4.206,5.216-6.827,7.544-.063-1.486-.146-2.971-.261-4.456,2.895-2.7,6.115-5.34,7.123-9.37.01-.04-.048-.062-.061-.022-1.195,3.769-4.368,6.065-7.123,8.649-.022-.264-.039-.528-.064-.793-.127-1.372-.268-2.742-.414-4.112a33.2,33.2,0,0,0,4.755-4.391,10.848,10.848,0,0,0,2.637-3.71.076.076,0,0,0-.141-.054c-1.151,3.039-4.722,5.531-7.312,7.561q-.222-2.074-.445-4.149c2.741-2.776,6.276-4.821,7.375-8.855a.1.1,0,0,0-.184-.058c-1.249,3.744-4.629,5.657-7.26,8.267q-.135-1.286-.262-2.573c3.17-2.167,6.495-6.121,7.54-9.746a.162.162,0,0,0-.307-.1,20.232,20.232,0,0,1-7.3,9.166c-.132-1.391-.254-2.783-.353-4.177a.382.382,0,0,0,.023-.132,20.71,20.71,0,0,0,3.868-3.535,12.624,12.624,0,0,0,3.239-4.9.172.172,0,0,0-.332-.091c-.852,3.163-4.068,5.951-6.851,7.78-.033-.509-.068-1.018-.094-1.528q-.06-1.146-.11-2.291c1.031-1.178,2.512-1.991,3.535-3.219a23.862,23.862,0,0,0,2.291-3.464.108.108,0,0,0-.182-.115,20.274,20.274,0,0,1-3.309,4.193c-.745.678-1.661,1.19-2.367,1.906-.055-1.249-.113-2.5-.181-3.746l.042-.021.048,0a.226.226,0,0,0,.189-.115c1.59-.857,2.52-2.648,3.346-4.182a.032.032,0,0,0-.055-.034,11.694,11.694,0,0,1-3.452,3.86l-.019,0-.127.012q-.087-1.523-.21-3.042a.229.229,0,0,0,.139-.3,18.447,18.447,0,0,0,1.947-2.757.065.065,0,0,0-.108-.07,15.009,15.009,0,0,1-2.04,2.38,62.011,62.011,0,0,0-.857-6.234c-.037-.191-.323-.121-.294.069.319,2.006.525,4.021.675,6.039a18.613,18.613,0,0,1-3.537-3.257c-.048-.065-.157-.006-.112.066a11.51,11.51,0,0,0,1.851,2.1,15.309,15.309,0,0,0,1.851,1.79c.074,1.085.132,2.172.179,3.26a5.91,5.91,0,0,1-3.432-.434,5.845,5.845,0,0,1-2.906-3.419c-.061-.183-.357-.107-.292.075a6.662,6.662,0,0,0,2.622,3.382,6.27,6.27,0,0,0,4.027.842q.078,1.885.134,3.772a28.587,28.587,0,0,1-3.476-1.434,41.048,41.048,0,0,1-4.708-3.057c-.165-.1-.33.147-.166.252a43.443,43.443,0,0,0,4.425,3.009,20.388,20.388,0,0,0,3.94,1.683l.007.2c-.01.016-.022.029-.031.044a.2.2,0,0,0,.041.274c.037,1.2.074,2.41.123,3.614,0,.075.009.151.012.226-2.969-.546-7.482-1.657-8.615-4.656a.214.214,0,1,0-.382.19c1.67,3.3,5.9,3.873,9.029,5.11q.1,2.039.263,4.073a16.234,16.234,0,0,1-5.42-1.259c-1.577-.712-3.752-1.5-4.751-2.97-.032-.046-.1.006-.068.052.953,1.469,2.9,2.153,4.387,2.944a13.227,13.227,0,0,0,5.881,1.844.364.364,0,0,0,.039.175q.109,1.287.233,2.575c-3.3.568-8.438-.743-10.707-2.771-.01-.01-.023.007-.014.016a10.935,10.935,0,0,0,5.736,2.7,13.522,13.522,0,0,0,5.027.5c.154,1.588.317,3.176.475,4.763-1.534-.168-3.083-.043-4.612-.248a10.912,10.912,0,0,1-5.9-2.906c-.1-.091-.262.048-.16.145,3.137,2.992,6.62,3.222,10.727,3.6q.192,1.955.358,3.912c-3.236.188-7.7-.687-9.942-2.973-.165-.167-.436.075-.272.245a10.323,10.323,0,0,0,5.349,2.562c1.408.315,3.392.926,4.907.662,0,.042.008.086.011.128.151,1.88.248,3.763.314,5.647-3.667.005-6.541-1.986-9.65-3.743-.216-.122-.424.2-.209.326,3.188,1.868,6.1,3.937,9.878,3.97q.074,2.3.092,4.606a16.432,16.432,0,0,1-9.081-3.342c-.177-.136-.355.154-.184.29a17.675,17.675,0,0,0,9.273,3.648c.011,1.649.009,3.3,0,4.947-3.133.3-5.934-1-8.395-2.915-.057-.045-.137.053-.081.1,2.23,1.88,5.4,3.9,8.473,3.419q0,.79-.009,1.579,0,.477-.01.965a17.677,17.677,0,0,1-7.027-1.436c-.063-.03-.1.067-.042.1a16.848,16.848,0,0,0,7.059,1.824c-.019.994-.045,2.017-.062,3.058a13.091,13.091,0,0,0-1.914-.3,12.83,12.83,0,0,1-3.265-.928c-.1-.032-.174.122-.068.158a13.351,13.351,0,0,0,2.878,1.04c.8.091,1.578.268,2.362.4-.061,4.127.061,8.466,1.5,12.049.318.795,1.753.664,1.746-.265C367.077,358.456,366.318,355.59,365.93,352.765Z" transform="translate(-349.708 -283.774)" fill="#747ded"/></g><path d="M379.727,366.66l-.4,7.4-.628,11.518-1.03,18.926H352.044l-1.03-18.926-.628-11.518-.4-7.4Z" transform="translate(-335.548 -294.26)" fill="#95a8b5"/><path d="M379.388,375.2l-.082,1.484-.082,1.535-.464,8.5H351.076l-.464-8.5-.082-1.535-.082-1.484Z" transform="translate(-335.611 -295.406)" fill="#a5b7c6"/><path d="M379.318,376.916l-.082,1.535h-28.61l-.082-1.535Z" transform="translate(-335.623 -295.636)" fill="#95a8b5"/></g></g></g></svg>