// lib/server/open-tests-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { ChapterPaper } from '@/types/paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  trialPapersUrl: `${API_BASE_URL}/test/sample/papers`,
  trialQuestionsUrl: `${API_BASE_URL}/test/sample/paper/new/`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerOpenTestsService {
  /**
   * Get trial papers
   */
  static async getTrialPapers(): Promise<ChapterPaper[]> {
    // Optional: redirect if not authenticated
    // if (!checkServerSideLoginStatus()) {
    //   redirect('/user/login')
    // }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.trialPapersUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching trial papers:', error)
      return []
    }
  }

  /**
   * Get trial questions for a specific paper
   */
  static async getTrialQuestions(paperId: string): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.trialQuestionsUrl}${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching trial questions for paper ${paperId}:`,
        error
      )
      throw error
    }
  }
}
