// lib/client/open-tests-service.client.ts

import axios from 'axios'
import { ChapterPaper, AnswerSubmission } from '@/types/paper-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  trialPapersUrl: `${API_BASE_URL}/test/sample/papers`,
  trialQuestionsUrl: `${API_BASE_URL}/test/sample/paper/new/`,
  submitUrl: `${API_BASE_URL}/test/open/submit`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class OpenTestsService {
  /**
   * Get trial papers
   */
  static async getTrialPapers(): Promise<ChapterPaper[]> {
    try {
      const response = await axios.get(
        ENDPOINTS.trialPapersUrl,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching trial papers:', error)
      return []
    }
  }

  /**
   * Get trial questions for a specific paper
   */
  static async getTrialQuestions(paperId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.trialQuestionsUrl}${paperId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching trial questions for paper ${paperId}:`,
        error
      )
      throw error
    }
  }

  /**
   * Submit answers
   */
  static async submitAnswers(submission: AnswerSubmission): Promise<any> {
    try {
      const response = await axios.post(
        ENDPOINTS.submitUrl,
        submission,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error submitting answers:', error)
      throw error
    }
  }
}
