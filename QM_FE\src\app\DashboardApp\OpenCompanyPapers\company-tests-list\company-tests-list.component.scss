.company-papers--wrap {
  display: grid;
  place-items: center;
  margin-bottom: 15em;

  .company-papers--heading {
    font-size: 26px;
    font-weight: 700;
    margin-top: 1em;
  }

  .company-papers--list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1em;
    width: 100%;
    max-width: 1200px;
    margin-top: 2em;

    .company-papers--card {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2em 1em;
      background-color: #fff;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.16);
      -webkit-transform: scale(1);
      transform: scale(1);
      transition: all 0.3s ease-out;

      &:hover {
        -webkit-transform: scale(1.05);
        transform: scale(1.05);
        box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.16);
        transition: all 0.2s ease-in;
      }

      img {
        width: 200px;
        height: 100px;
      }

      p {
        margin-top: 2em;
        margin-bottom: 0;
        font-weight: 700;
        text-align: center;
      }

      .locked-resource {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        width: 100%;
        height: 100%;
        background-color: rgba(146, 135, 135, 0.85);

        img {
          display: block;
          height: 30%;
          margin: auto;
          position: relative;
          top: 35%;
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .company-papers--wrap {
    margin-bottom: 5em;

    .company-papers--list {
      grid-template-columns: 1fr;
      width: 80%;
    }
  }
}