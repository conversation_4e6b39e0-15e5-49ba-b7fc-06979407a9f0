import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { OpenTestsService } from '../../Services/open-tests.service';

import { ChapterPaper } from '../../Models/Dashboard/Chapters/ChapterPaper';

declare let gtag: Function;

@Component({
  selector: 'app-free-paper',
  templateUrl: './free-paper.component.html',
  styleUrls: ['./free-paper.component.scss']
})
export class FreePaperComponent implements OnInit {

  public papers: ChapterPaper[];

  public showIndicator = false;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public openTestsService: OpenTestsService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    const that = this;

    this.showIndicator = true;
    this.openTestsService.getTrialPapers().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      that.papers = respText;
      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '5', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans() {
    this.router.navigate(['/plans']);
  }
}

