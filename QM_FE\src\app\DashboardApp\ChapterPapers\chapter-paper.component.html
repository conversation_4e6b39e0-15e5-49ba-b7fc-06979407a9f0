<div class="chapter-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Chapter wise papers covering all core aptitude concepts</p>
    </div>
  </div>
  <div class="container">
    <div class="header">
      <div *ngFor="let superGroup of superGroups, let i = index">
        <p [class.is-selected]="selectedGroup[i]" (click)="selectGroup(superGroup.super_group_id, i)">{{ superGroup.super_group_name }}</p>
      </div>
    </div>
    <div class="paper-group--list">
      <div class="paper-group" *ngFor="let paperGroups of paperGroups; let i = index">
        <div class="group-name--bar">
          <h5>{{paperGroups.group_name}}</h5>
          <div>
            <span>Number of Papers: {{ filterChapterPapers(paperGroups.group_id).length }}</span>
            <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseChapterGroup">
              <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                  d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                  transform="translate(-3.375 -3.375)" fill="#fff" />
              </svg>
            </button>
          </div>
        </div>
        <div class="paper-list" id="collapseChapterGroup" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div class="paper" *ngFor="let paper of filterChapterPapers(paperGroups.group_id)">
            <div class="paper-name--bar">
              <div class="locked-resource" (click)="takeToPlans()" *ngIf="paper.public == 0" title="Premium Feature">
                <img src="../../../assets/icons/lock.svg"/>
              </div>
              <h6>{{ paper.paper_name }}</h6>
              <p>{{ paper.time_lim == 0 ? "No Time Limit" : paper.time_lim / (1000 * 60) + " Minutes" }}</p>
              <p>Number of questions: {{ paper.no_of_ques }}</p>
              <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>