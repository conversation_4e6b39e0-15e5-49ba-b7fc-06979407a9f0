.apps-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 1200px;
  min-height: 50vh;
  padding: 1rem;
  margin: 2rem auto;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 5px 30px 0 rgba(125, 121, 125, 0.1);

  .apps-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2em;
    place-items: center;
    width: 80%;
    max-width: 1200px;
    margin-top: 2em;

    .app-card {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 30%;
      padding: 2em 1em 0.5em 1em;
      background-color: #fff;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.16);
      -webkit-transform: scale(1);
      transform: scale(1);
      transition: all 0.3s ease-out;

      &:hover {
        -webkit-transform: scale(1.05);
        transform: scale(1.05);
        box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.16);
        transition: all 0.2s ease-in;
      }

      img {
        width: 100px;
        height: 100px;
      }

      p {
        margin-top: 2em;
        margin-bottom: 0;
        font-weight: 700;
      }

      .options-bar {
        display: flex;
        align-items: flex-end;
        justify-content: flex-end;
        width: 100%;
        height: 40px;
        position: relative;
        bottom: 0;

        .extra-opts--wrap {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-evenly;
          height: 17px;
          width: 17px;
          border-radius: 50%;
        
          .extra-opts {
            display: block;
            content: "";
            height: 5px;
            width: 5px;
            background-color: rgb(187, 187, 187);
            border-radius: 50%;
            transform: scale(1);
            transition: all 0.3s ease-out;
          }

          &:hover {
            box-shadow: 1px 1px 6px rgba(0, 0, 0, 0.16);
            transition: all 0.2s ease-in;
          }

          &:hover .extra-opts {
            background-color: #777;
            transform: scale(1.05);
            transition: all 0.2s ease-in;
          }
        }

        .options-card {
          cursor: default;
          position: absolute;
          top: 100%;
          left: 103%;
          padding: 15px;
          color: #fff;
          background-color: #8E44AD;
          border-radius: 7px;
          box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.16);
          transform: scale(0);
          transform-origin: left top;
          transition: all 0.3s ease-in-out;

          p {
            font-size: 14px;
            font-weight: normal;
            margin: 0;
            margin-bottom: 0.5rem;
          }

          select {
            cursor: pointer;
            color: #fff;
            border: 1px solid #fff;
            background-color: #8E44AD;
          }
        }

        .is-open {
          transform: scale(1);
          transition: all 0.2s ease-out;
        }
      }

      .locked-resource {
        cursor: not-allowed;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        width: 100%;
        height: 100%;
        z-index: 10;
        background-color: rgba(146, 135, 135, 0.85);

        img {
          display: block;
          height: 100%;
          margin: auto;
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .apps-wrap {

    .apps-list {
      
      .app-card {
        width: 90%;

        .options-bar {
          justify-content: flex-start;

          .options-card {
            left: 10%;
          }
        }
      }
    }
  }
}