<div class="landing-2-wrap" [class.blur-bg]="blurOnEntry">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="logo-text">
    <a routerLink="/"><img src="../../../../assets/QM_Logo_No_Text.svg" />uantmasters</a>
  </div>

  <div class="mode-switch" (click)="switchTheme()">
    <label for="switch">
      <div class="toggle"></div>
      <div class="names">
        <p class="light">Light</p>
        <p class="dark">Dark</p>
      </div>
    </label>
  </div>

  <div class="sect-1">
    <div class="sect-left">
      <h2>Most Affordable 500+ Hours <span>Recorded</span> Placement Training Program</h2>
      <div class="extra-info">
        <div class="extra-info--2">
          <div class="sect-card">
            <div class="sect-body">
              <p class="sect-name">⏺ Daily doubt clearing sessions</p>
              <p class="sect-name">⏺ Revision sessions</p>
              <p class="sect-name">⏺ Learn at your own pace</p>
            </div>
          </div>
        </div>
        <div class="extra-info--2">
          <div class="sect-card card-spl">
            <div class="sect-body">
              <p class="sect-name">Included Infosys & TCS NQT Training</p>
            </div>
          </div>
        </div>
        <div class="google-info">
          <img src="../../../../assets/companyLogos/Google.svg" alt="Google Reviews">
          <p>4.9<span>/5</span> <small>★★★★★</small></p>
        </div>
        <div class="google-info">
          <img src="../../../../assets/companyLogos/google-play-png-logo.png" alt="Google Play Store Reviews">
          <p>4.9<span>/5</span> <small>★★★★★</small></p>
        </div>
        <div class="upcoming-info">
          <button class="qm-ring--btn" type="button" (click)="openRegForm(2)">Register Now</button>
        </div>
      </div>
    </div>
    <div class="sect-right">
      <img src="../../../../assets/icons/workshop/sect-1-main.svg" alt="Left Blob">
    </div>
  </div>

  <div class="shape-1-left">
    <img src="../../../../assets/icons/workshop/sect-1bg-left.svg" alt="Left Blob">
  </div>

  <div class="sect-3">
    <div class="sect-2" id="stats-area">
      <div class="sect-card">
        <img src="../../../../assets/companyLogos/tcs-logo.png" alt="TCS Logo">
        <p><span>1000+</span> Placements in TCS-NQT</p>
      </div>
      <div class="sect-card">
        <img src="../../../../assets/companyLogos/Accenture-logo.png" alt="Accenture Logo">
        <p><span>83%</span> Placements in ASE roles</p>
      </div>
      <div class="sect-card">
        <img src="../../../../assets/companyLogos/infosys-logo-JPEG.png" alt="Infosys Logo">
        <p><span>82%</span> Placements in SE Role</p>
      </div>
      <div class="sect-card">
        <img src="../../../../assets/companyLogos/wipro.png" alt="Wipro Logo">
        <p><span>78%</span> Placements</p>
      </div>
      <div class="sect-card">
        <img src="../../../../assets/companyLogos/capgemini.png" alt="Capegemini Logo">
        <p><span>76%</span> Placements</p>
      </div>
      <div class="blob-3">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="85.128" height="85.993"
          viewBox="0 0 85.128 85.993">
          <defs>
            <style>
              .a {
                fill: url(#a);
              }
            </style>
            <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#0a6fb0" />
              <stop offset="1" stop-color="#1b1749" />
            </linearGradient>
          </defs>
          <path class="a"
            d="M32.328,0C43.585.191,49.906,11.59,55.5,21.362,61.032,31.036,67.17,42,62,51.872,56.586,62.231,44.019,65.619,32.328,65.658,20.555,65.7,7.936,62.412,2.306,52.072c-5.463-10.034-.236-21.61,5.471-31.508S20.889-.189,32.328,0"
            transform="translate(85.128 59.982) rotate(156)" />
        </svg>
      </div>
    </div>
  </div>

  <div class="sect-3">
    <p class="sect-sub-heading--large">Hear it from them</p>
    <h5 class="sect-heading">Meet our champs!</h5>

    <div class="others-placed">
      <carousel [itemsPerSlide]="itemsPerSlide" [singleSlideOffset]="true" [noWrap]="false" [interval]="2000"
      [activeSlide]="0" [startFromIndex]="0">
        <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/tcs-logo.png" alt="TCS Logo">
              <p class="name">Akshay Pramod</p>
              <p class="position">System Engineer</p>
              <p class="package">11.5 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/AkshayPramod.jpeg" alt="">
          </div>
        </slide>
        <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @ Capgemini</p>
              <img src="../../../../assets/companyLogos/capgemini.png" alt="Capegemini Logo">
              <p class="name">Prashik Mayur</p>
              <p class="position">Senior Analyst</p>
              <p class="package">7.5 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/PrashikMayur.jpeg" alt="Yeetesh Image">
          </div>
        </slide>
        <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/Games247.jpg" alt="Games 24*7 Logo">
              <p class="name">Sahana B G</p>
              <p class="position">SDE 1</p>
              <p class="package">20 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/SahanaBG.jpeg" alt="">
          </div>
        </slide>
        <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/delllogo.png" alt="Dell Logo">
              <p class="name">Shivakumar B N</p>
              <p class="position">Software Engineer 1</p>
              <p class="package">9 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/ShivakumarBN.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/LamRsh.jpeg" alt="Lam Research Logo">
              <p class="name">Sinchale K L</p>
              <p class="position">Electrical Engineer-1</p>
              <p class="package">10.2 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/SinchanaKL.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/Zopsmart.png" alt="ZopSmart Logo">
              <p class="name">Shobit Gupta</p>
              <p class="position">Software Developer</p>
              <p class="package">10 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/ShobitGupta.jpeg" alt="">
          </div>
        </slide>
			  <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/FutureSense.jpg" alt="FutureSense Logo">
              <p class="name">S Aswin</p>
              <p class="position">Data Engineer</p>
              <p class="package">8 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/SAswin.jpeg" alt="">
          </div>
        </slide>
			  <slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/kpmg.png" alt="KPMG Logo">
              <p class="name">Prajwal P</p>
              <p class="position">Analyst</p>
              <p class="package">7 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/PrajwalP.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/lakeview.jpeg" alt="LatentView Analytics Logo">
              <p class="name">Manu C</p>
              <p class="position">Analyst</p>
              <p class="package">9 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/ManuC.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/sixt.jpg" alt="Sizt R&D Logo">
              <p class="name">Sumukh Raghavendra</p>
              <p class="position">Associate Software Engineer</p>
              <p class="package">19 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/SamukhR.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/saplabs.jpg" alt="SAP Labs Logo">
              <p class="name">Hemalatha J</p>
              <p class="position">Developer Associate</p>
              <p class="package">8.5 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/HemalathaJ.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/IBM_logoRR_pos_RGB.png" alt="IBM Logo">
              <p class="name">K Sasi Kiran</p>
              <p class="position">Associate Developer</p>
              <p class="package">7.5 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/SSasiKiran.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/PRNE_Hexaware_logo_Logo.jpg" alt="Hexaware Logo">
              <p class="name">Panchami</p>
              <p class="position">Software Engineer Trainee</p>
              <p class="package">9.87 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/Panchami.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/mercedes-benz.jpg" alt="Mercedez Benz Logo">
              <p class="name">Pratheeka K</p>
              <p class="position">Graduate Trainee Engineer</p>
              <p class="package">6.2 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/PrateekaK.jpeg" alt="">
          </div>
        </slide>
				<slide class="animated fade-in">
          <div class="placed-card">
            <div class="placed-left">
              <p class="first-text">Placed @</p>
              <img src="../../../../assets/companyLogos/oracle.png" alt="Oracle Logo">
              <p class="name">D Manjunatha</p>
              <p class="position">Cloud Analyst</p>
              <p class="package">8.5 LPA</p>
            </div>
            <img class="student-img" src="../../../../assets/testimonials/Manjunatha.jpeg" alt="">
          </div>
        </slide>
      </carousel>

      <div class="grid-4">
        <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
      </div>
    </div>
  </div>

  <div class="sect-3">
    <div class="sect-cards">
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name"><span>1 year access</span> to study materials & app</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Live technical & aptitude doubt clearing classes</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Live revision classes</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Company specific training</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Dedicated mentors</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">1-1 Mock HR interviews</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">1-1 Resume analysis & review</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Certifications & weekly exams</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Study materials</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Chapter-wise practice tests</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">Company specific papers</p>
        </div>
      </div>
      <div class="sect-card">
        <div class="sect-body">
          <p class="sect-name">No prior knowledge required, concepts will be taught from basics</p>
        </div>
      </div>
    </div>
    <div class="blob-4">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="223.987"
        height="212.741" viewBox="0 0 223.987 212.741">
        <defs>
          <style>
            .a {
              fill: url(#a);
            }

            .b {
              fill: none;
              stroke: #f49820;
              stroke-width: 3px;
            }
          </style>
          <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#0a6fb0" />
            <stop offset="1" stop-color="#1b1749" />
          </linearGradient>
        </defs>
        <g transform="translate(-1673.997 -1478.109)">
          <path class="a"
            d="M77.371.006c26.941.45,42.07,27.732,55.45,51.119,13.246,23.153,27.936,49.383,15.573,73.02-12.967,24.792-43.045,32.9-71.024,32.994-28.176.094-58.379-7.769-71.852-32.516C-7.556,100.608,4.955,72.9,18.613,49.215,32.291,25.494,49.993-.452,77.371.006"
            transform="translate(1897.984 1628.597) rotate(156)" />
          <path class="b"
            d="M34.081,0C45.948.2,52.613,12.218,58.507,22.52c5.835,10.2,12.306,21.753,6.86,32.165C59.655,65.605,46.406,69.177,34.081,69.218,21.67,69.26,8.366,65.8,2.431,54.9-3.328,44.317,2.183,32.113,8.2,21.679S22.022-.2,34.081,0"
            transform="translate(1704.477 1570.445) rotate(-115)" />
        </g>
      </svg>
    </div>
    <div class="blob-5">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="790.249"
        height="782.641" viewBox="0 0 790.249 782.641">
        <defs>
          <style>
            .a {
              fill: url(#a);
            }

            .b {
              fill: none;
              stroke: #f49820;
              stroke-width: 3px;
            }
          </style>
          <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#0a6fb0" />
            <stop offset="1" stop-color="#1b1749" />
          </linearGradient>
        </defs>
        <g transform="translate(-1634.056 -1235.331)">
          <path class="a"
            d="M77.371.006c26.941.45,42.07,27.732,55.45,51.119,13.246,23.153,27.936,49.383,15.573,73.02-12.967,24.792-43.045,32.9-71.024,32.994-28.176.094-58.379-7.769-71.852-32.516C-7.556,100.608,4.955,72.9,18.613,49.215,32.291,25.494,49.993-.452,77.371.006"
            transform="translate(1897.984 1628.597) rotate(156)" />
          <path class="b"
            d="M293.777.023c102.292,1.711,159.737,105.3,210.543,194.1,50.3,87.91,106.073,187.507,59.132,277.253C514.214,565.509,400.01,596.3,293.777,596.651c-106.985.358-221.663-29.5-272.82-123.461-49.645-91.185-2.142-196.377,49.718-286.321C122.606,96.8,189.824-1.716,293.777.023"
            transform="translate(1881.586 2016.036) rotate(-115)" />
        </g>
      </svg>
    </div>
  </div>

  <div class="sect-3">
    <h5 class="sect-heading">What we cover in your syllabus</h5>
    <p class="sect-sub-heading">We have carefully crafted the course which focuses on each and every aspect of your job
      preparation in holistic and analytical way which is divided in 5 parts</p>
    <div class="sect-modules">
      <div class="sect-module" [class.selected-module]="selectedModules[0]" (click)="openModuleDetails(0)">
        <p>Aptitude Module</p>
        <div class="arrow-down"></div>
      </div>
      <div class="sect-module" [class.selected-module]="selectedModules[1]" (click)="openModuleDetails(1)">
        <p>Technical Module</p>
        <div class="arrow-down"></div>
      </div>
      <div class="sect-module" [class.selected-module]="selectedModules[2]" (click)="openModuleDetails(2)">
        <p>Interview Training</p>
        <div class="arrow-down"></div>
      </div>
      <div class="sect-module" [class.selected-module]="selectedModules[3]" (click)="openModuleDetails(3)">
        <p>1:1 Resume Review</p>
        <div class="arrow-down"></div>
      </div>
      <div class="sect-module" [class.selected-module]="selectedModules[4]" (click)="openModuleDetails(4)">
        <p>1:1 Mock HR Interview</p>
        <div class="arrow-down"></div>
      </div>
    </div>
    <div class="sect--content">
      <div class="content-left">
        <ul>
          <li *ngFor="let point of modeulesContent[selectedContentIdx]" [innerHtml]="point"></li>
        </ul>
        <ul>
          <li *ngFor="let point of moreModsContent[selectedContentIdx]" [innerHtml]="point"></li>
        </ul>
        <ul>
          <li *ngFor="let point of evenMoreModsContent[selectedContentIdx]" [innerHtml]="point"></li>
        </ul>
      </div>
      <div class="content-right">
        <img src="{{ courseImages[selectedContentIdx] }}" alt="Aptitude Guy">
      </div>
    </div>
    <div class="blob-6">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="737.377"
        height="726.593" viewBox="0 0 737.377 726.593">
        <defs>
          <style>
            .a {
              fill: url(#a);
            }

            .b {
              fill: none;
              stroke: #f49820;
              stroke-width: 3px;
            }
          </style>
          <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#0a6fb0" />
            <stop offset="1" stop-color="#1b1749" />
          </linearGradient>
        </defs>
        <g transform="translate(2118.422 2287.355) rotate(-171)">
          <path class="a"
            d="M77.371.006c26.941.45,42.07,27.732,55.45,51.119,13.246,23.153,27.936,49.383,15.573,73.02-12.967,24.792-43.045,32.9-71.024,32.994-28.176.094-58.379-7.769-71.852-32.516C-7.556,100.608,4.955,72.9,18.613,49.215,32.291,25.494,49.993-.452,77.371.006"
            transform="translate(1897.984 1628.597) rotate(156)" />
          <path class="b"
            d="M293.777.023c102.292,1.711,159.737,105.3,210.543,194.1,50.3,87.91,106.073,187.507,59.132,277.253C514.214,565.509,400.01,596.3,293.777,596.651c-106.985.358-221.663-29.5-272.82-123.461-49.645-91.185-2.142-196.377,49.718-286.321C122.606,96.8,189.824-1.716,293.777.023"
            transform="translate(1881.586 2016.036) rotate(-115)" />
        </g>
      </svg>
    </div>
    <div class="blob-7">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="213.97" height="188.817"
        viewBox="0 0 213.97 188.817">
        <defs>
          <style>
            .a {
              fill: url(#a);
            }

            .b {
              fill: none;
              stroke: #f49820;
              stroke-width: 3px;
            }
          </style>
          <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#0a6fb0" />
            <stop offset="1" stop-color="#1b1749" />
          </linearGradient>
        </defs>
        <g transform="matrix(0.292, 0.956, -0.956, 0.292, 1026.218, -1979.179)">
          <path class="a"
            d="M38.546,0C51.968.227,59.505,13.819,66.172,25.47c6.6,11.535,13.918,24.6,7.759,36.378C67.47,74.2,52.485,78.24,38.546,78.286c-14.037.047-29.084-3.871-35.8-16.2C-3.764,50.123,2.469,36.32,9.273,24.519,16.087,12.7,24.907-.225,38.546,0"
            transform="translate(1787.854 1567.398) rotate(156)" />
          <path class="b"
            d="M63.2,0c22.008.368,34.367,22.654,45.3,41.759,10.821,18.913,22.821,40.341,12.722,59.65C110.631,121.666,86.06,128.29,63.2,128.367c-23.017.077-47.69-6.347-58.7-26.562-10.681-19.618-.461-42.25,10.7-61.6C26.378,20.826,40.84-.369,63.2,0"
            transform="translate(1684.341 1553.487) rotate(-115)" />
        </g>
      </svg>
    </div>
    <div class="grid-1">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
  </div>

  <div class="sect-5">
    <div class="sect-5--right">
      <div class="register-box">
        <div class="price-box">
          <p>All this at a exclusive price of only</p>
          <p>₹ 2499/-</p>
        </div>
        <button class="qm-ring--btn" type="button" (click)="openRegForm(2)">Register Now</button>
      </div>
      <div class="register-box" style="margin-top: 6rem;">
        <div class="price-box">
          <p><b>Group discount</b></p>
          <p>Join with 2 other friends ( 3 or more members ) @</p>
          <p>₹ 1999/-</p>
        </div>
        <button class="qm-ring--btn" type="button" (click)="sendToWhatsapp()">Know More</button>
      </div>
    </div>

    <div class="grid-2">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
    <div class="grid-3">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
    <div class="blob-8">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="683.672"
        height="652.142" viewBox="0 0 683.672 652.142">
        <defs>
          <style>
            .a {
              fill: url(#a);
            }

            .b {
              fill: none;
              stroke: #f29421;
              stroke-width: 3px;
            }
          </style>
          <linearGradient id="a" x1="0.88" y1="-0.851" x2="0" y2="1.847" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#0a6fb0" />
            <stop offset="1" stop-color="#1b1749" />
          </linearGradient>
        </defs>
        <g transform="translate(-1660.041 -3936.087)">
          <path class="a"
            d="M409.033,142.3c84.242,1.409,131.549,86.717,173.39,159.847,41.42,72.4,87.355,154.419,48.7,228.328C590.571,608,496.52,633.351,409.033,633.644c-88.106.295-182.548-24.294-224.677-101.675-40.884-75.094-1.764-161.724,40.944-235.8C268.067,222,323.423,140.866,409.033,142.3"
            transform="translate(2290.994 3785.358) rotate(73)" />
          <path class="b"
            d="M428.037,142.3c90.859,1.519,141.883,93.528,187.01,172.4,44.674,78.084,94.217,166.549,52.522,246.264C623.836,644.58,522.4,671.926,428.037,672.242c-95.027.318-196.888-26.2-242.326-109.662-44.1-80.993-1.9-174.428,44.161-254.318C276,228.26,335.7,140.755,428.037,142.3"
            transform="translate(2255.914 3736.513) rotate(73)" />
        </g>
      </svg>
    </div>
  </div>

  <div class="sect-3">
    <p class="sect-sub-heading--large">And the backbone to all the success</p>
    <h5 class="sect-heading">Meet our expert trainers</h5>

    <div class="sect-grid">
      <div class="trainer-info">
        <div class="trainer-pic">
          <img class="pic" src="../../../../assets/icons/workshop/tr-1-pic.jpg" alt="Himanshu Sharma Picture" />
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
        </div>
        <p class="trainer-name">Himanshu Sharma</p>
        <p class="trainer-intro">Founder & Director, Quant Masters</p>
        <ul>
          <li>Cleared CDS, AFCAT, RBI GRADE B, CAFs, IB, AMCAT(99.99%), CO-CUBES</li>
          <li>Recommended as Pilot in Indian Air Force</li>
          <li>Oracle Certified Java Programmer-OCJP(95%)</li>
          <li>Former Software Developer-Grade 4 @NTT DATA</li>
        </ul>
      </div>
      <div class="trainer-info">
        <div class="trainer-pic">
          <img class="pic" src="../../../../assets/icons/workshop/tr-2-pic.jpg" alt="Ritu Dudhoria Picture" />
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
        </div>
        <p class="trainer-name">Ritu Dudhoria</p>
        <p class="trainer-intro">Verbal Trainer</p>
        <ul>
          <li>Certified Neuro Linguistic Programmer(NLP)</li>
          <li>Practitioner (Sue Knight Accreditation)</li>
          <li>Certified Leadership and Life Coach (Sue Knight Accreditation)</li>
          <li>6+ Yrs Experience in Life & Communication Skills Training</li>
        </ul>
      </div>
      <div class="trainer-info">
        <div class="trainer-pic">
          <img class="pic" src="../../../../assets/icons/workshop/tr-3-pic.jpg" alt="Dinesh Gosai Picture" />
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
        </div>
        <p class="trainer-name">Dinesh Gosai</p>
        <p class="trainer-intro">Soft Skill Trainer</p>
        <ul>
          <li>Meta Coaching (Level 1 & 2)</li>
          <li>NLP Practitioner (A NLP)</li>
          <li>Master Practitioner (ISNS) MBA(ITM)</li>
        </ul>
      </div>
      <div class="trainer-info">
        <div class="trainer-pic">
          <img class="pic" src="../../../../assets/icons/workshop/tr-5-pic.jpeg" alt="Harshitha Aliveli Picture" />
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
        </div>
        <p class="trainer-name">Harshitha Aliveli</p>
        <p class="trainer-intro">Lead Trainer, Quant Masters</p>
        <ul>
          <li>BE in E&C Engineering</li>
          <li>Cleared SBI PO Prelims Exam</li>
          <li>Cleared RBI Grade B officers’ exam</li>
          <li>Scored 90+ percentile in E-LITMUS</li>
          <li>Scored 93% in AMCAT 2019</li>
        </ul>
      </div>
      <div class="trainer-info">
        <div class="trainer-pic">
          <img class="pic" src="../../../../assets/icons/workshop/tr-4-pic.jpeg" alt="Anudeep MP Picture" />
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
          <div class="image-blob">
            <img src="../../../../assets/icons/workshop/image-blob.svg" alt="Image Blob">
          </div>
        </div>
        <p class="trainer-name">Anudeep MP</p>
        <p class="trainer-intro">Lead Trainer, Quant Masters</p>
        <ul>
          <li>BE in E&C Engineering</li>
          <li>Cleared CAT 2018 with 92 percentile in Quant Section</li>
          <li>Cleared IBPS PO prelims round</li>
          <li>Cleared Co-cubes exam</li>
          <li></li>
        </ul>
      </div>
    </div>

    <div class="grid-5">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
  </div>

  <div class="sect-5">
    <div class="sect-5--right">
      <div class="register-box">
        <div class="price-box">
          <p>All this at a exclusive price of only</p>
          <p>₹ 2499/-</p>
        </div>
        <button class="qm-ring--btn" type="button" (click)="openRegForm(2)">Register Now</button>
      </div>
      <div class="register-box" style="margin-top: 6rem;">
        <div class="price-box">
          <p><b>Group discount</b></p>
          <p>Join with 2 other friends ( 3 or more members ) @</p>
          <p>₹ 1999/-</p>
        </div>
        <button class="qm-ring--btn" type="button" (click)="sendToWhatsapp()">Know More</button>
      </div>
    </div>
    <div class="grid-3">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
  </div>

  <div class="sect-3">
    <p class="sect-sub-heading--large">Get a gist of what happens in lectures</p>
    <h5 class="sect-heading">Session demo videos</h5>

    <div class="video-grid--1">
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/HkgXNtq0gso" frameborder="0"
          allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        <p class="video-title">Logical Demo Session</p>
        <p class="video-text">Shortcuts in Logical Reasoning : CLOCKS</p>
      </div>
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/kx9uW-vUi_8" frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen></iframe>
        <p class="video-title">Technical Demo Session</p>
        <p class="video-text">C Program Structure and Execution from a new perspective</p>
      </div>
    </div>

    <div class="grid-5">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
  </div>

  <div class="sect-3">
    <p class="sect-sub-heading--large">Still not convinced?</p>
    <h5 class="sect-heading">Here are a few student reviews</h5>

    <div class="video-grid--1">
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/e6e1yRjdgy8" frameborder="0"
          allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
      </div>
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/BaGEaL4ZfmM" frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen></iframe>
      </div>
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/yVToe7nTMUA" frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen></iframe>
      </div>
      <div class="video">
        <iframe width="600" height="400" src="https://www.youtube.com/embed/gp1Gz_oeGzw" frameborder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowfullscreen></iframe>
      </div>
    </div>

    <div class="grid-5">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>
  </div>

  <div class="sect-3">
    <h5 class="sect-heading">FAQs</h5>
    <div class="qm-faq--list">
      <div class="qm-faqs" *ngFor="let faq of faqs; let i = index">
        <div class="qm-faq--header">
          <h5>{{ faq.question }}</h5>
          <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseChapterGroup">
            <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
              <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                transform="translate(-3.375 -3.375)" fill="#fff" />
            </svg>
          </button>
        </div>
        <div class="qm-faq--item" id="collapseChapterGroup" id="collapseChapterGroup" [collapse]="isCollapsed[i]"
          [isAnimated]="true">
          <div class="faq-answer" *ngIf="i === 0">
            <p>
              Anyone who is preparing for placements in the IT sector or preparing for competitive exams such as banks(RBI, SBI, IBPO),
              AFCAT, MBA Exams like SNAP, NMAT, MHcet, ATMA, MAT and TISS can join this course.
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 1">
            <p>
              Yes absolutely, Everything will be taught from the scratch. The course is designed specifically keeping into consideration
              the non-IT students. So, no prior knowledge is required in technical and even aptitude to join this course.
            </p>
          </div>
          <!-- <div class="faq-answer" *ngIf="i === 2">
            <ul>
              <li>
                Yes, you can ask your doubts in the live sessions and separate live doubt clearing sessions will be arranged in case of unresolved doubts.
              </li>
              <li>
                Discussion groups for Aptitude and Technical modules will also be formed in the whatsapp wherein you can discuss your queries with the other
                students as well as the respective trainers.
              </li>
            </ul>
          </div>
          <div class="faq-answer" *ngIf="i === 3">
            <p>
              They will be conducted on zoom.
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 4">
            <p>
              Monday to Saturday 5:30 PM to 7:00 PM Aptitude & 7:30 PM to 9:00 pm Technical
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 5">
            <p>
              Yes, the mocks will be conducted every sunday or monday so that you have a track
              of practice and competitiveness.
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 6">
            <p>
              Yes, all the live sessions will get recorded. In case you happen to miss any live session,
              you can watch the recorded ones.
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 7">
            <p>Yes, you will get the app access only after the batch starts. The duration of the app access will be
              1 year and you will get the access to the following-
            </p>
            <ul>
              <li>Mock tests</li>
              <li>Practice papers</li>
              <li>Company Specific papers</li>
              <li>Chapter-wise assessments</li>
              <li>Back-up videos</li>
              <li>Latest off- campus job links and notifications</li>
            </ul>
          </div>
          <div class="faq-answer" *ngIf="i === 8">
            <p>
              Yes in HR training, we provide one-on-one mock HR, one-on-one resume review, GD preparation,
              soft-skills and communication skills training.
            </p>
          </div>
          <div class="faq-answer" *ngIf="i === 9">
            <p>
              As soon as the companies release vacancies, we will also provide you with all the latest off
              campus job notifications and links so that you can apply as per your convenience and eligibility criteria.
            </p>
          </div> -->
        </div>
      </div>
    </div>
  </div>

  <div class="ftr">
    <h5>Quant Masters Training Services</h5>
    <div class="icons">
      <div class="icon">
        <a href="https://www.facebook.com/Quant-Masters-239562816724244/" target="__blank"><img class="social-icon"
            src="../../assets/icons/facebook.svg" alt="facebook-logo" /></a>
      </div>
      <div class="icon">
        <a href="https://www.linkedin.com/company/qunat-masters" target="__blank"><img class="social-icon"
            src="../../assets/icons/linkedin.svg" alt="linkedin-logo" /></a>
      </div>
      <div class="icon">
        <a href="https://www.youtube.com/channel/UCnQweeTKKj0gUZeDG1_PDtQ" target="__blank"><img class="social-icon"
            src="../../assets/icons/youtube.svg" alt="youtube-logo" /></a>
      </div>
      <div class="icon">
        <a href="https://www.instagram.com/quant_masters/" target="__blank"><img class="social-icon"
            src="../../assets/icons/instagram.svg" alt="instagram-logo" /></a>
      </div>
    </div>
    <div class="copy-text">&copy; 2022 Quant Masters. All Rights Reserved.</div>
  </div>

  <div class="qm-contact--box" (click)="sendToWhatsapp()">
    <img src="../../../assets/icons/ml-internship/whatsapp-social-media.svg" alt="Whatsapp Icon">
  </div>

  <ng-template #regTemplate>
    <div class="modal-header" style="flex-direction:column;">
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
      <h4 class="modal-title text-info">Your Details</h4>
      <h5>Let us know how to get back to you</h5>
    </div>
    <div class="modal-body">
      <form autocomplete="off" #registerForm="ngForm" (ngSubmit)="registerQuick(registerForm.valid)"
        appPasswordEqualValidator>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="fName" placeholder="First Name *" [(ngModel)]="newUser.f_name" #fName="ngModel"
              required />
            <small class="form-error--text"
              *ngIf="fName.errors?.required && fName.touched || (fName.pristine && registerForm.submitted)">
              First Name is Required
            </small>
            <small class="form-error--text" *ngIf="fName.errors?.fName && fName.touched">
              First Name is Invalid
            </small>
          </div>
          <div class="form-elem">
            <input type="text" name="lName" placeholder="Last Name" [(ngModel)]="newUser.l_name" #lName="ngModel" />
            <small class="form-error--text" *ngIf="lName.errors?.lName && lName.touched">
              Last Name is Invalid
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="email" name="email"
              [class.is-inv--input]="email.invalid && email.touched || (email.pristine && registerForm.submitted)"
              placeholder="Student Mail Id *" [(ngModel)]="newUser.email" #email="ngModel" required email />
            <div class="form-fill--bar"
              [class.is-inv]="email.invalid && email.touched || (email.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="email.errors?.email && email.touched">
              That does not look like an email
            </small>
            <small class="form-error--text" *ngIf="email.errors?.required && email.touched">
              Please enter your email
            </small>
            <small id="emailHelpBlock" *ngIf="email.valid">
              Don't worry, your email is safe with us.
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="number" name="phoneNum"
              [class.is-inv--input]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)"
              placeholder="Student Whatsapp Number *" [(ngModel)]="newUser.phone_no" #phoneNum="ngModel" required
              pattern="[0-9]{10}" />
            <div class="form-fill--bar"
              [class.is-inv]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="phoneNum.errors?.pattern && phoneNum.touched">
              Mobile number must contain 10 digits
            </small>
            <small class="form-error--text" *ngIf="phoneNum.errors?.required && phoneNum.touched">
              Please enter your phone number
            </small>
            <small id="emailHelpBlock" *ngIf="phoneNum.valid">
              Don't worry, your number is safe with us.
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <button type="submit" value="submit" class="secondary-btn">
            Get Started
          </button>
        </div>
        <small class="form-error--text form-error-last" *ngIf="registerForm.invalid && registerForm.submitted">
          Please fill all the details first.
        </small>
      </form>
    </div>
  </ng-template>

  <ng-template #buyTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Book Your Seat</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide(); showIndicator=false">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <table class="item-tab" cellpadding="5">
        <tr>
          <th>Description</th>
          <th>Amount</th>
        </tr>
        <tr>
          <td>{{ selectedDesc }}</td>
          <td>₹ {{ selectedAmount }}.00/- (GST Incl.)</td>
        </tr>
      </table>
      <button class="secondary-btn" (click)="onCheckout()">Checkout</button>
    </div>
  </ng-template>

  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Thank You</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>You will recieve a mail with the receipt in your registered email.</p>
      <p>Kindly send a screenshot of the receipt (with visible email address) via WhatsApp to:
        <br /><b>{{ selContactNum }}</b>
      </p>
      <p>For any further queries contact <b>{{ selContactInfo }} @ {{ selContactNum }}</b></p>
    </div>
  </ng-template>

  <ng-template #errorTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-danger">Registration Failed!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      Please try again
    </div>
  </ng-template>
</div>
