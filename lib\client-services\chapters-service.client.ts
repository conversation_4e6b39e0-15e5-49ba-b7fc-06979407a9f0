// lib/client-services/chapters-service.client.ts

import axios from 'axios'
import {
  SuperGroup,
  Group,
  ChapterPaper,
  MarkSubmission
} from '@/types/chapter-paper-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  superGroupUrl: `${API_BASE_URL}/test/progression/super-groups`,
  groupUrl: `${API_BASE_URL}/test/progression/groups`,
  groupPaperUrl: `${API_BASE_URL}/test/progression/group`,
  submitUrl: `${API_BASE_URL}/test/progression/submit/marks`,
  imgUrl: `${API_BASE_URL}/test/progression/paper`,
  adminPaperUrl: `${API_BASE_URL}/admin/paper/progression/upload/test`,
  adminQuesUrl: `${API_BASE_URL}/admin/paper/progression/upload/test/question`,
  adminQuesGetUrl: `${API_BASE_URL}/admin/paper/progression/paper`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class ChaptersService {
  /**
   * Get super groups
   */
  static async getSuperGroups(): Promise<SuperGroup[]> {
    try {
      const response = await axios.get(
        ENDPOINTS.superGroupUrl,
        createAuthHeaders()
      )

      let superGroups: SuperGroup[] = response.data

      // Remove specific super group (temp code from original)
      superGroups = superGroups.filter(
        (x) => x.super_group_id !== '143dbc04-8493-3b7e-ad2d-4e3983ae4edd'
      )

      return superGroups
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get groups by super group ID
   */
  static async getGroups(superGrpId: string): Promise<Group[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.groupUrl}/${superGrpId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching groups for super group ${superGrpId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get papers of a group
   */
  static async getPapersOfAGroup(groupId: string): Promise<ChapterPaper[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.groupPaperUrl}/${groupId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching papers for group ${groupId}:`, error)
      return []
    }
  }

  /**
   * Get image explanation
   */
  static async getImageExplanation(
    paperId: string,
    quesNo: number
  ): Promise<any> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.imgUrl}/${paperId}/${quesNo}/explanation`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching image explanation for paper ${paperId}, question ${quesNo}:`,
        error
      )
      throw error
    }
  }

  /**
   * Submit marks
   */
  static async submitMarks(submission: MarkSubmission): Promise<string> {
    try {
      const response = await axios.post(
        ENDPOINTS.submitUrl,
        submission,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error submitting marks:', error)
      throw error
    }
  }

  /**
   * Update paper (admin function)
   */
  static async updatePaper(paper: ChapterPaper): Promise<string> {
    try {
      const response = await axios.put(
        ENDPOINTS.adminPaperUrl,
        paper,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating paper:', error)
      throw error
    }
  }

  /**
   * Update question (admin function)
   */
  static async updateQuestion(question: object): Promise<string> {
    try {
      const response = await axios.put(
        ENDPOINTS.adminQuesUrl,
        question,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating question:', error)
      throw error
    }
  }

  /**
   * Get questions for admin
   */
  static async getQuestionsForAdmin(paperId: string): Promise<string> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.adminQuesGetUrl}/${paperId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching questions for admin, paper ${paperId}:`,
        error
      )
      throw error
    }
  }
}
