import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription
} from '@/components/ui/card'
import {
  Video,
  MessageSquare,
  FileText,
  ListChecks,
  FileQuestion,
  FileCheck,
  ClipboardList,
  Award,
  Trophy,
  Layers,
  Plane,
  Briefcase,
  TrendingUp,
  Target
} from 'lucide-react'

export default function DashboardPage() {
  // Statistics data
  const statistics = [
    {
      title: "Today's Practice",
      value: '24',
      subtitle: 'Questions completed',
      icon: <Target className="text-lg" />,
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: 'Total Score',
      value: '3,600',
      subtitle: 'Points earned',
      icon: <Trophy className="text-lg" />,
      change: '+16%',
      changeType: 'positive' as const
    },
    {
      title: 'Active Tests',
      value: '8',
      subtitle: 'In progress',
      icon: <FileText className="text-lg" />,
      change: '+4%',
      changeType: 'positive' as const
    },
    {
      title: 'Completion Rate',
      value: '78%',
      subtitle: 'This month',
      icon: <TrendingUp className="text-lg" />,
      change: '-2%',
      changeType: 'negative' as const
    }
  ]

  // Quick access items
  const quickAccess = [
    {
      title: 'Trial Papers',
      icon: <FileText className="w-5 h-5" />,
      href: '/dashboard/trial-papers',
      description: 'Practice with trial papers'
    },
    {
      title: 'Technical MCQs',
      icon: <FileQuestion className="w-5 h-5" />,
      href: '/dashboard/technical-mcq-papers',
      description: 'Test your technical knowledge'
    },
    {
      title: 'Mock Papers',
      icon: <FileCheck className="w-5 h-5" />,
      href: '/dashboard/model-papers',
      description: 'Full-length mock tests'
    },
    {
      title: 'Company Papers',
      icon: <Briefcase className="w-5 h-5" />,
      href: '/dashboard/company-papers',
      description: 'Company-specific preparation'
    }
  ]

  return (
    <div>
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 mb-6 w-full lg:grid-cols-2 2xl:grid-cols-4">
        {statistics.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="icon-box">{stat.icon}</div>
                <div className="flex-shrink-0 ml-3">
                  <span className="text-2xl font-bold leading-none text-gray-900">
                    {stat.value}
                  </span>
                  <h3 className="text-base font-normal text-gray-500">
                    {stat.subtitle}
                  </h3>
                </div>
                <div
                  className={`flex flex-1 justify-end items-center ml-5 w-0 text-base font-bold ${
                    stat.changeType === 'positive'
                      ? 'text-green-500'
                      : 'text-red-500'
                  }`}
                >
                  {stat.change}
                  {stat.changeType === 'positive' ? (
                    <TrendingUp className="w-4 h-4 ml-1" />
                  ) : (
                    <TrendingUp className="w-4 h-4 ml-1 rotate-180" />
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Access Section */}
      <div className="grid grid-cols-1 gap-6 mb-6 xl:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Access</CardTitle>
            <CardDescription>
              Jump into your most used practice areas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {quickAccess.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="flex items-start p-4 rounded-lg border border-gray-200 hover:bg-gray-50 transition-all duration-200 group"
                >
                  <div className="icon-box-white mr-3">{item.icon}</div>
                  <div>
                    <h4 className="text-sm font-semibold text-gray-900 group-hover:text-sky-600">
                      {item.title}
                    </h4>
                    <p className="text-xs text-gray-500 mt-1">
                      {item.description}
                    </p>
                  </div>
                </a>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="gradient-dark text-white">
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex-shrink-0">
                <span className="text-2xl font-bold leading-none text-white sm:text-3xl">
                  45,385
                </span>
                <h3 className="text-base font-normal text-gray-400">
                  Questions this week
                </h3>
              </div>
              <div className="flex flex-1 justify-end items-center text-base font-bold text-green-400">
                12.5%
                <TrendingUp className="w-4 h-4 ml-1" />
              </div>
            </div>
            <div className="mt-8">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-400">Progress</span>
                <span className="text-white font-medium">68%</span>
              </div>
              <div className="mt-2 w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-sky-500 to-blue-500 h-2 rounded-full"
                  style={{ width: '68%' }}
                ></div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* All Practice Areas */}
      <Card>
        <CardHeader>
          <CardTitle>All Practice Areas</CardTitle>
          <CardDescription>
            Explore all available practice and assessment options
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[
              {
                title: 'Trial Papers',
                icon: <FileText className="w-5 h-5" />,
                href: '/dashboard/trial-papers'
              },
              {
                title: 'Chapter Practice',
                icon: <ListChecks className="w-5 h-5" />,
                href: '/dashboard/chapter-practice-papers'
              },
              {
                title: 'Technical MCQs',
                icon: <FileQuestion className="w-5 h-5" />,
                href: '/dashboard/technical-mcq-papers'
              },
              {
                title: 'Mock Papers',
                icon: <FileCheck className="w-5 h-5" />,
                href: '/dashboard/model-papers'
              },
              {
                title: 'Chapter Assessment',
                icon: <ClipboardList className="w-5 h-5" />,
                href: '/dashboard/chapter-papers'
              },
              {
                title: 'Competitive',
                icon: <Award className="w-5 h-5" />,
                href: '/dashboard/competitive-papers'
              },
              {
                title: 'Weekly Competitive',
                icon: <Trophy className="w-5 h-5" />,
                href: '/dashboard/open-competitive-papers'
              },
              {
                title: 'Section Wise',
                icon: <Layers className="w-5 h-5" />,
                href: '/dashboard/section-wise-papers'
              },
              {
                title: 'AFCAT Papers',
                icon: <Plane className="w-5 h-5" />,
                href: '/dashboard/afcat-papers'
              },
              {
                title: 'Company Papers',
                icon: <Briefcase className="w-5 h-5" />,
                href: '/dashboard/company-papers'
              },
              {
                title: 'Interviews',
                icon: <Video className="w-5 h-5" />,
                href: '/dashboard/student-review'
              },
              {
                title: 'Testimonials',
                icon: <MessageSquare className="w-5 h-5" />,
                href: '/dashboard/student-testimonials'
              }
            ].map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="flex flex-col items-center p-4 rounded-lg hover:bg-gray-50 transition-all duration-200 group"
              >
                <div className="icon-box-white mb-3 group-hover:shadow-md transition-shadow">
                  {item.icon}
                </div>
                <span className="text-sm text-gray-700 text-center group-hover:text-sky-600">
                  {item.title}
                </span>
              </a>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
