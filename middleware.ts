import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Paths that require authentication
const protectedPaths = ['/dashboard', '/admin', '/technical', '/account']

// Paths that require admin privileges
const adminPaths = ['/admin']

// Paths that should redirect to dashboard if already authenticated
const authPaths = ['/user/login', '/user/register', '/user/forgot-password']

// Admin UUID from the original Angular app for admin verification
const ADMIN_VERIFICATION_UUID = '821f069c-12dd-3d3d-9462-d6e84564b659'

/**
 * Check if user has admin privileges by calling the admin permission API
 * This replicates the Angular app's admin verification logic
 */
async function checkAdminPermissions(
  email: string,
  token: string
): Promise<boolean> {
  try {
    const response = await fetch(
      `https://api.quantmasters.in/admin/manage/${email}/perm/check`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      return false
    }

    const data = await response.json()

    return data.msg === ADMIN_VERIFICATION_UUID
  } catch (error) {
    console.error('Admin permission check failed:', error)
    return false
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if user has authentication cookies
  const hasQMAUserCookie = request.cookies.has('QMA_USR')
  const hasQMAKeyCookie = request.cookies.has('QMA_KEY')
  const hasQMATokenCookie = request.cookies.has('QMA_TOK')
  const isAuthenticated =
    hasQMAUserCookie && hasQMAKeyCookie && hasQMATokenCookie

  // Handle protected routes - redirect to login if not authenticated
  if (
    protectedPaths.some((path) => pathname.startsWith(path)) &&
    !isAuthenticated
  ) {
    const url = new URL('/user/login', request.url)
    // Add a redirect parameter to redirect back after login
    url.searchParams.set('redirect', pathname)
    return NextResponse.redirect(url)
  }

  // Handle admin routes - require admin privileges
  if (adminPaths.some((path) => pathname.startsWith(path)) && isAuthenticated) {
    const userEmail = request.cookies.get('QMA_USR')?.value
    const userToken = request.cookies.get('QMA_TOK')?.value

    if (!userEmail || !userToken) {
      const url = new URL('/user/login', request.url)
      url.searchParams.set('redirect', pathname)
      url.searchParams.set('error', 'missing_credentials')
      return NextResponse.redirect(url)
    }

    // Check admin permissions using the same logic as Angular app
    const hasAdminAccess = await checkAdminPermissions(userEmail, userToken)
    if (!hasAdminAccess) {
      // Redirect to dashboard with access denied message
      const url = new URL('/dashboard', request.url)
      url.searchParams.set('error', 'access_denied')
      // url.searchParams.set('message', 'You do not have admin privileges to access this area')
      return NextResponse.redirect(url)
    }
  }

  // Redirect authenticated users away from login/register pages
  if (authPaths.some((path) => pathname.startsWith(path)) && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  return NextResponse.next()
}

// Configure matcher for paths this middleware should run on
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/admin/:path*',
    '/technical/:path*',
    '/account/:path*',
    '/user/login',
    '/user/register',
    '/user/forgot-password'
  ]
}
