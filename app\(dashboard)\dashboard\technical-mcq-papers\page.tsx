// app/dashboard/technical-mcq-papers/page.tsx
import { Metadata } from 'next'
import TechnicalMCQClient from '@/components/technical-mcq-papers/technical-mcq-client'
import { TMCQServerService } from '@/lib/server-services/tmcq-service.server'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Technical MCQ Papers | Quant Masters',
  description: 'Practice technical MCQ papers for various topics'
}

export default async function TechnicalMCQPage() {
  const groups = await TMCQServerService.getTMCQGroups()

  return (
    <div>
      <TechnicalMCQClient initialGroups={groups} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
