import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-tests',
  templateUrl: './tests.component.html',
  styleUrls: ['./tests.component.scss']
})
export class TestsComponent implements OnInit {

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute) { }

  ngOnInit() {
  }

  takeToUploadExplanation() {
    this.router.navigate(['explanation'], { relativeTo: this.activatedRoute });
  }

  takeToUploadPaper() {
    this.router.navigate(['papers'], { relativeTo: this.activatedRoute });
  }
}
