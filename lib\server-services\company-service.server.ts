// lib/server-services/company-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import {
  ChapterPaper,
  UserPermissionResponse,
  getCompanyRegex
} from '@/types/company-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  papersUrl: `${API_BASE_URL}/test/company/papers`,
  openPapersUrl: `${API_BASE_URL}/test/open/papers`,
  freeCompanyPapersUrl: `${API_BASE_URL}/test/sample/company/papers`,
  checkUserUrl: `${API_BASE_URL}/user/manage`,
  submitUrl: `${API_BASE_URL}/test/company/submit/marks`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Get server-side user email from cookies
 */
const getServerSideEmail = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_USR')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerCompanyService {
  /**
   * Get all company papers from free company papers endpoint
   */
  static async getAllPapers(): Promise<ChapterPaper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.freeCompanyPapersUrl,
        createAuthHeaders(token)
      )
      return response.data || []
    } catch (error) {
      console.error('Error fetching company papers:', error)
      return []
    }
  }

  /**
   * Get company papers from company-specific endpoint
   */
  static async getCompanyPapers(): Promise<ChapterPaper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.papersUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching company papers:', error)
      return []
    }
  }

  /**
   * Check user permissions for company papers
   */
  static async checkUserPermissions(): Promise<{
    hasAccess: boolean
    isLocked: boolean
  }> {
    if (!(await checkServerSideLoginStatus())) {
      return { hasAccess: false, isLocked: true }
    }

    const token = await getServerSideToken()
    const email = await getServerSideEmail()

    if (!email) {
      return { hasAccess: false, isLocked: true }
    }

    try {
      const response = await axios.get(
        `${ENDPOINTS.checkUserUrl}/${email}/perm/check`,
        createAuthHeaders(token)
      )

      const data: UserPermissionResponse = response.data
      const hasAccess = data.msg === '821f069c-12dd-3d3d-9462-d6e84564b658'

      return {
        hasAccess,
        isLocked: !hasAccess
      }
    } catch (error) {
      console.error('Error checking user permissions:', error)
      return { hasAccess: false, isLocked: true }
    }
  }

  /**
   * Filter papers by company type using regex patterns
   */
  static filterPapersByCompanyType(
    papers: ChapterPaper[],
    companyType: number
  ): ChapterPaper[] {
    const regexp = getCompanyRegex(companyType)
    const filteredPapers = papers.filter(
      (paper) => paper.paper_name.match(regexp) != null
    )
    return filteredPapers
  }
}
