import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { UploadConsoleComponent } from './upload-console/upload-console.component';
import { UsersComponent } from './users/users.component';
import { TestsComponent } from './tests/tests.component';
import { TestimonialsComponent } from './testimonials/testimonials.component';
import { ResultsComponent } from './results/results.component';
import { UploadExplanationComponent } from './Upload/upload-explanation/upload-explanation.component';
import { AnalyticsComponent } from './Analytics/analytics.component';
import { VideoUsageComponent } from './Analytics/video-usage/video-usage.component';
import { MetricsComponent } from './metrics/metrics.component';
import { WorkshopsComponent } from './Workshops/workshops/workshops.component';
import { WorkshopVideosComponent } from './Workshops/workshop-videos/workshop-videos.component';
import { UpldPlacementPostComponent } from './Upload/upld-placement-post/upld-placement-post.component';
import { UploadTestsComponent } from './Upload/upload-tests/upload-tests.component';
import { BlogsComponent } from './blogs/blogs.component';
import { NotesComponent } from './notes/notes.component';
import { PaperDetailComponent } from './Upload/paper-detail/paper-detail.component';


const routes: Routes = [
  {
    path: '',
    component: UploadConsoleComponent,
    children: [
      { path: 'users', component: UsersComponent },
      { path: 'tests', component: TestsComponent },
      { path: 'tests/papers', component: UploadTestsComponent },
      { path: 'tests/explanation', component: UploadExplanationComponent },
      { path: 'results', component: ResultsComponent },
      { path: 'testimonials', component: TestimonialsComponent },
      { path: 'analytics', component: AnalyticsComponent },
      { path: 'analytics/video-usage', component: VideoUsageComponent },
      { path: 'workshops', component: WorkshopsComponent },
      { path: 'workshop/videos', component: WorkshopVideosComponent },
      { path: 'test-metrics', component: MetricsComponent },
      { path: 'placement', component: UpldPlacementPostComponent },
      { path: 'blogs', component: BlogsComponent },
      { path: 'notes', component: NotesComponent },
      { path: 'tests/papers/paperdetail/:p_id/:p_name/:p_show_ans/:p_timelim/:p_noofque/:p_status/:p_once_ans/:p_neg_marks/:p_rand_ques',
       component: PaperDetailComponent}
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminRoutingModule { }
