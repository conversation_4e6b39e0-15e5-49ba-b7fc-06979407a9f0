import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { VideoUsage } from '../Models/Tracking/VideoUsage';

@Injectable({
  providedIn: 'root'
})
export class TrackingService {

  private videoUsageTrackingUrl = 'https://api.quantmasters.in/track/video/usage';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  submitVideoTrackRecord(trackingData: VideoUsage): Observable<string> {

    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.videoUsageTrackingUrl, trackingData, httpOpts);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
