// app/dashboard/open-competitive-papers/page.tsx
import { Metadata } from 'next'
import { ServerOpenCompetitiveService } from '@/lib/server-services/open-competitive-service.server'
import OpenCompetitivePapersClient from '@/components/open-competitive-papers/open-competitive-papers-client'

export const metadata: Metadata = {
  title: 'Open Competitive Papers | Quant Masters',
  description: 'Access our collection of open competitive examination papers'
}

export default async function OpenCompetitivePapersPage() {
  // Fetch papers on the server
  const papers = await ServerOpenCompetitiveService.getOpenCompetitivePapers()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-2xl font-bold text-center">
            Open Competitive Papers
          </h1>
          <p className="text-center text-gray-600 mt-2">
            Weekly papers convering a wide array of topics
          </p>
        </div>
      </div>

      {/* Pass the fetched papers to the client component */}
      <OpenCompetitivePapersClient initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
