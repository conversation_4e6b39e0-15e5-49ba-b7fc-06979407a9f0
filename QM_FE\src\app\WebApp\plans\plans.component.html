<app-nav></app-nav>
<div class="home-wrapper">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="home-sect--1">
    <div class="card">
      <h2>Free</h2>
      <ul>
        <li>Free Open Tests</li>
        <li>1 Free Video</li>
        <li>2 Free Chapter Wise Tests</li>
        <li>2 Free Competitive Tests</li>
      </ul>
      <p>₹ 0/-</p>
      <button *ngIf="!bLogged" class="custom-btn" (click)="onRegister()">Login</button>
      <button *ngIf="bLogged" class="custom-btn" (click)="onOpenTests()">Open Tests</button>
    </div>
    <div class="card">
      <h2>Paid</h2>
      <ul>
        <li>Free Open Tests</li>
        <li>100+ Chapter Wise Aptitude Tests</li>
        <li>10+ Hours of Tutorial Videos</li>
        <li>Downloadable Notes with each video</li>
        <li>Papers of major companies</li>
        <li>Bank and government exam papers</li>
        <li class="price-item"><s>₹ 5000/-</s> <span>₹ {{ amount }}/-</span></li>
      </ul>
      <button class="custom-btn" *ngIf="!subscribed" (click)="onBuy()">Buy Now</button>
      <button class="custom-btn" *ngIf="subscribed" (click)="navigateToDashboard()">Dashboard</button>
    </div>
    <svg class="neg-space" xmlns="http://www.w3.org/2000/svg" width="184.715" height="186.713"
      viewBox="0 0 184.715 186.713">
      <ellipse id="Ellipse_1" data-name="Ellipse 1" cx="59" cy="72.5" rx="59" ry="72.5"
        transform="translate(97.024) rotate(42)" fill="#e88224" />
    </svg>
  </div>
  <app-footer></app-footer>
  <ng-template #buyTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Buy Complete Course</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide(); showIndicator=false">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <table class="item-tab" cellpadding="5">
        <tr>
          <th>Description</th>
          <th>Amount</th>
        </tr>
        <tr>
          <td>Quant Masters Complete Aptitude Course (1 Year)</td>
          <td>₹ {{ amount }}.00/-</td>
        </tr>
      </table>
      <button class="checkout-btn" (click)="onCheckout()">Checkout</button>
    </div>
  </ng-template>
  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Payment Successful!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Kickoff your learning journey right away.</p>
      <p class="logout-btn"><a (click)="navigateToDashboard()">Dashboard</a></p>
    </div>
  </ng-template>
  <ng-template #hideTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Not a member yet??</h4>
    </div>
    <div class="modal-body">
      <p>Login to Quant Masters to access the dashboard.</p>
      <p><a (click)="modalRef.hide()" routerLink="/login">Click here</a> to Login or
        <a (click)="modalRef.hide()" routerLink="/user/register">Click here</a> to Register, it's free!!</p>
    </div>
  </ng-template>
</div>