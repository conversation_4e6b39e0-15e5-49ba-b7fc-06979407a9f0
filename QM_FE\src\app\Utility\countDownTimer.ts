export function CountDownTimer(duration: number, granularity: number) {

  this.duration = duration;
  this.granularity = granularity || 1000;
  this.tickFns = [];
  this.hardStop = false;
  this.running = false;
}

/**
 * Function to start our custom timer
 */
CountDownTimer.prototype.start = function() {

  if (this.running) {
    return;
  }

  this.running = true;

  let to: number,
      diff: number,
      obj: { minutes: any; seconds: any; };

  const that = this,
        start = Date.now();

  (function timer() {

    diff = that.duration - (((Date.now() - start) / 1000) | 0);

    if (diff > 0) {
      to = window.setTimeout(timer, that.granularity);
    } else {
      diff = 0;
      that.running = false;
    }

    obj = CountDownTimer.parse(diff);
    that.tickFns.forEach((ftn: { call: (arg0: any, arg1: any, arg2: any) => void; }) => {
      ftn.call(this, obj.minutes, obj.seconds);
    }, that);

    if (that.hardStop) {
      clearTimeout(to);
      return;
    }
  }());
};

CountDownTimer.prototype.onTick = function(ftn: any) {

  if (typeof ftn === 'function') {
    this.tickFns.push(ftn);
  }

  return this;
};

CountDownTimer.prototype.expired = function() {

  return !this.running;
};

CountDownTimer.parse = function(seconds: number) {

  return {
    'minutes': (seconds / 60) | 0,
    'seconds': (seconds % 60) | 0
  };
};

CountDownTimer.prototype.stop = function() {
  this.hardStop = true;
  this.tickFns  = [];
};
