// app/dashboard/chapter-papers/page.tsx
import { Metadata } from 'next'
import { ServerChaptersService } from '@/lib/server-services/chapters-service.server'
import ChapterPapersClient from '@/components/chapter-papers/chapter-papers-client'

export const metadata: Metadata = {
  title: 'Chapter Papers | Quant Masters',
  description:
    'Practice with chapter-wise papers organized by subjects and topics'
}

export default async function ChapterPapersPage() {
  // Fetch super groups on the server
  const superGroups = await ServerChaptersService.getSuperGroups()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center mb-2">
            Chapter-wise Practice Papers
          </h1>
          <p className="text-center text-gray-600">
            Choose from organized practice papers by subject and topic
          </p>
        </div>
      </div>

      {/* Pass the fetched super groups to the client component */}
      <ChapterPapersClient initialSuperGroups={superGroups} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
