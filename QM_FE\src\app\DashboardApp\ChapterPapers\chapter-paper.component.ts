import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { ChaptersService } from '../../Services/Dashboard/chapters.service';
import { SuperGroup } from '../../Models/Dashboard/Chapters/SuperGroup';
import { Group } from '../../Models/Dashboard/Chapters/Group';
import { ChapterPaper } from '../../Models/Dashboard/Chapters/ChapterPaper';

declare let gtag: Function;

@Component({
  selector: 'app-chapter-paper',
  templateUrl: './chapter-paper.component.html',
  styleUrls: ['./chapter-paper.component.scss']
})
export class ChapterPaperComponent implements OnInit {

  public superGroups: SuperGroup[];
  public paperGroups: Group[];
  public ChapterPaper: ChapterPaper[];
  public isCollapsed = [];

  public selectedGroup = [];

  public showIndicator = false;

  private safeGroupId = '';
  private safeIdx = 0;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public chaptersService: ChaptersService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;
    this.showIndicator = true;

    if (localStorage.getItem('qmChapterGroupIdx') != null) {
      this.safeIdx = parseInt(localStorage.getItem('qmChapterGroupIdx'));
    }

    if (localStorage.getItem('qmChapterGroupId') != null) {
      this.safeGroupId = localStorage.getItem('qmChapterGroupId');
    }

    this.chaptersService.getSuperGroups().subscribe(response => {
      that.superGroups = response;

      // Temp Code
      that.superGroups.splice(that.superGroups.findIndex(x => x.super_group_id === '143dbc04-8493-3b7e-ad2d-4e3983ae4edd'), 1);

      for (let i = 0; i < that.superGroups.length; i++) {
        that.selectedGroup.push(false);
      }

      that.selectedGroup[that.safeIdx] = true;
      that.selectGroup(that.superGroups[that.safeIdx].super_group_id, that.safeIdx);
      that.showIndicator = false;
    }, error => {

    });
  }

  selectGroup(superGrp: string, groupIdx: number) {
    const that = this;

    this.showIndicator = true;
    localStorage.setItem('qmChapterGroupIdx', groupIdx.toString());
    localStorage.setItem('qmChapterGroupId', superGrp);
    that.chaptersService.getGroups(superGrp).subscribe(response => {
      that.paperGroups = response;

      that.selectedGroup.fill(false);
      that.selectedGroup[groupIdx] = true;

      for (const topic of this.paperGroups) {
          that.isCollapsed.push(true);

          that.ChapterPaper = [];
          that.chaptersService.getPapersOfAGroup(topic.group_id).subscribe(response2 => {

            if (response2.length > 0) {
              for (const paper of response2) {
                that.ChapterPaper.push(paper);
              }
            } else {
              this.paperGroups.splice(this.paperGroups.indexOf(topic), 1);
            }

            that.showIndicator = false;
          }, error => {
            that.showIndicator = false;
          });
      }
    }, error => {
      that.showIndicator = false;
    });
  }

  selectPaperGroup(paperGroupId: string) {
    const that = this;
    that.chaptersService.getPapersOfAGroup(paperGroupId).subscribe(response => {
      that.ChapterPaper = response;
    }, error => {

    });
  }

  filterChapterPapers(groupId: string) {
    return this.ChapterPaper.filter(x => x.group_id === groupId);
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '1', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans() {
    this.router.navigate(['/plans']);
  }
}
