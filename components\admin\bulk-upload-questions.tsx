'use client'

import React, { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Download,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import {
  bulkUploadQuestions,
  generateExcelTemplate
} from '@/lib/server-actions/bulk-upload'

interface BulkUploadQuestionsProps {
  paperId: string
  paperType: number
  onUploadComplete?: () => void
}

export function BulkUploadQuestions({
  paperId,
  paperType,
  onUploadComplete
}: BulkUploadQuestionsProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [uploadResult, setUploadResult] = useState<{
    success: boolean
    message: string
    details?: {
      totalRows: number
      successfulRows: number
      errors: Array<{ row: number; error: string }>
    }
  } | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDownloadTemplate = async () => {
    try {
      const arrayBuffer = await generateExcelTemplate()
      const blob = new Blob([arrayBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'questions-template.xlsx'
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading template:', error)
    }
  }

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploading(true)
    setProgress(0)
    setUploadResult(null)

    // Simulate progress for better UX
    const progressInterval = setInterval(() => {
      setProgress((prev) => {
        if (prev < 90) return prev + 10
        return prev
      })
    }, 500)

    try {
      const arrayBuffer = await file.arrayBuffer()

      const result = await bulkUploadQuestions(paperId, paperType, arrayBuffer)

      clearInterval(progressInterval)
      setProgress(100)

      setUploadResult({
        success: result.success,
        message: result.message,
        details: {
          totalRows: result.totalRows,
          successfulRows: result.successCount,
          errors: result.errors.map((error) => ({
            row: error.row,
            error: `${error.field}: ${error.message}`
          }))
        }
      })

      if (result.success && onUploadComplete) {
        onUploadComplete()
      }
    } catch (error) {
      clearInterval(progressInterval)
      setUploadResult({
        success: false,
        message:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred'
      })
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileSpreadsheet className="h-5 w-5" />
          Bulk Upload Questions
        </CardTitle>
        <CardDescription>
          Upload questions in bulk using an Excel file. Download the template to
          get started.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Template Download */}
        <div className="p-4 border rounded-lg bg-blue-50">
          <h3 className="font-medium mb-2">Step 1: Download Template</h3>
          <p className="text-sm text-gray-600 mb-3">
            Download the Excel template with predefined columns and sample data.
          </p>
          <Button
            variant="outline"
            onClick={handleDownloadTemplate}
            className="w-full sm:w-auto"
          >
            <Download className="h-4 w-4 mr-2" />
            Download Template
          </Button>
        </div>

        {/* File Upload */}
        <div className="p-4 border rounded-lg">
          <h3 className="font-medium mb-2">Step 2: Upload Filled Template</h3>
          <p className="text-sm text-gray-600 mb-3">
            Upload your filled Excel file. All questions will be validated
            before adding to the test paper.
          </p>

          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx,.xls"
            onChange={handleFileUpload}
            disabled={isUploading}
            className="hidden"
          />

          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="w-full sm:w-auto"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isUploading ? 'Uploading...' : 'Choose Excel File'}
          </Button>
        </div>

        {/* Progress */}
        {isUploading && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Processing questions...</span>
              <span>{progress}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* Results */}
        {uploadResult && (
          <Alert
            className={
              uploadResult.success
                ? 'border-green-200 bg-green-50'
                : 'border-red-200 bg-red-50'
            }
          >
            <div className="flex items-start gap-2">
              {uploadResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
              )}
              <div className="flex-1">
                <AlertDescription className="font-medium">
                  {uploadResult.message}
                </AlertDescription>

                {uploadResult.details && (
                  <div className="mt-2 text-sm">
                    <p>
                      Total rows processed: {uploadResult.details.totalRows}
                    </p>
                    <p>
                      Successfully added: {uploadResult.details.successfulRows}
                    </p>

                    {uploadResult.details.errors.length > 0 && (
                      <div className="mt-2">
                        <p className="font-medium text-red-700">Errors:</p>
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          {uploadResult.details.errors
                            .slice(0, 5)
                            .map((error, index) => (
                              <li key={index} className="text-red-600">
                                Row {error.row}: {error.error}
                              </li>
                            ))}
                          {uploadResult.details.errors.length > 5 && (
                            <li className="text-red-600">
                              ... and {uploadResult.details.errors.length - 5}{' '}
                              more errors
                            </li>
                          )}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </Alert>
        )}

        {/* Instructions */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>
            <strong>Template Columns:</strong>
          </p>
          <ul className="list-disc list-inside ml-2 space-y-0.5">
            <li>Question Text (required)</li>
            <li>Option A, Option B, Option C, Option D (required)</li>
            <li>Correct Answer (required - A, B, C, or D)</li>
            <li>Explanation (optional)</li>
            <li>Difficulty Level (Easy, Medium, Hard)</li>
            <li>Question Type (MCQ, True/False, etc.)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}
