// types/results-types.ts

export interface StudentMarks {
  paper_id: string
  paper_name: string
  paper_type: string
  paper_group: string
  marks: number
  total_marks: number
  created_at: string
  display_date?: string
  answer_id?: string
  sw_exists?: boolean
}

export interface SectionMarks {
  section_name: string
  marks: string
  total: string
  percentage: string
}

export interface PaginationConfig {
  max: number
  boundaryLinks: boolean
}

export interface ModalConfig {
  animated: boolean
  backdrop: boolean
  class: string
}

export interface ResultsState {
  allMarks: StudentMarks[]
  displayMarks: StudentMarks[]
  sectionMarks: SectionMarks[]
  numDispMarks: number
  showIndicator: boolean
  paperName: string
  answerDate: string
  currentPage: number
  itemsPerPage: number
}

export type TestType =
  | 'all'
  | 'trial'
  | 'weekly-competitive'
  | 'section-wise'
  | 'afcat'
  | 'company'
  | 'technical-mcq'
  | 'wp-practice'
