.navBox {
    width: 100%;
    max-width: 1330px;
    display: flex;
    justify-content: space-around;
    padding-top: 30px;
    margin: auto;
    background-color: rgba(0, 0, 0, 0);
    font: 17px 'Montserrat', 'sans-serif';

    .shape-1 {
        position: absolute;
        top: -8em;
        left: -10em;
        z-index: 1;

        svg {
            height: 480px;
            width: 330px;
        }
    }

    .logo-text {
        z-index: 10;

        a {
            text-decoration: none;
            font-size: 28px;
            font-weight: bold;
            color: rgb(84, 141, 179);

            img {
                height: 45px;
                width: 45px;
                margin-bottom: 10px;
            }
        }
    }

    .nav-links {
        display: flex;
        align-items: flex-start;

        .nav-link-spl {
            display: flex;
            flex-direction: column;
            position: relative;

            p {
                cursor: pointer;
                margin: 0;
                padding: 0.5rem 1rem;
    
                &:last-child {
                    cursor: pointer;
                }
    
                svg {
                    height: 20px;
                    transform: rotate(180deg);
                    transition: all 0.4s ease-out;
                }
            }

            .sub-nav--links,
            .sub-nav--links--1,
            .sub-nav--links--2,
            .sub-nav--links--4 {
                display: none;
                flex-direction: column;
                position: absolute;
                top: 100%;
                left: 10%;
                padding: 0.3em 1em;
                border-radius: 5px;
                box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.16);
                background-color: antiquewhite;
            }

            .is-sub--open {
                display: flex;
                z-index: 30;
            }

            .is-sub--open + p svg {
                transform: rotate(0deg);
                transition: all 0.3s ease-in;
            }
        }

        .nav-link {
            cursor: pointer;
            text-decoration: none;
            color: #707070;
            margin-right: 20px;

            &:last-of-type {
                margin-right: 0;
            }

            &:hover {
                color: #000000;
                transition: all 0.3s ease;
            }
        }

        .nav-bold {
            font-weight: bold;
            color: #000000;
        }

        .nav-inv {
            color: #ffffff;
            background-color: #E88224;
            border-radius: 1000px;
        }
    }

    .mob-nav {
        display: none;
        height: 40px;
        width: 40px;
        background-color: #FFDE7D;
        border-radius: 50%;

        .ham-bar,
        .ham-bar::before,
        .ham-bar::after {
            content: '';
            display: block;
            height: 3px;
            width: 20px;
            background-color: rgba(255, 255, 255, 0.7);
            margin: 0;
            transition: all 0.3s ease;
        }

        .ham-bar::before {
            position: relative;
            top: -6px;
            transition: all 0.3s ease;
        }

        .ham-bar::after {
            position: relative;
            top: 3px;
            transition: all 0.3s ease;
        }
    }

    .mob-links {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: absolute;
        top: 13%;
        width: 90%;
        min-height: 0;
        border-radius: 3px;
        background-color: #fff;
        transform: translateY(-175%);
        box-shadow: 0px 3px 20px rgba(17, 153, 158, 0.41);
        z-index: 20;
        transition: transform 0.3s ease;

        .mob-link {
            padding: 0.5em 1em;
            color: #707070;
        }

        .mob-bold {
            font-weight: bold;
            color: #E88224;
        }

        .mob-inv {
            font-weight: bold;
            color: #0B6FB1;
        }

        .mob-link-spl {
            .sub-nav--links {
                display: none;
                flex-direction: column;
                position: relative;
                top: 100%;
                padding: 0.3em 1em;
                border-radius: 5px;
                box-shadow: 0px 2px 2px rgba(0, 0, 0, 0.16);
                background-color: antiquewhite;
            }
            
            .is-sub--open {
                display: flex;
                z-index: 30;
            }

            .is-sub--open + p svg {
                transform: rotate(0deg);
                transition: all 0.3s ease-in;
            }
        }
    }

    .is-open {
        .ham-bar::before {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .ham-bar {
            transform: rotate(-45deg);
            transition: all 0.3s ease;
        }

        .ham-bar::after {
            top: -3px;
            transform: rotate(90deg);
            transition: all 0.3s ease;
        }

        & + .mob-links {
            transform: translateY(0%);
            transition: transform 0.3s ease;
        }
    }
}

@media (max-width: 440px) {
    .navBox {
        align-items: center;

        .shape-1 {
            display: none;
        }

        .nav-links {
            display: none;
        }

        .mob-nav {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}