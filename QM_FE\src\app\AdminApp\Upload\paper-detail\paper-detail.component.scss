.main-wrap {
  display: flex;
  flex-direction: column;
  .Header-class-font{
    padding-top: 0.6%;
    font: normal 20px "Montserrat", sans-serif;
  }

  .paper-detail {
    display: flex;
    flex-direction: column;
    box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);
    width: 98.5%;

    .Paper_header{
      width: 100%;
      background-color: #f7f7f7;
      margin-right: auto;
      // margin-bottom: 10px;
      padding: 10px;
      box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      span{
        padding-top: 0.6%;
        font: normal 20px "Montserrat", sans-serif;
      }
    }
    form {
      display: flex;
      flex-direction: column;
      align-items: center;
      
    
      .elem-custom-cta {
        display: flex;
        align-items: center;
      }
    
      .form-row--1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        width: 100%;
        // margin-top: 1em;
    
        .form-elem {
          display: flex;
        flex-direction: column;
          width: 98%;
          padding: 1%;
        }
      }
      .Note-Detail-Top{
        display: flex;
        flex-direction: row;
        margin-top: 1em;
        width: 100%;
        .form-elem {
          width: 98%;
          padding: 1%;
        }
      }
    
      input,
      select {
        display: block;
        height: 50px;
        width: 100%;
        padding: 3px 5px;
        border: solid 1.5px #707070;
        border-radius: 5px;
        transition: all 0.3s ease;
    
        &:focus {
          border: solid 1.5px #0b6fb1;
          transition: all 0.3s ease;
        }
    
        &:focus + .placeholder-text {
          top: -75px;
          font-size: 13px;
          transition: all 0.3s ease;
        }
      }
    
      .placeholder-text {
        position: relative;
        top: -56px;
        left: 10px;
        padding: 3px;
        font-size: 17px;
        background-color: #fff;
        transition: all 0.4s ease;
      }
    

    
      .clear-btn {
        background-color: #f25252;
      }
      .img-updl--btn1 {
        border: none;
        background-color: rgba(0, 0, 0, 0);
        justify-self: start;
        margin-left: 0;
        img {
          height: 40px;
          width: 40px;
        }
      }
      #passwordHelpBlock,
      .form-error--text {
        font-size: 80%;
        color: #dc3545;
      }
    
      .is-inv {
        background-color: #dc3545;
      }
    
      .hidden-xs-up {
        display: none;
      }
    
      .cntr {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        text-align: center;
      }
    }
  }

  .question-detail {
    .question_header {
      display: flex;
      flex-direction: column;
      // box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);
      width: 100%;
      background: aliceblue;  
      border-bottom-right-radius: 5px; 
      border-bottom-left-radius: 5px;

      img {
        height: 40px;
        width: 40px;
        margin-left: 1rem;
        cursor: pointer;
      }
    }
  }

  .custom-btn {
    // width: 100%;
    height: 40px;
    background-color: #17a2b8;
    border: none;
    border-radius: 4px;
    color: #fff;
    // margin-bottom: 1em;
    padding: 8px;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
  }

  .custom-btn:active:after {
    transition: 0s;
    opacity: 0.7;
    clip-path: circle(0% at 0% 0%);
  }

  .custom-btn::after {
    content: "";
    display: block;
    position: relative;
    top: -32px;
    left: -8px;
    height: 40px;
    width: 150px;
    background-color: #e88224;
    opacity: 0;
    clip-path: circle(150% at 0% 0%);
    transition: all 0.4s ease-in;
  }

  .preview-btn {
    background-color: #af7ac5;
    margin-right: 1em;
  }

  .btn-green {
    margin-right: 1em;
    color: #0B5345;
    background-color: #58d68d;
  }
}
.img-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;

  button {
    border: none;
    border-radius: 50%;
    margin-left: 0.5em;
    height: 3em;
    width: 3em;

    img {
      height: 100%;
      width: 100%;
    }
  }
}
