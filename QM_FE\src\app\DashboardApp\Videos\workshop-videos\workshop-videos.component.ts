import { Component, OnInit } from '@angular/core';
import { StudentVideoList } from 'src/app/Models/Streaming/StudentVideoList';
import { Router, ActivatedRoute } from '@angular/router';
import { StreamingService } from 'src/app/Services/streaming.service';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-workshop-videos',
  templateUrl: './workshop-videos.component.html',
  styleUrls: ['./workshop-videos.component.scss']
})
export class WorkshopVideosComponent implements OnInit {

  public showIndicator = false;

  public videoList: StudentVideoList[] = [];
  public allData = [];
  public mainGroups = [];
  public subGroups = [];
  public subSubGroups = [];
  public selectedGroup = '';
  public selectedSubGroup = '';
  public selectedSubSubGroup = '';
  public noDataTextShow = false;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public streamingService: StreamingService,
              public _sanitizationService: DomSanitizer) { }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.streamingService.getWorkshopVideoList().subscribe(response => {

      const resp = JSON.parse(JSON.stringify(response));
      this.allData = resp;

      resp.groups.forEach(indGroup => {
        that.mainGroups.push(indGroup.name);
      });

      that.showIndicator = false;
    }, error => {

    });
  }

  selectGroup(groupName: string) {

    const that = this;

    this.selectedGroup = groupName;
    this.subGroups = [];
    this.subSubGroups = [];
    JSON.parse(JSON.stringify(this.allData)).groups.forEach(group => {

      if (group.name === groupName) {
        group.sub_groups.forEach(subGroup => {
          that.subGroups.push(subGroup.name);
        });
      }
    });
  }

  selectSubGroup(subGroupName: string) {

    const that = this;
    this.selectedSubGroup = subGroupName;
    this.subSubGroups = [];
    JSON.parse(JSON.stringify(this.allData)).groups.forEach(group => {

      if (group.name === that.selectedGroup) {
        group.sub_groups.forEach(subGroup => {

          if (subGroup.name === subGroupName) {
            if (subGroup.sub_sub_groups) {
              subGroup.sub_sub_groups.forEach(subSubGroup => {
                that.subSubGroups.push(subSubGroup.name);
              });
            } else if (subGroup.videos) {
              that.videoList = subGroup.videos;
            }
          }
        });
      }
    });

    if (that.videoList.length === 0) {
      this.noDataTextShow = true;
    } else {

      that.videoList.forEach(video => {
        video.stream_link = that._sanitizationService.bypassSecurityTrustResourceUrl(video.link);
      });

      this.noDataTextShow = false;
    }
  }

  selectSubSubGroup(subSubGroupName: string) {

    const that = this;
    this.selectedSubSubGroup = subSubGroupName;
    JSON.parse(JSON.stringify(this.allData)).groups.forEach(group => {

      if (group.name === that.selectedGroup) {
        group.sub_groups.forEach(subGroup => {

          if (subGroup.name === that.selectedSubGroup) {
            if (subGroup.sub_sub_groups) {
              subGroup.sub_sub_groups.forEach(subSubGroup => {

                if (subSubGroup.name === subSubGroupName) {
                  if (subSubGroup.videos) {
                    that.videoList = subSubGroup.videos;
                  }
                }
              });
            } else if (subGroup.videos) {
              that.videoList = subGroup.videos;
            }
          }
        });
      }
    });

    if (that.videoList.length === 0) {
      this.noDataTextShow = true;
    } else {
      this.noDataTextShow = false;

      that.videoList.forEach(video => {
        video.stream_link = that._sanitizationService.bypassSecurityTrustResourceUrl(video.link);
      });
    }
  }

  startStream(videoId: string, videoType: number) {

    this.router.navigate(['stream', videoId, videoType], { relativeTo: this.activatedRoute });
  }
}
