<div class="video-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="player">
    <div *ngIf="streamType !== 5 && streamType !== 6">
      <vg-player (onPlayerReady)="onPlayerReady($event)">
        <vg-controls [vgAutohide]="true" [vgAutohideTime]="1.5">
          <vg-play-pause></vg-play-pause>
          <vg-playback-button></vg-playback-button>

          <vg-time-display vgProperty="current" vgFormat="mm:ss"></vg-time-display>

          <vg-scrub-bar>
            <vg-scrub-bar-current-time style="height: 7px;"></vg-scrub-bar-current-time>
            <vg-scrub-bar-buffering-time style="border: 1px solid #ADADAD; border-radius: 3px; height: 7px;">
            </vg-scrub-bar-buffering-time>
          </vg-scrub-bar>

          <vg-time-display vgProperty="total" vgFormat="mm:ss"></vg-time-display>

          <vg-mute></vg-mute>
          <vg-volume></vg-volume>

          <vg-quality-selector [bitrates]="bitRates" (onBitrateChange)="setRates($event)">
          </vg-quality-selector>

          <vg-fullscreen></vg-fullscreen>
        </vg-controls>
        <vg-buffering vgFor="singleVideo"></vg-buffering>
        <div class="left-skip" [hidden]="playing" (click)="skipBack()">
          <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px" y="0px" viewBox="0 0 231.7 208.1" style="enable-background:new 0 0 231.7 208.1;" xml:space="preserve">
            <style type="text/css">
              .st0 {
                fill: #FFFFFF;
              }

              .st1 {
                fill: #FFFFFF;
                stroke: #FFFFFF;
                stroke-width: 3;
                stroke-miterlimit: 10;
              }

              .st2 {
                font-family: 'MyriadPro-Regular';
              }

              .st3 {
                font-size: 74.8002px;
              }
            </style>
            <g id="XMLID_1_">
              <g id="XMLID_5_">
                <path id="XMLID_2_" class="st0" d="M31,130.2c1.4,1.7,3.7,1.7,5.1,0l30.3-37.1c1.4-1.7,0.7-3.1-1.5-3.1H48.9
        c-2.2,0-3.7-1.8-3.3-3.9c8.3-37.7,41.9-65.9,82-65.9c46.3,0,84,37.7,84,84c0,46.3-37.7,84-84,84c-5.6,0-10.1,4.5-10.1,10.1
        c0,5.6,4.5,10.1,10.1,10.1c57.4,0,104.1-46.7,104.1-104.1S185,0,127.6,0C76.4,0,33.7,37.2,25.1,86c-0.4,2.2-2.4,4-4.6,4H2.2
        c-2.2,0-2.9,1.4-1.5,3.1L31,130.2z" />
              </g>
              <text id="XMLID_8_" transform="matrix(1.1228 0 0 1 80.7514 130.0265)" class="st1 st2 st3">10</text>
            </g>
          </svg>
        </div>
        <div id="play-wrap" class="play-toggle" [hidden]="playing" (click)="togglePlay()">
          <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px" y="0px" viewBox="0 0 125.8 145.3" style="enable-background:new 0 0 125.8 145.3;" xml:space="preserve">
            <polygon id="XMLID_1_" points="0,0 125.8,72.7 0,145.3 " fill="#ADADAD" />
          </svg>
        </div>
        <div class="right-skip" [hidden]="playing" (click)="skipAhead()">
          <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px" y="0px" viewBox="0 0 225.7 208.1" style="enable-background:new 0 0 225.7 208.1;" xml:space="preserve">
            <style type="text/css">
              .st0 {
                fill: #FFFFFF;
              }

              .st1 {
                font-family: 'MyriadPro-Regular';
              }

              .st2 {
                font-size: 74.8002px;
              }
            </style>
            <g id="XMLID_6_">
              <path id="XMLID_7_" class="st0" d="M195.5,130.2c-1.4,1.7-3.6,1.7-5,0L161,93.1c-1.4-1.7-0.7-3.1,1.4-3.1h15.7
      c2.2,0,3.6-1.8,3.2-3.9c-8.1-37.7-40.8-65.9-79.9-65.9c-45.1,0-81.8,37.7-81.8,84c0,46.3,36.7,84,81.8,84c5.4,0,9.8,4.5,9.8,10.1
      c0,5.6-4.4,10.1-9.8,10.1C45.5,208.1,0,161.5,0,104.1S45.5,0,101.4,0c49.9,0,91.5,37.2,99.9,86c0.4,2.2,2.4,4,4.5,4h17.8
      c2.2,0,2.8,1.4,1.4,3.1L195.5,130.2z" />
            </g>
            <text id="XMLID_9_" transform="matrix(1.1228 0 0 1 54.7855 126.0267)" class="st0 st1 st2">10</text>
          </svg>
        </div>
        <video #media [vgMedia]="media" id="singleVideo" preload="auto" crossorigin="anonymous" width="auto"
          #vgHls="vgHls" [vgHls]="stream.source" type="video/MP2T" (onGetBitrates)="getRates($event)">
        </video>
      </vg-player>
    </div>
    <div *ngIf="streamType == 5 || streamType == 6">
      <iframe [src]="vimeoLink" width="100%" height="600" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen></iframe>
    </div>
  </div>

  <div class="player-legend">
    <div class="ratings">
      <p>Overall Rating: </p>
      <rating [(ngModel)]="displayRating" [max]="max" [readonly]="isReadonly" [customTemplate]="tt"></rating>
      <p>{{rating}}/5</p>
    </div>
    <div class="ratings-mine">
      <p>My Rating: </p>
      <rating [(ngModel)]="myRating" [max]="max" [readonly]="isReadonly" [customTemplate]="tt"></rating>
      <p>{{myRatingText}}/5</p>
    </div>
    <div class="user-comment-section">
      <h3>{{ videoCommentList.length }} Comments</h3>
      <div class="user-new-comment">
        <div class="image-div">
          <img class="user-image" src="{{userImage}}" />
        </div>
        <div class="comment-div">
          <textarea id="new-comment-area" ng-trim="false" [(ngModel)]="myComment" class="user-comment" placeholder="Add your comments.."></textarea>
          <div class="buttons-div">
            <button title="Clear" (click)="fnOnClickClearComment()">Clear</button>
            <button title="Confrim" (click)="fnOnClickConfrimComment(myComment)">Comment</button>
          </div>
        </div>
      </div>
    </div>

    <div class="video-comments" *ngFor="let cmt of videoCommentList; let i = index">
      <div class="image-div">
        <img class="user-image" src="{{cmt.user_avatar}}" />
      </div>
      <div class="comment-text-div">
        <p class="comment-heder">{{cmt.created_by}} - {{cmt.created_at}}</p>
        <span class="comments">{{cmt.text}}</span>
        <div class="comment-reply-section">
          <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseVideoList">REPLY</button>
          <button (click)="isCollapsedReplies[i] = !isCollapsedReplies[i]" [class.is-open]="!isCollapsedReplies[i]"
            [attr.aria-expanded]="!isCollapsedReplies" aria-controls="collapseVideoList">View {{cmt.replies.length}}
            Reply</button>
          <!-- <p>View {{cmt.replies.length}} Reply </p> -->
        </div>
        <div class="user-comment-section" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div class="comment-div">
            <textarea [(ngModel)]="commentReply[i]" class="user-comment user-comment--reply" placeholder="Add your comments.."></textarea>
            <div class="buttons-div">
              <button title="Clear" (click)="fnOnClickClearReply(i)">Cancel</button>
              <button title="Confrim"
                (click)="fnOnClickConfrimCommentReply(cmt.comment_id,commentReply[i], i)">Reply</button>
            </div>
          </div>
        </div>
        <div [collapse]="isCollapsedReplies[i]" [isAnimated]="true">
          <div class="reply_section_ExtraCSS"
            *ngFor="let replies of videoCommentList[i].replies; let j = index">
            <div class="image-div">
              <img class="user-image" src="{{replies.user_avatar}}" />
            </div>
            <div class="comment-text-div">
              <p class="comment-heder">{{replies.created_by}} - {{replies.created_at}}</p>
              <span class="comments">{{replies.text}}</span>
            </div>
          </div>
        </div>


      </div>

    </div>
  </div>


  <!--<h5>Using the player</h5> style="border-radius: 50%; border-block-color: black;
    <p>Click the play button to begin streaming.</p>
    <p>Press the Spacebar to play/pause the video.</p>
    <p>Press the Left Arrow to skip the video 10 seconds backwards.</p>
    <p>Press the Right Arrow to skip the video 10 seconds forwards.</p>
    <p>Press F to toggle in and out of fullscreen.</p>-->

</div>

<ng-template #ratingTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left text-info">Got a moment?</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>Please Rate your viewing experience.</p>
    <div class="ratings">
      <rating [(ngModel)]="userRating" [max]="max" [readonly]="false" [customTemplate]="tt"></rating>
      <button (click)="fnUpdateUserRating(userRating)" class="ratingSubmitButton">Submit</button>
    </div>
  </div>
</ng-template>

<ng-template #messageTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left text-info">{{messageHeader}}</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="messageModalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>{{message}}</p>
  </div>
</ng-template>

<ng-template #tt let-i="index" let-v="value">
  <button class="btn btn-{{i < v ? 'warning' : 'default'}}">
    {{i < v ? '&#9733;' : '&#9734;'}}
  </button>
</ng-template>


<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>