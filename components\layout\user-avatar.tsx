import { AvatarProps } from '@radix-ui/react-avatar'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { User } from '@/types/user'
import { UserIcon } from 'lucide-react'

interface UserAvatarProps extends AvatarProps {
  user?: Pick<User, 'image' | 'name'> | null
}

export function UserAvatar({ user, ...props }: UserAvatarProps) {
  return (
    <Avatar {...props}>
      {user?.image ? (
        <AvatarImage alt="Picture" src={user.image} />
      ) : (
        <AvatarFallback>
          <span className="sr-only">{user?.name || 'Anonymous'}</span>
          <UserIcon className="h-6 w-6" />
        </AvatarFallback>
      )}
    </Avatar>
  )
}
