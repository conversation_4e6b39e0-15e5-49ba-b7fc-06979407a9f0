{"name": "quant-masters-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3014", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --config \".prettierrc\"  \"./**/*.tsx\"  \"./**/*.ts\" --write"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "axios": "^1.8.3", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "js-cookie": "^3.0.5", "katex": "^0.16.22", "lucide-react": "^0.477.0", "next": "15.2.1", "next-themes": "^0.4.6", "prettier": "^3.5.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-math": "^6.0.0", "sonner": "^2.0.5", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.1", "tailwindcss": "^4", "typescript": "^5"}}