.workshops-wrap {
  width: 98.5%;
  background-color: #f7f7f7;
  margin-right: auto;
  padding: 15px;
  box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);

  .sect-1 {
    width: 100%;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;

    .info-box {
      button {
        position: relative;
        z-index: 8;
      }
    }

    .export-opts {
      position: absolute;
      z-index: 1;
      display: grid;
      place-items: center;
      padding: 2rem 1rem 0.5rem 1rem;
      background-color: #FCF3CF;
      border-radius: 3px;
      box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);

      button {
        width: 100px;
        margin-bottom: 1em;
        background-color: #DC7633;

        &:last-of-type {
          color: #1B2631;
          background-color: #48C9B0;
          margin-bottom: 0;
        }
      }
    }

    // .info-box {
    //   display: flex;
    //   flex-direction: column;
    //   align-items: center;
    //   justify-content: center;
    //   background-color: #fff;
    //   padding: 15px;
    //   border-radius: 10px;
    //   box-shadow: 3px 2px 9px rgba(0, 0, 0, 0.16);
    //   transition: all 0.1s ease;

    //   form {
    //     display: flex;
    //     flex-direction: column;
    //     align-items: flex-start;
    //     justify-content: center;

    //     .form-fields {
    //       display: flex;

    //       .clr-btn {
    //         background-color: #AEB6BF;
    //         margin-left: 15px;
    //         color: #1B2631;
    //       }
    //     }

    //     .form-elem {
    //       display: flex;
    //       flex-direction: column;
    //       align-items: flex-start;
    //       justify-content: center;
    //       margin-left: 20px;

    //       .basic-search {
    //         width: 300px;
    //         height: 35px
    //       }

    //       label {
    //         margin-bottom: 0;
    //       }

    //       &:first-of-type {
    //         margin-left: 0;
    //       }
    //     }

    //     &:first-of-type {
    //       margin-bottom: 2em;
    //     }
    //   }

    //   button {
    //     margin-top: 1em;
    //   }

    //   .icon-wrap {
    //     cursor: pointer;
    //     height: 35px;
    //     width: 35px;
    //     transition: all 0.4s ease-in;
    //     border-radius: 5px;

    //     svg {
    //       height: 100%;
    //       width: 100%;
    //     }

    //     &:hover {
    //       background-color: rgb(58, 60, 61);
    //       transition: all 0.1s ease-in;
    //     }
    //   }

    //   .ops-box {
    //     width: auto;
    //   }

    //   &:first-of-type {
    //     align-items: flex-start;
    //   }
    // }

    // .additional-controls-box {
    //   display: flex;
    //   align-items: flex-end;
    //   width: 40%;
    //   height: 100px;

    //   .grant-btn {
    //     background-color: #3949AB;
    //   }
    // }

    // .action-icon {
    //   position: relative;
    //   bottom: 6px;
    //   left: 90%;
    //   height: 30px;
    //   width: 30px;
    //   background-color: #fff;
    //   border-radius: 50%;
    //   box-shadow: 3px 3px 11px rgba(0, 0, 0, 0.36);
    //   display: flex;
    //   justify-content: center; 
    //   align-items: center;
    //   transform: scale(1);
    //   transition: all 0.2s ease-in;

    //   &:hover {
    //     transform: scale(1.05);
    //     transition: all 0.1s ease;
    //   }

    //   svg {
    //     height: 25px;
    //     width: 25px;
    //   }
    // }

    // .selected {
    //   background-color: #fff;
    //   transition: all 0.3s ease;
    // }
  }

  .sect-2 {
    width: 100%;
    margin: auto;
    margin-top: 2em;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 3px 2px 9px rgba(0, 0, 0, 0.16);

    .workshops-headers,
    .workshops-data {
      display: grid;
      grid-template-columns: 0.4fr 1fr 2fr 1fr 1fr 0.3fr 1fr 1fr 1fr 1fr;

      h6,
      p,
      input {
        padding: 15px;
        margin: 0;
      }

      h6 {
        font-size: 16px;
      }

      p {
        font-size: 13px;
      }

      input {
        align-self: center;
        justify-self: center;
      }
    }

    .workshops-headers {
      background-color: #48C9B0;
      border-top-right-radius: 10px;
      border-top-left-radius: 10px;
      color: #1B2631;
    }
    
    .workshops-data:last-of-type {
      margin-bottom: 1em;
    }

    .workshops-data:nth-of-type(2n) {
      background-color: #E8F8F5;
    }

    /deep/ .pagination {
      padding-left: 15px;
      padding-bottom: 1em;

      .page-item .disabled {
        color: #2E4053;
      }

      .page-link {
        color: #1B2631;
      }

      .active .page-link {
        background-color: #48C9B0;
        border-color: #1B2631;
        color: #fff;
      }
    }
  }
}

// .sorter-model {
//   width: 300px;

//   .form-row {
//     display: flex;
//     flex-direction: column;
//     align-items: flex-start;
//     justify-content: center;
//     margin-bottom: 1em;

//     &:last-of-type {
//       margin-top: 1.5em;
//       margin-bottom: 0;

//       button {
//         border: none;
//         padding: 5px 15px;
//         background-color: #3E5872;
//         color: #fff;
//         box-shadow: 3px 2px 6px rgba(0, 0, 0, 0.36);
//         border-radius: 10px;
//         transition: all 0.2s ease-out;

//         &:hover {
//           background-color: #2B3D4F;
//           box-shadow: 3px 2px 9px rgba(0, 0, 0, 0.56);
//           transition: all 0.1s ease-in;
//         }
//       }
//     }

//     .form-elem {
//       width: 100%;
//       height: 30px;
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       padding: 3px 5px;
//       cursor: pointer;

//       label {
//         cursor: pointer;
//         margin-bottom: 0;
//       }

//       @supports(-webkit-appearance: none) or (-moz-appearance: none) {
//         input[type='radio'] {
//           --active: #2B3D4F;
//           --active-inner: #fff;
//           --focus: 2px rgba(39, 94, 254, .3);
//           --border: #BBC1E1;
//           --border-hover: #2B3D4F;
//           --background: #fff;
//           --disabled: #F6F8FF;
//           --disabled-inner: #E1E6F9;
//           -webkit-appearance: none;
//           -moz-appearance: none;
//           height: 21px;
//           outline: none;
//           display: inline-block;
//           vertical-align: top;
//           position: relative;
//           margin: 0;
//           cursor: pointer;
//           border: 1px solid var(--bc, var(--border));
//           background: var(--b, var(--background));
//           transition: background .2s, border-color .2s, box-shadow .2s;
//           &:after {
//             content: '';
//             display: block;
//             left: 0;
//             top: 0;
//             position: absolute;
//             transition: transform var(--d-t, .2s) var(--d-t-e, ease), opacity var(--d-o, .2s);
//           }
//           &:checked {
//             --b: var(--active);
//             --bc: var(--active);
//             --d-o: .2s;
//             --d-t: .6s;
//             --d-t-e: cubic-bezier(.2, .85, .32, 1.2);
//           }
//           &:disabled {
//             --b: var(--disabled);
//             cursor: not-allowed;
//             opacity: .9;
//             &:checked {
//               --b: var(--disabled-inner);
//               --bc: var(--border);
//             }
//             & + label {
//               cursor: not-allowed;
//             }
//           }
//           &:hover {
//             &:not(:checked) {
//               &:not(:disabled) {
//                 --bc: var(--border-hover);
//               }
//             }
//           }
//           &:focus {
//             box-shadow: 0 0 0 var(--focus);
//           }
//           &:not(.switch) {
//             width: 21px;
//             &:after {
//               opacity: var(--o, 0);
//             }
//             &:checked {
//               --o: 1;
//             }
//           }
//           & + label {
//             font-size: 14px;
//             line-height: 21px;
//             display: inline-block;
//             vertical-align: top;
//             cursor: pointer;
//             margin-left: 4px;
//           }
//         }
//       }

//       input[type='radio'] {
//         border-radius: 50%;
//         &:after {
//           width: 19px;
//           height: 19px;
//           border-radius: 50%;
//           background: var(--active-inner);
//           opacity: 0;
//           transform: scale(var(--s, .7));
//         }
//         &:checked {
//           --s: .5;
//         }
//       }

//       &:hover {
//         background-color: #eee;
//       }

//       &:active {
//         background-color: #aaa;
//       }
//     }
//   }
// }

.custom-btn {
  height: 40px;
  width: 130px;
  font-size: 17px;
  font-weight: normal;
  border: none;
  border-radius: 4px;
  color: #fff;
  background-color: #Fe9C12;
  padding: 5px 10px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

.custom-btn:active:after {
  transition: 0s;
  opacity: 0.7;
  clip-path: circle(0% at 0% 0%);
}

.custom-btn::after {
  content: "";
  display: block;
  position: relative;
  top: -30px;
  height: 40px;
  background-color: #9FA8DA;
  opacity: 0;
  clip-path: circle(150% at 0% 0%);
  transition: all 0.4s ease-in;
}

@media (max-width: 440px) {
  .workshops-wrap {
    width: 98%;

    .sect-2 {

      .workshops-headers,
      .workshops-data {
        grid-template-columns: 1fr;
      }

      .workshops-headers {
        h6 {
          display: none;

          &:first-of-type {
            display: flex;
          }
        }
      }

      .workshops-data {
        input {
          justify-self: start;
          margin-left: 15px;
          margin-top: 5px;
        }

        p {
          padding-top: 5px;
          padding-bottom: 5px;
        }
      }
    }
  }
}