import { Component, OnInit, ViewChild, HostListener, Input, OnDestroy, TemplateRef } from '@angular/core';
import { ActivatedRoute, Router, NavigationEnd, NavigationStart, RouterEvent } from '@angular/router';
import { BitrateOption, VgAPI, VgFullscreenAPI, VgEvents } from 'videogular2/compiled/core';
import { VgHLS } from 'videogular2/compiled/src/streaming/vg-hls/vg-hls';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';

import { StreamingService } from '../../../Services/streaming.service';
import { UserService } from '../../../Services/user.service';
import { TrackingService } from '../../../Services/tracking.service';
import { VideoUsage } from '../../../Models/Tracking/VideoUsage';


declare let gtag: Function;

export interface IMediaStream {
  type: 'hls';
  source: string;
  label: string;
  token?: string;
}

@Component({
  selector: 'app-video-stream',
  templateUrl: './video-stream.component.html',
  styleUrls: ['./video-stream.component.scss']
})
export class VideoStreamComponent implements OnInit, OnDestroy {

  @ViewChild('ratingTemplate') public template: TemplateRef<any>;
  @ViewChild('messageTemplate') public messageTemplate: TemplateRef<any>;
  @ViewChild(VgHLS) vgHls: VgHLS;
  // @ViewChild('comment_Reply') public comment_Reply: TemplateRef<any>; // Added by gururaj

  public videoId: string;
  public streamType: number;
  public playing: boolean;

  public max = 5;
  public rate = 5;
  public isReadonly = true;

  public api: VgAPI;
  public fullscreenApi: VgFullscreenAPI;
  public stream: IMediaStream = {
    type: 'hls',
    label: 'HLS: Streaming',
    source: ''
  };
  public playerEvent: VgEvents;

  public vimeoLink: SafeResourceUrl;

  private totalWatchTime = 0;
  private lastWatchTime = 0;

  public showIndicator = false;

  public bitRates: BitrateOption[];
  public bitRateLabels = ['Auto', 'Low', 'Medium', 'High'];

  public rating: string;
  public displayRating: number;
  public userRating: number;

  public myRatingText = '';
  public myRating = 0;

  public myComment: string;
  public myCommentBody = { 'text': '', 'email': '' };
  public myReplyComment: string;
  public myCommentReplyBody = { 'text': '', 'email': '' };

  public modalRef: BsModalRef;
  public messageModalRef: BsModalRef;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };

  public message: string; // Added by Gururaj.
  public messageHeader: string;
  public videoCommentList = [];
  public userImage: string;
  public isCollapsed = [];
  public isCollapsedReplies = [];
  public commentReply = [];
  public sAWSSignledURl = '';
  public oUserAvatar = {};
  constructor(public router: Router,
    public route: ActivatedRoute,
    public streamingService: StreamingService,
    public trackingService: TrackingService,
    public modalService: BsModalService,
    public userService: UserService,
    public _sanitizationService: DomSanitizer) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }


  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.videoId = params.get('video_id');
      this.streamType = parseInt(params.get('stream_type'), 10);
      this.rating = parseFloat(params.get('rating')).toFixed(1);
      this.displayRating = parseInt(params.get('rating'), 10);
    });

    const that = this;

    let prevSecTick = 0,
      currentSecTick = 0;

    const videoElem = document.getElementById('singleVideo');

    if (videoElem) {
      videoElem.addEventListener(VgEvents.VG_TIME_UPDATE, () => {
        currentSecTick = parseInt(that.api.currentTime, 10);

        if (prevSecTick === currentSecTick - 1) {
          that.totalWatchTime++;
          prevSecTick = currentSecTick;
          that.lastWatchTime = that.api.currentTime;
        } else {
          prevSecTick = currentSecTick;
          that.lastWatchTime = that.api.currentTime;
        }
      });
    }

    const email = sessionStorage.getItem('QMail');
    this.streamingService.getStudentVideoRating(this.videoId, email).subscribe((response) => {
      const respJson = JSON.parse(JSON.stringify(response));

      this.myRatingText = respJson.rating_given;
      this.myRating = parseInt(respJson.rating_given, 10);
    }, error => {

    });


    // const that = this;
    this.showIndicator = true;
    if (this.streamType === 1) {
      this.streamingService.getVideoLink(this.videoId).subscribe(response => {
        that.showIndicator = false;

        that.api.pause();
        that.stream.source = response;
        that.api.play();
        that.playing = false;
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.streamType === 2) {
      this.streamingService.getTestimonyVideoLink(this.videoId).subscribe(response => {
        that.showIndicator = false;

        that.api.pause();
        that.stream.source = response;
        that.api.play();
        that.playing = false;
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.streamType === 3 || this.streamType === 6) {
      this.streamingService.getReviewVideoLink(this.videoId).subscribe(response => {
        that.showIndicator = false;

        if (this.streamType !== 6) {
          that.api.pause();
          that.stream.source = response;
          that.api.play();
          that.playing = false;
        } else {
          that.vimeoLink = that._sanitizationService.bypassSecurityTrustResourceUrl('https://www.youtube.com/embed/' + response);
        }
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.streamType === 4) {
      this.streamingService.getTrialVideoLink(this.videoId).subscribe(response => {
        that.showIndicator = false;

        that.api.pause();
        that.stream.source = response;
        that.api.play();
        that.playing = false;
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.streamType === 5) {
      this.streamingService.getWorkshopVideoLink(this.videoId).subscribe(response => {
        that.showIndicator = false;

        that.vimeoLink = that._sanitizationService.bypassSecurityTrustResourceUrl(response);

        // that.api.pause();
        // that.stream.source = response;
        // that.api.play();
        // that.playing = false;
      }, error => {
        this.showIndicator = false;
      });
    }

    // To get the current user profile avatar
    this.fnGetUserAvatar(email, 'CurrentUser', '', '', function () {
      that.showIndicator = true;
      // To get the list of comments for this note
      that.fnGetComments(that.videoId);
    });

    // this.userService.getUserProfileAvatar(email).subscribe(response2 => {
    //   const resp = JSON.parse(JSON.stringify(response2));
    //   this.userImage = resp.path;
    //   this.sAWSSignledURl = resp.path.substr(resp.path.indexOf('?'));
    //   // To get the list of comments for this video
    //   this.fnGetComments(this.videoId);
    // }, error => {

    // });

  }
  fnGetUserAvatar(mailId, sType, iIndex, jIndex, fnSuccess) {
    this.showIndicator = true;
    this.userService.getUserProfileAvatar(mailId).subscribe(response2 => {
      const resp = JSON.parse(JSON.stringify(response2));
      if (sType === 'CurrentUser') {
        this.userImage = resp.path;
        this.oUserAvatar[mailId] = resp.path;
        return fnSuccess();
      } else if (sType === 'OtherUser') {
        this.oUserAvatar[mailId] = resp.path;
        if ((iIndex || iIndex === 0) && (jIndex || jIndex === 0)) {
          this.videoCommentList[iIndex].replies[jIndex].user_avatar = resp.path;
        } else if ((iIndex || iIndex === 0) && jIndex === '') {
          this.videoCommentList[iIndex].user_avatar = resp.path;
        }
        // return resp.path;
      }
    }, error => {
      this.showIndicator = false;
    });
  }

  @HostListener('document:keyup', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {

    event.preventDefault();

    const replyFields = document.querySelectorAll('.user-comment--reply');
    let isReply = false;

    for (let index = 0; index < replyFields.length; index++) {
      const element = replyFields[index];

      if (element === event.target) {
        isReply = true;
        break;
      }
    }

    if (event.target !== document.getElementById('new-comment-area') && !isReply) {
      switch (event.key) {
        case 'ArrowLeft':
          this.skipBack();
          break;
        case 'ArrowRight':
          this.skipAhead();
          break;
        case ' ':
          this.togglePlay();
          break;
        case 'f' || 'F':
          this.fullscreenApi.toggleFullscreen();
          break;
      }
    }
  }

  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    const that = this;

    let playTOut: any;

    if (event.target !== document.getElementById('play-wrap') &&
      event.target !== document.getElementById('singleVideo')) {
      playTOut = setTimeout(() => {
        that.playing = true;
      }, 5000);
    } else {
      this.playing = false;
      clearTimeout(playTOut);
    }
  }

  onPlayerReady(api: VgAPI) {
    this.api = api;
    this.fullscreenApi = api.fsAPI;
  }

  getRates(option: BitrateOption[]) {
    this.bitRates = [];

    for (const idx in option) {
      if (Object.prototype.hasOwnProperty.call(option, idx)) {
        option[idx].label = this.bitRateLabels[idx];

        this.bitRates.push(option[idx]);
      }
    }
  }

  setRates(option: BitrateOption) {
    this.vgHls.setBitrate(option);
  }

  skipBack() {
    this.api.currentTime -= 10;
    this.api.play();
  }

  skipAhead() {
    this.api.currentTime += 10;
    this.api.play();
  }

  togglePlay() {
    if (this.api.state === 'playing') {
      this.api.pause();
    } else {
      this.api.play();
    }
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  openMessageModal(ShowMessage: TemplateRef<any>) {
    this.messageModalRef = this.modalService.show(ShowMessage, this.config);
  }


  fnUpdateUserRating(userRating) {
    if (userRating) {
      this.streamingService.postVideoRating(sessionStorage.getItem('QMail'), this.videoId, userRating).subscribe(response => {
        this.modalRef.hide();
        this.showIndicator = false;
        this.messageHeader = 'Done';
        this.message = 'Thank you for your valuable Feedback..!';
        this.openMessageModal(this.messageTemplate);
      }, error => {
        this.showIndicator = false;
        this.messageHeader = 'Failed';
        this.message = 'We Sorry, Failed to added your valuable ratings..!';
        this.openMessageModal(this.messageTemplate);
      });
    } else {
      this.messageHeader = 'Remember';
      this.message = 'Please provide your rating before submit..!';
      this.openMessageModal(this.messageTemplate);
    }
  }

  fnOnClickConfrimComment(comment) {
    if (comment) {
      this.myCommentBody.text = comment;
      this.myCommentBody.email = sessionStorage.getItem('QMail');
      this.showIndicator = true;
      this.streamingService.fnPostVideoComment(this.videoId, this.myCommentBody).subscribe(response => {
        // this.showIndicator = false;
        this.messageHeader = 'Done';
        this.message = 'Thank you for your valuable Feedback..!';
        this.openMessageModal(this.messageTemplate);
        this.myComment = '';
        this.fnGetComments(this.videoId);
      }, error => {
        this.showIndicator = false;
        this.messageHeader = 'Failed';
        this.message = 'We Sorry, Failed to added your valuable comments..!';
        this.openMessageModal(this.messageTemplate);
      });
    } else {
      this.messageHeader = 'Remember';
      this.message = 'Please provide your comment before submit..!';
      this.openMessageModal(this.messageTemplate);
    }
  }

  fnOnClickClearComment() {
    this.myComment = '';
  }

  fnGetComments(videoId) {
    this.streamingService.getVideoCommentsList(this.videoId).subscribe(response => {
      const responseParsed = JSON.parse(JSON.stringify(response));
      this.videoCommentList = responseParsed;

      for (let i = 0; i < responseParsed.length; i++) {
        this.isCollapsed.push(true);
        const date = new Date(responseParsed[i].created_at).toDateString();
        this.videoCommentList[i].created_at = date;

        if (!responseParsed[i].user_avatar) {
          if (this.oUserAvatar[responseParsed[i].email]) {
            responseParsed[i].user_avatar = this.oUserAvatar[responseParsed[i].email];
          } else {
            this.fnGetUserAvatar(responseParsed[i].email, 'OtherUser', i, '', '');
          }
        } else {
          if (this.oUserAvatar[responseParsed[i].email]) {
            responseParsed[i].user_avatar = this.oUserAvatar[responseParsed[i].email];
          } else {
            this.fnGetUserAvatar(responseParsed[i].email, 'OtherUser', i, '', '');
          }
        }
        if (responseParsed[i].replies.length > 0) {
          for (let j = 0; j < responseParsed[i].replies.length; j++) {
            this.isCollapsedReplies.push(true);
            const replyDate = new Date(responseParsed[i].replies[j].created_at).toDateString();
            this.videoCommentList[i].replies[j].created_at = replyDate;

            if (!responseParsed[i].replies[j].user_avatar) {
              if (this.oUserAvatar[responseParsed[i].replies[j].email]) {
                responseParsed[i].replies[j].user_avatar = this.oUserAvatar[responseParsed[i].replies[j].email];
              } else {
                this.fnGetUserAvatar(responseParsed[i].replies[j].email, 'OtherUser', i, j, '');
              }
            } else {
              if (this.oUserAvatar[responseParsed[i].replies[j].email]) {
                responseParsed[i].replies[j].user_avatar = this.oUserAvatar[responseParsed[i].replies[j].email];
              } else {
                this.fnGetUserAvatar(responseParsed[i].replies[j].email, 'OtherUser', i, j, '');
              }
            }
          }
        }
      }

      this.showIndicator = false;
      this.videoCommentList = responseParsed;

      window.onkeydown = function (event) {
        let isReply = false;
        const replyFields = document.querySelectorAll('.user-comment--reply');
        for (let index = 0; index < replyFields.length; index++) {
          const element = replyFields[index];

          if (element === event.target) {
            isReply = true;
            break;
          }
        }

        if (event.target !== document.getElementById('new-comment-area') && !isReply) {
          return !(event.keyCode === 32);
        }
      };
    }, error => {
      this.showIndicator = false;
    });
  }

  fnOnClickConfrimCommentReply(commentId, comment, i) {
    this.myCommentReplyBody = { 'text': '', 'email': '' };
    if (comment) {
      this.myCommentReplyBody.text = comment;
      this.myCommentReplyBody.email = sessionStorage.getItem('QMail');
      this.showIndicator = true;
      this.streamingService.fnPostCommentReply(this.videoId, commentId, this.myCommentReplyBody).subscribe(response => {
        this.messageHeader = 'Done';
        this.message = 'Thank you for your valuable response..!';
        this.openMessageModal(this.messageTemplate);
        this.myComment = '';
        this.commentReply[i] = '';
        this.isCollapsed[i] = true;
        this.fnGetComments(this.videoId);
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
        this.messageHeader = 'Failed';
        this.message = 'We Sorry, Failed to added your valuable response..!';
        this.openMessageModal(this.messageTemplate);
      });
    } else {
      this.messageHeader = 'Remember';
      this.message = 'Please provide your response before submit..!';
      this.openMessageModal(this.messageTemplate);
    }

  }

  fnOnClickClearReply(index) {
    this.isCollapsed[index] = true;
    this.commentReply[index] = '';
  }

  ngOnDestroy() {
    if (this.myRating === 0) {
      this.openModal(this.template);
    }

    const trackData = new VideoUsage();
    trackData.email = sessionStorage.getItem('QMail');
    trackData.videoId = this.videoId;
    trackData.totalLength = this.totalWatchTime;
    trackData.lastWatch = this.lastWatchTime;

    this.trackingService.submitVideoTrackRecord(trackData).subscribe(response => {

    }, error => {

    });
  }
}
