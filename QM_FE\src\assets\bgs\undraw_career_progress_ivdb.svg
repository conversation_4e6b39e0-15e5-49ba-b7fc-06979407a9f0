<svg id="undraw_career_progress_ivdb" xmlns="http://www.w3.org/2000/svg" width="956" height="621" viewBox="0 0 956 621">
  <rect id="Rectangle_48" data-name="Rectangle 48" width="114.239" height="32.281" rx="7.43" transform="translate(135.448 248.74)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_49" data-name="Rectangle 49" width="114.239" height="32.281" rx="7.43" transform="translate(255 281.029)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_50" data-name="Rectangle 50" width="114.239" height="32.281" rx="7.43" transform="translate(374.562 313.309)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_51" data-name="Rectangle 51" width="114.239" height="32.281" rx="7.43" transform="translate(135.448 200.318)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_52" data-name="Rectangle 52" width="114.239" height="32.281" rx="7.43" transform="translate(255 232.599)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_53" data-name="Rectangle 53" width="114.239" height="32.281" rx="7.43" transform="translate(374.562 264.888)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_54" data-name="Rectangle 54" width="114.239" height="32.281" rx="7.43" transform="translate(507.397 248.74)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_55" data-name="Rectangle 55" width="114.239" height="32.281" rx="7.43" transform="translate(626.949 281.029)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_56" data-name="Rectangle 56" width="114.239" height="32.281" rx="7.43" transform="translate(746.51 313.309)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_57" data-name="Rectangle 57" width="114.239" height="32.281" rx="7.43" transform="translate(507.397 200.318)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_58" data-name="Rectangle 58" width="114.239" height="32.281" rx="7.43" transform="translate(626.949 232.599)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_59" data-name="Rectangle 59" width="114.239" height="32.281" rx="7.43" transform="translate(746.51 264.888)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_60" data-name="Rectangle 60" width="114.239" height="32.281" rx="7.43" transform="translate(135.448 135.748)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_61" data-name="Rectangle 61" width="114.239" height="32.281" rx="7.43" transform="translate(255 168.029)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_62" data-name="Rectangle 62" width="114.239" height="32.281" rx="7.43" transform="translate(374.562 200.318)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_63" data-name="Rectangle 63" width="114.239" height="32.281" rx="7.43" transform="translate(135.448 87.319)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_64" data-name="Rectangle 64" width="114.239" height="32.281" rx="7.43" transform="translate(255 119.608)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_65" data-name="Rectangle 65" width="114.239" height="32.281" rx="7.43" transform="translate(374.562 151.888)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_66" data-name="Rectangle 66" width="114.239" height="32.281" rx="7.43" transform="translate(507.397 135.748)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_67" data-name="Rectangle 67" width="114.239" height="32.281" rx="7.43" transform="translate(626.949 168.029)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_68" data-name="Rectangle 68" width="114.239" height="32.281" rx="7.43" transform="translate(746.51 200.318)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_69" data-name="Rectangle 69" width="114.239" height="32.281" rx="7.43" transform="translate(507.397 87.319)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_70" data-name="Rectangle 70" width="114.239" height="32.281" rx="7.43" transform="translate(626.949 119.608)" fill="#0b6fb1" opacity="0.1"/>
  <rect id="Rectangle_71" data-name="Rectangle 71" width="114.239" height="32.281" rx="7.43" transform="translate(746.51 151.888)" fill="#0b6fb1" opacity="0.1"/>
  <ellipse id="Ellipse_22" data-name="Ellipse 22" cx="478" cy="81.5" rx="478" ry="81.5" transform="translate(0 458)" fill="#0b6fb1" opacity="0.1"/>
  <path id="Path_161" data-name="Path 161" d="M234.2,320.908,194,551.253l181.762-17.97-7.865-19.6-142,11.027L259.1,320.5Z" transform="translate(-24.472 -58.708)" fill="#3f3d56"/>
  <path id="Path_162" data-name="Path 162" d="M186,.817,430.68,0l70.782,500.713L255.909,531.753Z" transform="translate(-23.463)" fill="#3f3d56"/>
  <path id="Path_163" data-name="Path 163" d="M216.5,24.5H413.992l69.035,465.59L279.418,516.228Z" transform="translate(-27.31 -4.488)" fill="#fff"/>
  <path id="Path_164" data-name="Path 164" d="M478.259,637.655c1.241,2.222,2.167,4.623,3.784,6.624,4.719,5.9,13.676,6.82,20.4,10.668a1.828,1.828,0,0,1,.8.719c.306.678-.376,1.389-1.031,1.813-5.095,3.341-11.78,3.414-18.01,3.373a25.641,25.641,0,0,1-6.231-.555c-3.294-.817-6.117-2.965-9.368-3.839a26.474,26.474,0,0,0-5.147-.678l-6.825-.433a17.086,17.086,0,0,1-5.322-.923,5.138,5.138,0,0,1-3.356-3.717c-.192-1.356.428-2.671.8-3.994.874-3.137.6-6.927,3.216-9.067a10.224,10.224,0,0,1,3.626-1.634A122.179,122.179,0,0,1,470.1,632.4C474.143,631.659,476.467,634.437,478.259,637.655Z" transform="translate(-102.815 -172.996)" fill="#3f3d56"/>
  <path id="Path_165" data-name="Path 165" d="M390.6,655.541c.175,1.74.227,3.733-1.119,4.95a6.175,6.175,0,0,1-2.927,1.225c-6.458,1.4-13.658,1.5-19.277-1.781a5.242,5.242,0,0,1-1.52-1.217,4.239,4.239,0,0,1-.769-2.246c-.2-2.312.559-4.591,1.25-6.82a112.965,112.965,0,0,0,3.714-16.132c.07-.449,16.49,2.451,17.582,3.97,1.372,1.879,1.381,6.388,1.748,8.626Q390.109,650.8,390.6,655.541Z" transform="translate(-92.35 -173.214)" fill="#3f3d56"/>
  <path id="Path_166" data-name="Path 166" d="M450.406,394.339l4.369,37.974c.874,7.76,1.791,15.56,1.188,23.345-.7,8.985-3.391,17.97-2.255,26.874.446,3.463,1.459,6.861,1.608,10.349a52.723,52.723,0,0,1-.376,6.943c-1.975,23.043,4.273,45.906,7.769,68.785a27.928,27.928,0,0,1,.367,8.56c-.454,2.9-1.363,6.706,1.4,8.078l-25.936,8.789c-3.312-3.88-1.119-9.916-1.748-14.842-.577-4.435-3.181-8.389-4.57-12.661-1.258-3.839-1.521-7.882-1.931-11.868-2.263-21.7-9.21-42.842-10.836-64.594a27.6,27.6,0,0,0-.874-6.232c-.874-2.7-2.622-5.064-3.9-7.6-1.957-3.815-2.919-8-3.862-12.13l-5.549-24.3c-5.61,7.809-5.383,17.97-6.493,27.331-1.555,13.069-5.033,25.918-7.865,38.824a72.542,72.542,0,0,0-1.477,8.413c-.734,7.572.725,15.217-.052,22.781-.874,8.764-4.815,17.317-3.862,26.081.524,4.852,2.517,9.443,3.862,14.147s2.01,9.867-.044,14.352a50.277,50.277,0,0,0-22.467-2.32c-8.328-7.964-9.8-19.073-10.373-30.223s.8-22.316-.682-33.359c-.437-3.267-1.119-6.453-1.232-9.712-.367-10.447,5.007-20.225,7.681-30.378a60.2,60.2,0,0,0,.673-27.895c-1.311-6.1-3.565-12.024-4.369-18.207-1.092-8.667.76-17.4,2.622-25.959,1.25-5.775,2.491-11.55,3.906-17.292,1.521-6.167,3.329-12.465,7.393-17.513,6.5-8.078,18.045-11.664,28.837-10.88s20.868,5.375,29.781,11.092a110.743,110.743,0,0,1,19.295,15.25Z" transform="translate(-91.041 -124.566)" fill="#192534"/>
  <path id="Path_167" data-name="Path 167" d="M426.324,218.962a6.785,6.785,0,0,0-1,2.4c-.218,1.674.874,3.21,1.748,4.672a24.512,24.512,0,0,1,2.56,5.963,7.558,7.558,0,0,1-.76,6.837,15.09,15.09,0,0,1-7.585,7.115,14.246,14.246,0,0,1-8.206.31,25.714,25.714,0,0,1-7.506-3.333,22.449,22.449,0,0,1-5.138-4.166,28.4,28.4,0,0,1-3.548-5.718c-1.818-3.553-3.661-7.351-3.329-11.248a3.542,3.542,0,0,1,.21-1.037,5.57,5.57,0,0,1,1.171-1.634,20.448,20.448,0,0,0,5.348-14.825l12.732,2.7c2.805.6,5.61,1.193,8.38,1.936,2.272.613,5.767,1.462,7.244,3.357S427.329,217.23,426.324,218.962Z" transform="translate(-95.953 -94.601)" fill="#fbbebe"/>
  <ellipse id="Ellipse_23" data-name="Ellipse 23" cx="23.14" cy="21.63" rx="23.14" ry="21.63" transform="translate(300.415 79.077)" fill="#fbbebe"/>
  <path id="Path_168" data-name="Path 168" d="M414.087,243.5c.979-2,3.093-3.267,5.025-4.525s3.889-2.867,4.264-5.048a3.431,3.431,0,0,1,.384-1.421c.743-1.046,2.508-.523,3.565.245,2.569,1.871,4.317,4.525,6.021,7.115a59.55,59.55,0,0,1,5.636,9.957c2.456,6.053,2.753,12.661,3.024,19.122.245,5.881.481,11.852-1.049,17.554-.961,3.61-2.622,7.115-2.718,10.831-.288,13.5-5.3,26.408-5.654,39.91a96.843,96.843,0,0,0,.629,11.689l.821,8.765q-19.059-1.021-38.013-3.128a64.158,64.158,0,0,0-2.726-11.852,241.979,241.979,0,0,1-8.835-48.8c-.673-8.756-1.363-17.48-2.4-26.2-.935-7.931-2.77-15.912-1.669-23.827a31.411,31.411,0,0,1,3.583-10.308,6.929,6.929,0,0,1,2.8-3.128,7.938,7.938,0,0,1,1.975-.6,39.1,39.1,0,0,1,6.012-.662,4.42,4.42,0,0,1,1.468.131,4.48,4.48,0,0,1,2,1.813A35.815,35.815,0,0,0,414.087,243.5Z" transform="translate(-94.255 -99.157)" fill="#f2f2f2"/>
  <path id="Path_169" data-name="Path 169" d="M406.72,244.21c1.625,2.173,5.068,2.687,6.571,4.9a12.324,12.324,0,0,1,1.145,2.916c.918,2.736,2.77,5.113,3.9,7.784,1.625,3.839,1.669,8.087,1.687,12.212a37.839,37.839,0,0,1-.8,9.8c-.524,2.018-1.381,3.937-2.01,5.93a27.2,27.2,0,0,0-1.171,10.455,4.069,4.069,0,0,0,.629,2.164A5.052,5.052,0,0,0,418.9,301.8c2.56,1.054,5.243,2.132,8.039,1.887a3.085,3.085,0,0,0,1.6-.5,3.357,3.357,0,0,0,.786-.972l1.748-2.761c1.582-2.524,3.233-5.35,2.622-8.234A15.813,15.813,0,0,0,431.712,287a47.829,47.829,0,0,1-2.849-6.592c-2.788-7.49-5.61-15.087-6.239-22.994a18.971,18.971,0,0,1,1.049-8.92,25.05,25.05,0,0,1,3.076-4.974,2.371,2.371,0,0,0-2.456-.776,15.191,15.191,0,0,0-2.543.988c-2.569,1.013-5.418.645-8.013-.049-1.014-.278-2.622-1.225-3.67-1.16C409.709,242.56,406.781,244.308,406.72,244.21Z" transform="translate(-97.619 -101.601)" fill="#0b6fb1"/>
  <path id="Path_170" data-name="Path 170" d="M428.87,241.668c-.524-.392-1.337-.751-1.8-.294a1.842,1.842,0,0,0-.332.662c-.454,1.144-1.791,1.707-3.006,2.148s-2.525.686-3.259-.155a1.75,1.75,0,0,1,.114-2.181,7.033,7.033,0,0,1,1.914-1.511,17.386,17.386,0,0,0,7.62-11.207,5.263,5.263,0,0,1,2.089,3.374c.288,1.3.323,2.638.612,3.937.428,1.862,1.381,3.6,1.6,5.489a26.819,26.819,0,0,1,0,3.022,17.4,17.4,0,0,0,.367,2.924C432.742,245.85,431.3,243.473,428.87,241.668Z" transform="translate(-99.313 -99.149)" fill="#f2f2f2"/>
  <path id="Path_171" data-name="Path 171" d="M407.949,234.362c-.874-.776-1.748-1.511-2.622-2.3a31.571,31.571,0,0,1-8.45-12.538c-1.119.817-2.351,1.9-2.377,3.267a4.835,4.835,0,0,0,1.153,2.655,39.771,39.771,0,0,1,6.405,16.018,5.207,5.207,0,0,1,4.413-2.2c1.381,0,7.576,3.431,8.022,2.565C415.149,240.472,409,235.285,407.949,234.362Z" transform="translate(-96.078 -97.388)" fill="#f2f2f2"/>
  <path id="Path_172" data-name="Path 172" d="M419.962,228.241a73.615,73.615,0,0,0,29.344,16.606,6.769,6.769,0,0,1,2.962,1.4,5.5,5.5,0,0,1,1.232,2.532c2.132,7.793,2.849,15.855,3.557,23.868a12.442,12.442,0,0,1-.087,3.986c-.516,2.14-2.141,3.872-3.74,5.481-5.016,5.023-10.74,10.276-11.587,17.1a25.617,25.617,0,0,1-.5,3.921,47.57,47.57,0,0,1-2,4.337c-2.919,7.278,3.679,16.091-.341,22.871a39.447,39.447,0,0,1,4.422,12.465,15.344,15.344,0,0,0,1.2,4.8c1.2,2.336,3.661,3.823,5.6,5.718,5.916,5.669,6.641,14.343,6.991,22.258.218,4.46.149,9.581-3.5,12.506-2.106,1.707-4.981,2.263-7.734,2.687-3.661.572-7.646.98-10.888-.711-2.324-1.209-3.906-3.324-5.418-5.367-2.866-3.9-5.741-7.784-8.459-11.779-4.3-6.3-8.354-13.494-7.113-20.878-.35,7.556-2.054,15.168-6.021,21.752a31.571,31.571,0,0,1-18.106,14.254c-7.751,2.173-16.813.637-22.589-4.656a28.757,28.757,0,0,1-4.815-6.069,57.018,57.018,0,0,1-7.113-16.916c-.376-1.544-.655-3.267.175-4.648.393-.653,1-1.168,1.407-1.813,1.381-2.2.052-4.966-1.285-7.2-2.036.18-3.5-2.058-3.312-3.97s1.407-3.57,2.272-5.309c2.534-5.13,1.975-11.1.751-16.631s-3.05-11.027-2.91-16.671c.149-5.971-4.448-11.346-7.952-16.336-1.957-2.81-4.238-5.424-5.942-8.372a25.441,25.441,0,0,1-2.289-20.225,51.368,51.368,0,0,1,4.789-9.982l2.884-5.056a8.776,8.776,0,0,1,6.117-4.9l19.95-7.351a30.894,30.894,0,0,0,7.2-3.365c2.438-1.715,4.256-4.084,6.58-5.914a3.812,3.812,0,0,1,2.622-1.07c1.87.155,2.534,2.361,2.726,4.084.437,3.855,3.5,7.123,4.535,10.872,5.628,20.706,17.538,39.8,20.807,60.943,3.67-8.242,7.34-16.5,12.042-24.26a19.639,19.639,0,0,0,1.861-3.521,14.1,14.1,0,0,0,.62-5.465c-.236-5.318-1.748-10.513-3.242-15.65C423.729,241.612,421.981,235.167,419.962,228.241Z" transform="translate(-88.578 -97.77)" fill="#3f3d56"/>
  <path id="Path_173" data-name="Path 173" d="M472.412,302.94c.384,3.48.76,6.967,1.451,10.406A118.14,118.14,0,0,0,477.1,325.1a29.372,29.372,0,0,0,2.534,6.314,7.888,7.888,0,0,1,.935,1.871c.428,1.634-.551,3.267-.97,4.9-1.258,4.9,2.517,10.349.14,14.866-.717,1.372-1.966,2.508-2.438,3.978-.551,1.691,0,3.5.288,5.244.743,4.9-1.049,9.884-2.814,14.6l-3.006,7.956c-2.744-4.77-5.514-9.59-9.307-13.682-1.354-1.47-2.831-2.834-4.081-4.386a30.077,30.077,0,0,1-4.369-7.956,65.058,65.058,0,0,1-4.518-21.009,13.49,13.49,0,0,1,.743-6.045,31.608,31.608,0,0,1,1.94-3.267c2.342-3.945,2.823-8.617,2.779-13.126s-.559-9.034,0-13.518a29.468,29.468,0,0,1,3.5-10.725,15.453,15.453,0,0,1,4.745-5.53,8.953,8.953,0,0,1,5.243-1.593c2.858.155,2.2,2.451,2.447,4.664Z" transform="translate(-103.006 -109.198)" fill="#3f3d56"/>
  <path id="Path_174" data-name="Path 174" d="M327.266,288.788q-2.674,7.351-4.824,14.834-2.1,7.351-3.749,14.785a29.715,29.715,0,0,0-.944,6.649,28,28,0,0,0,1.424,7.686c5.628,18.958,15.887,36.447,27.439,52.889a49.857,49.857,0,0,1,11.622-9.916,2.415,2.415,0,0,0,.874-.817,2.092,2.092,0,0,0-.28-1.911c-3.19-6.11-8.24-11.493-9.831-18.134-.664-2.761-.69-5.636-1.442-8.381-.97-3.553-3.137-6.8-3.635-10.447-.533-3.97.874-8.3-1.119-11.819,4.063-1.478,6.414-5.391,8.136-9.14a47.679,47.679,0,0,0,3.915-12.015,34.176,34.176,0,0,0-4.728-22.994,37.3,37.3,0,0,0-7.908-9.279c-1.04-.874-3.5-3.382-5.007-3.357-2.019.041-2.665,3.1-3.242,4.468C331.636,277.491,329.329,283.1,327.266,288.788Z" transform="translate(-86.396 -106.162)" fill="#3f3d56"/>
  <path id="Path_175" data-name="Path 175" d="M367.388,403.776c.262.18.559.425.489.719s-.323.368-.551.49a6.389,6.389,0,0,0-2.517,3.186,85.37,85.37,0,0,0-3.793,9.385,42.045,42.045,0,0,0-6.851-7.474c-1.992-1.715-4.221-3.341-5.287-5.661,0-.065,4.579-3.226,5-3.57,1.285-1.07,2.9-4.035,4.229-4.689,1.5-.735,2.936,2.148,4.081,3.308a34.076,34.076,0,0,0,5.2,4.3Z" transform="translate(-90.323 -129.724)" fill="#fbbebe"/>
  <path id="Path_176" data-name="Path 176" d="M443.411,181.749a3.853,3.853,0,0,0-1.084-2.949,4.349,4.349,0,0,0-2.622-.678c-5.095-.155-10.364.645-15.17-.956-3.163-1.054-5.864-3.079-8.948-4.337s-7.035-1.593-9.525.523c-1.888,1.634-2.4,4.141-3.059,6.453a36.961,36.961,0,0,1-7.541,13.984,9.02,9.02,0,0,1-5.156-4.738c-3.714-7.074-1.888-15.52.752-23.018.874-2.516,1.844-5.007,2.814-7.49L396,153.054a11.775,11.775,0,0,0,7.305-3.4l.918,2.083a12.145,12.145,0,0,0,6.554-2.165c.734,1.291,2.622,1.634,4.151,1.217s2.849-1.217,4.369-1.634c4.063-1.225,8.4.662,12.662.874q2.707-.062,5.409.147c5.033.76,8.59,5.187,10.128,9.728,1.022,3.022,1.87,6.739,5.06,7.809.07,1.144-2.1,1.364-2.281,2.45a1.781,1.781,0,0,0,.262,1.037l2.927,5.873a7.753,7.753,0,0,1-4.229-.2C447.92,179.781,443.542,183.963,443.411,181.749Z" transform="translate(-95.284 -84.425)" fill="#192534"/>
  <path id="Path_177" data-name="Path 177" d="M620.244,199.049a101.493,101.493,0,0,1,3.5,13.665c.629,3.463,1,7.311-1.127,10.2,8.092-11.378,16.542-23.1,28.837-30.443a23.629,23.629,0,0,1-6.86-18.983c.07-.686-10.093,2.9-10.871,3.308-3.565,1.854-6.834,4.158-10.311,6.143-1.8,1.021-9.3,3.439-9.368,4.9,0,.939,2.98,3.815,3.539,4.8a35.879,35.879,0,0,1,2.665,6.4Z" transform="translate(-123.771 -88.941)" fill="#fbbebe"/>
  <path id="Path_178" data-name="Path 178" d="M656.839,728.78A14.347,14.347,0,0,0,643,727.8c-3.959,1.911-6.624,5.53-10.224,7.98-6.187,4.2-14.358,4.533-21.6,6.8-1.922.6-3.967,1.5-4.78,3.267a5.459,5.459,0,0,0,.358,4.55,12.185,12.185,0,0,0,5.995,5.775c3.251,1.454,6.991,1.519,10.591,1.56l31.284.343a273.991,273.991,0,0,1,28.837,1.331c2.228.261,4.64.547,6.606-.474,3.2-1.634,3.565-5.767,3.321-9.173a79.6,79.6,0,0,0-6.117-25.158c-.376-.89-.874-1.9-1.931-2.067a3.3,3.3,0,0,0-2.08.621c-8.826,4.9-20,7.67-29.388,3.749" transform="translate(-122.764 -189.529)" fill="#fff"/>
  <path id="Path_179" data-name="Path 179" d="M679.414,501.354c-3.5,18.975-1.215,38.472,2.718,57.366a94.59,94.59,0,0,0,2.857,11.142c1.407,4.166,3.3,8.168,4.623,12.359,3.356,10.619,2.884,21.973,2.386,33.057l-2.29,50.439c-.192,4.231-.367,8.56,1,12.6.551,1.634,1.223,3.676-.166,4.778a4.062,4.062,0,0,1-1.844.662,30.305,30.305,0,0,1-18.351-2.573l-.62-50.953c-.192-15.708-.393-31.48-2.98-46.992-2.053-12.334-5.619-24.5-5.855-36.986-.245-13.069,3.181-25.967,6.676-38.636.752-2.712,3.5-4.7,5.75-6.535a7.176,7.176,0,0,1,2.8-1.585C677.159,499.222,678.767,500.529,679.414,501.354Z" transform="translate(-129.679 -148.666)" fill="#3f3d56"/>
  <path id="Path_180" data-name="Path 180" d="M679.414,501.354c-3.5,18.975-1.215,38.472,2.718,57.366a94.59,94.59,0,0,0,2.857,11.142c1.407,4.166,3.3,8.168,4.623,12.359,3.356,10.619,2.884,21.973,2.386,33.057l-2.29,50.439c-.192,4.231-.367,8.56,1,12.6.551,1.634,1.223,3.676-.166,4.778a4.062,4.062,0,0,1-1.844.662,30.305,30.305,0,0,1-18.351-2.573l-.62-50.953c-.192-15.708-.393-31.48-2.98-46.992-2.053-12.334-5.619-24.5-5.855-36.986-.245-13.069,3.181-25.967,6.676-38.636.752-2.712,3.5-4.7,5.75-6.535a7.176,7.176,0,0,1,2.8-1.585C677.159,499.222,678.767,500.529,679.414,501.354Z" transform="translate(-129.679 -148.666)" opacity="0.1"/>
  <path id="Path_181" data-name="Path 181" d="M606.95,427.392c-1.293,4.974,1.451,10.129,1.145,15.242-.175,3.1-1.477,6.183-.874,9.222s2.927,5.326,4.168,8.111a20.733,20.733,0,0,1,1.416,7.253c.358,6.11.07,12.252-.175,18.338-.411,10.21-.717,20.478.725,30.6a90.928,90.928,0,0,0,7.576,25.444,42.4,42.4,0,0,1,2.823,6.853,45.9,45.9,0,0,1,.961,6.649,64.161,64.161,0,0,0,6.178,21c2.228,4.55,6.117,8.168,7.349,13,1.171,4.623.385,9.5,1.232,14.18.62,3.472,2.132,6.755,2.971,10.186,1.092,4.435,1.066,9.034,1.617,13.559,1.573,13.069,7.926,26.138,4.133,38.816-.76,2.548-1.94,5.138-1.407,7.735.262,1.274.926,2.532.629,3.8-.245,1.078-1.136,1.911-1.59,2.932-.874,1.911.052,4.182,1.608,5.661a17.5,17.5,0,0,0,5.68,3.137c6.44,2.573,13.108,5.187,20.1,5.13a4.448,4.448,0,0,0,4.885-3.594l3.583-8.283c.437-1,.874-2.181.271-3.112a6.062,6.062,0,0,0-1.5-1.307c-2.892-2.279-2.237-6.535-1.468-9.957a136.232,136.232,0,0,0,3.251-26.71c.384-17.9-2.77-35.7-5.916-53.371-1.092-6.134-2.193-12.31-4.369-18.191s-5.383-11.542-4.859-17.652c.559-6.6-1.276-13.518,0-20.029s3.294-12.881,4.6-19.383c1.687-8.438,2.106-17.039,2.622-25.6.218-3.5.5-7.106,2.246-10.21,1.022-1.813,2.508-3.365,3.653-5.121,2.954-4.566,3.425-10.227,6.248-14.866,1.337-2.2,3.181-4.133,4.16-6.486a17.065,17.065,0,0,0,1.066-5.767,64.2,64.2,0,0,0-.874-14.744,108.556,108.556,0,0,0-48.062-10.423c-6.388.1-12.846.7-18.84,2.769C618.188,424.182,612.979,426.967,606.95,427.392Z" transform="translate(-122.833 -134.005)" fill="#3f3d56"/>
  <path id="Path_182" data-name="Path 182" d="M641.569,745.379c-2.569,2.99-2.394,7.262-4.037,10.782-1.992,4.272-6.432,7.008-10.644,9.5l-26.216,15.52c-1.433.817-2.971,1.821-3.5,3.341a5.025,5.025,0,0,0,.131,2.932,13.183,13.183,0,0,0,9.158,8.691,28.9,28.9,0,0,0,7.087.678l11.788.147a73.815,73.815,0,0,0,10.757-.392c3.268-.441,6.449-1.315,9.7-1.871,5.916-1,12-.915,17.923-1.887a100.189,100.189,0,0,0,10.731-2.589c4.282-1.2,8.642-2.451,12.313-4.827a6.151,6.151,0,0,0,2.368-2.385,6.607,6.607,0,0,0-.07-4.321l-5.628-23.116a13.252,13.252,0,0,0-2.2-5.4,5.222,5.222,0,0,0-5.444-1.919,11.722,11.722,0,0,0-2.263,1.274,14.735,14.735,0,0,1-9.988,1.511c-2.2-.368-5.112-.931-6.886-2.3-1.346-1.037-1.748-2.647-2.892-3.823a8.515,8.515,0,0,0-6.2-2.313A8.425,8.425,0,0,0,641.569,745.379Z" transform="translate(-121.624 -193.206)" fill="#fff"/>
  <ellipse id="Ellipse_24" data-name="Ellipse 24" cx="31.572" cy="29.512" rx="31.572" ry="29.512" transform="translate(471.376 47.899)" fill="#fbbebe"/>
  <path id="Path_183" data-name="Path 183" d="M600.538,156.117c3.024-1.585,3.024-6.151,6.117-7.67s6.606,1.119,8.59,3.782a33.636,33.636,0,0,1,5.9,12.963c.28,1.323.542,2.769,1.608,3.692a6.478,6.478,0,0,0,3.828,1.168q5.986.555,11.989,1.013c4.457.335,9.158.6,13.2-1.184a9.494,9.494,0,0,0,3.967-3.071,13.283,13.283,0,0,0,1.7-4.149c2.089-7.425,4.194-14.94,4.369-22.618.07-3.169-.227-6.453-1.861-9.23-.953-1.634-2.351-3.055-2.744-4.852s.874-4.084,2.866-3.888a12.481,12.481,0,0,1-5.243-.931c-1.582-.817-2.753-2.573-2.193-4.174-4.439.531-7.672-3.57-11.133-6.224a3.125,3.125,0,0,0-2.115-.817,7.961,7.961,0,0,0-1.748.629c-1.748.572-3.574-.515-5.365-.988-4.806-1.258-10.914,1.781-14.48-1.487.341,2.083-2.394,3.48-4.649,3.439s-4.579-.817-6.711-.114a3.847,3.847,0,0,1,.224,3.474,4.265,4.265,0,0,1-2.723,2.374l1.057,2.156a5.812,5.812,0,0,0-4.588,4.019c-1.162,4.288-.874,10.382-.594,14.793C599.944,140.157,601.962,155.39,600.538,156.117Z" transform="translate(-121.944 -76.975)" fill="#192534"/>
  <path id="Path_184" data-name="Path 184" d="M626.738,207.831c-15.861,16.671-19.9,40.139-23.28,62.234l-4.981,32.624c-1.293,8.462-2.622,16.925-3.749,25.411-.786,5.808-1.521,11.705-.664,17.5.673,4.574,2.307,8.985,3.67,13.388a118.569,118.569,0,0,1,5.243,35.524c1.057-2.908,4.719-4.084,7.935-4.738a136.534,136.534,0,0,1,80.273,8.013c4.413-11.558,3.583-24.2,4.815-36.422,1.748-17.423,7.769-34.307,9.551-51.7.655-6.477.734-12.988.8-19.5.052-4.778.1-9.606-.961-14.286-1.188-5.179-3.7-10.006-6.292-14.7a378.328,378.328,0,0,0-23.874-37.917q-6.589-9.214-13.746-18.06-3.626-4.484-7.4-8.871c-2.438-2.834-3.5-5.293-7.367-3.594C639.4,195.947,632.06,202.236,626.738,207.831Z" transform="translate(-121.203 -92.382)" fill="#0b6fb1"/>
  <path id="Path_185" data-name="Path 185" d="M638.368,379.878a91.592,91.592,0,0,1-11.6-.343c-2.858-.261-5.776-.653-8.564,0a9.178,9.178,0,0,0-3.329,1.527,2.246,2.246,0,0,0-.717,2.477,2.479,2.479,0,0,0,2.141,1.607l2.989.245c1.9.155,3.871.335,5.348,1.634a3.057,3.057,0,0,1,1.084,1.969c.184,1.9-1.582,2.941-3.163,3.733a25.27,25.27,0,0,0-6.169,3.839,7.8,7.8,0,0,0-2.56,4.9.932.932,0,0,0,.473.838,1.075,1.075,0,0,0,1.013.036c3.5-1.634,5.776-6.477,9.612-5.669a15.481,15.481,0,0,0-8.442,6.069,3.479,3.479,0,0,0,.431,4.526,4.117,4.117,0,0,0,4.812.644,17.431,17.431,0,0,0,1.643-1.021,39.021,39.021,0,0,0,6.93-6.731c-2.8,5.514-5.628,11.966-2.412,17.447a1.884,1.884,0,0,0,1.748.964c1.014-.1,1.311-.988,1.477-1.773.524-2.524.655-5.187,1.984-7.433,3.076-5.211,10.661-5.718,16.437-8.258,6.93-3.006,11.675-9.475,13.588-16.385a11.807,11.807,0,0,0,.341-6.224c-1.285-4.648-7.419-5.718-11.36-3.471C647.272,377.893,644.467,379.7,638.368,379.878Z" transform="translate(-123.744 -125.666)" fill="#fbbebe"/>
  <path id="Path_186" data-name="Path 186" d="M634.2,260.457c6.117,8.83,16.734,13.8,25.84,20a21.121,21.121,0,0,1,5.715,5.081c2.3,3.316,2.622,7.531,2.15,11.436-.594,5.154-2.368,10.112-4.125,15.03l-3.819,10.66c-2.421,6.763-4.92,13.665-9.525,19.351-1.18,1.446-2.552,2.99-2.5,4.8a5.957,5.957,0,0,0,1.119,2.924,21.045,21.045,0,0,0,13.361,8.92,10.88,10.88,0,0,0,5.025,0c2.359-.653,4.133-2.45,5.776-4.149,7.865-8.111,15.729-16.336,21.409-25.853,5.549-9.287,8.87-19.6,11.928-29.822A59.253,59.253,0,0,0,709.18,287.3c.647-7.351-1.416-14.7-4.789-21.344S696.378,253.3,691.6,247.461a131.744,131.744,0,0,0-14.987-16.075c-9.508-8.307-21.139-13.192-33.862-8.479-8.983,3.333-12.348,10.284-13.152,19.187A27.69,27.69,0,0,0,634.2,260.457Z" transform="translate(-125.714 -97.672)" opacity="0.1"/>
  <path id="Path_187" data-name="Path 187" d="M637.838,257.736c6.117,8.83,16.743,13.8,25.84,20a21.166,21.166,0,0,1,5.689,5.113c2.3,3.316,2.622,7.523,2.15,11.436-.6,5.154-2.368,10.112-4.133,15.03l-3.819,10.619c-2.421,6.763-4.92,13.657-9.525,19.351-1.171,1.446-2.543,2.981-2.49,4.8a5.816,5.816,0,0,0,1.118,2.916,21.033,21.033,0,0,0,13.353,8.928,10.916,10.916,0,0,0,5.033,0c2.359-.645,4.125-2.451,5.776-4.141,7.865-8.119,15.729-16.336,21.41-25.852,5.549-9.287,8.87-19.6,11.928-29.83a58.657,58.657,0,0,0,2.622-11.534c.647-7.351-1.416-14.7-4.78-21.352S700,250.573,695.216,244.732a131.917,131.917,0,0,0-15.022-15.985c-9.5-8.3-21.13-13.192-33.853-8.47-8.992,3.333-12.348,10.276-13.16,19.187a27.722,27.722,0,0,0,4.658,18.272Z" transform="translate(-126.168 -97.189)" fill="#0b6fb1"/>
  <path id="Path_188" data-name="Path 188" d="M853.97,619.833a28.319,28.319,0,0,1-.7,6.339c-.087.392-.184.817-.28,1.152-2.482,9.3-9.481,16.108-17.835,16.541h-.874c-8.835,0-16.306-7.123-18.771-16.933-.07-.261-.131-.523-.192-.817a28.333,28.333,0,0,1-.7-6.339c0-13.29,8.8-24.056,19.662-24.056S853.97,606.567,853.97,619.833Z" transform="translate(-149.073 -166.299)" fill="#3f3d56"/>
  <path id="Path_189" data-name="Path 189" d="M853.975,619.842a28.323,28.323,0,0,1-.7,6.339c-.087.392-.184.817-.28,1.152h-.874a42.623,42.623,0,0,1-6.432-.817,41.612,41.612,0,0,1-5.732,1.225,42.788,42.788,0,0,1-4.492.392h-1.521a43.379,43.379,0,0,1-5.387-.335,41.753,41.753,0,0,1-8.446-1.96,42.059,42.059,0,0,1-4.562,1.144c-.07-.261-.131-.523-.192-.817a28.32,28.32,0,0,1-.7-6.339c0-13.29,8.8-24.055,19.662-24.055S853.975,606.577,853.975,619.842Z" transform="translate(-149.078 -166.308)" opacity="0.1"/>
  <path id="Path_190" data-name="Path 190" d="M889.116,558.237a35.234,35.234,0,0,0-4.282-16.835l-23.122,6.722,20.3-11.158a40.637,40.637,0,0,0-30.026-15.993,37.641,37.641,0,0,0-3.12-3.855l-33.207,9.663L842.9,511.808a42.233,42.233,0,0,0-37.151-5.746c-12.634,4-22.3,13.629-25.766,25.669l28.444,30.606-30.856-20.862c-16.155,10.71-21.483,30.864-12.524,47.374s29.5,24.4,48.291,18.543a42.782,42.782,0,0,0,25.587.662,42.138,42.138,0,0,0,33.646-7.362,36.53,36.53,0,0,0,14.958-29.117,22.962,22.962,0,0,0-.087-2.451,35.02,35.02,0,0,0,1.678-10.888Z" transform="translate(-142.273 -149.514)" fill="#0b6fb1"/>
  <path id="Path_191" data-name="Path 191" d="M888.417,576.936a113.848,113.848,0,0,0-37-.441c-13.886,2.148-28.077,6.87-41.657,3.423-7.97-2.026-14.856-6.714-22.642-9.32a45.837,45.837,0,0,0-23.376-1.307,35.546,35.546,0,0,0,7.589,39.847c10.67,10.742,27.072,14.663,41.95,10.028a42.782,42.782,0,0,0,25.587.662,42.138,42.138,0,0,0,33.646-7.362,36.53,36.53,0,0,0,14.958-29.117,22.977,22.977,0,0,0-.087-2.45,34.921,34.921,0,0,0,1.031-3.962Z" transform="translate(-142.265 -161.286)" opacity="0.1"/>
</svg>
