'use server'

import * as XLSX from 'xlsx'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'

export interface BulkQuestionRow {
  question: string
  option_1: string
  option_2: string
  option_3: string
  option_4: string
  option_5?: string
  correct_option: number
  explanation?: string
}

export interface ValidationError {
  row: number
  field: string
  message: string
}

export interface BulkUploadResult {
  success: boolean
  totalRows: number
  successCount: number
  errors: ValidationError[]
  message: string
}

/**
 * Validate a single question row
 */
function validateQuestionRow(row: any, rowIndex: number): ValidationError[] {
  const errors: ValidationError[] = []

  // Required fields validation
  if (!row.question?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'question',
      message: 'Question text is required'
    })
  }

  if (!row.option_1?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'option_1',
      message: 'Option 1 is required'
    })
  }

  if (!row.option_2?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'option_2',
      message: 'Option 2 is required'
    })
  }

  if (!row.option_3?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'option_3',
      message: 'Option 3 is required'
    })
  }

  if (!row.option_4?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'option_4',
      message: 'Option 4 is required'
    })
  }

  // Correct option validation
  const correctOption = Number(row.correct_option)
  if (!correctOption || correctOption < 1 || correctOption > 5) {
    errors.push({
      row: rowIndex,
      field: 'correct_option',
      message: 'Correct option must be a number between 1 and 5'
    })
  }

  // If correct option is 5, option_5 must be provided
  if (correctOption === 5 && !row.option_5?.trim()) {
    errors.push({
      row: rowIndex,
      field: 'option_5',
      message: 'Option 5 is required when correct option is 5'
    })
  }

  // Question length validation
  if (row.question?.length > 1000) {
    errors.push({
      row: rowIndex,
      field: 'question',
      message: 'Question text must be less than 1000 characters'
    })
  }

  // Options length validation
  const options = ['option_1', 'option_2', 'option_3', 'option_4', 'option_5']
  options.forEach((optionField) => {
    if (row[optionField]?.length > 200) {
      errors.push({
        row: rowIndex,
        field: optionField,
        message: `${optionField} must be less than 200 characters`
      })
    }
  })

  return errors
}

/**
 * Parse Excel file and extract questions data
 */
export async function parseExcelFile(fileBuffer: ArrayBuffer): Promise<{
  questions: BulkQuestionRow[]
  errors: ValidationError[]
}> {
  try {
    const workbook = XLSX.read(fileBuffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    // Convert to JSON with header row
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

    if (rawData.length < 2) {
      return {
        questions: [],
        errors: [
          {
            row: 1,
            field: 'file',
            message: 'Excel file must contain at least one data row'
          }
        ]
      }
    }

    // Get headers and validate them
    const headers = rawData[0] as string[]
    const expectedHeaders = [
      'question',
      'option_1',
      'option_2',
      'option_3',
      'option_4',
      'option_5',
      'correct_option',
      'explanation'
    ]

    const missingHeaders = expectedHeaders
      .slice(0, 7)
      .filter(
        (header) =>
          !headers.some((h) => h?.toLowerCase().replace(/\s+/g, '_') === header)
      )

    if (missingHeaders.length > 0) {
      return {
        questions: [],
        errors: [
          {
            row: 1,
            field: 'headers',
            message: `Missing required headers: ${missingHeaders.join(', ')}`
          }
        ]
      }
    }

    // Create header mapping
    const headerMap: { [key: string]: number } = {}
    expectedHeaders.forEach((expectedHeader) => {
      const index = headers.findIndex(
        (h) => h?.toLowerCase().replace(/\s+/g, '_') === expectedHeader
      )
      if (index >= 0) {
        headerMap[expectedHeader] = index
      }
    })

    // Process data rows
    const questions: BulkQuestionRow[] = []
    const errors: ValidationError[] = []

    for (let i = 1; i < rawData.length; i++) {
      const row = rawData[i] as any[]

      // Skip empty rows
      if (!row || row.every((cell) => !cell)) continue

      const questionData: any = {}

      // Map data according to headers
      Object.keys(headerMap).forEach((header) => {
        const colIndex = headerMap[header]
        questionData[header] = row[colIndex] || ''
      })

      // Validate row
      const rowErrors = validateQuestionRow(questionData, i + 1)
      errors.push(...rowErrors)

      if (rowErrors.length === 0) {
        questions.push({
          question: questionData.question.trim(),
          option_1: questionData.option_1.trim(),
          option_2: questionData.option_2.trim(),
          option_3: questionData.option_3.trim(),
          option_4: questionData.option_4.trim(),
          option_5: questionData.option_5?.trim() || '',
          correct_option: Number(questionData.correct_option),
          explanation: questionData.explanation?.trim() || ''
        })
      }
    }

    return { questions, errors }
  } catch (error) {
    return {
      questions: [],
      errors: [
        {
          row: 1,
          field: 'file',
          message: `Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`
        }
      ]
    }
  }
}

/**
 * Bulk upload questions to a paper
 */
export async function bulkUploadQuestions(
  paperId: string,
  paperType: number,
  fileBuffer: ArrayBuffer
): Promise<BulkUploadResult> {
  try {
    // Parse and validate Excel file
    const { questions, errors } = await parseExcelFile(fileBuffer)

    if (errors.length > 0) {
      return {
        success: false,
        totalRows: 0,
        successCount: 0,
        errors,
        message: `Validation failed with ${errors.length} errors`
      }
    }

    if (questions.length === 0) {
      return {
        success: false,
        totalRows: 0,
        successCount: 0,
        errors: [],
        message: 'No valid questions found in the file'
      }
    }

    // Upload questions in batches
    const batchSize = 5 // Process 5 questions at a time
    const uploadErrors: ValidationError[] = []
    let successCount = 0

    console.log(
      `Starting upload of ${questions.length} questions in batches of ${batchSize}`
    )

    for (let i = 0; i < questions.length; i += batchSize) {
      const batch = questions.slice(i, i + batchSize)

      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(questions.length / batchSize)}`
      )

      // Process batch concurrently
      const batchPromises = batch.map(async (question, batchIndex) => {
        try {
          const questionData = {
            question: question.question,
            opt_1: question.option_1,
            opt_2: question.option_2,
            opt_3: question.option_3,
            opt_4: question.option_4,
            opt_5: question.option_5 || '',
            correct_opt: question.correct_option,
            explanation: question.explanation || ''
          }

          await AdminPapersClientService.addQuestion(
            paperId,
            questionData,
            paperType
          )
          return { success: true, index: i + batchIndex }
        } catch (error) {
          return {
            success: false,
            index: i + batchIndex,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })

      const batchResults = await Promise.allSettled(batchPromises)

      batchResults.forEach((result, batchIndex) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            successCount++
          } else {
            uploadErrors.push({
              row: result.value.index + 2, // +2 for header row and 0-based index
              field: 'upload',
              message: result.value.error || 'Failed to upload question'
            })
          }
        } else {
          uploadErrors.push({
            row: i + batchIndex + 2,
            field: 'upload',
            message: 'Failed to upload question'
          })
        }
      })

      // Small delay between batches to avoid overwhelming the API
      if (i + batchSize < questions.length) {
        await new Promise((resolve) => setTimeout(resolve, 200))
      }
    }

    const isFullSuccess = uploadErrors.length === 0
    const message = isFullSuccess
      ? `Successfully uploaded ${successCount} questions`
      : `Uploaded ${successCount} out of ${questions.length} questions. ${uploadErrors.length} failed.`

    return {
      success: isFullSuccess,
      totalRows: questions.length,
      successCount,
      errors: uploadErrors,
      message
    }
  } catch (error) {
    return {
      success: false,
      totalRows: 0,
      successCount: 0,
      errors: [
        {
          row: 1,
          field: 'system',
          message: `System error: ${error instanceof Error ? error.message : 'Unknown error'}`
        }
      ],
      message: 'Bulk upload failed due to system error'
    }
  }
}

/**
 * Generate Excel template for bulk upload
 */
export async function generateExcelTemplate(): Promise<ArrayBuffer> {
  const templateData = [
    [
      'question',
      'option_1',
      'option_2',
      'option_3',
      'option_4',
      'option_5',
      'correct_option',
      'explanation'
    ],
    [
      'What is 2 + 2?',
      '3',
      '4',
      '5',
      '6',
      '',
      '2',
      'Basic arithmetic: 2 + 2 equals 4'
    ],
    [
      'Which of the following is a prime number?',
      '4',
      '6',
      '7',
      '8',
      '9',
      '3',
      'A prime number is divisible only by 1 and itself. 7 is only divisible by 1 and 7.'
    ]
  ]

  const workbook = XLSX.utils.book_new()
  const worksheet = XLSX.utils.aoa_to_sheet(templateData)

  // Set column widths
  worksheet['!cols'] = [
    { width: 50 }, // question
    { width: 20 }, // option_1
    { width: 20 }, // option_2
    { width: 20 }, // option_3
    { width: 20 }, // option_4
    { width: 20 }, // option_5
    { width: 15 }, // correct_option
    { width: 40 } // explanation
  ]

  XLSX.utils.book_append_sheet(workbook, worksheet, 'Questions Template')

  return XLSX.write(workbook, { type: 'array', bookType: 'xlsx' })
}
