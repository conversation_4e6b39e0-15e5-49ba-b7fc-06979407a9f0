import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { MlLandingComponent } from './ml-landing/ml-landing.component';
import { WorkshopLandingComponent } from './workshop-landing/workshop-landing.component';
import { RecordedLandingComponent } from './recorded-landing/recorded-landing.component';

const routes: Routes = [
  {
    path: 'internship/ml-ai',
    component: MlLandingComponent
  },
  {
    path: 'training/live',
    component: WorkshopLandingComponent
  },
  {
    path: 'training/recorded',
    component: RecordedLandingComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CampaignsAppRoutingModule { }
