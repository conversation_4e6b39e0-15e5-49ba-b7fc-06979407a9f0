<div class="apps-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="welcome-hdr">
    <h5>Welcome to our Technical Dashboard</h5>
  </div>
  <div class="apps-list">
    <div class="app-card" (click)="takeToApp($event, 1)">
      <div class="locked-resource" (click)="takeToPlans()" *ngIf="entryDenied" title="Premium Feature">
        <img src="../../../assets/icons/lock.svg"/>
      </div>
      <img class="app-comp" src="../../../assets/icons/onlinec.svg" alt="Online Compiler" />
      <p class="app-comp">Online Compiler</p>
      <div class="options-bar">
        <div class="extra-opts--wrap">
          <span class="extra-opts"></span>
          <span class="extra-opts"></span>
        </div>
        <div class="options-card">
          <p>Language:</p>
          <select [(ngModel)]="selectedLang" (change)="selectLanguage()">
            <option value="c">C</option>
            <option value="cpp">C++</option>
            <option value="python2">Python 2</option>
            <option value="python3">Python 3</option>
            <!-- <option value="javascript">Javascript</option> -->
          </select>
        </div>
      </div>
    </div>
    <!-- <div class="app-card">
      <img src="../../../assets/icons/grow-shop.svg" alt="Coming Soon" />
      <p>Coming Soon...</p>
    </div>
    <div class="app-card">
      <img src="../../../assets/icons/grow-shop.svg" alt="Coming Soon" />
      <p>Comin Soon...</p>
    </div> -->
  </div>
</div>
