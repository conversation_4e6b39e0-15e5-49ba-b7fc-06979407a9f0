// lib/client-services/api-auth.service.ts

import Crypto<PERSON><PERSON> from 'crypto-js'
import { LoginRefresh } from '../cookies'

interface AuthParams {
  auth_version: string
  auth_timestamp: number
  auth_key: string | null
  auth_signature: string
}

export class ApiAuthService {
  // Private Key used to Sign the payload
  private static readonly PRIVATE_KEY =
    'd33d2b82fb1f2cf9b10e99591893ab2312377d4b92c29cdf238b46bcfec0e676'

  // Salt for additional payload protection
  private static readonly SALT = '1633632502'

  // Auth Params to be appended to the body
  private static authParams: AuthParams = {
    auth_version: '1.0',
    auth_timestamp: 0,
    auth_key: '',
    auth_signature: ''
  }

  /**
   * Here we take the request method, the path and the request body
   * to create our payload to be signed but we use the salt on the data first
   *
   * So for protecting API with method POST at url /users/manage/email with body
   * {"email": "<EMAIL>"}
   *
   * We have to stringify the json body and concat it with method and url
   * Concat is done using the salt string
   *
   * so we get a string 'POST1633632502/users/manage/email1633632502{"email": "<EMAIL>"}'
   * as our payload to be signed
   * @param method HTTP Method
   * @param path URL Path
   * @param body Request Body
   */
  private static _createSign(method: string, path: string, body: any): string {
    return [method, path, JSON.stringify(body)].join(this.SALT)
  }

  /**
   * We sign the message using SHA256 with the private key and get the
   * signature string
   * @param message Message to be signed
   */
  private static _signMessage(message: string): string {
    return CryptoJS.HmacSHA256(message, this.PRIVATE_KEY).toString()
  }

  /**
   * Setting all auth params
   *
   * @param method Method of the api
   * @param path Path of the URL
   * @param body Request Body
   */
  private static _setAuthParams(
    method: string,
    path: string,
    body: any
  ): AuthParams {
    this.authParams.auth_version = '1.0'
    this.authParams.auth_timestamp = +new Date()
    this.authParams.auth_key = this.getAuthKey()
    this.authParams.auth_signature = this._signMessage(
      this._createSign(method, path, body)
    )

    return this.authParams
  }

  /**
   * Get auth key from session storage
   */
  private static getAuthKey(): string | null {
    if (typeof window !== 'undefined') {
      // First try sessionStorage
      const sessionKey = sessionStorage.getItem('QMA_KEY')
      if (sessionKey) return sessionKey

      // Fallback to other storage methods
      return LoginRefresh.getKey()
    }
    return null
  }

  /**
   * Setting our auth params and appending them to the request body
   *
   * Example usage would be generateAuthedBody("POST", "/v2/user/manage/password/forgot", { "email": "<EMAIL>" })
   * @param method Method of the api in caps so "PUT" or "POST"
   * @param path Path of the URL
   * @param body Request Body
   */
  static generateAuthedBody(method: string, path: string, body: any): any {
    this._setAuthParams(method, path, body)

    /**
     * Concatenating the auth params with the request body params
     */
    const newBody = { ...body }

    // Add auth parameters to the body
    for (const key of Object.keys(this.authParams)) {
      newBody[key as keyof AuthParams] =
        this.authParams[key as keyof AuthParams]
    }

    /**
     * We Base64Encode the body with auth params
     * attached for additional security
     */
    const protectedBody = {
      data: btoa(JSON.stringify(newBody))
    }

    return protectedBody
  }
}
