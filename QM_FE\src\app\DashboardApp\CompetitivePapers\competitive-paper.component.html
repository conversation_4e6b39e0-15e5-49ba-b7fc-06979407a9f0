<div class="competitive-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Papers of major Bank and PSU exams</p>
    </div>
  </div>
  <div class="data-wrap">
    <div class="group-wrap">
      <div class="group" [class.is-selected]="selectedGroup[i]" *ngFor="let group of groups, let i = index" (click)="getPapers(group.group_id, i)">{{ group.group_name }}</div>
    </div>
    <div class="paper-wrap" *ngIf="showPapers">
      <div class="paper" *ngFor="let paper of papersForDisplay">
        <div class="locked-resource" (click)="takeToPlans()" *ngIf="paper.public == 0" title="Premium Feature">
          <img src="../../../assets/icons/lock.svg"/>
        </div>
        <h6>{{ paper.paper_name }}</h6>
        <p>No Time Limit</p>
        <p>No. of Questions: {{ paper.no_of_ques }}</p>
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>
  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>
