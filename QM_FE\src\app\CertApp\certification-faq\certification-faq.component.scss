.qm-cert--wrap {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background-color: #c3ece5;

  .qm-credit {
    text-align: center;
  }

  .qm-cert--faq {
    width: 100%;
    max-width: 1220px;
    padding: 1em;
    margin: 3em auto;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);

    .qm-faq--list .qm-faqs {
      width: 90%;
      padding: 1em;
      margin: 2rem auto;
      background-color: #1ABC9C;
      color: #fff;
      border-radius: 0.15rem;
      box-shadow: 1px 2px 15px rgba(172, 255, 251, 0.76);

      .qm-faq--header {
        display: flex;
        justify-content: space-between;

        h5 {
          margin: 0;
        }

        button {
          background-color: #1ABC9C;
          border: none;

          svg {
            transform: rotate(0deg);
            transition: all 0.4s ease-in;
          }
        }

        .is-open {
          svg {
            transform: rotate(180deg);
            transition: all 0.3s ease-out;
          }
        }
      }

      .qm-faq--item {
        .faq-answer {
          margin-top: 1rem;

          p {
            margin-top: 1.5rem;
            margin-bottom: 0.5rem;
          }

          img {
            width: 80%;
          }
        }
      }
    }
  }

  .qm-ftr {
    text-align: center;
    color: #777;
  }
}

@media (max-width: 440px) {
}