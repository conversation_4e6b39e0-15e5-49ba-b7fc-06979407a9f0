import { Component, OnInit, OnD<PERSON>roy, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Router, NavigationEnd } from '@angular/router';
import { Title } from '@angular/platform-browser';
import { WindowRef } from 'src/app/Services/window-ref.service';
import { environment } from 'src/environments/environment.prod';

import { LoginService } from '../../Services/login.service';
import { PaymentService } from 'src/app/Services/payment.service';

declare let fbq: Function;
declare let gtag: Function;

@Component({
  selector: 'app-plans',
  templateUrl: './plans.component.html',
  styleUrls: ['./plans.component.scss'],
  providers: [WindowRef]
})
export class PlansComponent implements OnInit, OnDestroy {

  @ViewChild('buyTemplate') public bTemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('hideTemplate') public hTemplate: TemplateRef<any>;

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };
  public bLogged = false;
  public orderId: string;
  public amount: number;

  public showIndicator = false;
  public subscribed = false;

  constructor(public title: Title,
              public router: Router,
              public modalService: BsModalService,
              private winRef: WindowRef,
              private loginService: LoginService,
              private paymentService: PaymentService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        title.setTitle(title.getTitle() + ' - Plans');
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });

    Promise.resolve(null).then(() => sessionStorage.getItem('logged') ? this.bLogged = true : this.bLogged = false);
  }

  ngOnInit() {
    const that = this;

    localStorage.removeItem('QPage');

    this.showIndicator = true;
    this.paymentService.getCourseQuote().subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      this.amount = parseFloat(respObj.amount);

      this.paymentService.verifyUser(sessionStorage.getItem('QMail')).subscribe(response2 => {
        const resp = JSON.parse(JSON.stringify(response2));

        if (resp.msg === 'subscribed') {
          that.subscribed = true;
        }

        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
        if (localStorage.getItem('QOpts') === 'OPBY') {
          localStorage.removeItem('QOpts');
          this.openModal(this.bTemplate);
        }
      });
    }, error => {
      that.showIndicator = false;
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  onBuy() {
    if (this.bLogged) {
      // User is logged in
      if (this.amount > 0) {
        this.openModal(this.bTemplate);
      }
    } else {
      localStorage.setItem('QPage', 'PL');
      localStorage.setItem('QOpts', 'OPBY');
      this.router.navigate(['/user/login']);
    }
  }

  onRegister() {
    localStorage.setItem('QPage', 'DB');
    this.router.navigate(['/user/login']);
  }

  onOpenTests() {
    this.router.navigate(['/dashboard']);
  }

  onCheckout() {

    const that = this;
    this.showIndicator = true;
    this.paymentService.getOrderId(this.amount * 100).subscribe(orderResponse => {
      this.orderId = orderResponse;

      const options: any = {
        'key': environment.razorpayKey, // Enter the Key ID generated from the Dashboard
        'amount':  that.amount * 100, // '250000',
        'currency': 'INR',
        'name': 'Quant Masters',
        'description': 'Quant Masters Complete Aptitude Course',
        'image': 'https://quantmasters.in/assets/QM_Logo No Text.svg',
        'order_id': that.orderId,
        'modal': {
          'escape': false
        },
        'prefill': {
          'name': sessionStorage.getItem('QUser'),
          'email': sessionStorage.getItem('QMail')
        }
      };

      options.handler = ((paymentResponse) => {
        options['payment_response_id'] = paymentResponse.razorpay_payment_id;

        that.paymentService.confirmPayment(paymentResponse.razorpay_payment_id, options.amount).subscribe(ConfirmResponse => {
          const resp = JSON.parse(JSON.stringify(ConfirmResponse));

          if (resp.msg === 'Captured' || resp.msg === 'Already Captured') {
            that.modalRef.hide();
            that.showIndicator = false;

            // Pixel here
            fbq('track', 'Purchase', {
              value: 1,
              currency: 'INR',
            });

            that.loginService.refreshUserLogin(sessionStorage.getItem('QMail')).subscribe(refreshResp => {
              const respRefresh = JSON.parse(JSON.stringify(refreshResp));

              if (respRefresh.sess) {
                sessionStorage.setItem('QMSESS_ID', respRefresh.sess);
                sessionStorage.setItem('QMA_TOK', respRefresh.token);
              }
            }, error => {

            });

            that.openModal(this.sTemplate);
          }
        }, error => {
          const resp = JSON.parse(JSON.stringify(error));
          that.showIndicator = false;
          console.log(resp.msg);
        });
      });

      const rzp = new that.winRef.nativeWindow.Razorpay(options);
      rzp.open();
    }, error => {
      that.showIndicator = false;
    });
  }

  navigateToDashboard() {
    this.showIndicator = false;
    this.router.navigate(['/dashboard']);
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

}
