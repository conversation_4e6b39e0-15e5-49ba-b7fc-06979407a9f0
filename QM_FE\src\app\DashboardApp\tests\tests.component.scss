.test-container {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .header {
    display: flex;
    height: 2.5rem;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }

    .time-box {
      display: flex;

      p {
        margin-right: 1em;
      }
    }
  }

  .test-content {
    display: flex;
    background-color: #bce4ff;
    margin: 0.5rem;
    padding: 1rem;
    height: 100%;
    filter: blur(0px);

    .test-area {
      background-color: #fff;
      width: 80%;
      margin: 0.5rem;
      padding: 1rem;

      .question {
        margin-bottom: 4em;

        .instr-text {
          margin-bottom: 2em;
        }

        .option-wrap--2 {
          display: flex;
          align-items: center;

          input {
            margin-bottom: 0;
          }

          /deep/ p {
            margin: 0;
            margin-left: 10px; 
          }
        }

        input {
          margin: 0 0.5rem 0.5rem 2rem;
        }

        .options-wrap {
          display: flex;
        }

        small {
          margin-left: 1em;
          margin-right: 2em;
        }

        .option {
          display: flex;
          align-items: center;
          margin: 0.5rem;

          label {
            margin: 0.5rem;
          }

          &.ans-wrong {
            background-color: coral;
            border-radius: 0.5rem;
          }

          &.ans-correct {
            background-color: limegreen;
            border-radius: 0.5rem;
          }
        }

        .qn-explanation div {
          display: grid;
          place-items: center;
          margin-bottom: 1rem;

          img {
            max-height: 300px;
          }
        }
      }

      button {
        left: 80%;
      }
    }
  }

  button {
    cursor: pointer;
    position: relative;
    
    margin: 1rem;
    margin-top: 0;
    width: 9rem;
    padding: 0.3rem;
    background-color: #1ABC9C;
    border: none;
    border-radius: 5px;
    color: #000;
    box-shadow: 1px 0px 5px rgba(0, 0, 0, 0.16);

    &:disabled{
      cursor: not-allowed;
      background-color: gray;
    }
  }

  .tracking-wrap {
    position: fixed;
    top: 12%;
    left: 82%;
    width: 16%;
    height: 100%;
    overflow-y: scroll;

    .time-box-2 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }

    .tracking {
      width: 100%;
      padding: 1rem;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      grid-auto-rows: 2rem;
      grid-gap: 10px;

      .item {
        cursor: pointer;

        & p:hover {
          background-color: #ccc;
        }

        .item-inner {
          height: 100%;
          text-align: center;
          text-justify: center;
          border-radius: 3px;
          background-color: #fff;
        }
        .answered {
          background-color: rgb(66, 175, 66);
        }
        // .answered{
        //   background-color: rgb(212, 124, 124);
        // }
      }
    }

    .tracking-legend {
      height: 100%;
      padding: 1rem;

      .legend {
        display: flex;
        align-items: center;
        margin-bottom: 1em;

        div {
          height: 32px;
          width: 45px;
          margin-right: 1em;
          background-color: #fff;
        }

        p {
          margin: 0;
        }

        &:last-of-type div {
          background-color: rgb(66, 175, 66);
        }
      }
    }
  }

  .blur-bg {
    filter: blur(5px);
  }

  .footer {
    width: 100%;
    display: flex;
    align-items: flex-end;
    button {
      cursor: pointer;
      margin: 1rem;
      width: 10rem;
      padding: 0.3rem;
      background-color: #e88224;
      border: none;
      border-radius: 5px;
      color: white;

      &:disabled{
        cursor: not-allowed;
        background-color: gray;
      }
    }
  }
}

/deep/ .breakdown-table {

  .table-heading {
    letter-spacing: 2px;
  }

  .headers p {
    font-weight: 700;
  }

  div {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0.5rem;
    min-height: 2.5rem;

    &:nth-of-type(2n+1) {
      background-color: #D5D8DC;
    }

    &:nth-of-type(1) {
      background-color: #0B6FB1;
      color: #fff;
    }
  }
}

.copy-content {
  text-align: right;

  p {
    color: #707070;
    margin: 0;
  }
}

@media (max-width: 440px) {
  .test-container {
    .header {
      height: 3.5rem;

      .title {
        .time-box {
          display: none;
        }
      }
    }

    .test-content {
      padding: 0;

      .test-area {
        width: 100%;
        padding: 0.1rem;
        margin: 0;
        margin-top: 5em;

        /deep/ img {
          width: 100%;
        }

        button {
          left: 0;
        }
      }
    }

    .tracking-wrap {
      top: 80%;
      left: 68%;
      width: 30%;
      height: initial;
      text-align: center;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0px 3px 20px rgba(0, 0, 0, 0.16);

      .tracking {
        display: none;
      }

      .tracking-legend {
        display: none;
      }
    }
  }
}
