// lib/client-services/company-service.client.ts

import axios from 'axios'
import {
  ChapterPaper,
  UserPermissionResponse,
  getCompanyRegex
} from '@/types/company-paper-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  papersUrl: `${API_BASE_URL}/test/company/papers`,
  submitUrl: `${API_BASE_URL}/test/company/submit/marks`,
  checkUserUrl: `${API_BASE_URL}/user/manage`,
  openPapersUrl: `${API_BASE_URL}/test/open/papers`,
  freeCompanyPapersUrl: `${API_BASE_URL}/test/sample/company/papers`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class CompanyService {
  /**
   * Get all company papers from free company papers endpoint
   */
  static async getAllPapers(): Promise<ChapterPaper[]> {
    try {
      const response = await axios.get(
        ENDPOINTS.freeCompanyPapersUrl,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching company papers:', error)
      return []
    }
  }

  /**
   * Check user login and permissions
   */
  static async checkUserLogin(email: string): Promise<UserPermissionResponse> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.checkUserUrl}/${email}/perm/check`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error checking user permissions:', error)
      throw error
    }
  }

  /**
   * Submit company paper marks
   */
  static async submitMarks(submission: {
    email: string
    paper_id: string
    marks: number
    answered_on: string
  }): Promise<any> {
    try {
      const response = await axios.post(
        ENDPOINTS.submitUrl,
        submission,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error submitting marks:', error)
      throw error
    }
  }

  /**
   * Filter papers by company type using regex patterns
   */
  static filterPapersByCompanyType(
    papers: ChapterPaper[],
    companyType: number
  ): ChapterPaper[] {
    const regexp = getCompanyRegex(companyType)

    return papers.filter((paper) => paper.paper_name.match(regexp) != null)
  }
}
