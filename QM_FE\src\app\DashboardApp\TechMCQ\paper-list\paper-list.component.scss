.tmcq-wrap {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .page-header {
    display: flex;
    height: 2.5rem;
    margin-bottom: 2em;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .data-wrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .btn-blue {
      background-color: #0b6fb1;
      color: #fff;
    }

    .qm-select--wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .btn-group {
        width: 30%;

        ul {
          width: 100%;
          cursor: pointer;
        }
      }
    }

    .qm-tmcq--papers {
      width: 70%;
      padding: 10px;
      margin-top: 3em;

      .qm-paper {
        position: relative;
        display: grid;
        grid-template-columns: 30% 30% 20% 20%;
        align-items: center;
        padding: 0.5em;
        color: #fff;
        background-color: #38A3E9;
        margin-bottom: 1em;
  
        h6, p {
          margin: 0;
        }
  
        button {
          justify-self: end;
          padding: 0.5em 1em;
          color: #fff;
          cursor: pointer;
          background-color: #E88224;
          border: none;
          border-radius: 5px;
          transition: all 0.4s ease-out;
  
          &:hover {
            background-color: #0B6FB1;
            transition: all 0.2s ease-in;
          }
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .tmcq-wrap .data-wrap {
    .qm-select--wrap {
      flex-direction: column;
      align-items: flex-end;
      margin-right: 3em;

      .btn-group {
        width: 75%;
        margin-top: 1em;
      }
    }

    .qm-tmcq--papers {
      width: 100%;

      .qm-paper {
        padding: 20px;
        grid-template-columns: 1fr;
        gap: 0.7rem;

        .locked-resource {
          height: 100%;

          img {
            height: 50%;
          }
        }
      }
    }
  }
}