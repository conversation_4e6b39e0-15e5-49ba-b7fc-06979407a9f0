quant-masters-next/
├── app/
│   ├── (marketing)/
│   │   ├── page.tsx             # Home page
│   │   ├── achievers/
│   │   │   └── page.tsx
│   │   ├── checkout/
│   │   │   └── page.tsx
│   │   ├── links/
│   │   │   └── page.tsx
│   │   └── privacy-policy/
│   │       └── page.tsx
│   ├── (auth)/
│   │   └── user/
│   │       └── [...slug]/
│   │           └── page.tsx
│   ├── (dashboard)/
│   │   └── dashboard/
│   │       └── [...slug]/
│   │           └── page.tsx
│   ├── (technical)/
│   │   └── technical/
│   │       └── [...slug]/
│   │           └── page.tsx
│   ├── (admin)/
│   │   └── admin/
│   │       └── console/
│   │           └── [...slug]/
│   │               └── page.tsx
│   ├── blog/
│   │   └── [...slug]/
│   │       └── page.tsx
│   ├── notes/
│   │   └── [...slug]/
│   │       └── page.tsx
│   ├── placement/
│   │   └── [...slug]/
│   │       └── page.tsx
│   ├── certification/
│   │   └── [...slug]/
│   │       └── page.tsx
│   ├── faqs/
│   │   └── [...slug]/
│   │       └── page.tsx
│   ├── layout.tsx              # Root layout
│   ├── loading.tsx             # Global loading component
│   └── not-found.tsx           # 404 page
├── components/
│   ├── ui/                     # Shared UI components
│   ├── layout/
│   │   ├── nav.tsx             # Navigation component
│   │   └── footer.tsx          # Footer component
│   └── shared/
│       └── indicator.tsx       # Loading indicator
├── lib/
│   ├── auth.ts                 # Authentication utilities
│   ├── cookies.ts              # Cookie handling
│   └── utils.ts                # General utilities
├── public/
│   └── ...                     # Static assets
├── next.config.js
├── package.json
├── postcss.config.js           # For Tailwind
├── tailwind.config.js
└── tsconfig.json