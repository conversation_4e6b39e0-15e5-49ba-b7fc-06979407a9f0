'use client'

import dynamic from 'next/dynamic'
import { useEffect, useState, useRef } from 'react'

// Dynamically import all chart components with SSR disabled
const LazyLineChart = dynamic(
  () => import('./charts').then((mod) => mod.LineChart),
  { ssr: false }
)
const LazyBarChart = dynamic(
  () => import('./charts').then((mod) => mod.BarChart),
  { ssr: false }
)
const Lazy<PERSON>ie<PERSON>hart = dynamic(
  () => import('./charts').then((mod) => mod.PieChart),
  { ssr: false }
)
const LazyDoughnutChart = dynamic(
  () => import('./charts').then((mod) => mod.DoughnutChart),
  { ssr: false }
)
const LazyRadarChart = dynamic(
  () => import('./charts').then((mod) => mod.RadarChart),
  { ssr: false }
)
const LazyBubbleChart = dynamic(
  () => import('./charts').then((mod) => mod.<PERSON><PERSON><PERSON><PERSON>),
  { ssr: false }
)
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> = dynamic(
  () => import('./charts').then((mod) => mod.AreaChart),
  { ssr: false }
)

// Chart component with intersection observer for scroll reveal
const ScrollRevealChart = ({
  Chart,
  className
}: {
  Chart: React.FC
  className: string
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef(null)

  useEffect(() => {
    const current = ref.current

    const observer = new IntersectionObserver(
      ([entry]) => {
        // When the element enters the viewport
        if (entry.isIntersecting && current) {
          setIsVisible(true)
          // Once visible, no need to observe anymore
          observer.unobserve(current)
        }
      },
      {
        root: null, // viewport
        rootMargin: '50px', // Start revealing a bit earlier
        threshold: 0.1 // Trigger when 10% of the element is visible
      }
    )

    if (current) {
      observer.observe(current)
    }

    return () => {
      if (current) {
        observer.unobserve(current)
      }
    }
  }, [])

  return (
    <div
      ref={ref}
      className={`transition-all duration-1000 ease-out ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'} ${className}`}
    >
      {isVisible && <Chart />}
    </div>
  )
}

export default function PageFixedCharts() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  // Chart configurations - all with absolute positioning to scroll with the page
  const charts = [
    // Top section charts
    {
      id: 'top-right-line',
      Component: LazyLineChart,
      className:
        'absolute top-[50px] right-[-100px] w-96 h-64 opacity-30 transform rotate-6 z-0'
    },
    {
      id: 'top-left-area',
      Component: LazyAreaChart,
      className:
        'absolute top-[100px] left-[-50px] w-80 h-60 opacity-25 transform -rotate-3 z-0'
    },
    {
      id: 'top-center-pie',
      Component: LazyPieChart,
      className:
        'absolute top-[150px] right-[35%] w-52 h-52 opacity-20 transform rotate-12 z-0'
    },

    // Middle section charts
    {
      id: 'mid-right-doughnut',
      Component: LazyDoughnutChart,
      className:
        'absolute top-[450px] right-[100px] w-72 h-72 opacity-35 transform -rotate-6 z-0'
    },
    {
      id: 'mid-left-bubble',
      Component: LazyBubbleChart,
      className: 'absolute top-[550px] left-[-80px] w-64 h-64 opacity-25 z-0'
    },
    {
      id: 'mid-center-radar',
      Component: LazyRadarChart,
      className:
        'absolute top-[700px] left-[40%] w-60 h-60 opacity-20 transform rotate-15 z-0'
    },

    // Lower section charts
    {
      id: 'lower-right-bar',
      Component: LazyBarChart,
      className:
        'absolute top-[950px] right-[-60px] w-80 h-64 opacity-40 transform rotate-3 z-0'
    },
    {
      id: 'lower-left-line',
      Component: LazyLineChart,
      className:
        'absolute top-[1100px] left-[50px] w-70 h-56 opacity-30 transform -rotate-4 z-0'
    },
    {
      id: 'lower-center-area',
      Component: LazyAreaChart,
      className: 'absolute top-[1250px] left-[35%] w-80 h-64 opacity-25 z-0'
    },

    // Bottom section charts
    {
      id: 'bottom-right-pie',
      Component: LazyPieChart,
      className: 'absolute top-[1500px] right-[100px] w-60 h-60 opacity-30 z-0'
    },
    {
      id: 'bottom-left-radar',
      Component: LazyRadarChart,
      className:
        'absolute top-[1600px] left-[-100px] w-72 h-72 opacity-20 transform -rotate-12 z-0'
    },
    {
      id: 'bottom-center-doughnut',
      Component: LazyDoughnutChart,
      className: 'absolute top-[1800px] left-[40%] w-56 h-56 opacity-25 z-0'
    },

    // Extra charts extending beyond typical page length
    {
      id: 'extra-right-bubble',
      Component: LazyBubbleChart,
      className:
        'absolute top-[2000px] right-[-50px] w-72 h-72 opacity-30 transform rotate-10 z-0'
    },
    {
      id: 'extra-left-bar',
      Component: LazyBarChart,
      className:
        'absolute top-[2200px] left-[-80px] w-96 h-64 opacity-25 transform -rotate-8 z-0'
    },
    {
      id: 'extra-center-line',
      Component: LazyLineChart,
      className: 'absolute top-[2400px] left-[30%] w-80 h-60 opacity-20 z-0'
    }
  ]

  return (
    <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
      {charts.map((chart) => (
        <ScrollRevealChart
          key={chart.id}
          Chart={chart.Component as any}
          className={chart.className}
        />
      ))}
    </div>
  )
}
