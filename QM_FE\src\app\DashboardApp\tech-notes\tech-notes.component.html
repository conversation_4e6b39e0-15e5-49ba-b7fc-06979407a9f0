<div class="tech-otes-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Technical Notes</p>
    </div>
  </div>
  <div class="data-wrap">
    <div class="qm-select--wrap">
      <div class="btn-group" dropdown>
        <button id="button-topic" dropdownToggle type="button" class="btn btn-blue dropdown-toggle"
                aria-controls="dropdown-topic">
          {{ selectedGroup ? 'Group: ' + selectedGroup : 'Select Group' }} <span class="caret"></span>
        </button>
        <ul id="dropdown-topic" *dropdownMenu class="dropdown-menu"
            role="menu" aria-labelledby="button-topic">
          <li *ngFor="let sup_group of superGroups" role="menuitem"><a class="dropdown-item" (click)="onSelectSuperGroup(sup_group.super_id, sup_group.super_name)">{{ sup_group.super_name }}</a></li>
        </ul>
      </div>
      <div class="btn-group" dropdown>
        <button id="button-topic" dropdownToggle type="button" class="btn btn-blue dropdown-toggle"
                aria-controls="dropdown-sub-topic">
          {{ selectedSubGroup ? 'Sub Group: ' + selectedSubGroup : 'Select Sub Group' }} <span class="caret"></span>
        </button>
        <ul id="dropdown-topic" *dropdownMenu class="dropdown-menu"
            role="menu" aria-labelledby="button-sub-topic">
          <li *ngFor="let subGroup of displayGroups" role="menuitem"><a class="dropdown-item" (click)="getSubGroup(subGroup.group_id, subGroup.group_name)">{{ subGroup.group_name }}</a></li>
        </ul>
      </div>
    </div>
    <div class="qm-tech-notes" *ngIf="notesOfGroup && notesOfGroup.length > 0">
      <div class="qm-notes" *ngFor="let Notes of notesOfGroup; let i = index">
        <h6>{{ Notes.notes_name }}</h6>
        <p>Posted On: {{ Notes.posted_on }}</p>
        <button (click)="takeToNotesDetail(Notes.notes_id, i)">Start Reading</button>
      </div>
    </div>
  </div>
</div>