<div class="page-wrapper">
  <!-- <nav class="navbar navbar-expand-lg navbar-light admin-navbar" style="background-color: #e3f2fd;">
    <a class="navbar-brand" routerLink="/home">
      <img src="../../assets/QM_Logo_no_txt.svg" height="50" width="50" class="d-inline-block align-top"
        alt="QM Logo" />
      Quant Masters
    </a>
    <p *ngIf="!pageDisable">Welcome, {{ adminName }}</p>
    <button *ngIf="!pageDisable" (click)="logout()" class="btn btn-warning">Exit Admin Console</button>
  </nav> -->
  <app-admin-nav></app-admin-nav>
  <app-left-nav></app-left-nav>
  <router-outlet></router-outlet>
  <!-- <div [@openClose]="isOpen ? 'open' : 'closed'" class="left-sidebar" *ngIf="!pageDisable">
    <div (click)="showOpts(0)" tooltip="View Registrations" placement="right" container="body" class="view-reg--btn">
    </div>
    <div (click)="showOpts(1)" tooltip="Add New Test" placement="right" container="body" class="add-btn"></div>
    <div class="view-btn" tooltip="View All Tests" placement="right" container="body"></div>
  </div>
  <div [@openClose]="isOpen ? 'open' : 'closed'" (click)="toggleSidebar()" *ngIf="!pageDisable" class="sidebar-toggle">
    <span></span><span></span><span></span></div> -->
  <!-- <div *ngIf="showContent" class="content-settings--container">
    <div class="row">
      <div>
        <h5>View Registrations of Any Given Day</h5>
      </div>
    </div>
    <div class="row">
      <form class="form-inline" #byDateForm="ngForm" (ngSubmit)="viewRegistrations()">
        <div class="form-group ml-4 mr-4">
          <label for="dop" class="col-from-label">Select Date</label>
        </div>
        <div class="from-group">
          <input type="text" name="dop" class="form-control mr-4" bsDatepicker #dp="bsDatepicker" autocomplete="off"
            [bsConfig]="bsConfig" [bsValue]="bsValue" [(ngModel)]="selectedDate" #dop="ngModel" />
          <button class="btn btn-primary" type="submit">Show</button>
        </div>
      </form>
    </div>
  </div>
  <div *ngIf="showContent" class="content-area--container">
    <div class="card" *ngIf="showContent">
      <div class="card-body">
        <ul class="list-group">
          <li class="list-group-item list-group-item-primary users-grid">
            <h5>Email</h5>
            <h5>Full Name</h5>
            <h5>Qualification</h5>
            <h5>College / Institution</h5>
            <h5>USN</h5>
            <h5>Year of Passing</h5>
          </li>
          <li *ngFor="let user of userList" class="list-group-item  users-grid">
            <p>{{ user.email }}</p>
            <p>{{ user.f_name + " " + user.l_name }}</p>
            <p>{{ user.qual }}</p>
            <p>{{ user.inst_name }}</p>
            <p>{{ user.usn }}</p>
            <p>{{ user.yop }}</p>
          </li>
        </ul>
      </div>
    </div>
  </div> -->
  <!-- <div *ngIf="isOpenOpts1" class="add-new">
    <div *ngFor="let test of testTypes" class="opt-1">{{ test }}</div>
  </div> -->
  <ng-template #unauthTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-danger">Are you an Admin?</h4>
    </div>
    <div class="modal-body">
      <p>If so, kindly <a routerLink="/user/login">login</a> and come back here, otherwise...</p>
      <p>We think you'll be better off checking out our other cool content <a routerLink="/home">here.</a></p>
    </div>
  </ng-template>
  <ng-template #resEmptyTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-danger">Oops</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Looks like nobody regsitered on that day...</p>
    </div>
  </ng-template>
</div>