import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

import { NewUser } from '../Models/NewUser';
import { VideoWatchTrack } from '../Models/Tracking/VideoWatchTrack';
import { Workshop } from '../Models/Workshop';
import { Marks } from '../Models/Marks';
import { UserLoginTrack } from '../Models/UserLoginTrack';

import { ApiAuthService } from './api-auth.service';
import { User } from '../Models/User';

@Injectable({
  providedIn: 'root',
})
export class AdminViewService {
  private viewRegUrl = 'https://api.quantmasters.in/admin/view/registered';
  private viewRegAll = 'https://api.quantmasters.in/v2/admin/users/';
  private viewPurchaseUrl = 'https://api.quantmasters.in/admin/purchased';
  private viewRegLastWeekUrl =
    'https://api.quantmasters.in/admin/register/lastweek';

  private workshopAllUrl = 'https://api.quantmasters.in/admin/workshops/all';
  private workshopVideoUrl =
    'https://api.quantmasters.in/admin/workshop/videos/';

  private videoWatchUsageUrl =
    'https://api.quantmasters.in/track/video/usage/all';

  private basicFilterUrl =
    'https://api.quantmasters.in/v2/admin/results/filter/basic';

  private baseUrl = 'https://api.quantmasters.in';

  private JwtToken: string;

  constructor(
    private http: HttpClient,
    private apiAuthService: ApiAuthService
  ) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getAllRegisteredList(index: number, take: number): Observable<NewUser[]> {
    this.setSecurityToken();

    const url = `${this.viewRegAll}${index}/${take}/list`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<NewUser[]>(url, httpOps);
  }

  getAllPurchasedList(purchased: boolean): Observable<NewUser[]> {
    this.setSecurityToken();

    let option = 'not';
    if (purchased) {
      option = 'all';
    }

    const sUrl = this.viewPurchaseUrl + '/' + option;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<NewUser[]>(sUrl, httpOps);
  }

  getAllRegisteredLastWeek(): Observable<NewUser[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<NewUser[]>(this.viewRegLastWeekUrl, httpOps);
  }

  getRegisteredList(paperDate: string): Observable<String> {
    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
      params: new HttpParams().set('date', paperDate),
    };

    return this.http.get<string>(this.viewRegUrl, httpOps);
  }

  getUserLoginTrackData(idx: number): Observable<UserLoginTrack[]> {
    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<UserLoginTrack[]>(
      `${this.baseUrl}/v2/admin/user/${idx}/multiple-login`,
      httpOps
    );
  }

  getUserDetails(email: string): Observable<NewUser> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<NewUser>(
      `${this.baseUrl}/v2/admin/user/${email}/search`,
      httpOps
    );
  }

  getWorkshopsRegistrations(): Observable<Workshop[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<Workshop[]>(this.workshopAllUrl, httpOps);
  }

  getAllVideoWatchTrack(): Observable<VideoWatchTrack[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<VideoWatchTrack[]>(this.videoWatchUsageUrl, httpOps);
  }

  filterResultsBasics(sValue: string): Observable<Marks[]> {
    this.setSecurityToken();

    const url = this.basicFilterUrl;

    const body = {
      searchText: sValue,
    };

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.post<Marks[]>(url, body, httpOps);
  }

  filterResultsByPaperName(sValue: string): Observable<Marks[]> {
    this.setSecurityToken();

    const url = this.basicFilterUrl;

    const body = {
      paperName: sValue,
    };

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.post<Marks[]>(url, body, httpOps);
  }
  filterResultsByPaperID(sValue: string): Observable<Marks[]> {
    this.setSecurityToken();

    const url = `${this.baseUrl}/v2/admin/test/open/competitive/paper/${sValue}/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<Marks[]>(url, httpOps);
  }
  getWorkshopsViewGroups(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(this.workshopVideoUrl + 'groups', httpOps);
  }

  getWorkshopsViewSubGroups(groupId: string): Observable<string> {
    this.setSecurityToken();

    const url = this.workshopVideoUrl + groupId + '/subgroups';

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(url, httpOps);
  }

  getWeeklyCompetitivePapers(): Observable<string> {
    this.setSecurityToken();

    const url = this.baseUrl + `/v2/admin/test/open/competitive/papers`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(url, httpOps);
  }

  getCompanyPapers(): Observable<string> {
    this.setSecurityToken();

    const url = this.baseUrl + `/v2/admin/company/papers`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(url, httpOps);
  }

  putWeeklyCompetitiveDetails(
    paperDetails: object,
    paper_id: string
  ): Observable<any> {
    this.setSecurityToken();

    const url =
      this.baseUrl + '/v2/admin/test/open/competitive/paper/' + paper_id;

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    const protectedBody = this.apiAuthService.generateAuthedBody(
      'PUT',
      '/v2/admin/test/open/competitive/paper/' + paper_id,
      paperDetails
    );

    return this.http.put<string>(url, protectedBody, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
