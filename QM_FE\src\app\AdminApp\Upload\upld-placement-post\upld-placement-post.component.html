<div class="updl-placement--wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <h5>Manage Placement Posts</h5>
  <div class="posts-wrap">
    <button class="custom-btn" (click)="openAddNewPost()">New</button>
    <div class="posts-table">
      <h5>All Posts ({{ postsCount }})</h5>
      <div class="job-entry" *ngFor="let post of displayRecords; let i = index">
        <form autocomplete="off" #editForm="ngForm" (ngSubmit)="editPost(editForm.valid)">
          <input type="hidden" disabled name="id" [(ngModel)]="post.id" />
          <div class="form-elem form-elem--1">
            <button class="img-updl--btn1" type="button" (click)="inputDisabled[i] = !inputDisabled[i]">
              <img src="../../../assets/push-icons/edit-form.svg" />
            </button>
            <button class="img-updl--btn1" type="button" (click)="onClickDeleteJobPost(post.id, i)">
              <img src="../../../assets/push-icons/trash.svg" />
            </button>
            <button class="custom-btn" type="button" [hidden]="inputDisabled[i]" (click)="fnSaveModifiedData(post, i)">
              Save 
            </button>
          </div>
          <div class="form-elem">
            <label for="company">Company:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="company" [(ngModel)]="post.company" />
          </div>
          <div class="form-elem">
            <label for="title">Title:</label>
            <textarea class="text-title" type="text" [disabled]="inputDisabled[i]" name="title"
              [(ngModel)]="post.title"></textarea>
          </div>
          <div class="form-elem">
            <label for="description">Description:</label>
            <textarea type="text" [disabled]="inputDisabled[i]" name="description"
              [(ngModel)]="post.description"></textarea>
          </div>
          <div class="form-elem">
            <label for="location">Location:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="location" [(ngModel)]="post.location" />
          </div>
          <div class="form-elem">
            <label for="date">Date:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="date" [(ngModel)]="post.on_date" />
          </div>
          <div class="form-elem">
            <label for="position">Position:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="position" [(ngModel)]="post.detail.position" />
          </div>
          <div class="form-elem">
            <label for="experience">Experience:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="experience" [(ngModel)]="post.detail.experience" />
          </div>
          <div class="form-elem">
            <label for="qualification">Qualification:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="qualification" [(ngModel)]="post.detail.qualification" />
          </div>
          <div class="form-elem">
            <label for="batch">Batch:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="batch" [(ngModel)]="post.detail.batch" />
          </div>
          <div class="form-elem">
            <label for="branch">Branch:</label>
            <input type="text" [disabled]="inputDisabled[i]" name="branch" [(ngModel)]="post.detail.branch" />
          </div>
          <div class="form-elem">
            <label for="apply_link">Link:</label>
            <input class="text-link" type="text" [disabled]="inputDisabled[i]" name="apply_link"
              [(ngModel)]="post.apply_link" />
          </div>
          <div class="form-elem">
            <label for="existingImages">Existing Logo: </label>
            <select name="existingImages" (change)="selectExsitingImage()" [(ngModel)]="post.logo_name"
              #existingImages="ngModel">
              <option *ngFor="let img of exitingImages; let i = index">{{ img }}</option>
            </select>
            <button type="button" class="custom-btn" (click)="previewImage(post.logo_name)">Preview</button>
          </div>
        </form>
        <form enctype="multipart/form-data" class="form-edit-part">
          <div class="form-elem">
            <label>New Logo: </label>
            <input style="display: none" type="file" (change)="fileSelect($event, post.id)" #logoSelect />
            <button class="img-updl--btn" type="button" title="Select Image" (click)="logoSelect.click()">
              <img src="../../../assets/push-icons/edit.svg" />
            </button>
          </div>
        </form>
      </div>
      <pagination [boundaryLinks]="true" [totalItems]="allPostLegth" class="job-list-pagination" [rotate]="true"
          [maxSize]="7" [itemsPerPage]="5" (pageChanged)="pageChanged($event)"></pagination>
    </div>
  </div>
  <ng-template #createNewTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-primary">Create New Post</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <h5>Enter details</h5>
      <form autocomplete="off" #createForm="ngForm" (ngSubmit)="postNewPost(createForm.valid)">
        <div class="form-elem">
          <label for="company">Company:</label>
          <input type="text" name="company" [(ngModel)]="newPost.company" #company="ngModel"
            [class.is-inv--input]="company.invalid && company.touched || (company.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="company.errors?.required && company.touched || (company.pristine && createForm.submitted)">
          Company Name is Required
        </small>
        <div class="form-elem">
          <label for="title">Title:</label>
          <textarea class="text-title" type="text" name="title" [(ngModel)]="newPost.title" #title="ngModel"
            [class.is-inv--input]="title.invalid && title.touched || (title.pristine && createForm.submitted)"
            required></textarea>
        </div>
        <small class="form-error--text"
          *ngIf="title.errors?.required && title.touched || (title.pristine && createForm.submitted)">
          Title is Required
        </small>
        <div class="form-elem">
          <label for="description">Description:</label>
          <textarea type="text" name="description" [(ngModel)]="newPost.description" #description="ngModel"
            [class.is-inv--input]="description.invalid && description.touched || (description.pristine && createForm.submitted)"
            required></textarea>
        </div>
        <small class="form-error--text"
          *ngIf="description.errors?.required && description.touched || (description.pristine && createForm.submitted)">
          Description is Required
        </small>
        <div class="form-elem">
          <label for="location">Location:</label>
          <input type="text" name="location" [(ngModel)]="newPost.location" #location="ngModel"
            [class.is-inv--input]="location.invalid && location.touched || (location.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="location.errors?.required && location.touched || (location.pristine && createForm.submitted)">
          Location is Required
        </small>
        <div class="form-elem">
          <label for="on_date">Date:</label>
          <input type="text" name="on_date" bsDatepicker #dp="bsDatepicker" autocomplete="off" [bsConfig]="bsDateConfig"
            [bsValue]="bsValue" [(ngModel)]="newPost.on_date" #on_date="ngModel"
            [class.is-inv--input]="on_date.invalid && on_date.touched || (on_date.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="on_date.errors?.required && on_date.touched || (on_date.pristine && createForm.submitted)">
          Date is Required
        </small>
        <div class="form-elem">
          <label for="position">Position:</label>
          <input type="text" name="position" [(ngModel)]="newDetail.position" #position="ngModel"
            [class.is-inv--input]="position.invalid && position.touched || (position.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="position.errors?.required && position.touched || (position.pristine && createForm.submitted)">
          Position is Required
        </small>
        <div class="form-elem">
          <label for="experience">Experience:</label>
          <input type="text" name="experience" [(ngModel)]="newDetail.experience" #experience="ngModel"
            [class.is-inv--input]="experience.invalid && experience.touched || (experience.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="experience.errors?.required && experience.touched || (experience.pristine && createForm.submitted)">
          Experience is Required
        </small>
        <div class="form-elem">
          <label for="qualification">Qualification:</label>
          <input type="text" name="qualification" [(ngModel)]="newDetail.qualification" #qualification="ngModel"
            [class.is-inv--input]="qualification.invalid && qualification.touched || (qualification.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="qualification.errors?.required && qualification.touched || (qualification.pristine && createForm.submitted)">
          Qualification is Required
        </small>
        <div class="form-elem">
          <label for="batch">Batch:</label>
          <input type="text" name="batch" [(ngModel)]="newDetail.batch" #batch="ngModel"
            [class.is-inv--input]="batch.invalid && batch.touched || (batch.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="batch.errors?.required && batch.touched || (batch.pristine && createForm.submitted)">
          Batch is Required
        </small>
        <div class="form-elem">
          <label for="branch">Branch:</label>
          <input type="text" name="branch" [(ngModel)]="newDetail.branch" #branch="ngModel"
            [class.is-inv--input]="branch.invalid && branch.touched || (branch.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="apply_link.errors?.required && apply_link.touched || (apply_link.pristine && createForm.submitted)">
          Branch is Required
        </small>
        <div class="form-elem">
          <label for="apply_link">Link:</label>
          <input type="text" name="apply_link" [(ngModel)]="newPost.apply_link" #apply_link="ngModel"
            [class.is-inv--input]="apply_link.invalid && apply_link.touched || (apply_link.pristine && createForm.submitted)"
            required />
        </div>
        <small class="form-error--text"
          *ngIf="apply_link.errors?.required && apply_link.touched || (apply_link.pristine && createForm.submitted)">
          Link is Required
        </small>
        <div class="form-elem">
          <button class="custom-btn" type="submit">Create</button>
        </div>
      </form>
    </div>
  </ng-template>
  <ng-template #succTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-success">Its Done!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      {{ successText }}
    </div>
  </ng-template>
  <ng-template #errTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-danger">Oopsie!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>{{ errMsg }}</p>
    </div>
  </ng-template>
  <ng-template #imgUpdlTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Change Profile Image</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Only .png, jpg and .jpeg images are supported</p>
      <p>Max file size is 10MB</p>
      <button class="btn btn-primary mr-4" (click)="uploadFile()">Upload</button>
      <button class="btn btn-warning" (click)="cancelUpload()">Cancel</button>
    </div>
  </ng-template>
</div>