'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { ChapterPaper } from '@/types/company-paper-types'
import { CompanyService } from '@/lib/client-services/company-service.client'
import LoadingIndicator from '@/components/shared/indicator'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter
} from '@/components/ui/card'
import { Clock, FileText, ArrowLeft } from 'lucide-react'

interface CompanyPapersDetailProps {
  companyType: number
  pageHeading: string
  initialPapers?: ChapterPaper[]
  hasAccess?: boolean
}

export default function CompanyPapersDetail({
  companyType,
  pageHeading,
  initialPapers = []
}: CompanyPapersDetailProps) {
  const router = useRouter()
  const [papers, setPapers] = useState<ChapterPaper[]>(initialPapers)
  const [isLoading, setIsLoading] = useState(false)

  // If no initial papers provided, fetch them client-side
  useEffect(() => {
    if (initialPapers.length === 0) {
      const fetchPapers = async () => {
        setIsLoading(true)

        try {
          // Fetch all papers from the API
          const allPapers = await CompanyService.getAllPapers()

          // Filter papers by company type using the service method
          const filteredPapers = CompanyService.filterPapersByCompanyType(
            allPapers,
            companyType
          )

          setPapers(filteredPapers)
        } catch (error) {
          console.error('Error fetching company papers:', error)
          setPapers([])
        } finally {
          setIsLoading(false)
        }
      }

      fetchPapers()
    }
  }, [companyType, initialPapers.length])

  const beginTest = (paperId: string, paperName: string, paperLim: number) => {
    // Navigate to test page with proper parameters
    router.push(
      `/dashboard/test/7/${paperId}/${encodeURIComponent(paperName)}/${paperLim}`
    )
  }

  const goBack = () => {
    router.back()
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingIndicator isLoading={isLoading} />
      </div>
    )
  }

  return (
    <div className="company-papers--wrap">
      {/* Back button */}
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={goBack}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Company Papers
        </Button>
      </div>

      {/* Papers list */}
      <div className="papers-wrap">
        {papers.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-semibold text-gray-600 mb-2">
              No Papers Available
            </h3>
            <p className="text-gray-500">
              No papers are currently available for {pageHeading}.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {papers.map((paper) => (
              <Card
                key={paper.paper_id}
                className="h-full flex flex-col hover:shadow-lg transition-shadow"
              >
                <CardHeader>
                  <CardTitle className="text-lg">{paper.paper_name}</CardTitle>
                  <CardDescription className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>
                      {paper.time_lim > 0
                        ? `${paper.time_lim / (1000 * 60)} Minutes`
                        : 'No Time Limit'}
                    </span>
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="flex items-center space-x-1">
                    <FileText className="h-4 w-4" />
                    <span>No. of Questions: {paper.no_of_ques}</span>
                  </div>
                </CardContent>

                <CardFooter className="mt-auto">
                  <Button
                    onClick={() =>
                      beginTest(
                        paper.paper_id,
                        paper.paper_name,
                        paper.time_lim
                      )
                    }
                    className="w-full"
                  >
                    Begin Test
                    <ArrowLeft className="ml-2 h-4 w-4 rotate-180" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
