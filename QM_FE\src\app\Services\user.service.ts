import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ApiAuthService } from './api-auth.service';

import { NewUser } from '../Models/NewUser';
import { UserLoginTrack } from '../Models/UserLoginTrack';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private userUrl       = 'https://api.quantmasters.in/user/manage';
  private subscriberUrl = 'https://api.quantmasters.in/prelogin';

  private v2BaseUrl = 'https://api.quantmasters.in/v2/';

  private JwtToken: string;

  constructor(private http: HttpClient,
              private apiAuthService: ApiAuthService) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getUserProfileDetails(email: string): Observable<NewUser> {
    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const getDetailUrl = this.userUrl + '/' + email + '/profile';

    return this.http.get<NewUser>(getDetailUrl , httpOpts);
  }

  setUserProfileDetails(email: string, userInfo: NewUser) {
    this.setSecurityToken();

    const setDetailUrl = this.v2BaseUrl + 'user/manage/' + email + '/profile';

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const protectedBody = this.apiAuthService.generateAuthedBody('PUT', '/v2/user/manage/' + email + '/profile', userInfo);

    return this.http.put<NewUser>(setDetailUrl, protectedBody, httpOpts);
  }

  getUserProfileAvatar(email: string): Observable<string> {
    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const getAvatarUrl = this.userUrl + '/' + email + '/profile/image';

    return this.http.get<string>(getAvatarUrl, httpOpts);
  }

  setUserProfileAvatar(email: string, selectedFile: File): Observable<string> {
    this.setSecurityToken();

    const getAvatarUrl = this.userUrl + '/' + email + '/profile/image';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const uploadData = new FormData();
    uploadData.append('image_upload', selectedFile);

    return this.http.post<string>(getAvatarUrl, uploadData, httpOpts);
  }

  /**
   * Adds a new email to the Quant Masters Mailing List
   *
   * @param email New email to be added to the maililing list
   */
  addNewsLetterSubscriber(email: string): Observable<string> {
    this.setSecurityToken();

    const addSubscriberUrl = this.subscriberUrl + '/' + email + '/subscription';

    return this.http.put<string>(addSubscriberUrl, '');
  }

  postUserTrackData(userLoginTrack: UserLoginTrack): Observable<string> {
    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(`${this.v2BaseUrl}user/record`, userLoginTrack, httpOpts);
  }

  getUserListForCertificate(idx: number): Observable<any> {
    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<any>(`${this.v2BaseUrl}admin/internship/${idx}/certificates`, httpOps);
  }

  postCertificationDetail(reqBody: any, category){
    this.setSecurityToken();
    const url = this.v2BaseUrl + `admin/internship/set/${category}/eligibility`;

    const httpOps = {
        headers: new HttpHeaders({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + this.JwtToken
        })
    };

    // const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/admin/internship/set/eligibility', reqBody);

    return this.http.post<any>(url, reqBody, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
