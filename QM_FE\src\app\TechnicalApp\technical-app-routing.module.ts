import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { TechDashboardComponent } from './tech-dashboard/tech-dashboard.component';
import { AppSelectComponent } from './Layout/app-select/app-select.component';
import { OnlineCompilerComponent } from './online-compiler/online-compiler.component';

const routes: Routes = [{
  path: '',
  component: TechDashboardComponent,
  children: [
    { path: 'apps', component: AppSelectComponent },
    { path: 'apps/compiler/:lang', component: OnlineCompilerComponent }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class TechnicalAppRoutingModule { }
