// components/admin/tests/topic-subtopic-selectors.tsx
import { useState, memo } from 'react'
import { Plus } from 'lucide-react'
import { getPaperTypeConfig } from '@/types/admin-types'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import CreateGroupModal from './create-group-modal'

interface TopicSubtopicSelectorsProps {
  paperType: number
  topics: any[]
  subtopics: any[]
  selectedTopic: string
  selectedSubtopic: string
  onTopicChange: (topicId: string) => void
  onSubtopicChange: (subtopicId: string) => void
  onTopicsUpdate?: (topics: any[]) => void
  onSubtopicsUpdate?: (subtopics: any[]) => void
}

const TopicSubtopicSelectors = memo(
  function TopicSubtopicSelectors({
    paperType,
    topics,
    subtopics,
    selectedTopic,
    selectedSubtopic,
    onTopicChange,
    onSubtopicChange,
    onTopicsUpdate,
    onSubtopicsUpdate
  }: TopicSubtopicSelectorsProps) {
    const config = getPaperTypeConfig(paperType)
    const [showCreateModal, setShowCreateModal] = useState(false)
    const [modalType, setModalType] = useState<'group' | 'subgroup'>('group')

    if (!config.hasTopicDropdown) {
      return null
    }

    // Check if paper type supports creating groups/subgroups
    const canCreateGroup =
      paperType === 11 || paperType === 13 || paperType === 14
    const canCreateSubgroup = paperType === 11 && selectedTopic

    // Get the appropriate ID and name fields based on paper type
    const getTopicFields = () => {
      switch (paperType) {
        case 1: // Chapter-Wise Papers
        case 6: // Chapter-Wise Practice
          return { idField: 'super_group_id', nameField: 'super_group_name' }
        case 11: // Technical MCQs
          return { idField: 'group_id', nameField: 'group_name' }
        case 13: // Verbal Practice
        case 14: // Technical Practice
          return { idField: 'group_id', nameField: 'group_name' }
        default:
          return { idField: 'id', nameField: 'name' }
      }
    }

    const getSubtopicFields = () => {
      switch (paperType) {
        case 1: // Chapter-Wise Papers
        case 6: // Chapter-Wise Practice
          return { idField: 'group_id', nameField: 'group_name' }
        case 11: // Technical MCQs
          return { idField: 'sub_group_id', nameField: 'sub_group_name' }
        default:
          return { idField: 'id', nameField: 'name' }
      }
    }

    const topicFields = getTopicFields()
    const subtopicFields = getSubtopicFields()

    const handleCreateGroup = () => {
      setModalType('group')
      setShowCreateModal(true)
    }

    const handleCreateSubgroup = () => {
      setModalType('subgroup')
      setShowCreateModal(true)
    }

    const handleModalSuccess = (newItem: any) => {
      if (modalType === 'group' && onTopicsUpdate) {
        onTopicsUpdate([...topics, newItem])
      } else if (modalType === 'subgroup' && onSubtopicsUpdate) {
        onSubtopicsUpdate([...subtopics, newItem])
      }
      setShowCreateModal(false)
    }

    return (
      <>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Topic Selector */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="topic">{config.topicLabel}</Label>
                {canCreateGroup && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCreateGroup}
                    className="h-7"
                  >
                    <Plus className="h-3 w-3 mr-1" />
                    Create {config.topicLabel}
                  </Button>
                )}
              </div>
              <Select value={selectedTopic} onValueChange={onTopicChange}>
                <SelectTrigger id="topic">
                  <SelectValue placeholder={`Select ${config.topicLabel}`} />
                </SelectTrigger>
                <SelectContent>
                  {topics.map((topic, index) => (
                    <SelectItem key={index} value={topic[topicFields.idField]}>
                      {topic[topicFields.nameField]}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Subtopic Selector */}
            {config.hasSubtopicDropdown && selectedTopic && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label htmlFor="subtopic">{config.subtopicLabel}</Label>
                  {canCreateSubgroup && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCreateSubgroup}
                      className="h-7"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Create {config.subtopicLabel}
                    </Button>
                  )}
                </div>
                <Select
                  value={selectedSubtopic}
                  onValueChange={onSubtopicChange}
                >
                  <SelectTrigger id="subtopic">
                    <SelectValue
                      placeholder={`Select ${config.subtopicLabel}`}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {subtopics.map((subtopic) => (
                      <SelectItem
                        key={subtopic[subtopicFields.idField]}
                        value={subtopic[subtopicFields.idField]}
                      >
                        {subtopic[subtopicFields.nameField]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        </div>

        {/* Create Group/Subgroup Modal */}
        <CreateGroupModal
          isOpen={showCreateModal}
          modalType={modalType}
          paperType={paperType}
          groups={modalType === 'subgroup' ? topics : []}
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleModalSuccess}
        />
      </>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison function for memo
    return (
      prevProps.paperType === nextProps.paperType &&
      prevProps.selectedTopic === nextProps.selectedTopic &&
      prevProps.selectedSubtopic === nextProps.selectedSubtopic &&
      prevProps.topics.length === nextProps.topics.length &&
      prevProps.subtopics.length === nextProps.subtopics.length &&
      // Deep compare first items to detect data changes
      JSON.stringify(prevProps.topics[0]) ===
        JSON.stringify(nextProps.topics[0]) &&
      JSON.stringify(prevProps.subtopics[0]) ===
        JSON.stringify(nextProps.subtopics[0])
    )
  }
)

export default TopicSubtopicSelectors
