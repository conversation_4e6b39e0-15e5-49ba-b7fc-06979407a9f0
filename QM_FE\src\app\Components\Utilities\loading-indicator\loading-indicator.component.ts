import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';

@Component({
  selector: 'app-loading-indicator',
  templateUrl: './loading-indicator.component.html',
  styleUrls: ['./loading-indicator.component.scss']
})
export class LoadingIndicatorComponent implements OnInit, OnDestroy {

  constructor() { }

  ngOnInit() {
    document.getElementsByTagName('body')[0].classList.add('body-no--scroll');
  }

  ngOnDestroy() {
    const classNames     = document.getElementsByTagName('body')[0].className,
          lastClassIndex = classNames.lastIndexOf(' ');

    document.getElementsByTagName('body')[0].className = classNames.substr(0, lastClassIndex);
  }
}
