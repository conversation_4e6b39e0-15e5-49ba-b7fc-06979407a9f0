import { Component, OnInit } from '@angular/core';

import { SectionWisePapersService } from '../../Services/Dashboard/section-wise-papers.service';

import { ChapterPaper } from '../../Models/Dashboard/Chapters/ChapterPaper';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-section-wise-papers',
  templateUrl: './section-wise-papers.component.html',
  styleUrls: ['./section-wise-papers.component.scss']
})
export class SectionWisePapersComponent implements OnInit {

  public papers: ChapterPaper[];

  public showIndicator = false;

  constructor(private router: Router,
              private activatedRoute: ActivatedRoute,
              private sectionWisePapersService: SectionWisePapersService) { }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.sectionWisePapersService.getPapers().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      that.papers = respText;
      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  takeToLanding() {

    this.router.navigate(['/placement/training/live']);
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {

    this.router.navigate(['../test', '9', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }
}
