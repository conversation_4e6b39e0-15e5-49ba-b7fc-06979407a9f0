<div class="competitive-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Weekly papers covering a wide array of topics</p>
    </div>
  </div>
  <div class="data-wrap">
    <div class="paper-wrap">
      <p *ngIf="!papers || papers.length == 0">Oops no papers as of now, but do come back later, great content is on its way</p>
      <div class="paper" *ngFor="let paper of papers">
        <h6>{{ paper.paper_name }}</h6>
        <p>{{ paper.time_lim ? paper.time_lim / 60000 + " Minutes": "No Time Limit" }}</p>
        <p>No. of Questions: {{ paper.no_of_ques }}</p>
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>
  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>

