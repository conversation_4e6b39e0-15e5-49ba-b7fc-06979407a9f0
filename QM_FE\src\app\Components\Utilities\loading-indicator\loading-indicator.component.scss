body.body-no--scroll {
  overflow: hidden;
}

.indicator-wrap {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  min-height: 100%;
  height: auto;
  border: none;
  background-color:rgba(0, 0, 0, 0.3);
}

.indicator-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 110;
  margin: auto;
  display: block;
  width: 80px;
  height: 100px;
}

.indicator-grid--cube {
  position: relative;
  box-sizing: border-box;
  float: left;
  margin: 0 10px 10px 0;
  width: 12px;
  height: 12px;
  border-radius: 3px;
  background: #FFF;
}

.indicator-grid--cube:nth-child(4n+1) { animation: wave 2s ease .0s infinite; }
.indicator-grid--cube:nth-child(4n+2) { animation: wave 2s ease .2s infinite; }
.indicator-grid--cube:nth-child(4n+3) { animation: wave 2s ease .4s infinite; }
.indicator-grid--cube:nth-child(4n+4) { animation: wave 2s ease .6s infinite; margin-right: 0; }

@keyframes wave {
  0%   { top: 0;     opacity: 1; }
  50%  { top: 30px;  opacity: .2; }
  100% { top: 0;     opacity: 1; }
}