'use client'

import { memo } from 'react'
import Image from 'next/image'
import { StudentVideoListType } from '@/types/video-types'
import { Card, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  Play,
  Video,
  Users,
  Presentation,
  BookOpen,
  Youtube
} from 'lucide-react'
import { AspectRatio } from '@/components/ui/aspect-ratio'
import { Badge } from '@/components/ui/badge'

interface VideoCardProps {
  video: StudentVideoListType
  startStream: (videoId: string, videoType: string, videoName: string) => void
}

function VideoCard({ video, startStream }: VideoCardProps) {
  // Get YouTube thumbnail instead of loading iframe
  const getYoutubeThumbnail = (youtubeId: string): string => {
    return `https://img.youtube.com/vi/${youtubeId}/hqdefault.jpg`
  }

  // Determine video type and set icon and label
  const getVideoTypeInfo = () => {
    switch (video.video_type) {
      case '1':
        return {
          label: 'Course Video',
          icon: <Video size={14} className="mr-1" />
        }
      case '2':
        return {
          label: 'Testimony',
          icon: <Users size={14} className="mr-1" />
        }
      case '3':
        return {
          label: 'Review',
          icon: <BookOpen size={14} className="mr-1" />
        }
      case '4':
        return {
          label: 'Trial',
          icon: <Video size={14} className="mr-1" />
        }
      case '5':
        return {
          label: 'Workshop',
          icon: <Presentation size={14} className="mr-1" />
        }
      case '6':
        return {
          label: 'YouTube',
          icon: <Youtube size={14} className="mr-1" />
        }
      default:
        return {
          label: 'Video',
          icon: <Video size={14} className="mr-1" />
        }
    }
  }

  const { label, icon } = getVideoTypeInfo()

  // Get the thumbnail URL based on video type
  const getThumbnailUrl = (): string => {
    // YouTube videos use YouTube thumbnails
    if (['2', '6'].includes(video.video_type) && video.link) {
      return getYoutubeThumbnail(video.link)
    }

    // Use provided thumbnail or fallback
    return video.thumbnail || '/images/video-placeholder.jpg'
  }

  // Handle click to play video
  const handlePlayVideo = (): void => {
    startStream(video.video_id, video.video_type, video.video_name)
  }

  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg border border-gray-200 group">
      <div className="relative cursor-pointer" onClick={handlePlayVideo}>
        <AspectRatio ratio={16 / 9}>
          <Image
            src={getThumbnailUrl()}
            alt={video.video_name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            priority={false}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
          <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
            <div className="w-12 h-12 rounded-full bg-white/90 flex items-center justify-center transform translate-y-4 group-hover:translate-y-0 transition-transform duration-300">
              <Play size={20} className="text-primary ml-1" />
            </div>
          </div>
        </AspectRatio>

        <div className="absolute top-2 left-2">
          <Badge
            variant="secondary"
            className="flex items-center bg-black/70 text-white"
          >
            {icon}
            {label}
          </Badge>
        </div>

        {video.length && (
          <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
            {video.length}
          </div>
        )}
      </div>

      <CardContent className="p-4">
        <h3 className="font-semibold text-base mb-1 line-clamp-2 group-hover:text-primary transition-colors duration-300">
          {video.video_name}
        </h3>
        <p className="text-sm text-gray-500 line-clamp-2">
          {video.description ||
            `Watch this ${label.toLowerCase()} to learn more`}
        </p>
      </CardContent>

      <CardFooter className="p-0">
        <Button
          className="w-full cursor-pointer rounded-none h-12 flex items-center justify-center gap-2 bg-gray-50 hover:bg-primary text-gray-800 hover:text-white border-t border-gray-100 transition-colors duration-300"
          onClick={handlePlayVideo}
          variant="ghost"
        >
          <Play size={18} />
          Watch Now
        </Button>
      </CardFooter>
    </Card>
  )
}

export default memo(VideoCard)
