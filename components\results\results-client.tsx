'use client'

import React, { useState } from 'react'
import { useAuth } from '@/lib/hooks/useAuth'
import { ResultsService } from '@/lib/client-services/results-service.client'
import { StudentMarks, TestType, ResultsState } from '@/types/results-types'
import ResultsTable from './results-table'
import LoadingIndicator from '@/components/shared/indicator'
import { Button } from '@/components/ui/button'

const ITEMS_PER_PAGE = 3

const TEST_TYPE_BUTTONS = [
  { type: 'all' as TestType, label: 'All My Marks', action: 'getAllMarks' },
  {
    type: 'trial' as TestType,
    label: 'Trial Papers',
    action: 'getTrialPaperMarks'
  },
  {
    type: 'weekly-competitive' as TestType,
    label: 'Weekly Competitive Papers',
    action: 'getWeeklyCompetitivePaperMarks'
  },
  {
    type: 'section-wise' as TestType,
    label: 'Section Wise Mock Papers',
    action: 'getSectionWisePaperMarks'
  },
  {
    type: 'afcat' as TestType,
    label: 'AFCAT Papers',
    action: 'getAfcatPaperMarks'
  },
  {
    type: 'company' as TestType,
    label: 'Company Papers',
    action: 'getCompanyPaperMarks'
  },
  {
    type: 'technical-mcq' as TestType,
    label: 'Technical MCQ Papers',
    action: 'getTechnicalMCQMarks'
  },
  {
    type: 'wp-practice' as TestType,
    label: 'Verbal / Technical Practice Papers',
    action: 'getWPPracticeMarks'
  }
]

export default function ResultsClient() {
  const { user, isAuthenticated } = useAuth()

  const [state, setState] = useState<ResultsState>({
    allMarks: [],
    displayMarks: [],
    sectionMarks: [],
    numDispMarks: 0,
    showIndicator: false,
    paperName: '',
    answerDate: '',
    currentPage: 1,
    itemsPerPage: ITEMS_PER_PAGE
  })

  const [activeTestType, setActiveTestType] = useState<TestType | null>(null)

  // Set pagination config based on screen size
  const setPaginatorConfig = () => {
    if (typeof window !== 'undefined') {
      const isMobile = window.innerWidth <= 440
      return {
        max: isMobile ? 5 : 7,
        boundaryLinks: !isMobile
      }
    }
    return { max: 7, boundaryLinks: true }
  }

  const processMarksData = (marks: StudentMarks[]) => {
    setState((prev) => ({
      ...prev,
      allMarks: marks,
      numDispMarks: marks.length,
      displayMarks: marks.slice(0, ITEMS_PER_PAGE),
      currentPage: 1,
      showIndicator: false
    }))
  }

  const handleError = (error: any, testType: string) => {
    console.error(`Error fetching ${testType}:`, error)
    setState((prev) => ({ ...prev, showIndicator: false }))

    const errorMessage =
      error.response?.data?.message ||
      error.message ||
      `Failed to load ${testType}`
    console.error('Error details:', errorMessage)
  }

  const getAllMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('all')

    try {
      const marks = await ResultsService.getAllMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'all marks')
    }
  }

  const getTrialPaperMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('trial')

    try {
      const marks = await ResultsService.getTrialPaperMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'trial paper marks')
    }
  }

  const getWeeklyCompetitivePaperMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('weekly-competitive')

    try {
      const marks = await ResultsService.getWeeklyCompetitivePaperMarks(
        user.email
      )
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'weekly competitive marks')
    }
  }

  const getSectionWisePaperMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('section-wise')

    try {
      const marks = await ResultsService.getSectionWisePaperMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'section wise marks')
    }
  }

  const getAfcatPaperMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('afcat')

    try {
      const marks = await ResultsService.getAfcatPaperMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'AFCAT marks')
    }
  }

  const getCompanyPaperMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('company')

    try {
      const marks = await ResultsService.getCompanyPaperMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'company paper marks')
    }
  }

  const getTechnicalMCQMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('technical-mcq')

    try {
      const marks = await ResultsService.getTechnicalMCQMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'technical MCQ marks')
    }
  }

  const getWPPracticeMarks = async () => {
    if (!user?.email) return

    setState((prev) => ({ ...prev, showIndicator: true }))
    setActiveTestType('wp-practice')

    try {
      const marks = await ResultsService.getWPPracticeMarks(user.email)
      processMarksData(marks)
    } catch (error) {
      handleError(error, 'WP practice marks')
    }
  }

  const showSectionMarks = async (
    answerId: string,
    paperName: string,
    answerDate: string
  ) => {
    if (!user?.email) return

    setState((prev) => ({
      ...prev,
      showIndicator: true,
      paperName,
      answerDate
    }))

    try {
      const sectionMarks = await ResultsService.getSectionWiseBreakdown(
        user.email,
        answerId
      )
      setState((prev) => ({
        ...prev,
        sectionMarks,
        showIndicator: false
      }))
    } catch (error) {
      handleError(error, 'section wise breakdown')
    }
  }

  const handlePageChange = (page: number) => {
    const startItem = (page - 1) * ITEMS_PER_PAGE
    const endItem = page * ITEMS_PER_PAGE

    setState((prev) => ({
      ...prev,
      displayMarks: prev.allMarks.slice(startItem, endItem),
      currentPage: page
    }))
  }

  const actionMap = {
    getAllMarks,
    getTrialPaperMarks,
    getWeeklyCompetitivePaperMarks,
    getSectionWisePaperMarks,
    getAfcatPaperMarks,
    getCompanyPaperMarks,
    getTechnicalMCQMarks,
    getWPPracticeMarks
  }

  if (!isAuthenticated) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Please log in to view your results.</p>
      </div>
    )
  }

  return (
    <div className="results-wrap bg-white rounded-lg shadow-lg p-6 max-w-7xl mx-auto">
      <LoadingIndicator
        isLoading={state.showIndicator}
        text="Loading results..."
      />

      {/* Options Box */}
      <div className="options-box grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        {TEST_TYPE_BUTTONS.map((button) => (
          <Button
            key={button.type}
            onClick={() => actionMap[button.action as keyof typeof actionMap]()}
            className={`
              min-h-[40px] px-4 py-2 rounded-md font-medium transition-all duration-300
              ${
                activeTestType === button.type
                  ? 'bg-gray-800 hover:bg-gray-900 text-white shadow-lg'
                  : 'bg-gray-600 hover:bg-gray-700 text-white shadow-md hover:shadow-lg'
              }
            `}
            disabled={state.showIndicator}
          >
            {button.label}
          </Button>
        ))}
      </div>

      {/* Results Table */}
      <ResultsTable
        displayMarks={state.displayMarks}
        numDispMarks={state.numDispMarks}
        currentPage={state.currentPage}
        itemsPerPage={ITEMS_PER_PAGE}
        onPageChange={handlePageChange}
        onShowSectionMarks={showSectionMarks}
        sectionMarks={state.sectionMarks}
        paperName={state.paperName}
        answerDate={state.answerDate}
        paginatorConfig={setPaginatorConfig()}
      />
    </div>
  )
}
