<div class="company-papers--wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="paper-heading">{{ pageHeading }}</div>
  <div class="papers-wrap">
    <div class="paper" *ngFor="let paper of papers">
      <div class="oth-papers" *ngIf="paper.paper_name.indexOf('Advanced') == -1 && paper.paper_name.indexOf('Foundation') == -1">
        <h6>{{ paper.paper_name }}</h6>
        <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
        <p>No. of Questions: {{ paper.no_of_ques }}</p>
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>

    <div class="tcs-digital-2022" *ngIf="pageHeading === 'TCS-NQT Papers (Previous Papers)' || pageHeading === 'TCS-NQT 2022 (Latest Pattern)'">
      <div class="tcs-folder--1">
        <h5 *ngIf="pageHeading === 'TCS-NQT Papers (Previous Papers)'">Advanced Coding</h5>
        <h5 *ngIf="pageHeading === 'TCS-NQT 2022 (Latest Pattern)'">Foundation Level</h5>
        <button (click)="tcsNqt22FoundationCollapsed = !tcsNqt22FoundationCollapsed" [class.is-open]="!tcsNqt22FoundationCollapsed"
          [attr.aria-expanded]="!tcsNqt22FoundationCollapsed" aria-controls="collapsePaperGroup1">
          <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
            <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
              d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
              transform="translate(-3.375 -3.375)" fill="#fff" />
          </svg>
        </button>
      </div>
      <div class="paper" *ngFor="let paper of papers" id="collapseChapterGroup" [collapse]="tcsNqt22FoundationCollapsed"
      [isAnimated]="true">
        <div class="oth-papers" *ngIf="paper.paper_name.indexOf('Foundation') > 1">
          <h6>{{ paper.paper_name }}</h6>
          <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
          <p>No. of Questions: {{ paper.no_of_ques }}</p>
          <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
        </div>
      </div>
    </div>

    <div class="tcs-digital-2022" *ngIf="pageHeading === 'TCS-NQT Papers (Previous Papers)' || pageHeading === 'TCS-NQT 2022 (Latest Pattern)'">
      <div class="tcs-folder--1">
        <h5 *ngIf="pageHeading === 'TCS-NQT Papers (Previous Papers)'">Advanced Coding</h5>
        <h5 *ngIf="pageHeading === 'TCS-NQT 2022 (Latest Pattern)'">Advanced Level</h5>
        <button (click)="tcsDig2022Collapsed = !tcsDig2022Collapsed" [class.is-open]="!tcsDig2022Collapsed"
          [attr.aria-expanded]="!tcsDig2022Collapsed" aria-controls="collapsePaperGroup1">
          <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
            <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
              d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
              transform="translate(-3.375 -3.375)" fill="#fff" />
          </svg>
        </button>
      </div>
      <div class="paper" *ngFor="let paper of papers" id="collapseChapterGroup" [collapse]="tcsDig2022Collapsed"
      [isAnimated]="true">
        <div class="oth-papers" *ngIf="paper.paper_name.indexOf('Advanced') > 1">
          <h6>{{ paper.paper_name }}</h6>
          <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
          <p>No. of Questions: {{ paper.no_of_ques }}</p>
          <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
        </div>
      </div>
    </div>
  </div>
</div>