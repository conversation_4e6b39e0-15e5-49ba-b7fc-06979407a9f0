.chapter-wrap {
    width: 100%;
    min-height: 100%;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
    margin-bottom: 1em;

    .page-header {
      display: flex;
      height: 2.5rem;
      margin-bottom: 2em;

      .title {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin: 0 1rem;
        padding: 0.5rem;
        border-bottom: 1px solid;
      }
    }
}

.copy-content {
    text-align: right;
  
    p {
      color: #707070;
      margin: 0;
    }
}
.container{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #ffffff;
    border-radius: 3px;

    .header{
        display: flex;
        flex-direction: row;
        width: 100%;
        margin-bottom: 1rem;
        color: #fff;
        div{
          cursor: pointer;
          width: 100%;
        }
        .is-selected {
          background-color: #e27723;
          transition: all 0.3s ease;
        }
        p{
            margin: auto;
            padding: 0.8rem;
            width: 100%;
            text-align: center;
            background-color: #0b6fb1;
            border-right: 1px solid;
            &:hover {
                background-color: #E88224;
                transition: all 0.3s ease;
            }
        }
        .active {
            background-color: #e27723;
        }
        
    }
    .detailSection{
        width: 100%;
        svg{
            width: 100%;
            height: 100%;
        }
    }
    table {
        border-bottom: 2px solid;
        text-align: center;
  
        th {
          width: 10%;
          height: 2rem;
          background-color: #0b6fb1;
          color: white;
        }
  
        td {
          height: 2rem;
        }
  
        .active {
          background-color: #e27723;
        }
      }
    .selected-Group--active {
        background-color: #E88224;
    } 
}

.paper-group--list{
    width: 100%;
    margin: auto;

    .paper-group{
        width: 100%;
        height: 100%;
        margin-bottom: 3em;

        .group-name--bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            padding: 0.45em;
            color: #fff;
            background-color: #38A3E9;
      
            h5 {
              margin: 0;
            }
      
            span {
              margin-right: 1em;
            }
          }
          button {
            cursor: pointer;
            border: none;
            background-color: rgba(0, 0, 0, 0.0);

            svg {
              transform: rotate(0deg);
              transition: all 0.4s ease-in;
            }
          }

          .is-open {
            svg {
              transform: rotate(180deg);
              transition: all 0.3s ease-out;
            }
          }
    }
    .paper-list{
        width: 100%;
        padding: 1em;
        background-color: #E4F9F5;

        .paper {
          .paper-name--bar {
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 1em;
            padding: 0.5em;

            .locked-resource {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              margin: auto;
              width: 100%;
              height: 50px;
              background-color: rgba(146, 135, 135, 0.85);

              img {
                display: block;
                height: 100%;
                margin: auto;
              }
            }

            h6, p {
              margin: 0;
            }

            button {
              padding: 0.5em 1em;
              color: #fff;
              background-color: #E88224;
              border-radius: 5px;
              transition: all 0.4s ease-out;

              &:hover {
                background-color: #0B6FB1;
                transition: all 0.2s ease-in;
              }
            }
          }
        }
    }
}

@media (max-width: 440px) {
  .chapter-wrap {
    .page-header {
      height: 3.5rem;
    }

    .container {
 
      .header {
        margin-bottom: 2rem;

        div {
          
          p {
            height: 100%;
          }
        }
      }
    }
  }

  .paper-group--list {

    .paper-group {

      .group-name--bar {
        flex-direction: column;
        align-items: flex-start;

        h5 {
          margin-bottom: 0.5em;
        }

        div {
          display: flex;
          justify-content: space-between;
          width: 100%;

          button {
            margin-left: auto;
          }
        }
      }
    }

    .paper-list{

      .paper {

        .paper-name--bar {
          display: grid;
          grid-template-columns: 1fr;
          grid-gap: 0.3em;

          button {
            justify-self: end;
          }

          .locked-resource {
            height: 145px;
  
            img {
              height: 50%;
            }
          }
        }
      }
    }
  }
}