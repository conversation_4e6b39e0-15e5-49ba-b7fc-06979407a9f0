.tests-cockpit {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .center-controls {
    width: 98%;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);

    .hide-heading {
      display: none;
    }

    .main-ctrls {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(4, 15%);
      align-items: center;
      justify-items: start;
      margin-top: 2rem;

      .main-ctrl {
        cursor: pointer;
        display: grid;
        place-items: center;
        width: 170px;
        color: #000;
        background-color: #FFF;
        margin-right: 1rem;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 2px 5px 10px 0 rgba(121, 124, 125, 0.3);
        transform: scale(1);
        transition: all 0.3s ease-in;

        &:hover {
          color: #FFF;
          background-color: #CD5C5C;
          box-shadow: 2px 5px 10px 0 rgba(205, 92, 92, 0.301);
          transform: scale(1.07);
          transition: all 0.2s ease-in;
        }

        &:active {
          color: #FFF;
          background-color: #F08080;
          box-shadow: 2px 5px 10px 0 rgba(205, 92, 92, 0.301);
        }
      }

      #active {
        background-color: #FA8072;
        color: #FFF;
        box-shadow: 2px 5px 10px 0 rgba(250, 128, 114, 0.534);
        transition: all 0.3s ease;
      }

      .scoot-away {
        transform: translateX(-50px);
        opacity: 0;
        transition: all 0.3s ease-in;
      }

      .back-ctrl {
        display: none;
        cursor: pointer;
        transform: scale(1);
        transition: all 0.3s ease-in;

        &:hover {
          transform: scale(1.07);
          transition: all 0.2s ease-in;
        }

        img {
          height: 55px;
          width: 75px;
        }
      }

      .show-back {
        display: inline-block;
      }

      .create-ctrls {
        grid-column: 4;
        grid-row: 1;
      }

      .paper-select {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 170px;
        color: #000;
        background-color: #AED6F1;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 2px 5px 10px 0 rgba(121, 124, 125, 0.3);
        transform: scale(1);
        transition: all 0.3s ease-in;

        &:hover {
          color: #FFF;
          background-color: #3498DB;
          box-shadow: 2px 5px 10px 0 rgba(36, 113, 163, 0.301);
          transform: scale(1.07);
          transition: all 0.2s ease-in;
        }
      }

      .opts {
        cursor: pointer;
        display: grid;
        background-color: #EBF5FB;
        place-items: center;
        position: absolute;
        width: 11%;
        border-bottom-left-radius: 5px;
        border-bottom-right-radius: 5px;
        box-shadow: 2px 5px 10px 0 rgba(121, 124, 125, 0.3);

        .select-opt {
          width: 100%;
          padding: 10px;
          text-align: center;

          &:hover {
            background-color: #EAECEE;
          }
        }
      }
    }
  }

  .main-content {
    width: 98%;
    padding: 35px;
    margin-top: 1rem;
    border-radius: 10px;
    box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);

    .custom-btn {
      // width: 100%;
      height: 40px;
      background-color: #17a2b8;
      border: none;
      border-radius: 4px;
      color: #fff;
      // margin-bottom: 1em;
      padding: 8px;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
    }

    .custom-btn:active:after {
      transition: 0s;
      opacity: 0.7;
      clip-path: circle(0% at 0% 0%);
    }

    .custom-btn::after {
      content: "";
      display: block;
      position: relative;
      top: -32px;
      left: -8px;
      height: 40px;
      width: 150px;
      background-color: #e88224;
      opacity: 0;
      clip-path: circle(150% at 0% 0%);
      transition: all 0.4s ease-in;
    }

    .btn-2 {
      background-color:#7524EA;
    }

    .paper-metadata {
      width: 100%;
      margin-bottom: 2em;
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .mtcq-groups {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-evenly;
      }

      .btn-group {
        display: flex;
        align-items: center;
      }

      label {
        margin-right: 0.5rem;
      }

      input,
      select {
        display: block;
        height: 50px;
        width: 100%;
        padding: 3px 5px;
        border: solid 1.5px #707070;
        border-radius: 5px;
        transition: all 0.3s ease;

        &:focus {
          border: solid 1.5px #0b6fb1;
          transition: all 0.3s ease;
        }

        &:focus + .placeholder-text {
          top: -75px;
          font-size: 13px;
          transition: all 0.3s ease;
        }

        option {
          cursor: pointer;
        }
      }

      select {
        width: initial;
      }

      .placeholder-text {
        position: relative;
        top: -56px;
        left: 10px;
        padding: 3px;
        font-size: 17px;
        background-color: #fff;
        transition: all 0.4s ease;
      }

      .paper-ctrls {
        display: flex;

        button {
          margin-right: 0.5rem;
        }
      }
    }

    .papers-list {
      .papers {
        display: grid;
        grid-template-columns: 30% 10% 10% 20% 10% 10% 10%;
        gap: 0.3em;
        align-items: center;
        padding: 10px;
        margin-bottom: 1.5rem;
        background-color: #D6EAF8;
        border-radius: 5px;
        box-shadow: 2px 5px 10px 0 rgba(40, 116, 166, 0.3);

        label {
          margin: 0;
        }

        p {
          margin-bottom: 0;
        }

        img {
          cursor: pointer;
          height: 45px;
          width: 45px;
        }
      }
    }
  }
}

.create-modal {
  form {
    .form-group {
      display: flex;
      justify-content: flex-start;

      .form-elem {
        margin-right: 1em;
        width: 100%;
      }

      input, textarea, select {
        display: block;
        height: 30px;
        width: 100%;
        padding: 3px 5px;
        border: solid 1.5px #707070;
        font-size: 15px;
        border-radius: 5px;
        transition: all 0.3s ease;

        &:focus {
          border: solid 1.5px #0B6FB1;
          transition: all 0.3s ease;
        }

        &:focus + .placeholder-text {
            top: -75px;
            font-size: 13px;
            transition: all 0.3s ease;
        }
      }

      textarea {
        resize: both;
        height: 20rem;
      }

      button {
        background-color: rgb(116, 62, 62);
        margin-top: 1rem;
        border: none;
        border-radius: 4px;
        color: #fff;
        padding: 5px 20px;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
      }
    }

    .row-spl {
      flex-direction: column;
    }

    .form-group {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:first-of-type {
        justify-content: flex-end;
      }

      .form-elem {
        width: 60%;
        margin-top: 0.5rem;
      }

      .img-updl--btn1 {
        border: none;
        background-color: #fff;

        img {
          height: 30px;
          width: 30px;
        }
      }

      input {
        width: 100%;
      }

      input[name="usn"] {
        width: 70%;
      }

      .is-inv {
        color: #E74C3C;
        border: solid 2px #E74C3C;
      }

      .form-error--text {
        color: #E74C3C;
      }
    }
  }

  .notFound-wrap {
    margin-top: 2em;

    p {
      color: #D32F2F;
    }
  }

  .user-table {
    margin-top: 2em;

    .header,
    .content {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1fr;
      padding: 5px;
    }

    .header {
      background-color: #D35400;
      color: #fff;
    }

    .content:nth-of-type(2n) {
      background-color: #FBEEE6;
    }

    .level-btn {
      background-color: #B03A2E;
      color: #fff;
      margin-top: 1em;
    }

    .premium-btn {
      background-color: #fc0;
      color: #000;
      margin-left: 15px;
    }

    .revoke-btn {
      background-color: #E74C3C;
      color: #FFF;
      margin-left: 15px;
    }
  }

  agm-map {
    height: 500px;
  }
}

@media (max-width: 440px) {
  .tests-cockpit .center-controls .main-ctrls {
    grid-template-columns: 1fr !important;
    gap: 1em;

    .main-ctrl {
      width: initial;
    }

    .create-ctrls {
      grid-column: 1;
      grid-row: 2;
    }

    .opts {
      width: 45%;
    }
  }

  .tests-cockpit .main-content {
    padding: 15px;

    .papers-list {
      .papers {
        grid-template-columns: 1fr 1fr;
        gap: 0.5em;

        img {
          grid-row: 3;
        }
      }
    }
  }
}
