// app/(admin)/admin/tests/papers/page.tsx
import { PAPER_TYPES } from '@/types/admin-types'
import PaperTypeGrid from '@/components/admin/tests/paper-type-grid'
import AdminBreadcrumb from '@/components/admin/admin-breadcrumb'

export default function AdminTestsPapersPage() {
  return (
    <div className="px-4 py-6">
      <AdminBreadcrumb
        items={[
          { label: 'Admin Console', href: '/admin' },
          { label: 'Tests & Papers', isCurrentPage: true }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Tests & Papers Management
        </h1>
        <p className="mt-2 text-gray-600">
          Select a paper type below to manage papers and their configurations
        </p>
      </div>

      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Select Paper Type</h2>
        <PaperTypeGrid paperTypes={PAPER_TYPES} />
      </div>
    </div>
  )
}
