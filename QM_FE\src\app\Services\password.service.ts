import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ApiAuthService } from './api-auth.service';

@Injectable({
  providedIn: 'root'
})
export class PasswordService {
  protected resReqUrl = 'https://api.quantmasters.in/password/forgot';
  protected resValUrl = 'https://api.quantmasters.in/password/reset/validate';
  protected resDobUrl = 'https://api.quantmasters.in/password/reset/validate/dob';
  protected resUpdUrl = 'https://api.quantmasters.in/password/reset/save';

  protected v2BaseUrl = 'https://api.quantmasters.in';

  private JwtToken: string;

  constructor(private http: HttpClient,
              private apiAuthService: ApiAuthService) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  createResetRequest(email: string): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const reqBody = {
      email
    };

    return this.http.post<string>(this.resReqUrl, reqBody, httpOps);
  }

  validateResetRequest(conf_key: string): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };

    const reqBody = {
      conf_key
    };

    return this.http.post<string>(this.resValUrl, reqBody, httpOps);
  }

  validateDOBRequest(email: string, dob: string) {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const reqBody = {
      email,
      dob
    };

    return this.http.post<string>(this.resDobUrl, reqBody, httpOps);
  }

  createPasswordChangeRequest(email: string, password: string): Observable<string> {

    this.setSecurityToken();

    const url = this.v2BaseUrl + '/v2/user/manage/reset/validate';

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const reqBody = {
      email,
      password
    };

    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/user/manage/reset/validate', reqBody);

    return this.http.post<string>(url, protectedBody, httpOps);
  }

  sendPasswordResetMail(email: string): Observable<string> {

    const url = this.v2BaseUrl + '/v2/user/manage/password/forgot';

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };

    const reqBody = {
      email
    };

    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/user/manage/password/forgot', reqBody);

    return this.http.post<string>(url, protectedBody, httpOps);
  }

  saveChangedPassword(email: string, password: string, reset_key: string): Observable<string> {

    this.setSecurityToken();

    const url = this.v2BaseUrl + '/v2/user/manage/reset/save';

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };

    const reqBody = {
      email,
      password,
      reset_key
    };

    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v2/user/manage/reset/save', reqBody);

    return this.http.post<string>(url, protectedBody, httpOps);
  }

  saveNewPassword(email: string, password: string): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const reqBody = {
      email,
      password
    };

    return this.http.post<string>(this.resUpdUrl, reqBody, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
