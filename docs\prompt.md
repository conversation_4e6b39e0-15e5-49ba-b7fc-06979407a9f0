We are in the process of rewriting an angular project to a nextjs 15 SSR based one.

Use this as reference, but you can stylise for better UX and use tailwind and shadcn components. Try to keep code as server side, and move any client side requirements as a separete component to import. Feel free to use lucide icons instead of hardcoded SVG. Do not write any hardcoded svg, if you come across any, you can add a comment  and go to next line. 

We have already come up with a mock auth and middleware service.
make changes to cookie service and middleware if you come across anything new:

import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// Paths that require authentication
const protectedPaths = ['/dashboard', '/admin', '/technical', '/account']

// Paths that should redirect to dashboard if already authenticated
const authPaths = ['/user/login', '/user/register', '/user/forgot-password']

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Check if user has authentication cookies
  const hasQMAUserCookie = request.cookies.has('QMA_USR')
  const hasQMAKeyCookie = request.cookies.has('QMA_KEY')
  const isAuthenticated = hasQMAUserCookie && hasQMAKeyCookie

  // Handle protected routes - redirect to login if not authenticated
  if (
    protectedPaths.some((path) => pathname.startsWith(path)) &&
    !isAuthenticated
  ) {
    const url = new URL('/user/login', request.url)
    // Add a redirect parameter to redirect back after login
    url.searchParams.set('redirect', pathname)
    return NextResponse.redirect(url)
  }

  // Redirect authenticated users away from login/register pages
  if (authPaths.some((path) => pathname.startsWith(path)) && isAuthenticated) {
    return NextResponse.redirect(new URL('/dashboard', request.url))
  }

  return NextResponse.next()
}

// Configure matcher for paths this middleware should run on
export const config = {
  matcher: [
    '/dashboard/:path*',
    '/admin/:path*',
    '/technical/:path*',
    '/account/:path*',
    '/user/login',
    '/user/register',
    '/user/forgot-password'
  ]
}


import Cookies from 'js-cookie'

const COOKIE_EXPIRATION = 7

export interface AuthResponse {
  text: string
  user: string
  key: string
  sess: string
  token: string
  email: string
  complete: string
  subscribed: string
}

export const LoginRefresh = {
  fnSetCookiesAndSession: (
    data: AuthResponse,
    email: string,
    userName: string,
    complete: string,
    key: string
  ) => {
    // Set cookies (including the token!)
    Cookies.set('QMA_USR', email, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_KEY', key, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_NAME', userName, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_COMPLETE', complete, { expires: COOKIE_EXPIRATION })
    Cookies.set('QMA_TOK', data.token, { expires: COOKIE_EXPIRATION }) // Add token to cookies
    Cookies.set('QMA_SESS', data.sess, { expires: COOKIE_EXPIRATION }) // Add session to cookies
    Cookies.set('QMA_SUBSCRIBED', data.subscribed || '0', { expires: COOKIE_EXPIRATION })

    // Set localStorage items
    if (typeof window !== 'undefined') {
      localStorage.setItem('QMA_USR', email)
      localStorage.setItem('QMA_NAME', userName)
      localStorage.setItem('QMA_TOK', data.token)
      localStorage.setItem('QMA_SESS', data.sess)
      localStorage.setItem('QMA_KEY', key)
      localStorage.setItem('QMA_COMPLETE', complete)
      localStorage.setItem('QMA_SUBSCRIBED', data.subscribed || '0')
    }

    return true
  },

  fnClearCookiesAndSession: () => {
    // Clear cookies
    Cookies.remove('QMA_USR')
    Cookies.remove('QMA_KEY')
    Cookies.remove('QMA_NAME')
    Cookies.remove('QMA_COMPLETE')
    Cookies.remove('QMA_TOK') // Add token removal
    Cookies.remove('QMA_SESS') // Add session removal
    Cookies.remove('QMA_SUBSCRIBED')

    // Clear localStorage
    if (typeof window !== 'undefined') {
      localStorage.removeItem('QMA_USR')
      localStorage.removeItem('QMA_NAME')
      localStorage.removeItem('QMA_TOK')
      localStorage.removeItem('QMA_SESS')
      localStorage.removeItem('QMA_KEY')
      localStorage.removeItem('QMA_COMPLETE')
      localStorage.removeItem('QMA_SUBSCRIBED')
    }

    return true
  },

  fnCheckUserLoginStatus: (): boolean => {
    if (typeof window !== 'undefined') {
      // Check both cookie and localStorage for better reliability
      const tokenCookie = Cookies.get('QMA_TOK')
      const emailCookie = Cookies.get('QMA_USR')
      
      // If cookies exist, use them
      if (tokenCookie && emailCookie) {
        return true
      }
      
      // Fallback to localStorage
      const tokenStorage = localStorage.getItem('QMA_TOK')
      const emailStorage = localStorage.getItem('QMA_USR')
      
      return !!(tokenStorage && emailStorage)
    }
    return false
  },
  
  // Helper to get token from either cookie or localStorage
  getAuthToken: (): string | null => {
    if (typeof window !== 'undefined') {
      // First try cookie
      const tokenCookie = Cookies.get('QMA_TOK')
      if (tokenCookie) return tokenCookie
      
      // Fallback to localStorage
      return localStorage.getItem('QMA_TOK')
    }
    return null
  },
  
  // Sync cookies from localStorage if needed
  syncFromLocalStorage: (): void => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('QMA_TOK')
      const email = localStorage.getItem('QMA_USR')
      
      if (token && email && !Cookies.get('QMA_TOK')) {
        // If we have auth in localStorage but not in cookies, restore cookies
        Cookies.set('QMA_TOK', token, { expires: COOKIE_EXPIRATION })
        Cookies.set('QMA_USR', email, { expires: COOKIE_EXPIRATION })
        
        // Also sync other values if available
        const name = localStorage.getItem('QMA_NAME')
        const key = localStorage.getItem('QMA_KEY')
        const complete = localStorage.getItem('QMA_COMPLETE')
        const sess = localStorage.getItem('QMA_SESS')
        const subscribed = localStorage.getItem('QMA_SUBSCRIBED')
        
        if (name) Cookies.set('QMA_NAME', name, { expires: COOKIE_EXPIRATION })
        if (key) Cookies.set('QMA_KEY', key, { expires: COOKIE_EXPIRATION })
        if (complete) Cookies.set('QMA_COMPLETE', complete, { expires: COOKIE_EXPIRATION })
        if (sess) Cookies.set('QMA_SESS', sess, { expires: COOKIE_EXPIRATION })
        if (subscribed) Cookies.set('QMA_SUBSCRIBED', subscribed, { expires: COOKIE_EXPIRATION })
      }
    }
  }
}