.user-wrap {
  width: 98.5%;
  background-color: #f7f7f7;
  margin-right: auto;
  padding: 15px;
  box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);

  .sect-1 {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    .info-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      background-color: #777;
      width: 200px;
      height: 130px;
      padding: 10px;
      margin-right: 2.5em;
      border-radius: 10px;
      box-shadow: 3px 2px 9px rgba(0, 0, 0, 0.16);
      transition: all 0.3s ease;
    }

    .additional-controls-box {
      display: grid;
      grid-template-columns: 1fr 1fr;
      width: 40%;
      // height: 100px;

      .grant-btn {
        background-color: #3949AB;
        color: #fff;
        margin-left: 0.5em;
        margin-bottom: 0.2em;
      }
    }

    .action-icon {
      position: relative;
      left: -5%;
      height: 35px;
      width: 35px;
      cursor: pointer;
      background-color: #777;
      border-radius: 50%;
      box-shadow: -2px -2px 9px rgba(0, 0, 0, 0.36);
      display: flex;
      justify-content: center; 
      align-items: center;
      transform: scale(1);
      transition: all 0.2s ease-in;

      &:hover {
        transform: scale(1.05);
        transition: all 0.1s ease;
      }

      svg {
        height: 25px;
        width: 25px;
      }
    }

    .selected {
      background-color: #fff;
      transition: all 0.3s ease;
    }
  }

  .sect-15 {
    margin-top: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;

    form {

      .form-elem {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        
        input{
          margin-right: 20px;
          padding: 10px;
          border-radius: 5px;
        }

        .is-inv {
          color: #E74C3C;
          border: solid 2px #E74C3C;
        }

        .form-error--text {
          color: #E74C3C;
        }

        .search-btn {
          color: #000;
        }
      }
    }

    .page-ctrls {
      button {
        background-color: #ec7063;
        color: #fff;
        padding: 0.6em;
        margin-right: 1em;
        border-radius: 50%;
        border: none;
        transform: scale(1);
        transition: all 0.2s ease-out;

        &:hover {
          transform: scale(1.05);
          transition: all 0.3s ease;
        }
      }

      p {
        display: inline-block;
        margin-right: 1rem;
      }
    }

    .ctrls-user button {
      background-color: #2E86C1;
      color: #fff;
    }

    .ctrls-cert button {
      background-color: #f4d03f;
      color: #000;
    }
  }

  .sect-2 {
    width: 100%;
    margin: auto;
    margin-top: 2em;
    border-radius: 10px;
    background-color: #fff;
    box-shadow: 3px 2px 9px rgba(0, 0, 0, 0.16);

    .users-headers,
    .users-data {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr 1fr 2fr 1fr 1fr;

      h6,
      p {
        font-size: 0.8rem;
        padding: 0 15px 5px 15px;
        margin: 0;
      }

      button {
        align-self: center;
        height: 2rem;
        width: 90%;
        border: none;
        border-radius: 10px;
        background-color: #58d68d;
      }
    }

    .users-headers h6 {
      padding: 0.5rem 0.5rem 0.2rem 0.5rem;
    }

    .track-headers,
    .track-data {
      grid-template-columns: 25% 15% 15% 10% 10% 10% 10% 5%;
    }

    .cert-headers,
    .cert-data {
      grid-template-columns: 1.5fr 1fr 1fr 2fr 1fr;
    }

    .users-headers {
      background-color: #2E86C1;
      color: #fff;
      border-top-right-radius: 10px;
      border-top-left-radius: 10px;
    }

    .track-headers {
      background-color: #ec7063;
    }

    .cert-headers {
      background-color: #f4d03f;
      color: #000;
    }
    
    .users-data:last-of-type {
      margin-bottom: 1em;
    }

    .users-data:nth-of-type(2n) {
      background-color: #EAF2F8;
    }

    .track-data:nth-of-type(2n) {
      background-color: #fadbd8;
    }

    .cert-data:nth-of-type(2n) {
      background-color: #fcf3cf;
    }

    .sub-header,
    .sub-data {
      display: grid;
      grid-template-columns: 1fr;
    }

    .sub-sub-header,
    .sub-sub-data {
      width: 100%;
      display: grid;
      grid-template-columns: 1fr 1fr;
    }
  }
}

.custom-btn {
  height: 40px;
  font-size: 17px;
  font-weight: normal;
  border: none;
  border-radius: 4px;
  color: #000;
  padding: 5px 20px;
  background-color: #F0B27A;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

.custom-btn:active:after {
  transition: 0s;
  opacity: 0.7;
  clip-path: circle(0% at 0% 0%);
}

.custom-btn::after {
  content: "";
  display: block;
  position: relative;
  top: -33px;
  // height: 40px;
  width: 150px;
  background-color: #9FA8DA;
  opacity: 0;
  clip-path: circle(150% at 0% 0%);
  transition: all 0.4s ease-in;
}

.access-modal {
  form {
    .form-row--1 {
      display: flex;
      justify-content: flex-start;

      .form-elem {
        margin-right: 1em;
        width: 100%;
      }

      input, textarea {
        display: block;
        height: 40px;
        width: 100%;
        padding: 3px 5px;
        border: solid 1.5px #707070;
        font-size: 15px;
        border-radius: 5px;
        transition: all 0.3s ease;

        &:focus {
          border: solid 1.5px #0B6FB1;
          transition: all 0.3s ease;
        }

        &:focus + .placeholder-text {
            top: -75px;
            font-size: 13px;
            transition: all 0.3s ease;
        }
      }

      textarea {
        resize: both;
        height: 20rem;
      }

      button {
        background-color: rgb(116, 62, 62);
        margin-top: 1rem;
        border: none;
        border-radius: 4px;
        color: #fff;
        padding: 5px 20px;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
      }
    }

    .row-spl {
      flex-direction: column;
    }

    .form-group {
      display: flex;
      align-items: center;
      justify-content: space-between;

      &:first-of-type {
        justify-content: flex-end;
      }

      .form-elem {
        width: 70%;
        margin-top: 0.5rem;
      }

      .img-updl--btn1 {
        border: none;
        background-color: #fff;

        img {
          height: 30px;
          width: 30px;
        }
      }

      input {
        width: 100%;
      }

      button {
        color: #000;
      }

      input[name="usn"] {
        width: 70%;
      }

      .is-inv {
        color: #E74C3C;
        border: solid 2px #E74C3C;
      }

      .form-error--text {
        color: #E74C3C;
      }
    }
  }

  .notFound-wrap {
    margin-top: 2em;

    p {
      color: #D32F2F;
    }
  }

  .user-table {
    margin-top: 2em;

    .header,
    .content {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1fr;
      padding: 5px;
    }

    .header {
      background-color: #D35400;
      color: #fff;
    }

    .content:nth-of-type(2n) {
      background-color: #FBEEE6;
    }

    .level-btn {
      background-color: #B03A2E;
      color: #fff;
      margin-top: 1em;
    }

    .premium-btn {
      background-color: #fc0;
      color: #000;
      margin-left: 15px;
    }

    .revoke-btn {
      background-color: #E74C3C;
      color: #FFF;
      margin-left: 15px;
    }
  }

  agm-map {
    height: 500px;
  }
}

@media (max-width: 440px) {
  .user-wrap {
    .sect-1 {
      flex-direction: column;

      .info-box {
        margin-right: 0;
        margin-bottom: 0.5rem;
      }

      .action-icon {
        position: absolute;
        top: 40%;
        left: 70%;
      }

      .additional-controls-box {
        width: 100%;
        grid-template-columns: 1fr;
        align-items: center;
        justify-content: center;

        button {
          margin-top: 1em;
        }
      }
    }

    .sect-15 {
      flex-direction: column;
      align-items: center;

      .page-ctrls {
        margin-top: 1em;
      }

      form {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .form-elem {
          flex-direction: column;
          justify-content: center;

          input {
            margin-right: 0;
          }

          button {
            margin-top: 1rem;
          }
        }
      }
    }

    .sect-2 {
      .users-headers,
      .users-data {
        grid-template-columns: 1fr;

        h6,
        p {
          padding: 0;
          padding-left: 15px;
          padding-bottom: 5px;
        }

        button {
          margin-left: 15px;
          margin-bottom: 1em;
          padding: 0.5em;
          height: initial;
        }
      }
    }
  }

  .access-modal {

    .user-table {
      .header, .content {
        grid-template-columns: 1fr;
      }
    }

    .form-group {
      margin-top: 1rem;
    }
  }
}