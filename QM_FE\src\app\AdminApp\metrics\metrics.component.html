<div class="metrics-wrap">
  <h6>Cloud Front Video Stream Test 72520</h6>
  <div class="player">
    <vg-player (onPlayerReady)="onPlayerReady($event)">
      <vg-controls [vgAutohide]="true" [vgAutohideTime]="1.5">
        <vg-play-pause></vg-play-pause>
        <vg-playback-button></vg-playback-button>

        <vg-time-display vgProperty="current" vgFormat="mm:ss"></vg-time-display>

        <vg-scrub-bar>
          <vg-scrub-bar-current-time style="height: 7px;"></vg-scrub-bar-current-time>
          <vg-scrub-bar-buffering-time style="border: 1px solid #ADADAD; border-radius: 3px; height: 7px;">
          </vg-scrub-bar-buffering-time>
        </vg-scrub-bar>

        <vg-time-display vgProperty="total" vgFormat="mm:ss"></vg-time-display>

        <vg-mute></vg-mute>
        <vg-volume></vg-volume>

        <vg-quality-selector [bitrates]="bitRates" (onBitrateChange)="setRates($event)">
        </vg-quality-selector>

        <vg-fullscreen></vg-fullscreen>
      </vg-controls>
      <vg-buffering vgFor="singleVideo"></vg-buffering>
      <video #media [vgMedia]="media" id="singleVideo" preload="auto" crossorigin="use-credentials" width="auto"
        #vgHls="vgHls" [vgHls]="stream.source" [vgHlsHeaders]="hlsHeaders" type="video/MP2T" (onGetBitrates)="getRates($event)">
      </video>
    </vg-player>
  </div>
</div>
