.left-nav {
  height          : 100%;
  width           : 235px;
  position        : absolute;
  top             : 0;
  left            : 0;
  background-color: #565454;
  font            : 21px 'Noto Sans JP', sans-serif;
  z-index         : 10;
  padding-bottom  : 100%;
  margin-bottom   : -100%;

  .mob-ham {
    display         : none;
    align-items     : center;
    justify-content : center;
    position        : absolute;
    top             : 1%;
    left            : 100%;
    height          : 70px;
    width           : 55px;
    background-color: #565454;

    span,
    span::before {
      content         : "";
      width           : 80%;
      height          : 10px;
      border-radius   : 5px;
      background-color: #fff;
      transform       : rotate(45deg);
    }

    span {
      position: relative;
      top     : -11px;
    }

    span::before {
      position : relative;
      display  : block;
      width    : 100%;
      transform: rotate(-90deg);
      top      : 18px;
      left     : 17px;
    }
  }

  .logo-sect {
    display        : flex;
    justify-content: space-between;
    align-items    : center;
    padding        : 10px;
  
    .qm-logo {
      height: 50px;
      width : 50px;

      svg {
        height: 100%;
        width : 100%;
      }
    }

    .admin-text {
      margin-right: 5px;
      color       : #fff;
      font-weight : 500;
    }
  }

  .link-sect {
    width      : 100%;
    height     : 50px;
    display    : flex;
    align-items: center;  
    border-top : 1px solid #707070;
    font-size  : 16px; 
    color      : #fff;
    cursor     : pointer;
    transition : all 0.3s ease-out;

    &:last-child {
      border-bottom: 1px solid #707070;
    }

    &:hover {
      background-color: rgba(0, 0, 0, 0.23);
      transition      : background-color 0.2s ease-in;
    }

    a {
      height: 100%;
      width: 100%;
      line-height: 50px;
      text-decoration: none;
      color: #fff;
      padding-left: 3em;
    }

    .selected {
      background-color: rgba(0, 0, 0, 0.23);
  
      span {
        display         : block;
        content         : "";
        position        : absolute;
        left            : 0;
        height          : 50px;
        width           : 15px;
        background-color: #2874A6;
      }
    }
  }

  .attr-text {
    position : absolute;
    bottom   : 0%;
    font-size: 12px;
    color    : #fff;
  }
}

@media (max-width: 440px) {
  .left-nav {
    .mob-ham {
      display: flex;
    }
  }
}