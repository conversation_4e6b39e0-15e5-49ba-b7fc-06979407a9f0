#!/bin/bash

# <AUTHOR> <EMAIL>
# Written for quantmasters.in

# Constants
RED='\033[0;31m'
GREEN='\033[0;32m'
BROWN='\033[0;33m'
NC='\033[0m'

# Functions
hint_usage(){
	echo "Usage: ./build+deploy.sh"
}

error_msg(){
	local message="$1"
	local no_usage=$2

	echo "${RED}[ERROR]${NC} $message\n"

	if [ -z $no_usage ]; then
		hint_usage
	fi

	exit 1
}

dbg_msg(){
	local message="$1"

	echo "${BROWN}[DEBUG]${NC}: $message\n"
}

login(){
	local password=$(echo "Mys4UzAqfm4yS2pT" | base64 --decode)

ftp -n ************ <<END_SCRIPT
quote USER quantkev
quote PASS "${password}"
ls
quit
END_SCRIPT

}

# Main Processing
echo "\nDeploying Angular to quantmasters.in...\n"

echo "Checking whether ftp is available..."
if ! command -v ftp >/dev/null; then
	msg="ftp is not installed"
	error_msg "${msg}"
else
	echo "${GREEN}OK${NC}\n"
fi

echo "Logging into the server..."
login
