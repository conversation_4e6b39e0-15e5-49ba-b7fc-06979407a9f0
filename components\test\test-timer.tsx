'use client'

import React, { useEffect, useState } from 'react'
import { Clock } from 'lucide-react'

interface TestTimerProps {
  minutes: number
  seconds: number
  isPopout?: boolean
}

const TestTimer: React.FC<TestTimerProps> = ({
  minutes,
  seconds,
  isPopout = false
}) => {
  const [color, setColor] = useState('text-green-600')

  useEffect(() => {
    if (minutes < 5) {
      setColor('text-red-600')
    } else if (minutes < 10) {
      setColor('text-orange-500')
    } else {
      setColor('text-green-600')
    }
  }, [minutes])

  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds

  return (
    <div
      className={`${isPopout ? 'fixed top-2 right-2 z-50 shadow-md bg-white rounded-md p-3' : ''}`}
    >
      <div className={`flex flex-col items-center ${color}`}>
        <p className="text-sm font-medium">Time Remaining</p>
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4" />
          <h3 className="text-xl font-bold">
            {formattedMinutes} : {formattedSeconds}
          </h3>
        </div>
      </div>
    </div>
  )
}

export default TestTimer
