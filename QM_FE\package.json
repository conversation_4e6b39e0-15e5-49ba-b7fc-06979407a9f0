{"name": "quant-masters", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-prod": "ng build --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@agm/core": "^1.0.0-beta.7", "@angular/animations": "^7.2.16", "@angular/common": "^7.2.16", "@angular/compiler": "^7.2.16", "@angular/core": "^7.2.16", "@angular/forms": "^7.2.16", "@angular/material": "^7.3.7", "@angular/platform-browser": "^7.2.16", "@angular/platform-browser-dynamic": "^7.2.16", "@angular/router": "^8.2.14", "@ngstack/code-editor": "^0.4.4", "@types/crypto-js": "^4.0.1", "angular-google-maps": "^2.4.1", "angular-markdown-editor": "^2.0.2", "angular-socialshare": "^2.3.11", "bootstrap": "^4.5.2", "core-js": "^2.6.11", "crypto-js": "^4.0.0", "file-saver": "^2.0.2", "hls.js": "^0.13.2", "jquery": "^3.5.1", "ng2-pdf-viewer": "5.0.1", "ngx-bootstrap": "^5.6.1", "ngx-cookie-service": "^2.4.0", "ngx-device-detector": "^2.0.0", "ngx-markdown": "^8.2.2", "ngx-mathjax": "0.0.11", "ngx-monaco-editor": "^7.0.0", "node-sass": "^4.13.1", "npm-upgrade": "^1.4.1", "rxjs": "~6.3.3", "tslib": "^1.13.0", "videogular2": "^7.0.1", "xlsx": "^0.16.4", "zone.js": "~0.8.26"}, "devDependencies": {"@angular-devkit/build-angular": "~0.12.0", "@angular-devkit/build-ng-packagr": "~0.12.0", "@angular/cli": "~7.2.1", "@angular/compiler-cli": "^7.2.16", "@angular/language-service": "^7.2.16", "@types/core-js": "^2.5.3", "@types/jasmine": "^2.8.17", "@types/jasminewd2": "^2.0.8", "@types/node": "~8.9.4", "codelyzer": "~4.5.0", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~3.1.1", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.1.1", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "ng-packagr": "^4.2.0", "protractor": "^5.4.3", "ts-node": "~7.0.0", "tsickle": ">=0.34.0", "tslib": "^1.9.0", "tslint": "~5.11.0", "typescript": "~3.2.2", "typescript-tslint-plugin": "^0.5.5"}, "browser": {"crypto": false}}