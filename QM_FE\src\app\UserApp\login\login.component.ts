import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { LoginService } from '../../Services/login.service';

import { User } from '../../Models/User';


declare let gtag: Function;

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {

  public user: User = new User();      // New user that wants to login
  public modalRef: BsModalRef;        // Login Message Dialog Template
  public loginErr: string;           // Login Error (if any)
  public showIndicator = false;
  public askReg = false;
  public expiredTime;

  @ViewChild('failureTemplate') public template: TemplateRef<any>; // ng-template to show login error message

  constructor(public loginService: LoginService,
              public modalService: BsModalService,
              public router: Router,
              ) {
    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    this.showIndicator = false;
  }

  /**
   * Login Function: Logs the user in after they enter their
   * login details
   */
  login(isValid: string) {
    this.showIndicator = true;

    if (!isValid) {
      this.showIndicator = false;
      return;
    }

    this.loginService.loginUser(this.user).subscribe(response => {
      const resText = JSON.parse(JSON.stringify(response));

      if (resText.text === 'User Logged in Successfully') {
        sessionStorage.setItem('logged', 'true');
        sessionStorage.setItem('QUser', resText.user);
        sessionStorage.setItem('QMSESS_ID', resText.sess);
        sessionStorage.setItem('QMA_TOK', resText.token);
        sessionStorage.setItem('QMail', resText.email);
        sessionStorage.setItem('QUCmpl', resText.complete);
        sessionStorage.setItem('QMA_KEY', resText.key);

        //Setting up the cookies
        this.fnSetCookies(resText);
        this.showIndicator = false;

        const page = localStorage.getItem('QPage');
        if (page) {
          if (page === 'AC') {

            this.loginService.checkUserLogin(resText.email).subscribe(response2 => {
              const checkResp = JSON.parse(JSON.stringify(response2));

              if (checkResp.msg === '821f069c-12dd-3d3d-9462-d6e84564b658') {
                this.router.navigate(['/admin/console']);
              }
            }, error => {
              this.router.navigate(['/home']);
            });
          } else if (page === 'PL') {
            this.router.navigate(['/plans']);
          } else if (page === 'DB') {
            this.router.navigate(['/dashboard']);
          } else if (page === 'IC') {
            this.router.navigate(['/certification/view']);
            localStorage.removeItem('QPage');
          }
        } else {
          this.router.navigate(['/home']);
        }
      }
    }, error => {
      if (error.error) {
        const resText = JSON.parse(JSON.stringify(error.error));

        this.loginErr = 'Email / Password Wrong';

        if (resText.err === 'NF') {
          this.askReg = true;
        }
        this.openModal(this.template);
        this.showIndicator = false;
      }
    });
  }

  /**
   * Setting up the cookies
   * @param template Login deatils
   */
  fnSetCookies(oData){
    this.expiredTime = new Date();
    this.expiredTime.setHours( this.expiredTime.getHours() + 12 ).toString(); 

    document.cookie = `Login_User=${oData.user}; path=/`;
    document.cookie = `Login_SessionId=${oData.sess}; path=/`;
    document.cookie = `Login_Token=${oData.token}; path=/`;
    document.cookie = `Login_Email=${oData.email}; path=/`;
    document.cookie = `Login_Complete=${oData.complete}; path=/`;
    document.cookie = `Login_Key=${oData.key}; path=/`;
    document.cookie = `Login_Expires=${this.expiredTime.toString()}; path=/`;


    // sessionStorage.setItem('logged', 'true');
    // sessionStorage.setItem('QUser', resText.user);
    // sessionStorage.setItem('QMSESS_ID', resText.sess);
    // sessionStorage.setItem('QMA_TOK', resText.token);
    // sessionStorage.setItem('QMail', resText.email);
    // sessionStorage.setItem('QUCmpl', resText.complete);
    // sessionStorage.setItem('QMA_KEY', resText.key);

  }

  /**
   * Generic Function: To open an ng-template
   * as a dialog on the page
   *
   * @param template ng-template to be opened
   *
   */
  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }
}
