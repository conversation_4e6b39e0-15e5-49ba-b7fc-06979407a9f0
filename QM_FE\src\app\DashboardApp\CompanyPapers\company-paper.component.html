<div class="chapter-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="header">
    <div class="title">
      <p>Placement Papers of all major companies</p>
    </div>
  </div>

  <div class="logo-wrap">
    <img src="../../../assets/companyLogos/Accenture-logo.png" class="logo"/>
    <img src="../../../assets/companyLogos/Amazon-Logo_Feature.png" class="logo"/>
    <img src="../../../assets/companyLogos/guideline_based1.png" class="logo"/>
    <img src="../../../assets/companyLogos/IBM_logoRR_pos_RGB.png" class="logo"/>
    <img src="../../../assets/companyLogos/imagesnmj.png" class="logo"/>
    <img src="../../../assets/companyLogos/imageswdr.png" class="logo"/>
    <img src="../../../assets/companyLogos/index.png" class="logo"/>
    <img src="../../../assets/companyLogos/infosys-logo-JPEG.png" class="logo"/>
  </div>

  <div class="form-wrap">
    <div class="locked-resource" (click)="takeToPlans()" title="Premium Feature" *ngIf="noPermission">
      <img src="../../../assets/icons/lock.svg" />
      <p>Subscribe to Quant Masters to Access Company Papers and more</p>
    </div>
    <div class="item">
      <label>Choose Company :</label>
      <select [(ngModel)]="selectedCompany" #company (change)="onSelectCompany($event, company.value)">
        <option value="0" selected disabled> Select Company Name</option>
        <option *ngFor="let company of companies">{{ company }}</option>
      </select>
    </div>
    <div class="item">
      <label>Choose Test Paper Publish Year :</label>
      <select [(ngModel)]="selectedYear" #year (change)="onSelectYear($event, year.value)">
        <option value="0" disabled selected>Select Test Paper Publish Year</option>
        <option *ngFor="let year of paperYears">{{ year }}</option>
      </select>
    </div>
    <div class="item">
      <button (click)="listPapers()">Get Papers</button>
    </div>
    <div class="item">
      <div class="papers">
        <div class="paper" *ngFor="let paper of papersSelected">
          <h6>{{ paper.paper_name }}</h6>
          <p>No time limt</p>
          <p>No. of questions: {{ paper.no_of_ques }}</p>
          <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>