import { Component, OnInit, TemplateRef, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PasswordService } from 'src/app/Services/password.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-password-recovery',
  templateUrl: './password-recovery.component.html',
  styleUrls: ['./password-recovery.component.scss']
})
export class PasswordRecoveryComponent implements OnInit, OnDestroy {
  public modalRef: BsModalRef;

  private resetKey: string;

  public showIndicator = false;

  public acceptPass = false;
  public email: string;
  public newPassword: string;
  public conf_password: string;

  public apiError: string;

  @ViewChild('genErrorTemplate') public genErrTemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public successTemplate: TemplateRef<any>;

  constructor(public route: ActivatedRoute,
              public modalService: BsModalService,
              public passwordService: PasswordService) { }

  ngOnInit() {
    this.route.paramMap.subscribe(params => {
      this.resetKey = params.get('reset_key');
      this.email = params.get('email');
    });
  }

  saveNewPassword() {
    this.showIndicator = true;
    this.passwordService.saveChangedPassword(this.email, this.newPassword, this.resetKey).subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      if (respObj.msg === 'updated') {
        this.openModal(this.successTemplate);
      }

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
      this.apiError = error.error.msg;
      this.openModal(this.genErrTemplate);
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  ngOnDestroy() {
    if (typeof this.modalRef !== 'undefined') {
      this.modalRef.hide();
    }
  }
}
