import { NgModule } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { CommonModule } from '@angular/common';

import { AlertModule } from 'ngx-bootstrap/alert';

import { MarkdownModule } from 'ngx-markdown';

import { NotesAppRoutingModule } from './notes-app-routing.module';

import { NotesHomeComponent } from './notes-home/notes-home.component';
import { NotesDetailComponent } from './notes-detail/notes-detail.component';

import { SharedNavModule } from '../Components/Utilities/shared-nav/shared-nav.module';
import { SharedFooterModule } from '../Components/Utilities/shared-footer/shared-footer.module';
import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { CollapseModule } from 'ngx-bootstrap/collapse';
import { FormsModule } from '@angular/forms';

@NgModule({
  declarations: [
    NotesHomeComponent,
    NotesDetailComponent
  ],
  imports: [
    CommonModule,
    HttpClientModule,
    NotesAppRoutingModule,
    SharedNavModule,
    SharedFooterModule,
    SharedIndicatorModule,
    MarkdownModule.forRoot(),
    AlertModule.forRoot(),
    CollapseModule.forRoot(),
    FormsModule
  ]
})
export class NotesAppModule { }
