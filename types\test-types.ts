// types/test-types.ts

export interface Question {
  paper_id: string
  ques_no: string
  question_no?: string
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  opt_5: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: number | string
  options: string[]
  explanation: string
  img_expln: string
  original_qno?: string
}

export interface CompetitiveQuestion {
  paper_id: string
  question_no: string
  question_origin: string
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  opt_5: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: number
  options: string[]
  explanation: string
}

export interface CompanyQuestion {
  paper_id: string
  question_no: string
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: number
  options: string[]
  explanation: string
}

export interface ModelQuestion {
  paper_id: string
  question_no: string
  question: string
  option_1: string
  option_2: string
  option_3: string
  option_4: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: string | number
  options: string[]
  explanation: string
}

export interface PaperAdditionData {
  question_no: number
  type: number
  value: string
}

export interface SectionData {
  section_name: string
  marks: number
}

export interface TestProps {
  testType: number
  paperId: string
  paperName: string
  paperLim: number
}
