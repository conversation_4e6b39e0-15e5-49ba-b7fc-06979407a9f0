import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { Execute } from '../Model/Execute';
import { InlineResponse200 } from '../Model/InlineResponse200';

@Injectable({
  providedIn: 'root'
})
export class JdoodleService {

  public proxyUrl       = 'https://cors-anywhere.herokuapp.com/';
  public jdoodleBaseUrl = 'https://api.jdoodle.com/v1';

  constructor(private http: HttpClient) { }

  executePost(body: Execute): Observable<InlineResponse200> {

    const executeUrl = this.proxyUrl + this.jdoodleBaseUrl + '/execute';

    const httpOps = {
      headers: new HttpHeaders({
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      })
    };

    return this.http.post<InlineResponse200>(executeUrl, body, httpOps);
  }
}
