import { animate, stagger, state, style, transition, trigger, query } from '@angular/animations';
import { AfterViewInit, Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import * as $ from 'jquery';
import { BsDropdownConfig } from 'ngx-bootstrap/dropdown';

import { ChaptersService } from '../../../Services/Dashboard/chapters.service';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';
import { SuperGroup } from '../../../Models/Dashboard/Chapters/SuperGroup';
import { Group } from '../../../Models/Dashboard/Chapters/Group';

import { ChapterPracticeService } from '../../../Services/Dashboard/chapter-practice.service';

import { OpenTestsService } from '../../../Services/open-tests.service';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { Router, ActivatedRoute } from '@angular/router';
import { PaperService } from 'src/app/Services/Paper.service';
import { AdminViewService } from '../../../Services/admin-view.service';
import { TmcqService } from '../../../Services/Dashboard/TMCQ/tmcq.service';
import { TMCQGroup } from '../../../Models/Dashboard/TMCQ/TMCQGroup';
import { TMCQSubGroup } from '../../../Models/Dashboard/TMCQ/TMCQSubGroup';
import { SectionWisePapersService } from 'src/app/Services/Dashboard/section-wise-papers.service';
import { CompetitiveService } from 'src/app/Services/Dashboard/competitive.service';
import { CompetitivePapers } from 'src/app/Models/Dashboard/Competitive/CompetitivePapers';
import { TestsService } from '../../../Services/tests.service';


@Component({
  selector: 'app-upload-tests',
  templateUrl: './upload-tests.component.html',
  styleUrls: ['./upload-tests.component.scss'],
  animations: [
    trigger('fadeIn', [
      transition(':enter', [
        style({ transform: 'translateY(50px)', opacity: 0 }),
        query('.main-ctrl', [
          style({ transform: 'translateY(55px)', opacity: 0 })
        ]),
        animate('0.5s ease-in', style({ transform: 'translateY(0px)', opacity: 1 })),
        query('.main-ctrl', [
          stagger(-100, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(0px)', opacity: 1 }))
          ])
        ])
      ])
    ]),
    trigger('hideGreetingMsg', [
      state('hidden', style({ transform: 'translateY(-20px)', opacity: 0 })),
      state('notHidden', style({ transform: 'translateY(0px)', opacity: 1 })),
      transition('notHidden => hidden', [animate('0.3s ease-out')]),
      transition('hidden => notHidden', [animate('0.3s ease-in')])
    ]),
    trigger('bringInPapers', [
      transition('* => *', [
        query(':enter', [
          style({ transform: 'translateY(50px)', opacity: 0 }),
          stagger(120, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(0px)', opacity: 1 }))
          ])
        ], { optional: true })
      ])
    ])
  ],
  providers: [{ provide: BsDropdownConfig, useValue: { isAnimated: true, autoClose: true } }],
  encapsulation: ViewEncapsulation.None
})
export class UploadTestsComponent implements OnInit, AfterViewInit {

  @ViewChild('successTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public eTemplate: TemplateRef<any>;

  @ViewChild('createPaperTemplate') public createTemplate: TemplateRef<any>;
  @ViewChild('createGroup') public createGroup: TemplateRef<any>;
  @ViewChild('createSubGroup') public createSubGroup: TemplateRef<any>;
  @ViewChild('refreshTemplate') public refreshTemplate: TemplateRef<any>;

  public greetingMsgs = [
    'Well hello there, what are we doing today?',
    'Beautiful day today, what can I help you with?',
    'Hii, What are we tackling today?'
  ];
  public selectdGreeting: string;
  public selectedPaperTypeMsg = "Select Paper Type";

  public showIndicator = false;
  public showBackIcon = false;
  public showMainContent = false;
  public selectedAction = [false, false, false, false];
  public hideGreeting = 'notHidden';
  public selectStates = [false];
  public createPaperState = false;
  public editPaperState = false;

  public selectedPaperType: number;

  public papers: ChapterPaper[] | CompetitivePapers[];
  public selectedPaper: ChapterPaper | CompetitivePapers;
  public displayRecords: ChapterPaper[] | CompetitivePapers[];

  public newPaper: ChapterPaper;

  public paperGroups = false;
  public tmcqGroups: TMCQGroup[];
  public tmcqSubGroups: TMCQSubGroup[];
  public chapterSuperGroups: SuperGroup[];
  public chapterGroups: Group[];
  public selectedTopic = '';
  public selectedSubTopic = '';
  public newTmcqGroup = '';
  public newTmcqGroupId = '';
  public newTmcqSubGroup = '';
  public modalRef: BsModalRef;
  public competitiveGroups: string[];

  public config = {
    backdrop: true,
    class: 'create-modal',
    isAnimated: 'true'
  };

  public successMsg = '';

  public showCopyAlert = false;
  public copyAlert = {
    type: 'info',
    msg: 'Url Copied!',
    timeout: 2000,
    isOpen: false
  };

  public allPapersLegth: number;
  constructor(private openTestsService: OpenTestsService,
              private paperService: PaperService,
              private competitiveService: CompetitiveService,
              private adminViewService: AdminViewService,
              private tmcqService: TmcqService,
              private modalService: BsModalService,
              private sectionWiseService: SectionWisePapersService,
							private testsService: TestsService,
              private chapterService: ChaptersService,
              private chapterPracticeService: ChapterPracticeService,
              public router: Router,
              public activatedRoute: ActivatedRoute) { }

  ngOnInit() {
    this.selectRandomGreeting();
    this.newPaper = new ChapterPaper();
  }

  selectRandomGreeting() {
    const selector = Math.floor(Math.random() * Math.floor(this.greetingMsgs.length));
    this.selectdGreeting = this.greetingMsgs[selector];
  }

  selectMainAction(num: number) {
    this.selectedAction = [false, false, false, false];

    this.showBackIcon = true;
    this.hideGreeting = 'hidden';
    switch (num) {
      case 0:
        this.selectedAction[0] = true;
        this.createPaperState = true;
        sessionStorage.setItem('paperState', 'Create');
        if (this.selectedTopic !== '' && this.selectedSubTopic !== '')
          this.getTMCQPapers();
        break;
      case 1:
        this.selectedAction[1] = true;
        this.editPaperState = true;
        sessionStorage.setItem('paperState', 'Edit');
        break;
      case 2:
        this.selectedAction[2] = true;
        break;
      case 3:
        this.selectedAction[3] = true;
        break;
    }

    setTimeout(() => {
      $('div.main-ctrl:not(#active)').addClass('scoot-away');
      $('#active').removeClass('scoot-away').css({ 'grid-column': '2', 'grid-row': '1' });
      $('.main-ctrls').css({ 'grid-template-columns': 'repeat(5, 8%)' });
    });
  }

  openSelect(num: number) {
    if (num === 1) {
      this.selectStates[0] = !this.selectStates[0];
    }
  }

  getTrialPapers() {

    this.showIndicator = true;
    this.openTestsService.getPapers().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.papers = resp;
      this.papers = this.papers.reverse();
      this.allPapersLegth = resp.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toString() + ' Mins';
      });

      this.selectedPaperType = 5;

      this.showMainContent = true;
      this.selectStates = [false];
      this.showIndicator = false;
      sessionStorage.setItem('paperType', '5');
      sessionStorage.setItem('openPaperType', '0');

      this.selectedPaperTypeMsg = "Trial Papers";
    }, error => {
      this.showIndicator = false;
    });
  }

  // For Weekly competitive
  getWeeklyCompetitivePapers() {
    this.showIndicator = true;
    this.adminViewService.getWeeklyCompetitivePapers().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.papers = resp;
      this.papers = this.papers.reverse();
      this.allPapersLegth = resp.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toString() + ' Mins';
      });

      this.selectedPaperType = 8;

      this.showMainContent = true;
      this.selectStates = [false];
      this.showIndicator = false;
      sessionStorage.setItem('paperType', '8');
      sessionStorage.setItem('openPaperType', '2');

      this.selectedPaperTypeMsg = "Weekly Competitive Papers";
    }, error => {
      this.showIndicator = false;
    });
  }

  // For Comapmy Papers
  getCompanyPapers() {
    this.showIndicator = true;
    this.adminViewService.getCompanyPapers().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.papers = resp;
      this.papers = this.papers.reverse();
      this.allPapersLegth = resp.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toString() + ' Mins';
      });

      this.selectedPaperType = 5;

      this.showMainContent = true;
      this.selectStates = [false];
      this.showIndicator = false;
      sessionStorage.setItem('paperType', '5');
      sessionStorage.setItem('openPaperType', '1');

      this.selectedPaperTypeMsg = "Company Papers";
    }, error => {
      this.showIndicator = false;
    });
  }

  getTechnicalMCQResources() {
    this.showIndicator = true;
    this.tmcqService.getTMCQGroupsAdmin().subscribe(response => {
      this.selectedPaperType = 11;

      this.paperGroups = true;
      this.showMainContent = true;
      this.selectStates = [false];

      this.tmcqGroups = response;
      sessionStorage.setItem('paperType', '11');

      this.selectedPaperTypeMsg = "Technical MCQs";
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getTMCQSubGroups() {
    this.showIndicator = true;

    this.tmcqService.getMTCQSubGroupsAdmin(this.selectedTopic).subscribe(response => {

      this.tmcqSubGroups = response;

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getTMCQPapers() {

    this.showIndicator = true;

    this.tmcqService.getPapersOfAGroupAdmin(this.selectedSubTopic).subscribe(response => {
      this.papers = response.reverse();
      this.allPapersLegth = response.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getSectionWisePapers() {

    this.showIndicator = true;
    this.sectionWiseService.getPapers().subscribe(response => {

      this.selectedPaperType = 9;
      sessionStorage.setItem('paperType', '9');

      this.showMainContent = true;

      const respObj = JSON.parse(JSON.stringify(response));
      this.papers = respObj.reverse();
      this.allPapersLegth = respObj.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.selectedPaperTypeMsg = "Section-Wise Papers";
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get a list of Verbal Notes Practice Paper groups,
   * these are the papers that will link with the
   * verbal-notes.quantmasters.in wordpress site.
   */
  getVerbalNotesGroups(overrideCloseOptions: boolean) {

    this.showIndicator = true;
    this.competitiveService.getVerbalPracticeGroups().subscribe(response => {

      this.selectedPaperType = 13;
      sessionStorage.setItem('paperType', '13');

      this.showMainContent = true;

      const respObj = JSON.parse(JSON.stringify(response));
      this.competitiveGroups = respObj;

      if (!overrideCloseOptions) {
        this.openSelect(1);
      }

      this.selectedPaperTypeMsg = "Verbal Practice Papers";
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getPracticePapers() {

    this.showIndicator = true;
    this.competitiveService.getPapersOfAGroup(this.selectedTopic).subscribe(response => {

      this.papers = response.reverse();
      this.allPapersLegth = response.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get a list of Technical Notes Practice Paper groups,
   * these are the papers that will link with the
   * tech-notes.quantmasters.in wordpress site.
   */
  getTechnicalNotesGroups(overrideCloseOptions: boolean) {

    this.showIndicator = true;
    this.competitiveService.getTechnicalPracticeGroups().subscribe(response => {

      this.selectedPaperType = 14;
      sessionStorage.setItem('paperType', '14');

      this.showMainContent = true;

      const respObj = JSON.parse(JSON.stringify(response));
      this.competitiveGroups = respObj;

      if (!overrideCloseOptions) {
        this.openSelect(1);
      }

      this.selectedPaperTypeMsg = "Technical Practice Papers";
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

	getMockPapers() {
		this.showIndicator = true;
		this.testsService.getPapers().subscribe(response => {

			this.selectedPaperType = 4;
      sessionStorage.setItem('paperType', '4');

      this.showMainContent = true;

			const respObj = JSON.parse(JSON.stringify(response))
      this.papers = respObj.reverse();
      this.allPapersLegth = respObj.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.selectedPaperTypeMsg = "Mock Papers";
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
	}

	/**
   * Get all chapter-wise assessment Groups
   */
	getChapterSuperGroups() {

    this.showIndicator = true;

    this.chapterService.getSuperGroups().subscribe(response => {

      this.selectedPaperType = 1;
      sessionStorage.setItem('paperType', '1');

      this.showMainContent = true;
      this.chapterSuperGroups = response;

      this.selectedPaperTypeMsg = "Chapter-Wise Papers"
      this.selectStates[0] = false;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get the groups of a super group for chapter-wise
   * assessments.
   */
  getChapterGroups() {

    this.showIndicator = true;

    this.chapterService.getGroups(this.selectedTopic).subscribe(response => {

      this.chapterGroups = response;

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get the papers of a selected Chapter-Wise Assessment
   * Group.
   */
  getChapterPapers() {
    this.showIndicator = true;
		this.chapterService.getPapersOfAGroup(this.selectedSubTopic).subscribe(response => {

      this.showMainContent = true;

      this.papers = response;
      this.allPapersLegth = response.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get all chapter-wise practice assessment Groups
   */
  getChapterPracticeSuperGroups() {
    this.showIndicator = true;

    this.chapterPracticeService.getSuperGroups().subscribe(response => {

      this.selectedPaperType = 6;
      sessionStorage.setItem('paperType', '6');

      this.showMainContent = true;
      this.chapterSuperGroups = response;

      this.selectedPaperTypeMsg = "Chapter-Wise Practice Papers";
      this.selectStates[0] = false;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get the groups of a super group for chapter-wise
   * practice assessments.
   */
  getChapterPracticeGroups() {
    this.showIndicator = true;

    this.chapterPracticeService.getGroups(this.selectedTopic).subscribe(response => {

      this.chapterGroups = response;

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  /**
   * Get the papers of a selected Chapter-Wise Practice
   * Assessment Group.
   */
  getChapterPracticePapers() {
    this.showIndicator = true;
		this.chapterPracticeService.getPapersOfAGroup(this.selectedSubTopic).subscribe(response => {

      this.showMainContent = true;

      this.papers = response;
      this.allPapersLegth = response.length;
      this.displayRecords = this.papers.slice(0, 10);
      this.papers.forEach(paper => {
        paper.time_lim = (parseInt(paper.time_lim, 10) / 60000).toFixed(0).toString() + ' Mins';
      });

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  openCreatePaper() {
    this.openModal(this.createTemplate);
  }

  openCreateGroup() {
    this.newTmcqGroup = '';
    this.openModal(this.createGroup);
  }

  openCreateSubGroup() {
    this.newTmcqGroupId = '';
    this.newTmcqSubGroup = '';
    this.openModal(this.createSubGroup);
  }

  createNewPaper(isValid: boolean) {

    if (!isValid)
      return;

    this.showIndicator = true;

    if (this.selectedPaperType === 11) {
      this.newPaper.sub_group_id = this.selectedSubTopic;
      this.newPaper.show_ans = '1';

      this.tmcqService.createNewPaper(this.newPaper).subscribe(response => {
        this.showIndicator = false;
        this.modalRef.hide();
        this.getTMCQPapers();
        this.successMsg = 'Paper Created Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.selectedPaperType === 13 || this.selectedPaperType === 14) {

      this.newPaper.group_id = this.selectedTopic;
      this.newPaper.status   = '1';
      this.newPaper.show_ans = '1';
      this.newPaper.public   = '0';

      this.competitiveService.createNewCompetitivePaper(this.newPaper).subscribe(response => {
        this.showIndicator = false;
        this.modalRef.hide();
        this.getPracticePapers();
        this.successMsg = 'Paper Created Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
      });
    }
  }

  createNewGroup(isValid: Boolean) {

    if (!isValid)
      return;

    this.showIndicator = true;

    if (this.selectedPaperType === 11) {
      const oBody = {
        'group_name' : this.newTmcqGroup
      };

      this.tmcqService.createNewGroup(oBody).subscribe(response => {
        this.showIndicator = false;
        this.modalRef.hide();
        this.getTechnicalMCQResources();
        this.successMsg = 'Group Created Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
      });
    } else if (this.selectedPaperType === 13 || this.selectedPaperType === 14) {
      const oGroup = {
        'group_id': this.newTmcqGroup,
        'group_name' : this.newTmcqGroup
      };

      this.competitiveService.createNewVerbalPracticeGroup(oGroup).subscribe(response => {
        this.showIndicator = false;
        this.modalRef.hide();
        this.selectedPaperType === 14 ? this.getTechnicalNotesGroups(true) : this.getVerbalNotesGroups(true);
        this.successMsg = 'Group Created Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
      })
    }
  }

  createNewSubGroup(isValid: Boolean) {
    if (!isValid)
    return;
    // this.showIndicator = true;
    const oBody = {
      'group_id': this.newTmcqGroupId,
      'sub_group_name' : this.newTmcqSubGroup
    };
    this.tmcqService.createNewSubGroup(oBody).subscribe(response => {
      this.showIndicator = false;
      this.modalRef.hide();
      this.successMsg = 'SubGroup Created Successfully!';
      this.openModal(this.sTemplate);
    }, error => {
      this.showIndicator = false;
    });
  }

  savePaperDetails(paper: ChapterPaper) {

    this.showIndicator = true;
    paper.time_lim = (parseInt(paper.time_lim, 10) * 60000).toString();

    if (this.selectedPaperType === 5) {

      paper.type = 0;

      this.paperService.putPaperHeaderDetails(paper, paper.paper_id).subscribe(response => {

        this.getTrialPapers();
        this.showIndicator = false;
        this.successMsg = 'Data Updated Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.selectedPaperType === 8) {

      paper.type = 2;

      this.adminViewService.putWeeklyCompetitiveDetails(paper, paper.paper_id).subscribe(response => {

        this.getWeeklyCompetitivePapers();
        this.showIndicator = false;
        this.successMsg = 'Data Updated Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.selectedPaperType === 11 || this.selectedPaperType === 14) {

      paper.type = 0;

      this.tmcqService.updatePaper(paper).subscribe(response => {
        this.getTMCQPapers();
        this.showIndicator = false;
        this.successMsg = 'Data Updated Successfully!';
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    }

    this.showIndicator = false;
  }

  editPaper(paperId: string) {
    // this.selectedPaper = this.papers.find(x => x.paper_id = paperId);

    // console.log('this.selectedPaper :>> ', this.selectedPaper);
  }

  deletePaper(paperId: string) {

    this.showIndicator = true;
    switch (this.selectedPaperType) {
      case 5:
        this.paperService.deleteOpenPaperDetails(paperId).subscribe(response => {
          this.getCompanyPapers();
          this.showIndicator = false;
          this.successMsg = 'Paper deleted Successfully!';
          this.openModal(this.sTemplate);
        }, error => {
          this.showIndicator = false;
          this.openModal(this.eTemplate);
        });

        break;
        
      case 11:
        this.tmcqService.deletePaper(paperId).subscribe(response => {
          this.getTMCQPapers();
          this.showIndicator = false;
          this.successMsg = 'Paper deleted Successfully!';
          this.openModal(this.sTemplate);
        }, error => {
          this.showIndicator = false;
          this.openModal(this.eTemplate);
        });
        break;

      case 13:
        this.competitiveService.deleteCompetitivePaper(paperId).subscribe(response => {
          this.getPracticePapers();
          this.showIndicator = false;
          this.successMsg = 'Paper deleted Successfully!';
          this.openModal(this.sTemplate);
        }, error => {
          this.showIndicator = false;
          this.openModal(this.eTemplate);
        });

      case 14:
        this.competitiveService.deleteCompetitivePaper(paperId).subscribe(response => {
          this.getPracticePapers();
          this.showIndicator = false;
          this.successMsg = 'Paper deleted Successfully!';
          this.openModal(this.sTemplate);
        }, error => {
          this.showIndicator = false;
          this.openModal(this.eTemplate);
        });

      default:
        break;
    }
  }

  goBack() {
    $('#active').css({ 'grid-column': '', 'grid-row': '' });
    $('.main-ctrls').css({ 'grid-template-columns': 'repeat(4, 15%)' });
    $('div.main-ctrl').removeClass('scoot-away');

    this.selectedAction = [false, false, false, false];
    this.showBackIcon = false;
    this.createPaperState = false;
    this.editPaperState = false;
    this.selectStates = [false];
    this.showMainContent = false;
    this.papers = [];

    this.selectRandomGreeting();
    this.hideGreeting = 'notHidden';
  }

  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayRecords = this.papers.slice(startItem, endItem);
  }

  ngAfterViewInit() {
  }

  takeToPaperDetails(paper) {

    sessionStorage.setItem('QPaperType', this.selectedPaperType.toString());

    if (this.selectedPaperType === 11) {
      sessionStorage.setItem('QPaperGroup', paper.sub_group_id);
    }

    if (this.selectedPaperType === 13 || this.selectedPaperType === 14) {
      sessionStorage.setItem('QPracticePaperGroup', paper.group_id);
    }

    if (this.selectedPaperType === 4) {
      sessionStorage.setItem('MockPaperDesc', paper.paper_desc);
    }

    if (this.selectedPaperType === 1 || this.selectedPaperType === 6) {
      sessionStorage.setItem('QPaperSupGroup', paper.sup_group_id);
      sessionStorage.setItem('QPaperGroup', paper.group_id);
    }

    const timeLim = Number(paper.time_lim.split(' ')[0]);
    this.router.navigate(['paperdetail', paper.paper_id, paper.paper_name, paper.show_ans, timeLim,
      paper.no_of_ques, paper.status, paper.once_ans, paper.neg_marks || 0, paper.rand_ques || 0],
      { relativeTo: this.activatedRoute });
  }

  refreshCache() {
    this.showIndicator = true;
    if (this.selectedPaperType === 8) {
      this.openTestsService.etOpenCompetitivePapersOverriden().subscribe(response => {
        this.showIndicator = false;
        this.openModal(this.refreshTemplate);
      }, error => {
        this.showIndicator = false;
      });
    }
  }

  copyListUrlForWordpress() {

    const that = this;

    if (this.selectedTopic) {
      const url = `https://quantmasters.in/dashboard/practice/${this.selectedTopic}/papers`;

      that.copyAlert.type = 'info';
      that.copyAlert.msg = 'Url Copied!';

      navigator['clipboard'].writeText(url).then(() => {
        that.showCopyAlert = true;
      }, () => {});
    } else {
      that.copyAlert.type = 'danger';
      that.copyAlert.msg = 'Please Select a Topic';
      that.showCopyAlert = true;
    }
  }

  onClosed() {
    this.showCopyAlert = false;
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }
}
