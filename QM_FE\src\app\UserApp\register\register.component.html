<app-nav></app-nav>
<div class="register-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="reg-card">
    <div class="left-sect">
      <div class="reg-heading">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="70" height="70"
          viewBox="0 0 70 70">
          <defs>
            <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
              <stop offset="0" stop-color="#38a3e9" />
              <stop offset="1" stop-color="#0b6fb1" stop-opacity="0.98" />
            </linearGradient>
          </defs>
          <g id="Group_18" data-name="Group 18" transform="translate(-315 -256)">
            <rect id="Rectangle_85" data-name="Rectangle 85" width="70" height="70" transform="translate(315 256)"
              fill="url(#linear-gradient)" />
            <g id="ic_perm_identity_24px" transform="translate(330 271)">
              <path id="ic_perm_identity_24px-2" data-name="ic_perm_identity_24px"
                d="M24,8.75A5.25,5.25,0,1,1,18.75,14,5.249,5.249,0,0,1,24,8.75m0,22.5c7.425,0,15.25,3.65,15.25,5.25v2.75H8.75V36.5c0-1.6,7.825-5.25,15.25-5.25M24,4A10,10,0,1,0,34,14,10,10,0,0,0,24,4Zm0,22.5c-6.675,0-20,3.35-20,10V44H44V36.5C44,29.85,30.675,26.5,24,26.5Z"
                transform="translate(-4 -4)" fill="#fff" />
            </g>
          </g>
        </svg>
        <h5 class="card-title-cust">Sign Up on QuantMasters</h5>
        <p>Create a account and get access to resources</p>
      </div>
      <form #registerForm="ngForm" appPasswordEqualValidator (ngSubmit)="register(registerForm.valid)"
        autocomplete="off">
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="firstname" placeholder="First Name *"
              [class.is-inv--input]="firstname.invalid && firstname.touched || (firstname.pristine && registerForm.submitted)"
              [(ngModel)]="newUser.f_name" #firstname="ngModel" required pattern="[a-zA-Z][a-zA-Z ]*" />
            <!-- <small class="placeholder-text">First Name</small> -->
            <small class="form-error--text"
              *ngIf="firstname.errors?.required && firstname.touched || (firstname.pristine && registerForm.submitted)">
              First Name is Required
            </small>
            <small class="form-error--text" *ngIf="firstname.errors?.pattern && firstname.touched">
              First Name can only contain letters
            </small>
          </div>
          <div class="form-elem">
              <input type="text" name="lastname" placeholder="Last Name" [class.is-inv--input]="lastname.invalid && lastname.touched"
              [(ngModel)]="newUser.l_name" #lastname="ngModel" pattern="[a-zA-Z]+|^$" />
            <small class="form-error--text" *ngIf="lastname.errors?.pattern && lastname.touched">
              Last Name can only contain letters
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="dob"
              [class.is-inv--input]="dob.invalid && dob.touched || (dob.pristine && registerForm.submitted)"
              bsDatepicker #dp="bsDatepicker" placeholder="Date of Birth *" autocomplete="off" [bsConfig]="bsConfig"
              [bsValue]="bsValue" [(ngModel)]="newUser.dob" #dob="ngModel" required />
            <svg id="Icon_Event_Rounded" data-name="Icon / Event / Rounded" xmlns="http://www.w3.org/2000/svg"
              width="24" height="24" viewBox="0 0 24 24">
              <g id="Icon_Event_Rounded-2" data-name="Icon / Event / Rounded" opacity="0.5">
                <rect id="Box" width="24" height="24" fill="none" />
                <path id="Path_2017" data-name="Path 2017"
                  d="M879.9,18h-12a1.075,1.075,0,0,1-1-1V7h14V17A1,1,0,0,1,879.9,18Zm-2-17V2h-8V1a1,1,0,0,0-2,0V2h-1a2.006,2.006,0,0,0-2,2V18a2.006,2.006,0,0,0,2,2h14a2.006,2.006,0,0,0,2-2V4a2.006,2.006,0,0,0-2-2h-1V1a1,1,0,0,0-2,0Zm0,10h-3a1.075,1.075,0,0,0-1,1v3a1.075,1.075,0,0,0,1,1h3a1.075,1.075,0,0,0,1-1V12A1,1,0,0,0,877.9,11Z"
                  transform="translate(-862 2)" />
              </g>
            </svg>
            <div class="form-fill--bar"
              [class.is-inv]="dob.invalid && dob.touched || (dob.pristine && registerForm.submitted)"></div>
            <small class="form-error--text"
              *ngIf="dob.errors?.required && dob.touched || (dob.pristine && registerForm.submitted)">
              Date of Birth is Required
            </small>
          </div>
          <div class="form-elem">
            <input type="number" name="phoneNum"
              [class.is-inv--input]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)"
              placeholder="Mobile Number *" [(ngModel)]="newUser.phone_no" #phoneNum="ngModel" required
              pattern="[0-9]{10}" />
            <div class="form-fill--bar"
              [class.is-inv]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="phoneNum.errors?.pattern && phoneNum.touched">
              Mobile number must contain 10 digits
            </small>
            <small class="form-error--text" *ngIf="phoneNum.errors?.required && phoneNum.touched">
              Please enter your phone number
            </small>
            <small id="emailHelpBlock" *ngIf="phoneNum.valid">
              Don't worry, your number is safe with us.
            </small>
          </div>
        </div>
        <div class="form-row--2">
          <div class="form-elem">
            <input type="email" name="email"
              [class.is-inv--input]="email.invalid && email.touched || (email.pristine && registerForm.submitted)"
              placeholder="Email *" aria-describedby="emailHelpBlock" [(ngModel)]="newUser.email" #email="ngModel"
              required email />
            <div class="form-fill--bar"
              [class.is-inv]="email.invalid && email.touched || (email.pristine && registerForm.submitted)"></div>
            <small class="form-error--text"
              *ngIf="email.errors?.required && email.touched || (email.pristine && registerForm.submitted)">
              Email is Required
            </small>
            <small class="form-error--text" *ngIf="email.errors?.email && email.touched">
              Email is Invalid
            </small>
            <small id="emailHelpBlock" *ngIf="email.valid">
              Don't worry, we won't share your email with anyone.
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="inst"
              [class.is-inv--input]="instname.invalid && instname.touched  || (instname.pristine && registerForm.submitted)"
              placeholder="Your College or School Name *" [(ngModel)]="newUser.inst_name" #instname="ngModel" required />
            <div class="form-fill--bar"
              [class.is-inv]="instname.invalid && instname.touched  || (instname.pristine && registerForm.submitted)">
            </div>
            <small class="from-text text-danger"
              *ngIf="instname.errors?.required && instname.touched  || (instname.pristine && registerForm.submitted)">
              Please enter your College name
            </small>
          </div>
          <div class="form-elem">
            <select id="section" name="section" class="custom-select form-control"
              [class.is-inv--input]="section.invalid && section.touched || (section.pristine && registerForm.submitted)"
              [(ngModel)]="newUser.section" #section="ngModel" required>
              <option [ngValue]="null">Section *</option>
              <option>A</option>
              <option>B</option>
              <option>C</option>
              <option>D</option>
              <option>E</option>
              <option>F</option>
            </select>
            <div class="form-fill--bar"
              [class.is-inv]="section.invalid && section.touched || (section.pristine && registerForm.submitted)"></div>
            <small class="form-error--text"
              *ngIf="section.errors?.required && section.touched || (section.pristine && registerForm.submitted)">
              Please select your section
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <select id="qual" name="qual" class="custom-select form-control"
              [class.is-inv--input]="course.invalid && course.touched || (course.pristine && registerForm.submitted)"
              [(ngModel)]="newUser.qual" #course="ngModel" (change)="filterSelectedBranch($event.target.value)" required>
              <option [ngValue]="null">Course *</option>
              <option *ngFor="let str of branches.data">{{ str.bName }}</option>
            </select>
            <div class="form-fill--bar"
              [class.is-inv]="course.invalid && course.touched || (course.pristine && registerForm.submitted)"></div>
            <small class="form-error--text"
              *ngIf="course.errors?.required && course.touched || (course.pristine && registerForm.submitted)">
              Please select your latest course
            </small>
          </div>
          <div class="form-elem">
            <select id="branch" name="branch" class="custom-select form-control"
              [class.is-inv--input]="branch.invalid && branch.touched || (branch.pristine && registerForm.submitted)"
              [(ngModel)]="newUser.branch" #branch="ngModel">
              <option [ngValue]="null">Branch</option>
              <option *ngFor="let str of displayBranches.subName">{{ str }}</option>
              <option value="Oher">Other</option>
            </select>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="usn" [class.is-inv--input]="usnCtrl.touched && usnCtrl.invalid"
              [(ngModel)]="newUser.usn" placeholder="College Roll No. / Registration No." #usnCtrl="ngModel" />
            <div class="form-fill--bar" [class.is-inv]="usnCtrl.invalid && usnCtrl.touched"></div>
          </div>
          <div class="form-elem">
            <select id="yop" name="yop" class="custom-select form-control"
              [class.is-inv--input]="yop.invalid && yop.touched || (yop.pristine && registerForm.submitted)"
              [(ngModel)]="newUser.yop" #yop="ngModel" required>
              <option [ngValue]="null">Year of Passing *</option>
              <option>2028</option>
              <option>2027</option>
              <option>2026</option>
              <option>2025</option>
              <option>2024</option>
              <option>2023</option>
              <option>2022</option>
              <option>2021</option>
              <option>2020</option>
              <option>2019</option>
              <option>2018</option>
              <option>2017</option>
              <option>2016</option>
              <option>2015</option>
              <option>2014</option>
              <option>2013</option>
              <option>2012</option>
              <option>2011</option>
              <option>2010</option>
              <option>2009</option>
            </select>
            <div class="form-fill--bar"
              [class.is-inv]="yop.invalid && yop.touched || (yop.pristine && registerForm.submitted)"></div>
            <small class="form-error--text"
              *ngIf="yop.errors?.required && yop.touched || (yop.pristine && registerForm.submitted)">
              Please select your Year of Passing
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="password" name="password"
              [class.is-inv--input]="password.invalid && password.touched || (password.pristine && registerForm.submitted)"
              placeholder="Password *" aria-describedby="passwordHelpBlock" [(ngModel)]="newUser.password"
              #password="ngModel" required minlength="8" appPasswordValidator />
            <div class="form-fill--bar"
              [class.is-inv]="password.invalid && password.touched || (password.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="password.errors?.minlength && password.touched">
              Password Must be at least 8 characters long
            </small>
            <!-- <small class="form-error--text"
                  *ngIf="password.errors?.passwordNumber && password.touched && !password.errors?.required">
                  Password Must Contain at least 1 number
                </small> -->
            <small class="form-error--text"
              *ngIf="password.errors?.passwordCaps && password.touched && !password.errors?.required">
              Please include one Capital Letter in your password
            </small>
            <small class="form-error--text"
              *ngIf="registerForm.errors?.notEqual && confPass.touched && !confPass.errors?.required">
              Passwords do not match
            </small>
            <small class="form-error--text"
              *ngIf="password.errors?.required && password.touched || (password.pristine && registerForm.submitted)">
              Password is Required
            </small>
          </div>
          <div class="form-elem">
            <input type="password" name="conf_password"
              [class.is-inv--input]="confPass.invalid && confPass.touched || (confPass.pristine && registerForm.submitted)"
              placeholder="Confirm Password *" [(ngModel)]="conf_password" #confPass="ngModel" required #passwordInp
              appPasswordValidator />
            <div class="form-fill--bar"
              [class.is-inv]="confPass.invalid && confPass.touched || (confPass.pristine && registerForm.submitted)">
            </div>
            <img src="../../../assets/push-icons/eye.png" alt="Show Password" id="passwordShow" />
            <small class="form-error--text"
              *ngIf="registerForm.errors?.notEqual && confPass.touched && !confPass.errors?.required">
              Passwords do not match
            </small>
            <small class="form-error--text"
              *ngIf="confPass.errors?.required && confPass.touched || (confPass.pristine && registerForm.submitted)">
              You have to confirm your password
            </small>
          </div>
        </div>
        <small id="passwordHelpBlock" *ngIf="password.pristine">
          Password must be at leat 8 characters long, and must include 1 capital letter and 1 number.
        </small>
        <div class="form-row--2">
          <div class="form-elem elem-custom-cta">
            <input class="hidden-xs-up" name="subscriber" id="cbx" type="checkbox" checked
              [(ngModel)]="newUser.subscribed" #subscriber="ngModel" />
            <label class="cbx" for="cbx"></label><label class="lbl" for="cbx">Sign me up for email updates about <i>bank
                exams, government exams, company interview dates and much more</i></label>
          </div>
        </div>
        <!-- <div class="form-row--2">
          <div class="form-elem elem-custom-cta">
            <input class="hidden-xs-up" name="subscriber" id="cbx" type="checkbox" checked
              [(ngModel)]="newUser.subscribed" #subscriber="ngModel" />
            <label class="cbx" for="cbx"></label><label class="lbl" for="cbx">I accept the terms & conditions</label>
          </div>
        </div> -->
        <div class="form-row--1">
          <button type="submit" value="submit" class="custom-btn register-btn">
            Register
          </button>
          <!-- <button type=reset" value="clear" class="custom-btn clear-btn">Clear</button> -->
        </div>
        <small class="form-error--text form-error-last" *ngIf="registerForm.invalid && registerForm.submitted">
          Please fill all the details first.
        </small>
      </form>
    </div>
    <div class="right-sect">
      <div class="svg-wrap">
        <svg id="undraw_profile_6l1l" xmlns="http://www.w3.org/2000/svg" width="748" height="625.83"
          viewBox="0 0 748 625.83">
          <line id="Line_13" data-name="Line 13" x2="120.259" transform="translate(0 625.33)" fill="none"
            stroke="#3f3d56" stroke-miterlimit="10" stroke-width="1" />
          <rect id="Rectangle_688" data-name="Rectangle 688" width="533.922" height="420.963" rx="19.398"
            transform="translate(176.042 107.774)" fill="#f2f2f2" />
          <rect id="Rectangle_689" data-name="Rectangle 689" width="533.922" height="420.963" rx="19.398"
            transform="translate(159.742 81.2)" fill="none" stroke="#3f3d56" stroke-miterlimit="10" stroke-width="1" />
          <line id="Line_14" data-name="Line 14" x2="533.922" transform="translate(159.742 121.559)" fill="none"
            stroke="#3f3d56" stroke-miterlimit="10" stroke-width="1" />
          <rect id="Rectangle_690" data-name="Rectangle 690" width="145.615" height="26.152" rx="7.093"
            transform="translate(478.864 216.178)" fill="#0b6fb1" opacity="0.3" />
          <rect id="Rectangle_691" data-name="Rectangle 691" width="201.398" height="21.09" rx="7.093"
            transform="translate(450.972 288.729)" fill="#0b6fb1" opacity="0.3" />
          <rect id="Rectangle_692" data-name="Rectangle 692" width="201.398" height="21.09" rx="7.093"
            transform="translate(450.972 346.095)" fill="#0b6fb1" opacity="0.3" />
          <rect id="Rectangle_693" data-name="Rectangle 693" width="201.398" height="21.09" rx="7.093"
            transform="translate(450.972 403.46)" fill="#0b6fb1" opacity="0.3" />
          <rect id="Rectangle_694" data-name="Rectangle 694" width="145.615" height="26.152" rx="7.093"
            transform="translate(486.108 207.742)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <rect id="Rectangle_695" data-name="Rectangle 695" width="201.398" height="21.09" rx="7.093"
            transform="translate(458.217 280.293)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <rect id="Rectangle_696" data-name="Rectangle 696" width="201.398" height="21.09" rx="7.093"
            transform="translate(458.217 337.658)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <rect id="Rectangle_697" data-name="Rectangle 697" width="201.398" height="21.09" rx="7.093"
            transform="translate(458.217 395.024)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <ellipse id="Ellipse_98" data-name="Ellipse 98" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(189.445 93.854)" fill="#0b6fb1" opacity="0.3" />
          <ellipse id="Ellipse_99" data-name="Ellipse 99" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(219.872 93.854)" fill="#0b6fb1" opacity="0.3" />
          <ellipse id="Ellipse_100" data-name="Ellipse 100" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(250.299 93.854)" fill="#0b6fb1" opacity="0.3" />
          <ellipse id="Ellipse_101" data-name="Ellipse 101" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(193.791 88.793)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <ellipse id="Ellipse_102" data-name="Ellipse 102" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(224.218 88.793)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <ellipse id="Ellipse_103" data-name="Ellipse 103" cx="10.867" cy="12.654" rx="10.867" ry="12.654"
            transform="translate(254.645 88.793)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <path id="Path_2343" data-name="Path 2343"
            d="M527.289,637.842a146.111,146.111,0,0,1-1.69-32.361s-9.2-47.062-16.438-51.453l0,0c-.111.166-3.624,5.529-4.069,22.123-.452,16.858-5.578,27.921-5.578,27.921s1.659,21.012.71,33.768Z"
            transform="translate(-198.855 -153.395)" fill="#0b6fb1" />
          <path id="Path_2344" data-name="Path 2344"
            d="M560.176,298.379H393.222c-9.512,0-17.222,8.979-17.222,20.055V577.807c0,11.076,7.71,20.054,17.222,20.054H451.8c-1.956-9.448-4.388-24.2-1.925-34.464v-.007l0,0c.115-.479.237-.951.374-1.409a62.011,62.011,0,0,0,1.66-20.545,49.572,49.572,0,0,0,0-17.911c-1.809-9.833-3.076-19.492-3.076-19.492s-1-12.994-.127-20.959c.019-.175.042-.341.063-.511l-.48-.128s2.564-13.521,3.468-22.126c.214-2.031.495-4.606.8-7.352a13.456,13.456,0,0,1-2.458-.726c-8.143-3.336-10.707-33.54-10.707-33.54v-9.57s-2.714-14.663,6.786-21.16c9.148-6.257,12.8-7.969,13.059-8.087.07-.058.138-.111.209-.175a9.28,9.28,0,0,0,2.383-3.572q.019-.362.009-.719c-.154-3.431-2.205-6.551-4.151-9.273l-4.979-6.966a7.07,7.07,0,0,1-1.282-2.417,6.452,6.452,0,0,1-.135-1.522q0-.284,0-.568.041-2.6.121-5.193c.047-1.512.374-3.42,1.645-3.73.661-.162,1.537.12,1.886-.553a1.519,1.519,0,0,0,.085-.895c0,.022-.005.046-.011.068,0-.028-.008-.056-.013-.084a21.843,21.843,0,0,1-.471-3.7,5.684,5.684,0,0,1,.2-1.662c.737-2.51,3.273-3.54,5.514-4.013s4.757-.881,6.164-2.967a23.033,23.033,0,0,1,1.223-2,4.592,4.592,0,0,1,2.294-1.347,11.038,11.038,0,0,1,8.336.862c1.084.576,2.173,1.352,3.362,1.264,1.235-.091,2.29-1.1,3.51-1.35,1.97-.4,3.853,1.321,4.824,3.356a9.421,9.421,0,0,1,.893,4.238,6.418,6.418,0,0,1-1.278,4c-.848,1.047-2.113,1.767-2.465,3.146-.143.562-.11,1.166-.237,1.733-.016.071-.042.137-.062.206.023.035.049.068.072.1a20.324,20.324,0,0,1,2.426,16.176A16.889,16.889,0,0,1,479.4,374c-.006.178-.012.355-.011.534a8.44,8.44,0,0,0,2.057,5.96l13.437,6.417s8.089-1.142,11.008,12.915a28.2,28.2,0,0,1,.5,7.135,24.227,24.227,0,0,0,2.215,10.68c3.318,7.726,2.564,25.462-4.222,27.218-6.428,1.663-8.928.965-9.174.886l-.015.311c.106,3.328,1.123,32.031,4.816,35.5,1.021.96.826,1.609-.012,2.046l0,0s.007.184.023.516c.111,2.1.681,10.094,3.153,14.643,2.866,5.268,2.565,35.649,2.565,35.649L507.7,552.33s-1.206,11.063,0,12.994c.685,1.095,1.515,18.056,2.1,32.538h50.371c9.511,0,17.222-8.979,17.222-20.054V318.433c0-11.076-7.711-20.055-17.222-20.054Z"
            transform="translate(-164.822 -113.415)" fill="#0b6fb1" />
          <rect id="Rectangle_698" data-name="Rectangle 698" width="201.398" height="299.482" rx="23.772"
            transform="translate(193.791 162.187)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <path id="Path_2345" data-name="Path 2345"
            d="M182.068,400.576s-6.755-2.326-8.2.205,1.975,20.877,1.975,20.877l14.885,1.053-5.194-15.275Z"
            transform="translate(-109.025 -129.245)" fill="#2f2e41" />
          <path id="Path_2346" data-name="Path 2346"
            d="M109.122,579.347s13.543,34.624,3.113,33.536-14.477-33.536-14.477-33.536Z"
            transform="translate(-88.153 -157.355)" fill="#a0616a" />
          <path id="Path_2347" data-name="Path 2347"
            d="M235.038,574.477s-5.941,37.412,3.952,33.416,7.074-36.62,7.074-36.62Z"
            transform="translate(-125.493 -156.092)" fill="#a0616a" />
          <path id="Path_2348" data-name="Path 2348"
            d="M170.215,389.29s-.156,17.4,5.6,18.309-3.269,10.151-3.269,10.151l-10.274,2.357-14.789-1.269-6.538-9.789s11.52-6.345,6.538-26.1Z"
            transform="translate(-100.054 -126.64)" fill="#a0616a" />
          <path id="Path_2349" data-name="Path 2349"
            d="M170.215,389.29s-.156,17.4,5.6,18.309-3.269,10.151-3.269,10.151l-10.274,2.357-14.789-1.269-6.538-9.789s11.52-6.345,6.538-26.1Z"
            transform="translate(-100.054 -126.64)" opacity="0.1" />
          <path id="Path_2350" data-name="Path 2350"
            d="M192.455,564.944s7.317,18.309,1.557,75.774c0,0,1.712,20.3.311,24.11S181.4,720.3,181.4,720.3s-14.789,3.807-16.813-7.614c0,0,2.024-16.315,1.868-18.853s3.269-30.273,3.269-30.273L162.722,609.9l-5.293,48.4s0,13.958-1.712,17.04,0,65.078,0,65.078,4.826,9.426-2.491,9.608-13.855-.181-14.477-6.163-.934-11.964-2.491-15.59-3.58-45.138-3.113-59.64-10.274-89.007-6.85-96.8S192.455,564.944,192.455,564.944Z"
            transform="translate(-95.83 -155.097)" fill="#2f2e41" />
          <ellipse id="Ellipse_104" data-name="Ellipse 104" cx="18.681" cy="21.753" rx="18.681" ry="21.753"
            transform="translate(43.541 225.67)" fill="#a0616a" />
          <path id="Path_2351" data-name="Path 2351"
            d="M140.931,413.175s11.364,9.789,27.865,1.994c0,0,5.448-3.082,4.981-7.795s11.364,37.887,11.364,37.887L197.128,519.4l-.623,30.273s-5.293-3.082-21.794,3.807-43.432-7.795-43.432-7.795l.934-77.768.934-45.319,2.491-9.97S136.883,409.912,140.931,413.175Z"
            transform="translate(-97.39 -130.403)" fill="#f2f2f2" />
          <path id="Path_2352" data-name="Path 2352"
            d="M182.063,399.89s2.75-.52,5.4,4.374,8.873,7.976,8.873,7.976,15.1,1.088,17.28,10.514,7.628,22.3,10.274,46.044,7.939,61.634,8.718,64.716-1.09,12.508,1.09,15.409-15.411,2.9-15.411,2.9-1.557-14.865-2.646-14.865-.778,9.97.311,17.221-7.939,0-7.939,0S201,521.006,194.309,491.1s-10.585-46.406-9.34-53.114,4.67-25.923,2.179-27.735S182.063,399.89,182.063,399.89Z"
            transform="translate(-111.383 -129.287)" fill="#2f2e41" />
          <path id="Path_2353" data-name="Path 2353"
            d="M134.124,399.874s-4.067,2.578-14.341,11.1-17.28,15.046-17.28,15.046-4.047,3.625-6.538,25.2-8.1,77.405-5.916,84.475,2.958,11.239,2.179,15.409,16.345,3.988,16.657,3.082-.467-17.4.311-17.765,4.981,9.789,5.76,18.309,9.807,15.227,11.831,15.227,12.454-49.126,12.609-82.662-3.425-61.815-6.071-65.8S134.124,399.874,134.124,399.874Z"
            transform="translate(-85.893 -129.287)" fill="#2f2e41" />
          <path id="Path_2354" data-name="Path 2354"
            d="M183.6,747.258a4.31,4.31,0,0,1,5.6-2.719c4.359,1.269,7.317-3.263,7.317-3.263s2.335-.362.778,3.082-2.8,5.8-2.8,5.8,9.34,24.835,3.269,30.817a11.512,11.512,0,0,1-13.076,2.357s-6.85-5.62-6.538-20.484c0,0-.311-4.713-4.047-12.327s-6.538-19.215-2.024-21.391,5.676-2.256,5.676-2.256-2.113,11.251,3.344,15.183Z"
            transform="translate(-107.99 -180.426)" fill="#3f3d56" />
          <path id="Path_2355" data-name="Path 2355"
            d="M147.174,782.542s-3.736-8.157,2.491-9.064,12.921-1.45,12.609,2.357-1.09,9.608-1.09,9.608l1.09,4.169s.311,10.333,1.4,11.6,7.005,12.145-5.6,12.145-16.345-1.632-15.256-5.076,1.4-20.484,1.4-20.484Z"
            transform="translate(-100.519 -187.611)" fill="#3f3d56" />
          <path id="Path_2356" data-name="Path 2356"
            d="M152.491,348.745s8.718,2.719,13.232,3.807,4.047-1.994,4.047-1.994,5.916.906,5.6.181,2.491-4.532-.778-8.883-2.335-7.614-2.335-7.614h-2.8l-.778-1.269h-4.359s-3.269-1.45-11.831,0-20.237,1.813-21.171,7.795-5.76,13.414-4.047,16.859,11.52,17.584,12.454,23.566,5.3,7.311,5.138,3.565-4.516-16.8-.78-17.342S154.2,355.814,152.491,348.745Z"
            transform="translate(-96.185 -118.724)" fill="#2f2e41" />
          <path id="Path_2357" data-name="Path 2357"
            d="M1013.447,86.584c-6.3.414-11.855,4.938-14.351,11.685-3.589,10.1,1.032,21.967,7.968,29.333s15.846,11.576,23.942,17.11c10.874,7.433,20.645,17.752,26.085,31s6.019,29.653-.376,42.307c-5.936,11.745-16.73,18.775-27.024,25.258"
            transform="translate(-336.17 -80.293)" fill="none" stroke="#3f3d56" stroke-miterlimit="10"
            stroke-width="1" />
          <ellipse id="Ellipse_105" data-name="Ellipse 105" cx="12.5" cy="7" rx="12.5" ry="7" transform="translate(674)"
            fill="#0b6fb1" />
          <ellipse id="Ellipse_106" data-name="Ellipse 106" cx="12.5" cy="7.5" rx="12.5" ry="7.5"
            transform="translate(684 47)" fill="#0b6fb1" />
          <ellipse id="Ellipse_107" data-name="Ellipse 107" cx="12.5" cy="7" rx="12.5" ry="7"
            transform="translate(661 55)" fill="#0b6fb1" />
          <ellipse id="Ellipse_108" data-name="Ellipse 108" cx="12.5" cy="7.5" rx="12.5" ry="7.5"
            transform="translate(697 94)" fill="#0b6fb1" />
          <ellipse id="Ellipse_109" data-name="Ellipse 109" cx="13" cy="7.5" rx="13" ry="7.5"
            transform="translate(722 94)" fill="#0b6fb1" />
        </svg>
      </div>
    </div>
    <ng-template #successTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-success">Register Successful</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        Thank you for registering, we're all set for you to start taking your first test.
      </div>
    </ng-template>
    <ng-template #errorTemplate>
      <div class="modal-header">
        <h4 class="modal-title pull-left text-danger">Registration Failed!</h4>
        <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        {{ regError }}
      </div>
    </ng-template>
  </div>
</div>