// app/free-papers/page.tsx
import { Metadata } from 'next'
import { ServerOpenTestsService } from '@/lib/server-services/open-tests-service.server'
import FreePapersClient from '@/components/free-papers/free-papers-client'

export const metadata: Metadata = {
  title: 'Free Practice Papers | Quant Masters',
  description: 'Sample of all the papers we offer for practice'
}

export default async function FreePapersPage() {
  // Fetch papers on the server
  const papers = await ServerOpenTestsService.getTrialPapers()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-2xl font-bold text-center">
            A sample of all the papers we offer
          </h1>
        </div>
      </div>

      {/* Pass the fetched papers to the client component */}
      <FreePapersClient initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
