.home-wrapper {
    width: 100%;
    height: 100vh;
    font: 17px 'Montserrat', 'sans-serif';

    .center-news {
        cursor: pointer;
        width: 100%;
        display: grid;
        place-items: center;
        font-size: 1.3em;
        margin-top: 2em;
        transition: all 0.3s ease-out;

        strong {
            text-decoration: none;
            background: #e52d27;
            background: -webkit-linear-gradient(to right, #b31217, #e52d27);
            background: linear-gradient(to right, #b31217, #e52d27);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        &:hover {
            transform: scale(1.015);
            transition: all 0.2s ease;
        }
    }

    .news-tr {
        margin-top: 0.5em;
        
        strong {
            background: #8E2DE2;  /* fallback for old browsers */
            background: -webkit-linear-gradient(to right, #4A00E0, #8E2DE2);  /* Chrome 10-25, Safari 5.1-6 */
            background: linear-gradient(to right, #4A00E0, #8E2DE2); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, <PERSON> 12+, <PERSON>fari 7+ */
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .home-sect--1 {
        width: 100%;
        max-width: 1330px;
        margin: 5.19em auto;
        display: flex;
    }

    .sect-left {
        width: 650px;
        margin-left: 8em;
        z-index: 10;
    }
    
    .sect-right {
        width: 600px;
        height: 400px;

        svg {
            width: 100%;
            height: 100%;
        }
    }

    .sect-text--1 {
        font-size: 51px;
        font-weight: lighter;
        margin: 1.4em 0 0 0;
    }

    .sect-text--2 {
        width: 80%;
    }

    .home-btn {
        cursor: pointer;
        width: 220px;
        height: 60px;
        font-size: 20px;
        color: #ffffff;
        background-color: #E88224;
        border: none;
        border-radius: 1000px;
        transition: all 0.4s ease;

        &:hover {
            background-color: #0B6FB1;
            transition: all 0.3s ease;
        }
    }

    .neg-space {
        position: absolute;
        top: 80%;
        left: -65px;
    }

    .home-sect--2 {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 15em;

        .product-shape {
            position: absolute;
            height: 300px;
            width: 100px;
            left: 100px;
            top: 655px;
            z-index: 1;
        }

        .product-card {
            width: 350px;
            padding: 1.5em;
            background-color: #fff;
            border-radius: 15px;
            box-shadow: 0px 3px 20px rgba(17, 153, 158, 0.41);
            z-index: 2;

            svg {
                height: 60%;
                width: 100%;
            }

            p {
                text-align: center;
            }

            p:first-of-type {
                font-size: 41px;
                font-weight: lighter;
            }
        }
    }

    .home-sect--3 {
        width: 100%;
        max-width: 1160px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: auto;

        svg.neg-space--2 {
            position: absolute;
            left: -75px;
            top: 1500px;
            z-index: 1;
        }

        .sect-left {
            width: 500px;
        }

        .sect-right {
            width: 600px;

            svg {
                width: 100%;
                height: 100%;
            }
        }
    }

    .home-sect--4 {
        width: 100%;
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-top: 20em;

        svg.neg-space--2 {
            position: absolute;
            left: -75px;
            top: 1800px;
        }

        .sect-left {
            width: 600px;
            z-index: 2;

            svg {
                width: 100%;
                height: 100%;
            }
        }

        .sect-right {
            width: 500px;
            z-index: 2;
        }
    }

    .neg-space--2 {
        position: absolute;
        top: 3100px;
        left: 150px;
    }

    .home-sect--5 {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 30em;
        margin-bottom: 5em;

        h5 {
            font-size: 51px;
            font-weight: lighter;
        }

        .testimonials-carousel {
            margin-top: 4em;
            display: flex;

            .testimony-quotes {
                position: relative;
                top: -80px;
                left: 110px;
            }

            .testimony-card {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 600px;
                width: 350px;
                padding: 2em 3em 1em 3em;
                border-radius: 50px;
                background-color: #E4F9F5;
                box-shadow: 0px 3px 20px rgba(255, 222, 125, 0.16);

                h6 {
                    height: 70%;
                }

                p {
                    font-size: 36px;
                    font-weight: lighter;
                    text-align: right;
                }
            }
        }
    }
}

@media (max-width: 440px) {
    .home-wrapper {
        width: 100%;

        .center-news {
            width: 95%;
            margin: auto;
            margin-top: 2em;
            text-align: center;
        }

        .home-sect--1 {
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 0 2em;
        }

        .sect-left {
            margin: 0;
            width: 100%;
        }

        .sect-text--1 {
            text-align: justify;
            font-size: 30px;
            margin-bottom: 1em;
        }

        .sect-text--2 {
            font-size: 13px;
            text-align: justify;
            margin-right: 0;
            margin-bottom: 2em;
        }

        .sect-right {
            width: 100%;

            svg {
                width: 100%;
            }
        }

        .neg-space {
            height: 70px;
            width: 70px;
            top: 110%;
            left: -35px;
        }

        .home-sect--2 {
            flex-direction: column;
            margin: 0;

            .product-shape {
                top: 775px;
                left: 40px;
                
                svg {
                    width: 100%;
                    height: 100%;
                }
            }

            .product-card {
                width: 75%;
                margin-bottom: 2em;
            }

            & + svg {
                display: none;
            }
        }

        .home-sect--3,
        .home-sect--4 {
            flex-direction: column;
            padding: 0 2em;

            .sect-left,
            .sect-right {
                width: 100%;
            }

            .sect-left {
                margin-bottom: 5em;
            }
        }

        .neg-space--2 {
            display: none;
        }

        .home-sect--4 {
            flex-direction: column-reverse;
            margin: 2em 0;

            .sect-right {
                margin-bottom: 5em;
            }
        }

        .home-sect--5 {
            display: none;
            margin-top: 0;
            padding: 0 2em;

            h5 {
                font-size: 30px;
            }
        }
    }
}