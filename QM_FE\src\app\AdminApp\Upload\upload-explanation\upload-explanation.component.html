<div class="expl-wrapper">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="ops-wrap">
    <h5>Upload Explanation/Image</h5>
    <div class="form-wrap">
      <form>
        <div class="form-elem">
          <label for="paperType">Paper Type</label>
          <select name="paperType" [(ngModel)]="selectedType" #paperType (change)="paperTyepSelected()">
            <option value="-1" selected>Select Paper Type</option>
            <option value="0">Chapter Papers</option>
            <option value="1">Competitive Papers</option>
            <option value="2">Company Papers</option>
            <option value="3">Mock Papers</option>
            <option value="4">Chapter Wise Practice</option>
            <option value="5">Trial Papers</option>
            <option value="6">Section-Wise Papers</option>
          </select>
        </div>
        <div class="form-elem">
          <label for="subType">Select SubType</label>
          <select name="subType" [(ngModel)]="selectedSubType" #subType (change)="paperSubTypeSelected()">
            <option selected value="-1">Select Type</option>
            <option *ngFor="let subType of subTypes" value="{{subType.super_group_id}}">{{ subType.super_group_name }}
            </option>
          </select>
        </div>
        <div class="form-elem">
          <label for="subSubType">Select Chapter</label>
          <select name="subSubType" [(ngModel)]="selectedChapter" #subSubType (change)="paperChapterSelected()">
            <option selected value="-1">Select Chapter</option>
            <option *ngFor="let subType of subSubTypes" value="{{subType.group_id}}">{{ subType.group_name }}</option>
          </select>
        </div>
        <div class="form-elem">
          <label for="paper">Select Paper</label>
          <select name="paper" [(ngModel)]="selectedPaper" #paper (change)="paperSelected()"
            *ngIf="!chapterPapers && !modelPapers">
            <option selected value="-1">Select Paper</option>
          </select>
          <select name="paper" [(ngModel)]="selectedPaper" #paper (change)="paperSelected()" *ngIf="chapterPapers">
            <option selected value="-1">Select Paper</option>
            <option *ngFor="let paper of chapterPapers" value="{{paper.paper_id}}">{{ paper.paper_name }}</option>
          </select>
          <select name="paper" [(ngModel)]="selectedPaper" #paper (change)="paperSelected()" *ngIf="modelPapers">
            <option selected value="-1">Select Paper</option>
            <option *ngFor="let paper of modelPapers" value="{{paper.paper_id}}">{{ paper.paper_name }}</option>
          </select>
        </div>
        <div class="form-elem">
          <label for="paper">Select Question</label>
          <select name="paper" [(ngModel)]="selectedQuestion" #paper (change)="questionSelected(true)"
            *ngIf="!chapterQuestions && !modelQuestions">
            <option selected value="-1">Select Question</option>
          </select>
          <select name="paper" [(ngModel)]="selectedQuestion" #paper (change)="questionSelected(true)"
            *ngIf="chapterQuestions">
            <option selected value="-1">Select Question</option>
            <option *ngFor="let question of chapterQuestions" value="{{question.ques_no}}">{{ question.ques_no }}
            </option>
          </select>
          <select name="paper" [(ngModel)]="selectedQuestion" #paper (change)="questionSelected(true)"
            *ngIf="modelQuestions">
            <option selected value="-1">Select Question</option>
            <option *ngFor="let question of modelQuestions" value="{{question.question_no}}">{{ question.question_no }}
            </option>
          </select>
        </div>
        <div class="form-elem" *ngIf="selectedQuestion != '-1'">
          <label for="uploadfor">Upload For</label>
          <select name="uploadFor" [(ngModel)]="slectedUploadType" #uploadFor
            (change)="onSelectUploadFor(slectedUploadType)">
            <option selected value="-1">Select Option</option>
            <option value="forAnswer">For Answer</option>
            <option value="forQuestion">For Question</option>
          </select>
        </div>
        <div class="img-updl--toggle p-0" *ngIf="slectedUploadType == 'forAnswer'">
          <label class="font-weight-bold">Image Upload</label>
          <span class="switcher switcher-2 m-auto">
            <input type="checkbox" id="switcher-2" [checked]="imageUpdl" (change)="imageUpdl = !imageUpdl">
            <label for="switcher-2"></label>
          </span>
        </div>
      </form>

    </div>
  </div>
  <div class="data-wrap">
    <div class="explBox">
      <div class="ipopBox" *ngIf="imageUpdl === false && slectedUploadType == 'forAnswer'">
        <textarea [(ngModel)]="currentExplanation"></textarea>
        <div>
          <div [appMathjaxBind]="currentExplanation" #math></div>
        </div>
      </div>
      <div class="img-box" *ngIf="imageUpdl === true">
        <p>Upload Image</p>
        <form (ngSubmit)="userAvatarSelect.click()" enctype="multipart/form-data">
          <input style="display: none" type="file" (change)="fileSelect($event)" #userAvatarSelect />
          <button type="submit" title="Select Image">
            <img src="../../../assets/push-icons/edit.svg" />
          </button>
        </form>
      </div>
      <button class="custom-btn" (click)="updateExplanation()" *ngIf="slectedUploadType == 'forAnswer'">Update
        </button>
    </div>
    <div *ngIf="slectedUploadType == 'forQuestion'">
      <b>Question</b>
      <markdown [data]="markdownText"></markdown>
    </div>
  </div>
  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-success">Success!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Explanation Updated!</p>
    </div>
  </ng-template>
  <ng-template #imgUpdlTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Upload Image</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Only .png, jpg and .jpeg images are supported</p>
      <button class="btn btn-primary mr-4" (click)="uploadFile()">Upload</button>
      <button class="btn btn-warning" (click)="cancelUpload()">Cancel</button>
    </div>
  </ng-template>
</div>
