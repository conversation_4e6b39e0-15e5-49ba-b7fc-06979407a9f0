import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { style, transition, trigger, query, animate, stagger, state } from '@angular/animations';

import { NotesService } from '../../Services/Blog/notes.service';

import { NotesGroup } from '../models/NotesGroup';
import { Notes } from '../models/Notes';

declare let fbq: Function;
declare let gtag: Function;

@Component({
  selector: 'app-notes-home',
  templateUrl: './notes-home.component.html',
  styleUrls: ['./notes-home.component.scss'],
  animations: [
    trigger('greetingMovements', [
      state('hidden', style({ transform: 'translateY(30px)', opacity: 0 })),
      state('notHidden', style({ transform: 'translateY(0px)', opacity: 1 })),
      transition('hidden => notHidden', [animate('0.5s ease-in')]),
    ]),
    trigger('superGrpMvmts', [
      transition('* => *', [
        query(':enter', [
          style({ transform: 'translateY(35px)', opacity: 0 }),
          stagger(100, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(0px)', opacity: 1 }))
          ])
        ], { optional: true }),
        query(':leave', [
          stagger(100, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(-35px)', opacity: 0 }))
          ])
        ], { optional: true })
      ])
    ]),
    trigger('scootSuperGroups', [
      state('up', style({ transform: 'translateY(-15px) scale(0.9)', opacity: 0.3 })),
      state('normal', style({ transform: 'translateY(0px) scale(1)', opacity: 1 })),
      transition('normal => up', animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)'))
    ]),
    trigger('slectGroupMvmts', [
      transition('* => *', [
        query(':enter', [
          style({ transform: 'translateY(50px)', opacity: 0 }),
          stagger(120, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(0px)', opacity: 1 }))
          ]),
          query('.group-1', animate('0.5s ease-in-out', style({ transform: 'translateY(-25px)', opacity: 0.3 })), { optional: true }),
        ], { optional: true })
      ]),
    ]),
    trigger('scootGroups', [
      state('up', style({ transform: 'translateY(-15px) scale(0.9)', opacity: 0.3 })),
      state('normal', style({ transform: 'translateY(0px) scale(1)', opacity: 1 })),
      transition('normal => up', animate('0.5s cubic-bezier(0.25, 0, 0.25, 1)'))
    ]),
    trigger('selectNotesMvmts', [
      transition('* => *', [
        query(':enter', [
          style({ transform: 'translateY(150px)', opacity: 0 }),
          stagger(120, [
            animate('0.6s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(0px)', opacity: 1 }))
          ])
        ], { optional: true }),
        query(':leave', [
          stagger(120, [
            animate('0.5s cubic-bezier(0.35, 0, 0.25, 1)', style({ transform: 'translateY(50px)', opacity: 0 }))
          ])
        ], { optional: true })
      ]),
    ])
  ]
})
export class NotesHomeComponent implements OnInit {

  public superGroups: [{ super_id: string, super_name: string, super_seq: number }];
  public displaySupGroups: [{ super_id: string, super_name: string, super_seq: number }];
  public groups: NotesGroup[];
  public displayGroups: NotesGroup[];
  public notesOfGroup: Notes[];

  public showIndicator: boolean;

  public appReady = 'hidden';
  public groupSelected = '';
  public scootSuperGroups = 'normal';
  public scootGroups = 'normal';

  public selectedSupGroup = '';
  public selectedGroup = '';

  constructor(private notesService: NotesService,
    private router: Router,
    private activatedRoute: ActivatedRoute) {

    this.router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });

        fbq('track', 'PageView', {
          value: 0.1,
          currency: 'INR',
        });
      }
    });
  }

  ngOnInit() {
    this.showIndicator = true;
    this.notesService.getSuperGroups().subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      for (const obj of respObj) {
        if (!this.superGroups) {
          this.superGroups = [{ super_id: '', super_name: '', super_seq: 0 }];
          this.superGroups.pop();

          const pushObj = {
            super_id: obj.super_id,
            super_name: obj.super_name,
            super_seq: obj.super_seq
          };

          this.superGroups.push(pushObj);
        } else if (!this.superGroups.find(x => x.super_id === obj.super_id)) {
          this.superGroups.push(obj);
        }
      }

      this.superGroups.sort((a, b) => {
        if (a.super_seq < b.super_seq) {
          return 0;
        } else if (a.super_seq > b.super_seq) {
          return 1;
        } else {
          return -1;
        }
      });

      this.displaySupGroups = this.superGroups;
      this.groups = respObj;

      if (localStorage.getItem('QNotesData') !== null) {
        const notesData = JSON.parse(localStorage.getItem('QNotesData'));

        this.onSelectSuperGroup(notesData.selectedSupGroup);
        this.onSelectGroup(notesData.selectedGroup);

        localStorage.removeItem('QNotesData');
      }

      this.appReady = 'notHidden';
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  onSelectSuperGroup(supId: string) {
    this.scootSuperGroups = this.scootSuperGroups === 'up' ? 'normal' : 'up';
    this.displayGroups = this.groups.filter(x => x.super_id === supId);

    this.displayGroups.sort((a, b) => {
      if (a.group_seq < b.group_seq) {
        return 0;
      } else if (a.group_seq > b.group_seq) {
        return 1;
      } else {
        return -1;
      }
    });

    this.selectedSupGroup = supId;

    this.scootGroups = 'normal';
    this.notesOfGroup = [] as unknown as Notes[];
  }

  focusSuperGroups() {
    this.scootSuperGroups = this.scootSuperGroups === 'up' ? 'normal' : 'up';
  }

  onSelectGroup(grpId: string) {
    this.scootGroups = this.scootGroups === 'up' ? 'normal' : 'up';
    this.scootSuperGroups = 'up';

    this.selectedGroup = grpId;

    this.showIndicator = true;
    this.notesService.getNotesOfaGroup(grpId).subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      this.notesOfGroup = respObj;
      for (const obj of this.notesOfGroup) {
        obj.posted_on = new Date(obj.posted_on).toLocaleDateString();
      }

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  takeToNotesDetail(noteId: string, idx: number) {

    const currNote = this.notesOfGroup[idx].notes_name.replace(/ /g, '-');
    const nextNote = idx !== this.notesOfGroup.length - 1 ? this.notesOfGroup[idx + 1] : new Notes();
    const prevNote = idx !== 0 ? this.notesOfGroup[idx - 1] : new Notes();

    const notedData = {
      thisNoteId: this.notesOfGroup[idx].notes_id,
      selectedSupGroup: this.selectedSupGroup,
      selectedGroup: this.selectedGroup,
      nextNoteId: nextNote.notes_id || '',
      nextNoteName: nextNote.notes_name || '',
      prevNoteId: prevNote.notes_id || '',
      prevNoteName: prevNote.notes_name || ''
    };

    localStorage.setItem('QNotesData', JSON.stringify(notedData));

    this.router.navigate(['read', currNote], { relativeTo: this.activatedRoute });
  }

  takeToPlans() {
    this.router.navigate(['/placement/training/live']);
  }
}
