<div class="left-nav" [@openClose]="isNavOpen ? 'open' : 'closed'">
  <div class="mob-ham" (click)="toggleNav()">
    <span></span>
  </div>
  <div class="logo" (click)="gotoHome()">
    <svg xmlns="http://www.w3.org/2000/svg" width="33.538" height="23.353" viewBox="0 0 33.538 23.353">
      <g id="QM_Logo_No_Text" data-name="QM_Logo No Text" transform="translate(190.6 -199.1)">
        <path id="Path_2377" data-name="Path 2377"
          d="M-115.591,289.962a4.06,4.06,0,0,0,.611,1.1c-.4-.106-1.129-.152-2.019-.265a14.676,14.676,0,0,1-2.392-.5l-.186-.06c-.145-.046-.29-.1-.425-.152a7.354,7.354,0,0,1-2.837-1.808c-.839-.907-.963-1.728-.88-1.894.031-.06.228-.642.4-.987.135-.278.342-.569.476-.854.041-.086.072-.172.1-.252a3.605,3.605,0,0,0,.145-1.145c.166-2.145-2.651-3.781-6.016-3.787-3.4-.007-6.151,1.748-6.161,3.913a2.654,2.654,0,0,0,.031.4,2.383,2.383,0,0,0,.083.371c.507,2.695,4.08,4.721,8.325,4.721.259,0,.507-.007.766-.02a5.265,5.265,0,0,0,4.08,2.523l.062.013a20.726,20.726,0,0,1-7.269,1.271h-.072c-.1,0-.217,0-.321-.007-7.59-.132-13.7-4.032-13.906-8.886,0-.073-.01-.139-.01-.212v-.265c0-.06.01-.113.01-.166a.148.148,0,0,1,.01-.066c.01-.152.031-.3.052-.45.714-4.529,6.668-8.124,13.9-8.237,7.9-.119,14.455,3.873,14.641,8.925v.219a7.256,7.256,0,0,1-.632,2.662C-115.519,287.373-116.068,288.611-115.591,289.962Z"
          transform="translate(-42.671 -70.184)" fill="#fff" />
        <path id="Path_2378" data-name="Path 2378"
          d="M-123.594,290.942a8.989,8.989,0,0,0,1.129.265,20.748,20.748,0,0,1-7.279,1.291h-.4c-7.673-.113-13.865-4.065-14.041-8.972,0-.073-.01-.146-.01-.212v-.033c0-.139.01-.278.021-.417a.148.148,0,0,1,.01-.066c.01-.152.031-.3.052-.457.828-5.052,7.911-8.713,15.8-8.177a20.691,20.691,0,0,1,5.447,1.092c-6.71-1.357-13.875,1.02-16,5.317s1.595,8.872,8.315,10.23a19.808,19.808,0,0,0,6.958.139Z"
          transform="translate(-41.595 -70.046)" fill="#fff" />
        <path id="Path_2379" data-name="Path 2379"
          d="M-148.648,246.588l1.294,1.384a.613.613,0,0,0,.414.159,3.032,3.032,0,0,0,.425-.013c.238-.033,4.339-1.265,4.9-1.417.507-.132,6.265-2.377,5.944-2.225-.456.185,1.253-.556,1.522-.722.818-.49,2.123-1.324,2.164-1.364s.311-.278.176-.4c-.093-.086-.849-.768-1.253-1.139a.683.683,0,0,0-.663-.093l-14.745,5.376c-.238.086-.321.285-.186.444C-148.648,246.581-148.648,246.581-148.648,246.588Z"
          transform="translate(-37.541 -38.86)" fill="#fff" />
        <path id="Path_2380" data-name="Path 2380" d="M-178.941,201.947-190.6,208.3l14.786-3.635,9.837-5.562Z"
          fill="#fff" />
        <path id="Path_2381" data-name="Path 2381" d="M-172.8,329.605v.867h.942V329.4Z"
          transform="translate(-15.957 -121.673)" fill="#fff" />
        <path id="Path_2382" data-name="Path 2382"
          d="M-165.8,199.1h-.01c-.01,0-9.04,4.906-9.826,5.337a.307.307,0,0,1-.114.04l-14.652,3.807.114.132s14.714-3.582,14.786-3.628l9.7-5.5"
          transform="translate(-0.179)" fill="#fff" />
        <path id="Path_2383" data-name="Path 2383"
          d="M-177.44,350.628s-.041-.477-.207-.589a5.756,5.756,0,0,1-.9-.616.389.389,0,0,1,.124-.391c.228-.245.052-3.277.114-3.364a.881.881,0,0,1,.3-.172h1.139a.142.142,0,0,1,.155.172c-.021.185,0,3.191,0,3.191s.124.166.2.252c.124.139.083.311-.1.417a2.084,2.084,0,0,0-.632.523C-177.347,350.225-177.43,350.794-177.44,350.628Z"
          transform="translate(-10.789 -136.704)" fill="#fff" />
        <path id="Path_2384" data-name="Path 2384"
          d="M-82.926,247.86c-.155.02-.311-.04-.342-.139a.125.125,0,0,1,.062-.152,3.879,3.879,0,0,1,.6-.4,3.1,3.1,0,0,1,1.056-.437c.383-.046.445-.007.466.093s-.176.291-.321.377a14.555,14.555,0,0,1-1.522.662Z"
          transform="translate(-96.212 -44.457)" fill="#fff" />
        <path id="Path_2385" data-name="Path 2385"
          d="M-43.636,336.586c0,2.1-1.905,4-4.887,4.873-.839-.907-.963-1.728-.88-1.894.031-.06.228-.642.4-.987.135-.278.342-.569.476-.854.041-.086.072-.172.1-.252a3.6,3.6,0,0,0,.145-1.145c.166-2.145-2.651-3.781-6.016-3.787-2.6-.007-4.929,1.039-5.809,2.609,1.232-2.854,5.861-4.529,10.324-3.734C-46.152,332.057-43.636,334.176-43.636,336.586Z"
          transform="translate(-116.987 -123.37)" fill="#fff" />
        <rect id="Rectangle_703" data-name="Rectangle 703" width="1.595" height="0.166"
          transform="translate(-189.099 211.495)" fill="#fff" />
        <rect id="Rectangle_704" data-name="Rectangle 704" width="1.595" height="0.166"
          transform="translate(-189.099 211.236)" fill="#fff" />
      </g>
    </svg>
    <h5>uantMasters</h5>
  </div>
  <div class="user-profile">
    <div class="profile-avatar"><img src="{{ userImage }}" /></div>
    <p>{{ userName }}</p>
  </div>
  <div class="navs">
    <a class="nav-page" (click)="toggleNav()" routerLink="student-exp" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Company Interviews
    </a>
    <a class="nav-page" (click)="toggleNav()" routerLink="student-testimonials" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Testimonials
    </a>
    <!-- <a class="nav-page" (click)="toggleNav()" routerLink="trial-videos" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Trial Videos
    </a> -->
    <a routerLink="trial-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Trial Papers
    </a>
    <!-- <a class="nav-page" (click)="toggleNav()" routerLink="videos" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Videos
    </a> -->
    <a class="nav-page" (click)="toggleNav()" routerLink="chapter-practice-papers"
      [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Chapter-wise Practice
    </a>
    <a href="https://tech-notes.quantmasters.in/" (click)="toggleNav()" class="nav-page"
      [routerLinkActive]="'nav-link--active'">
      <svg version="1.1" id="Uploaded to svgrepo.com" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="32px" height="32px" viewBox="0 0 32 32"
        style="enable-background:new 0 0 32 32;" xml:space="preserve">
        <style type="text/css">
          .sharpcorners_een {
            fill: #fff;
          }

          .st0 {
            fill: #fff;
          }
        </style>
        <path class="sharpcorners_een" d="M4,23h24V5H4V23z M19,11h5v1h-5V11z M19,13h5v1h-5V13z M19,15h5v1h-5V15z M8,11h5v1H8V11z M8,13h5
	v1H8V13z M8,15h5v1H8V15z M31,6v20H18v1h-4v-1H1V6h2v18h26V6H31z" />
      </svg>
      Technical Notes
      <span>★</span>
    </a>
    <a routerLink="technical-mcq-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Technical MCQs
    </a>
    <a href="https://verbal-notes.quantmasters.in/" (click)="toggleNav()" class="nav-page"
      [routerLinkActive]="'nav-link--active'">
      <svg version="1.1" id="Uploaded to svgrepo.com" xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="32px" height="32px" viewBox="0 0 32 32"
        style="enable-background:new 0 0 32 32;" xml:space="preserve">
        <style type="text/css">
          .sharpcorners_een {
            fill: #fff;
          }

          .st0 {
            fill: #fff;
          }
        </style>
        <path class="sharpcorners_een" d="M4,23h24V5H4V23z M19,11h5v1h-5V11z M19,13h5v1h-5V13z M19,15h5v1h-5V15z M8,11h5v1H8V11z M8,13h5
	v1H8V13z M8,15h5v1H8V15z M31,6v20H18v1h-4v-1H1V6h2v18h26V6H31z" />
      </svg>
      Verbal Notes
      <span>★</span>
    </a>
    <a routerLink="model-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Mock Papers
    </a>
    <a routerLink="chapter-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Chapter-Wise Assessment
    </a>
    <a routerLink="competitive-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Competitive
    </a>
    <a routerLink="open-competitive-papers" (click)="toggleNav()" class="nav-page"
      [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Weekly Competitive
    </a>
    <a routerLink="section-wise-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Section Wise Papers
    </a>
    <a routerLink="afcat-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      AFCAT Papers
    </a>
    <a routerLink="company-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24"
        viewBox="0 0 26.779 24">
        <defs>
          <style>
            .a {
              fill: #fff;
            }

            .b {
              filter: url(#a);
            }
          </style>
          <filter id="a" x="0" y="0" filterUnits="userSpaceOnUse">
            <feOffset dy="6" input="SourceAlpha" />
            <feGaussianBlur stdDeviation="10" result="b" />
            <feFlood flood-opacity="0.161" />
            <feComposite operator="in" in2="b" />
            <feComposite in="SourceGraphic" />
          </filter>
        </defs>
        <path class="a"
          d="M22.75,3H17.333c0-1.655-1.943-3-4.333-3S8.667,1.345,8.667,3H3.25C1.456,3,0,4.008,0,5.25v16.5C0,22.992,1.456,24,3.25,24h19.5C24.544,24,26,22.992,26,21.75V5.25C26,4.008,24.544,3,22.75,3ZM6.5,19.875c-.9,0-1.625-.5-1.625-1.125S5.6,17.625,6.5,17.625s1.625.5,1.625,1.125S7.4,19.875,6.5,19.875Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,13.125,6.5,13.125s1.625.5,1.625,1.125S7.4,15.375,6.5,15.375Zm0-4.5c-.9,0-1.625-.5-1.625-1.125S5.6,8.625,6.5,8.625s1.625.5,1.625,1.125S7.4,10.875,6.5,10.875Zm6.5-9c.9,0,1.625.5,1.625,1.125S13.9,4.125,13,4.125,11.375,3.623,11.375,3,12.1,1.875,13,1.875Zm8.667,17.25c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Zm0-4.5c0,.206-.244.375-.542.375h-9.75c-.3,0-.542-.169-.542-.375v-.75c0-.206.244-.375.542-.375h9.75c.3,0,.542.169.542.375Z"
          transform="translate(-2 -2)" />
      </svg>
      Company Papers
    </a>
    <!-- <a class="nav-page" (click)="toggleNav()" routerLink="workshops" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Workshop Videos
    </a> -->
    <!-- <a routerLink="company-papers" (click)="toggleNav()" class="nav-page" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="26.779" height="24" viewBox="0 0 110 72.505">
        <defs>
          <filter id="Icon_awesome-handshake" x="0" y="0" width="110" height="72.505" filterUnits="userSpaceOnUse">
            <feOffset dy="3" input="SourceAlpha"/>
            <feGaussianBlur stdDeviation="3" result="blur"/>
            <feFlood flood-opacity="0.161"/>
            <feComposite operator="in" in2="blur"/>
            <feComposite in="SourceGraphic"/>
          </filter>
        </defs>
        <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Icon_awesome-handshake)">
          <path id="Icon_awesome-handshake-2" data-name="Icon awesome-handshake" d="M62.488,4.5H50.14a4.632,4.632,0,0,0-3.1,1.192L32.9,18.462c-.014.014-.029.043-.043.057a5.593,5.593,0,0,0-.3,7.946,6.053,6.053,0,0,0,8.064.383c.014-.014.043-.014.057-.028L52.167,16.433a2.331,2.331,0,0,1,3.249.142,2.247,2.247,0,0,1-.144,3.207L51.52,23.173,72.45,39.944a10.3,10.3,0,0,1,1.136,1.093V13.581L65.737,5.834A4.6,4.6,0,0,0,62.488,4.5ZM78.2,13.609V45.378a4.566,4.566,0,0,0,4.6,4.54H92V13.609Zm6.9,31.769a2.27,2.27,0,1,1,2.3-2.27A2.292,2.292,0,0,1,85.1,45.378ZM0,49.9H9.2a4.566,4.566,0,0,0,4.6-4.54V13.609H0Zm6.9-9.067a2.27,2.27,0,1,1-2.3,2.27A2.292,2.292,0,0,1,6.9,40.838Zm62.661,2.639L48.1,26.28l-4.312,3.9a10.46,10.46,0,0,1-14.619-.624,10.128,10.128,0,0,1,.632-14.43L41.558,4.5H29.512a4.622,4.622,0,0,0-3.249,1.334L18.4,13.581V45.35h2.631L34.04,56.97a9.281,9.281,0,0,0,12.938-1.32l.029-.028,2.573,2.2a5.39,5.39,0,0,0,7.518-.766l4.514-5.477.776.624a4.634,4.634,0,0,0,6.469-.667l1.366-1.66a4.509,4.509,0,0,0-.661-6.4Z" transform="translate(9 1.5)" fill="#fff"/>
        </g>
      </svg>
      Company Specific
    </a> -->
    <!-- <a class="nav-page" (click)="toggleNav()" routerLink="workshops" [routerLinkActive]="'nav-link--active'">
      <svg xmlns="http://www.w3.org/2000/svg" width="26.779" height="24" viewBox="0 0 26.779 24">
        <path id="ic_video_library_24px"
          d="M4.678,6.8H2V23.6A2.558,2.558,0,0,0,4.678,26H23.423V23.6H4.678ZM26.1,2H10.034A2.558,2.558,0,0,0,7.356,4.4V18.8a2.558,2.558,0,0,0,2.678,2.4H26.1a2.558,2.558,0,0,0,2.678-2.4V4.4A2.558,2.558,0,0,0,26.1,2ZM15.39,17V6.2l8.034,5.4Z"
          transform="translate(-2 -2)" fill="#fff" />
      </svg>
      Workshops
    </a> -->
  </div>
</div>