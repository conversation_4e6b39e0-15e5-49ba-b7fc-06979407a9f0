import { Metadata } from 'next'
import { StudentVideoListType } from '@/types/video-types'
import StudentReviewClient from '@/components/student-exp/student-review-client'
import { ServerStreamingService } from '@/lib/server-services/streaming-service.server'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Student Reviews - Quant Masters',
  description: 'Key insights into the interview processes of major IT companies'
}

export default async function StudentReviewPage() {
  let videoList: StudentVideoListType[] = []
  let error: string | undefined = undefined

  try {
    // Fetch data server-side
    videoList = await ServerStreamingService.getTestimonyVideoList()

    // Process thumbnails URLs
    videoList = videoList.map((video) => ({
      ...video,
      thumbnail: `https://quantmasters.in/${video.thumbnail}`
    }))
  } catch (err) {
    console.error('Error fetching video list:', err)
    error = 'Failed to load videos'
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8 pb-4 border-b border-gray-200">
        <div className="text-3xl text-gray-700 font-semibold">
          <h1>See what our students have to say about us and our services</h1>
        </div>
      </div>
      <StudentReviewClient initialVideoList={videoList} error={error} />
    </div>
  )
}
