// lib/utils/question-utils.ts
import { Question, PaperAdditionData } from '@/types/test-types'

/**
 * Sanitize superscripts and subscripts in text
 */
export function sanitizeSupSubScripts(val: string): string {
  if (typeof val !== 'undefined' && val !== null) {
    if (val.match(/\^/g)) {
      val = val.split('^').join('<sup>')
      val = val.replace(
        /(<sup>(?:(?!<sup>).)*)<sup>((?:(?!<sup>).)*)/g,
        '$1</sup>$2'
      )
    }

    if (val.match(/~/g)) {
      val = val.split('~').join('<sub>')
      val = val.replace(
        /(<sub>(?:(?!<sub>).)*)<sub>((?:(?!<sub>).)*)/g,
        '$1</sub>$2'
      )
    }
  }

  return val
}

/**
 * Sanitize images in text
 */
export function sanitizeImages(val: string): string {
  if (typeof val !== 'undefined' && val !== null) {
    if (val.match(/.*@\dpx|\dpx/g)) {
      const split = val.split('![')[1].split('](')[1].split('@')
      const url = split[0]
      const height = split[1].split('|')[0]
      const width = split[1].split('|')[1].split(')')[0]

      val =
        val.split('![')[0] +
        '\r\r<img src="' +
        url +
        '" height="' +
        height +
        '" width="' +
        width +
        '" />'
    }
  }

  return val
}

/**
 * Prepare questions by sanitizing and setting initial values
 */
export function prepareQuestions(questions: Question[]): Question[] {
  return questions.map((q) => {
    q.bAnswered = false

    q.question = sanitizeSupSubScripts(q.question)
    q.question = sanitizeImages(q.question)

    q.opt_1 = sanitizeSupSubScripts(q.opt_1)
    q.opt_2 = sanitizeSupSubScripts(q.opt_2)
    q.opt_3 = sanitizeSupSubScripts(q.opt_3)
    q.opt_4 = sanitizeSupSubScripts(q.opt_4)
    q.opt_5 = sanitizeSupSubScripts(q.opt_5)

    q.options = [q.opt_1, q.opt_2, q.opt_3, q.opt_4, q.opt_5].filter(
      (opt) => opt
    )
    q.selected_ans = -1

    return q
  })
}

/**
 * Shuffle questions randomly
 */
export function shuffleQuestions(
  questions: Question[],
  bAll: boolean,
  startIdx: number | null,
  endIdx: number | null
): Question[] {
  const questionsCopy = [...questions]
  let currIdx = bAll ? questionsCopy.length : endIdx || questionsCopy.length

  while ((bAll ? 0 : startIdx || 0) !== currIdx) {
    let randomIdx = Math.floor(Math.random() * currIdx)

    while (!bAll && randomIdx < (startIdx || 0) - 1) {
      randomIdx = Math.floor(Math.random() * currIdx)
    }

    currIdx -= 1

    const tempQues = questionsCopy[currIdx]
    questionsCopy[currIdx] = questionsCopy[randomIdx]
    questionsCopy[randomIdx] = tempQues

    questionsCopy[currIdx].question_no = currIdx.toString()
    questionsCopy[randomIdx].question_no = randomIdx.toString()
  }

  const question = questionsCopy.find((x) => x.question_no === '0')
  if (question) {
    question.question_no = questionsCopy.length.toString()
  }

  return questionsCopy.sort((a, b) => {
    const aNo = parseInt(a.question_no || '0', 10)
    const bNo = parseInt(b.question_no || '0', 10)

    if (aNo < bNo) return -1
    else if (aNo > bNo) return 1
    else return 0
  })
}

/**
 * Calculate section-wise marks from questions and addition data
 */
export function calculateSectionMarks(
  questions: Question[],
  additionData: PaperAdditionData[] | undefined,
  paperId: string,
  negMarks: boolean
): {
  sectionMarks: Record<string, number>
  sectionTrack: { section_name: string; marks: number }[]
} {
  const sectionMarks: Record<string, number> = {}
  const sectionTrack: { section_name: string; marks: number }[] = []

  if (!additionData || additionData.length === 0) {
    return { sectionMarks, sectionTrack }
  }

  // Calculate section-wise marks
  for (const question of questions) {
    const quesNo = question.question_no || question.ques_no
    const reqAdditionData = additionData.find(
      (x) => x.type === 0 && x.question_no === parseInt(quesNo, 10)
    )

    if (reqAdditionData) {
      const key = `${paperId}:${reqAdditionData.question_no}`
      let currentMarks = sectionMarks[key] || 0

      if (question.selected_ans === parseInt(question.correct_opt, 10)) {
        currentMarks += negMarks ? 2 : 1
      } else if (question.selected_ans !== -1 && negMarks) {
        currentMarks -= 1
      }

      sectionMarks[key] = currentMarks
    }
  }

  // Create section track for display
  if (Object.keys(sectionMarks).length > 0) {
    Object.keys(sectionMarks).forEach((key) => {
      const questionNo = key.split(':')[1]
      const addData = additionData.find(
        (y) => y.question_no === parseInt(questionNo, 10) && y.type === 0
      )

      if (addData) {
        sectionTrack.push({
          section_name: addData.value.replace(/[#]+/g, ''),
          marks: sectionMarks[key]
        })
      }
    })
  }

  return { sectionMarks, sectionTrack }
}
