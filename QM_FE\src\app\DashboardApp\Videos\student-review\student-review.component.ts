import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { StreamingService } from 'src/app/Services/streaming.service';
import { DomSanitizer } from '@angular/platform-browser';


import { StudentVideoList } from '../../../Models/Streaming/StudentVideoList';

declare let gtag: Function;

@Component({
  selector: 'app-student-review',
  templateUrl: './student-review.component.html',
  styleUrls: ['./student-review.component.scss']
})
export class StudentReviewComponent implements OnInit {

  public showIndicator = false;

  public videoList: StudentVideoList[];

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public streamingService: StreamingService,
              public sanitizer: DomSanitizer) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.streamingService.getReviewVideoList().subscribe(response => {
      that.videoList = response;

      for (const video of this.videoList) {
        video.thumbnail = 'https://quantmasters.in/' + video.thumbnail;
      }

      that.showIndicator = false;
    }, error => {

    });
  }

  getYoutubeUrl(link: string) {
    return this.sanitizer.bypassSecurityTrustResourceUrl('https://www.youtube.com/embed/' + link + '?controls=0');
  }

  startStream(videoId: string, videoType: number) {
    this.router.navigate(['stream', videoId, videoType], { relativeTo: this.activatedRoute });
  }
}
