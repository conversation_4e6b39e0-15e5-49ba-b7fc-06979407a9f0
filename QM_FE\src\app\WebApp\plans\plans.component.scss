.home-wrapper {
  width: 100%;
  height: 100vh;
  font: 17px "Montserrat", "sans-serif";

  .home-sect--1 {
    width: 100%;
    max-width: 1330px;
    margin: 5.19em auto;
    display: flex;
    justify-content: center;

    .card {
      width: 500px;
      padding: 1.5em;
      background-color: #fff;
      border-radius: 15px;
      box-shadow: 0px 3px 20px rgba(17, 153, 158, 0.41);
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;

      h2 {
        margin-bottom: 1em;
      }

      ul {
        margin: 0;
        margin-bottom: 0.5rem;
        padding: 0;

        li {
          margin: 1.5em 0;
          padding: 0;
          list-style: none;
          text-align: center;
        }

        .price-item {
          display: flex;
          flex-direction: column;
          align-items: center;

          span {
            width: 40%;
            font-size: 24px;
            font-weight: bold;
            padding: 3px;
            background-color: rgb(78, 243, 120);
            border-radius: 5px;
          }
        }
      }

      &:nth-of-type(1) {
        margin-right: 3.5rem;
      }
    }
  }

  .custom-btn {
    cursor: pointer;
    width: 200px;
    height: 50px;
    font-size: 20px;
    color: #ffffff;
    background-color: #e88224;
    border: none;
    border-radius: 1000px;
    transition: all 0.4s ease;
  
    &:hover {
      background-color: #0b6fb1;
      transition: all 0.3s ease;
    }
  }

  .item-tab {
    width: 100%;
    td {
      padding-right: 1rem;
    }
  }

  .sect-text--1 {
    font-size: 51px;
    font-weight: lighter;
    margin: 1.4em 0 0 0;
  }

  .sect-text--2 {
    width: 80%;
  }

  .neg-space {
    position: absolute;
    top: 80%;
    left: -65px;
  }
}
.logout-btn {
  cursor: pointer;
  width: 110px;
  height: 40px;
  margin: 1.5em 0 0.5em 0;
  border: none;
  border-radius: 100px;
  text-align: center;
  background-color: #28a745;
  transition: all 0.5s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: #0b6fb1;
    transition: all 0.3s ease;
  }

  a {
    color: #fff;
  }
}

.checkout-btn {
  cursor: pointer;
  width: 110px;
  height: 40px;
  margin-top: 1.5em;
  margin-left: 15em;
  font-size: 20px;
  color: #ffffff;
  background-color: #e88224;
  border: none;
  border-radius: 1000px;
  transition: all 0.4s ease;

  &:hover {
    background-color: #0b6fb1;
    transition: all 0.3s ease;
  }
}

@media (max-width: 440px) {
  .home-wrapper {
    padding: 1em;

    .home-sect--1 {
      flex-direction: column;
      margin: 1em auto 1em auto;

      .card {
        width: 100%;
        margin-bottom: 2em;
      }
    }
  }
}
