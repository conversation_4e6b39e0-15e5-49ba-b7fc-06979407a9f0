import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';
import { Question } from 'src/app/Models/Dashboard/Chapters/Question';
import { TMCQGroup } from 'src/app/Models/Dashboard/TMCQ/TMCQGroup';
import { TMCQSubGroup } from 'src/app/Models/Dashboard/TMCQ/TMCQSubGroup';
import { ApiAuthService } from '../../api-auth.service';
import { PaperDetails } from '../../../Models/PaperDetails';

@Injectable({
  providedIn: 'root'
})
export class TmcqService {

  private baseUrlUser = 'https://api.quantmasters.in/v3/tmcq';
  private baseUrlAdm = 'https://api.quantmasters.in/v3/admin/tmcq';
  private JwtToken: string;

  constructor(private http: HttpClient,
    private apiAuthService: ApiAuthService) { }

  getTMCQGroupsAdmin(): Observable<TMCQGroup[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/group`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<TMCQGroup[]>(url, httpOps);
  }

  getMTCQSubGroupsAdmin(groupId: string): Observable<TMCQSubGroup[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/subgroup/${groupId}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<TMCQSubGroup[]>(url, httpOps);
  }

  getPapersOfAGroupAdmin(groupId: string): Observable<ChapterPaper[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/papers/${groupId}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<ChapterPaper[]>(url, httpOps);
  }

  getTMCQGroups(): Observable<TMCQGroup[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlUser}/group`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<TMCQGroup[]>(url, httpOps);
  }

  getTMCQSubGroups(group_id: string): Observable<TMCQSubGroup[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlUser}/subgroup/${group_id}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<TMCQSubGroup[]>(url, httpOps);
  }

  getPapersOfAGroup(subGroupId: string): Observable<ChapterPaper[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlUser}/papers/${subGroupId}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<ChapterPaper[]>(url, httpOps);
  }

  getQuestions(paperId: string): Observable<Question[]> {
    this.setSecurityToken();

    const url = `${this.baseUrlUser}/paper/${paperId}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<Question[]>(url, httpOps);
  }

  submitMarks(paper_id: string, email: string, marks: number): Observable<string> {

    this.setSecurityToken();

    const url = `${this.baseUrlUser}/paper/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    const body = {
      paper_id,
      email,
      marks
    };

    return this.http.post<string>(url, body, httpOps);
  }

  getMarksForAStudent(email: string): Observable<string> {
    this.setSecurityToken();

    const url = `${this.baseUrlUser}/paper/${email}/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(url, httpOps);
  }

  createNewPaper(newPaper: ChapterPaper): Observable<string> {
    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/paper`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.post<string>(url, newPaper, httpOps);
  }

  updatePaper(paper: ChapterPaper): Observable<string> {

    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/paper`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.put<string>(url, paper, httpOps);
  }

  deletePaper(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/paper/${paper_id}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.delete<string>(url, httpOps);
  }

  // Create question for paper
  postPaperQuestionDetails(QuestionDetails: object, paper_id: string): Observable<any> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v3/admin/tmcq/paper/' + paper_id + '/question', QuestionDetails);
    const url = `${this.baseUrlAdm}/paper/${paper_id}/question`;

    // const Url = this.BaseUrl + '/v3/admin/tmcq/paper/' + paper_id + '/question';
    return this.http.post<PaperDetails>(url, protectedBody, httpOps);
  }

  deleteQuestion(paper_id: string, ques_no: string): Observable<string> {

    this.setSecurityToken();

    const url = `${this.baseUrlAdm}/paper/${paper_id}/${ques_no}`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.delete<string>(url, httpOps);
  }

  createNewGroup(postBody) {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v3/admin/tmcq/group', postBody);
    const url = `${this.baseUrlAdm}/group`;

    return this.http.post<PaperDetails>(url, protectedBody, httpOps);
  }

  createNewSubGroup(postBody) {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    // const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/v3/admin/tmcq/group', postBody);
    const url = `${this.baseUrlAdm}/subgroup`;

    return this.http.post<PaperDetails>(url, postBody, httpOps);
  }


  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
