// lib/server-services/open-competitive-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { ChapterPaper } from '@/types/open-competitive-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  openCompetitiveUrl: `${API_BASE_URL}/v2/test/open/competitive/papers`,
  openCompetitiveQuesUrl: `${API_BASE_URL}/v2/test/open/competitive/paper/`,
  openCompetitiveSubmitUrl: `${API_BASE_URL}/v2/test/open/competitive/submit/marks`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerOpenCompetitiveService {
  /**
   * Get open competitive papers
   */
  static async getOpenCompetitivePapers(): Promise<ChapterPaper[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.openCompetitiveUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching open competitive papers:', error)
      return []
    }
  }

  /**
   * Get open competitive questions for a specific paper
   */
  static async getOpenCompetitiveQuestions(paperId: string): Promise<any> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.openCompetitiveQuesUrl}${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching open competitive questions for paper ${paperId}:`,
        error
      )
      throw error
    }
  }
}
