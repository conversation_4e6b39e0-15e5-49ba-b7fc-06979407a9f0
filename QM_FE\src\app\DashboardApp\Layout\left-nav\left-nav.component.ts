import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from 'src/app/Services/user.service';
import { state, style, trigger, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-left-nav',
  templateUrl: './left-nav.component.html',
  styleUrls: ['./left-nav.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({
        transform: 'translateX(0)'
      })),
      state('closed', style({
        transform: 'translateX(-250px)'
      })),
      transition('open => closed', [
        animate('0.5s ease-in-out')
      ]),
      transition('closed => open', [
        animate('0.4s ease-in-out')
      ])
    ])
  ]
})
export class LeftNavComponent implements OnInit {

  public userImage: string;
  public userName: string;

  public isNavOpen = false;

  constructor(public router: Router,
              public userService: UserService) { }

  ngOnInit() {
    this.userName = sessionStorage.getItem('QUser');

    const email = sessionStorage.getItem('QMail');

    this.userService.getUserProfileAvatar(email).subscribe(response2 => {
      const resp = JSON.parse(JSON.stringify(response2));

      this.userImage = resp.path;
    }, error => {
      const resp = JSON.parse(JSON.stringify(error));

      if (resp.error.err === 'No pic uploaded') {
        this.userImage = null;
      }
    });

    this.isNavOpen = true;
  }

  toggleNav() {
    if (window.innerWidth <= 440) {
      this.isNavOpen = !this.isNavOpen;
    }
  }

  gotoHome() {
    this.router.navigate(['/']);
  }
}
