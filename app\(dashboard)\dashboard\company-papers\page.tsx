// app/dashboard/company-papers/page.tsx
import { Metadata } from 'next'
import { ServerCompanyService } from '@/lib/server-services/company-service.server'
import CompanyTestsList from '@/components/company-papers/company-tests-list'

export const metadata: Metadata = {
  title: 'Company Papers | Quant Masters',
  description:
    'Practice with company-specific test papers and assessments from top IT companies'
}

export default async function CompanyPapersPage() {
  // Fetch user permissions on the server
  const userPermissions = await ServerCompanyService.checkUserPermissions()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center text-gray-800">
            Company Papers
          </h1>
          <p className="text-center text-gray-600 mt-2">
            Practice with real company test papers and assessments from top IT
            companies
          </p>
        </div>
      </div>

      {/* Company tests list component with server-side permissions */}
      <CompanyTestsList
        initialLockedResource={userPermissions.isLocked}
        hasAccess={userPermissions.hasAccess}
      />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
