@import "src/mixins";

.notes-wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 3rem auto 5rem auto;
  max-width: 1000px;

  @include for-size(phone-only) {
    width: 90%;
  }

  h4 {
    text-align: center;
  }

  .ctrls-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: end;
    width: 100%;
    padding: 1rem 0 0.3rem 0;
    margin-bottom: 2rem;
    border-top: 0.5px solid rgba(0, 0, 0, 0.15);
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.15);

    @include for-size(phone-only) {
      grid-template-columns: 1fr;
    }

    .social-share--area {
      display: flex;
      justify-content: center;
      align-items: flex-end;
      justify-self: end;

      @include for-size(phone-only) {
        justify-self: start;
        margin-top: 0.5rem;
      }

      p {
        margin-right: 20px;
        margin-bottom: 0;
      }

      .social-share--icon {
        cursor: pointer;
        width: 30px;
        margin-right: 15px;

        &:last-of-type {
          margin-right: 0;
        }

        &:nth-of-type(1) img:hover {
          background-color: #4267b2;
        }

        &:nth-of-type(2) img:hover {
          background-color: #1da1f2;
        }

        &:nth-of-type(3) img:hover {
          background-color: #0e76a8;
        }

        &:nth-of-type(4) img:hover {
          background-color: #64b161;
        }

        &:nth-of-type(5) img:hover {
          background-color: #808080;
        }

        img {
          border-radius: 50%;
          height: 100%;
          width: 100%;
        }
      }
    }

    .posted-on {
      justify-self: start;
      margin-bottom: 0;
      font-size: 14px;
    }
  }

  .reading-area {
    width: 100%;
    min-height: 30vh;

    /deep/ markdown {
      width: 100%;
    }

    /deep/ img {
      width: 60%;
      display: block;
      margin: auto
    }

    /deep/ ul {
      width: 100%;
    }

    @include for-size(phone-only) {
      /deep/ pre {
        max-width: 23em;
        overflow-wrap: break-word;
      }

      /deep/ img {
        width: 100%;
      }

      /deep/ ul {
        width: 90%;
      }
    }
  }

  .embed-area {
    width: 100%;
    margin-top: 2em;

    @include for-size(phone-only) {
      iframe {
        width: 100%;
      }
    }
  }

  .nav-area {
    width: 35%;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    margin-top: 3em;

    .nav-btn {
      cursor: pointer;
      padding: 0.75em;
      border-radius: 50%;
      border: 1px solid rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;

      img {
        height: 2em;
        width: 2em;
      }

      &:hover {
        transform: scale(1.1);
        background-color: #aaa;
        transition: all 0.2s ease;
      }
    }
  }
  .player-legend {
    margin-top: 3em;
    width: 100%;
    border-top: 0.5px solid rgba(0, 0, 0, 0.15);
    // border-bottom: 0.5px solid rgba(0, 0, 0, 0.15);
    .user-comment-section {
      width: 100%;
      flex-direction: column;
      display: flex;

      h4 {
        margin: 0.5em 0 1em 0;
      }

      .user-new-comment {
        display: flex;
      }

      .image-div {
        // padding-left: 1%;
        width: 7%;
        .user-image {
          border-radius: 50%;
          width: 3rem !important;
          height: 3rem !important;
        }
      }

      .comment-div {
        width: 100%;
        display: flex;
        flex-direction: column;

        .user-comment {
          width: 100% !important;
          // border: none;
          border-bottom-style: ridge;
          outline: none;
        }
        .buttons-div {
          display: flex;
          flex-direction: row;
          width: 100%;
          justify-content: flex-end;
          padding-top: 1.5%;
          padding-right: 1%;

          button {
            padding: 5px 15px;
            margin-right: 1.5%;
            background-color: #17a2b8;
            border: none;
            color: #fff;
            box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
            border-radius: 1000px;
            outline: none;
          }
        }
      }
    }

    .video-comments {
      width: 90%;
      margin-left: 1%;
      flex-direction: row;
      display: flex;
      border-bottom-style: inset;
      .image-div {
        padding: 1%;
        width: 6%;
        .user-image {
          border-radius: 50%;
          width: 2rem;
          height: 2rem;
        }
      }
      .comment-text-div {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding-top: 1%;

        .comment-heder {
          margin-bottom: 0%;
          width: 100%;
          font: 14px "Montserrat", "serif";
          font-weight: bold;
          color: #063250;

          u {
            text-decoration: none;

            span {
              font-size: 12px;
            }
          }
        }
        .comments {
          word-break: break-all;
          word-break: break-word;
          font: 14px "Montserrat", "serif";
          padding: 4px 0px;
        }
        .comment-reply-section {
          display: flex;
          flex-direction: row;
          padding-bottom: 2%;

          button {
            background-color: #3097e6e6;
            color: #fff;
            border: none;
            border-radius: 3px;
            outline: none;
            margin-right: 2%;
            margin-top: 0.5%;
            padding: 5px;
            font: 12px "Montserrat", "serif";
          }
        }
      }

      .reply_section_ExtraCSS {
        display: flex;
        flex-direction: coloumn;
        background-color: #c4e3fb;
        border-radius: 10px;
        border-top-left-radius: 0px;
        width: 100%;
        margin: 1% 0% 1% 0%;
      }
    }
  }
}
@media (max-width: 440px) {
  .user-comment-section {
    width: 100%;
    .image-div {
      width: 17% !important;
      .user-image {
        width: 2.5rem !important;
        height: 2.5rem !important;
        margin-top: 4%;
      }
    }
    .comment-div {
      width: 80% !important;
    }
  }

  .video-comments {
    width: 100%;
    margin-left: 0%;
    margin-bottom: 3%;
    .image-div {
      width: 14% !important;
    }
    .comment-text-div {
      padding: 0% 1% 0% 2%;
    }
  }
}
