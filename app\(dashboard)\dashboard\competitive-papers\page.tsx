// app/dashboard/competitive-papers/page.tsx
import { Metadata } from 'next'
import { ServerCompetitivePapersService } from '@/lib/server-services/competitive.service.server'
import CompetitivePapersClient from '@/components/competitive-papers/competitive-papers-client'

export const metadata: Metadata = {
  title: 'Competitive Papers | Quant Masters',
  description: 'Practice with our competitive examination papers'
}

export default async function CompetitivePapersPage() {
  // Fetch groups and papers on the server
  const [groups, papers] = await Promise.all([
    ServerCompetitivePapersService.getGroups(),
    ServerCompetitivePapersService.getAllPapers()
  ])

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center text-gray-800">
            Competitive Papers
          </h1>
          <p className="text-center text-gray-600 mt-2">
            Papers of major Bank and PSU exams
          </p>
        </div>
      </div>

      {/* Pass the fetched data to the client component */}
      <CompetitivePapersClient initialGroups={groups} initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
