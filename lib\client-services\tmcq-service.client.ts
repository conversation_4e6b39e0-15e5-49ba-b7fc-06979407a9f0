/* eslint-disable @typescript-eslint/no-unused-vars */
// lib/client-services/tmcq-service.client.ts
import axios from 'axios'
import { LoginRefresh } from '../cookies'
import {
  TMCQGroup,
  TMCQSubGroup,
  ChapterPaper
} from '@/types/technical-mcq-types'

const API_BASE_URL = 'https://api.quantmasters.in/v3/tmcq'

const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class TMCQClientService {
  static async getTMCQSubGroups(groupId: string): Promise<TMCQSubGroup[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/subgroup/${groupId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching TMCQ subgroups:', error)
      return []
    }
  }

  static async getPapersOfAGroup(subGroupId: string): Promise<ChapterPaper[]> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/papers/${subGroupId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching TMCQ papers:', error)
      return []
    }
  }
}
