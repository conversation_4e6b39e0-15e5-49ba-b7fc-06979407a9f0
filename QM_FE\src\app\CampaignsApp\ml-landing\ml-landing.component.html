<div class="qm-ml--wrap">
  <div class="qm-ml--landing">
    <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>

    <div class="qm-logo">
      <a routerLink="/"><img src="../../../../assets/QM_Logo_No_Text.svg" alt="QM Logo" /> Quantmasters</a>
    </div>

    <div class="qm-mode--switch" (click)="switchTheme()">
      <label for="switch">
        <div class="qm-toggle"></div>
        <div class="qm-names">
          <p class="light">Light</p>
          <p class="dark">Dark</p>
        </div>
      </label>
    </div>

    <div class="qm-sect--reg qm-sect--intro">
      <h1>Internship Program</h1>
      <h2>On Machine Learning & Artificial Intelligence</h2>
      <h3>Pre-requisites for the Course: None</h3>
      <h4>(We Will be Training from Basics)</h4>
      <h4>10 Weeks LIVE Classes</h4>

      <div class="qm-ratings">
        <div class="rm-data--box">
          <img src="../../../assets/icons/ml-internship/google.svg" alt="Google Rating" />
          <div class="rating-stars" style="--rating: 4.9;"
            aria-label="Quantmasters is rated 4.9/5 from 1300+ Reviews on Google"></div>
          <h1>4.9<small>/5</small></h1>
          <p>From <span>1300+</span> reviews on Google</p>
        </div>
        <div class="rm-data--box">
          <img src="../../../assets/icons/ml-internship/google-play-store.svg" alt="Google Rating" />
          <div class="rating-stars" style="--rating: 4.9;"
            aria-label="Quantmasters is rated 4.9/5 from 1300+ Reviews on Google"></div>
          <h1>4.9<small>/5</small></h1>
          <p>Reviews on Google Play Store</p>
        </div>
      </div>

      <div class="qm-features--grid">
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Team_building.svg" alt="Feature Practical Exposure" />
          <p>Practical Exposure</p>
        </div>
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Team_work.svg" alt="Feature Weekly Assignments" />
          <p>Weekly Assignments</p>
        </div>
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Conference_presentation.svg" alt="Feature Expert Mentoring" />
          <p>Expert Mentoring</p>
        </div>
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Campaign_launch.svg" alt="Feature Real World Project" />
          <p>Real World Project</p>
        </div>
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Certificate.svg" alt="Feature Internship Certificate" />
          <p>Internship Certificate</p>
        </div>
        <div class="qm-feature--card">
          <img src="../../../assets/icons/ml-internship/Data_Setting.svg" alt="Backup Classes Will Be Provided" />
          <p>Backup Classes Will Be Provided</p>
        </div>
      </div>

      <div class="qm-upcoming--info">
        <p>Upcoming Batches</p>
        <div class="qm-info--card">
          <p>12<sup>th</sup> August 2023</p>
          <button class="qm-sec--btn" type="button" (click)="takeToDetails()">Read More</button>
        </div>
      </div>
    </div>

    <div class="qm-sect--reg qm-sect--detail">
      <h1>What our students have to say</h1>
      <div class="qm-card--grid">
        <div class="qm-student-rev--card">
          <div class="student-avatar">
            <img src="../../../assets/icons/ml-internship/ml-reviews/pragnasya.jpeg" alt="Pragnasya Avatar">
          </div>
          <div class="student-review">
            <p><i>"</i>Joining their internship program is so far THE BEST decision I made this year!! 
              I gained a lot of knowledge...And I really admire the way they actually take care of their students...
              l also really like the way Shashank sir handles class with a lot of patience...Sir explains each and every 
              single concept in a simple way and always makes sure that whatever he is teaching is registered in our minds 
              and clarifies each and every single doubt of a student...This platform provides awesome services in such a 
              minimum cost that no other platform provides...If someone wants to join any internship/training program I would 
              definitely 💯 recommend QuantMasters...<i>"</i></p>
          </div>
          <div class="student-name">
            <p>- - Pragnasya</p>
          </div>
        </div>
        <div class="qm-student-rev--card">
          <div class="student-avatar">
            <img src="../../../assets/icons/ml-internship/ml-reviews/bhagya.jpeg" alt="Bhagya Avatar">
          </div>
          <div class="student-review">
            <p><i>"</i>This is one of the best platforms to learn a concept from scratch at ace at it in Computer Science. 
              Was really helpful and had amazing real world projects for greater exposure.<i>"</i></p>
          </div>
          <div class="student-name">
            <p>- - Bhagya</p>
          </div>
        </div>
        <div class="qm-student-rev--card">
          <div class="student-avatar">
            <img src="../../../assets/icons/ml-internship/ml-reviews/chaitanya.jpeg" alt="Chaitanya Avatar">
          </div>
          <div class="student-review">
            <p><i>"</i>Great Internship experience at Quant Masters. I was trained in ML and Al domain from scratch and the company 
              provided in-depth knowledge about the domain. Portal access is smooth. Highly recommended for students interested 
              in this domain.<i>"</i></p>
          </div>
          <div class="student-name">
            <p>- - Chaitanya</p>
          </div>
        </div>
        <div class="qm-student-rev--card">
          <div class="student-avatar">
            <img src="../../../assets/icons/ml-internship/ml-reviews/viswanathn.jpeg" alt="Viswanathn Avatar">
          </div>
          <div class="student-review">
            <p><i>"</i>It is a very good platform to learn everything...from placement training to machine learning and artificial intelligence.. 
              everything is thought in a very understandable way..best experience<i>"</i></p>
          </div>
          <div class="student-name">
            <p>- - Viswanathn</p>
          </div>
        </div>
      </div>
    </div>

    <div class="qm-sect--reg qm-sect--detail">
      <h1>Reviews from our champs</h1>
      <div class="video-grid--1">
        <div class="qm-video">
          <iframe width="600" height="400" src="https://www.youtube.com/embed/r1DzfFFhSCE" frameborder="0"
            allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
        <div class="qm-video">
          <iframe width="600" height="400" src="https://www.youtube.com/embed/TN1l_eDgHVk" frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen></iframe>
        </div>
      </div>
    </div>

    <div class="qm-sect--reg qm-sect--detail">
      <h1>Internship details</h1>
      <div class="qm-detail--lists">
        <div class="qm-detail--list">
          <h2>Topics covered</h2>
          <ul>
            <li><span>Unit 1:</span> Python Fundamentals</li>
            <li><span>Unit 2:</span> Data Structures in Python</li>
            <li><span>Unit 3:</span> Exception Handling</li>
            <li><span>Unit 4:</span> Numpy for Machine Learning</li>
            <li><span>Unit 5:</span> Pandas for Data Cleaning</li>
            <li><span>Unit 6:</span> MatPlotLob and SeaBorn for Visualization</li>
            <li><span>Unit 7:</span> Data Exploration and Analysis</li>
            <li><span>Unit 8:</span> Sklearn for Machine learning </li>
            <li><span>Unit 9:</span> Statistics for Machine Learning Algorithms</li>
            <li><span>Unit 10:</span> Linear Regression</li>
            <li><span>Unit 11:</span> Feature Selection Methods and Engineering</li>
            <li><span>Unit 12:</span> Polynomial Regression</li>
            <li><span>Unit 13:</span> Logistic Regression</li>
            <li><span>Unit 14:</span> Performance Evaluation of Regression Models</li>
            <li><span>Unit 15:</span> Classification</li>
            <li><span>Unit 16:</span> KNN</li>
            <li><span>Unit 17:</span> Naïve Bayes</li>
            <li><span>Unit 18:</span> SVM</li>
            <li><span>Unit 19:</span> Decision Trees</li>
            <li><span>Unit 20:</span> Classification Model Performance Evaluation</li>
            <li><span>Unit 21:</span> Finetuning a Model</li>
            <li><span>Unit 22:</span> Bagging, Boosting and XGBoost</li>
            <li><span>Unit 23:</span> K Means Clustering</li>
            <li><span>Unit 24:</span> Hierarchical Clustering</li>
            <li><span>Unit 25:</span> Apriori and Eclat Association Rule Mining</li>
            <li><span>Unit 26:</span> Reinforcement Learning</li>
            <li><span>Unit 27:</span> Natural Language Processing</li>
            <li><span>Unit 28:</span> Deep Learning</li>
            <li><span>Unit 29:</span> Active Learning</li>
            <li><span>Unit 30:</span> ANN</li>
            <li><span>Unit 31:</span> CNN</li>
            <li><span>Unit 32:</span> RNN</li>
            <li><span>Unit 33:</span> Dimensionality Reduction using PCA and LDA</li>
            <li><span>Unit 34:</span> BERT Modeling</li>
          </ul>
        </div>

        <div class="qm-detail--list">
          <h2>Projects Include</h2>
          <ul>
            <li>Movie Recommendations with Movielens Dataset</li>
            <li>Sales Forecasting with Walmart</li>
            <li>Stock Price Predictions</li>
            <li>Wine Quality Predictions</li>
            <li>Iris Classification</li>
            <li>Sentiment Analysis of Tweets on Twitter</li>
            <li>Turning Handwritten Documents into Digitized Versions</li>
            <li>MNIST Digit Classification</li>
            <li>Titanic Survival Project</li>
            <li>Music Genre Classification</li>
            <li>House Price Prediction</li>
            <li>And More...</li>
          </ul>
        </div>
      </div>
    </div>

    <div class="qm-sect--reg qm-sect--enroll" id="qm-enroll--box">
      <h1>Start You Data Science Career Today!</h1>
      <p><span>Practical Exposure:</span> You will gain a practical exposure towards ML and AI usage in real world
        applications such as stock prediction, image classification, sentiment analysis and more.</p>
      <p><span>Weekly Assignments:</span> You will receive a series of assignments that incrementally solve a real-world
        machine learning problem.</p>
      <p><span>Mentoring:</span> You can reach out to one of our Machine Learning mentors, in case you face any
        difficulty at any point in the program.</p>
      <p><span>Project:</span> At the end of the 8 weeks, you will have a real-world project assigned to you with our
        team of experts, who will test you on the knowledge learned during your internship.</p>
      <p><span>Internship Certificate:</span> At the end of the internship, you will receive an internship completion
        certificate upon completing the initial training and the project of the internship.</p>
      <h2><span><sup></sup> </span></h2>
      <h2><span><sup></sup> </span></h2>
      <h2>Start Date: <span>12<sup>th</sup> Aug 2023.</span></h2>
      <h2>End Date: <span>15<sup>th</sup> Oct 2023.</span></h2>
      <h2>Timing: <span>Saturday 6:00 PM - 8:00 PM <br/> Sunday 9:00 AM - 12:00 PM</span></h2>
      <h2></h2>
      <!-- <p>First 100 students to get exclusive discount</p> -->
      <h2>Course Fee: <span>₹ {{ selectedAmount }} /-</span></h2>
      <button class="qm-prim--btn" type="button" (click)="openRegForm()">Enroll Now</button>
      <!-- <p>Price will be ₹ 3199/- after 100 registrations, hurry up!</p> -->
      <!-- <h2 class="qm-spl-text">Join in a group of 5 Friends and Pay only <span>₹ 1999 /-</span> per head.</h2>
      <p>Send us a message on Whatsapp by clicking the button below to Join as a Group of 5</p>
      <button class="qm-sec--btn" type="button" (click)="sendToWhatsapp()">Enroll As Group</button> -->
      <div class="offer-box">
        <img src="../../../assets/icons/workshop/OfferML27Aug22.jpeg" alt="Offer New" />
      </div>
    </div>

    <div class="qm-grid--1">
      <img src="../../../../assets/icons/workshop/sect-4bg-center.svg" alt="Grid 1">
    </div>

    <div class="qm-ftr">
      <p>Quant Masters Training Services</p>
      <div class="icons">
        <div class="icon">
          <a href="https://www.facebook.com/Quant-Masters-239562816724244/" target="__blank"><img class="social-icon"
              src="../../../assets/icons/facebook.svg" alt="facebook-logo" /></a>
        </div>
        <div class="icon">
          <a href="https://www.linkedin.com/company/qunat-masters" target="__blank"><img class="social-icon"
              src="../../../assets/icons/linkedin.svg" alt="linkedin-logo" /></a>
        </div>
        <div class="icon">
          <a href="https://www.youtube.com/channel/UCnQweeTKKj0gUZeDG1_PDtQ" target="__blank"><img class="social-icon"
              src="../../../assets/icons/youtube.svg" alt="youtube-logo" /></a>
        </div>
        <div class="icon">
          <a href="https://www.instagram.com/quant_masters/" target="__blank"><img class="social-icon"
              src="../../../assets/icons/instagram.svg" alt="instagram-logo" /></a>
        </div>
      </div>
      <div class="copy-text">&copy; 2025 Quant Masters. All Rights Reserved.</div>
    </div>
  </div>

  <div class="qm-contact--box" (click)="sendToWhatsapp()">
    <img src="../../../assets/icons/ml-internship/whatsapp-social-media.svg" alt="Whatsapp Icon">
  </div>

  <ng-template #regTemplate>
    <div class="modal-header" style="flex-direction:column;">
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
      <h4 class="modal-title text-info">Your Details</h4>
      <h5>Let us know how to get back to you</h5>
    </div>
    <div class="modal-body">
      <form autocomplete="off" #registerForm="ngForm" (ngSubmit)="registerQuick(registerForm.valid)"
        appPasswordEqualValidator>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="text" name="fName" placeholder="First Name *" [(ngModel)]="newUser.f_name" #fName="ngModel"
              required />
            <small class="form-error--text"
              *ngIf="fName.errors?.required && fName.touched || (fName.pristine && registerForm.submitted)">
              First Name is Required
            </small>
            <small class="form-error--text" *ngIf="fName.errors?.fName && fName.touched">
              First Name is Invalid
            </small>
          </div>
          <div class="form-elem">
            <input type="text" name="lName" placeholder="Last Name" [(ngModel)]="newUser.l_name" #lName="ngModel" />
            <small class="form-error--text" *ngIf="lName.errors?.lName && lName.touched">
              Last Name is Invalid
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="email" name="email"
              [class.is-inv--input]="email.invalid && email.touched || (email.pristine && registerForm.submitted)"
              placeholder="Student Mail Id *" [(ngModel)]="newUser.email" #email="ngModel" required email />
            <div class="form-fill--bar"
              [class.is-inv]="email.invalid && email.touched || (email.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="email.errors?.email && email.touched">
              That does not look like an email
            </small>
            <small class="form-error--text" *ngIf="email.errors?.required && email.touched">
              Please enter your email
            </small>
            <small id="emailHelpBlock" *ngIf="email.valid">
              Don't worry, your email is safe with us.
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <div class="form-elem">
            <input type="number" name="phoneNum"
              [class.is-inv--input]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)"
              placeholder="Student Whatsapp Number *" [(ngModel)]="newUser.phone_no" #phoneNum="ngModel" required
              pattern="[0-9]{10}" />
            <div class="form-fill--bar"
              [class.is-inv]="phoneNum.invalid && phoneNum.touched || (phoneNum.pristine && registerForm.submitted)">
            </div>
            <small class="form-error--text" *ngIf="phoneNum.errors?.pattern && phoneNum.touched">
              Mobile number must contain 10 digits
            </small>
            <small class="form-error--text" *ngIf="phoneNum.errors?.required && phoneNum.touched">
              Please enter your phone number
            </small>
            <small id="emailHelpBlock" *ngIf="phoneNum.valid">
              Don't worry, your number is safe with us.
            </small>
          </div>
        </div>
        <div class="form-row--1">
          <button type="submit" value="submit" class="secondary-btn">
            Get Started
          </button>
        </div>
        <small class="form-error--text form-error-last" *ngIf="registerForm.invalid && registerForm.submitted">
          Please fill all the details first.
        </small>
      </form>
    </div>
  </ng-template>

  <ng-template #errorTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-danger">Registration Failed!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      Please try again
    </div>
  </ng-template>

  <ng-template #buyTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Book Your Seat</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide(); showIndicator=false">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <table class="item-tab" cellpadding="5">
        <tr>
          <th>Description</th>
          <th>Amount</th>
        </tr>
        <tr>
          <td>{{ selectedDesc }}</td>
          <td>₹ {{ selectedAmount }}.00/-</td>
        </tr>
      </table>
      <button class="secondary-btn" (click)="onCheckout()">Checkout</button>
    </div>
  </ng-template>

  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Thank You</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>You will recieve a mail with the receipt in your registered email.</p>
      <p>Kindly send a screenshot of the receipt (with visible email address) via WhatsApp to:
        <br /><b>{{ selContactNum }}</b>
      </p>
      <p>For any further queries contact <b>{{ selContactInfo }} @ {{ selContactNum }}</b></p>
    </div>
  </ng-template>
</div>