<div class="cert-header">
  <app-nav></app-nav>
  <div class="Main-containt">
    <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
    <div class="alrt-box" *ngIf="showCopyAlert">
      <alert [type]="copyAlert.type" [dismissOnTimeout]="copyAlert.timeout" (onClosed)="onClosed()">{{ copyAlert.msg }}
      </alert>
    </div>
    <div class="edit-profile">
      <p>Internship Certification</p>
    </div>
    <div class="left-sect">
      <div class="left-bar">
        <div class="user-options">
          <div class="option-link" (click)="getCertificate('User', true)">
            Generate/Get your certificate
          </div>
          <div class="option-link verify-link" (click)="verifyCertificate()">
            Verify certificate
          </div>
          <div class="option-link faq-link" (click)="takeToFAQs()">
            FAQs
          </div>
        </div>
      </div>
      <div class="detail-bar">
        <p *ngIf="certAuthText" class="certificate-auth-text">{{ certAuthText }}</p>
        <div class="options">
          <div class="option-link" (click)="downloadCertificate()" *ngIf="pdfPath.length > 0">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-download"
              viewBox="0 0 16 16">
              <path
                d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z" />
              <path
                d="M7.646 11.854a.5.5 0 0 0 .708 0l3-3a.5.5 0 0 0-.708-.708L8.5 10.293V1.5a.5.5 0 0 0-1 0v8.793L5.354 8.146a.5.5 0 1 0-.708.708l3 3z" />
            </svg>
            Download
          </div>
          <div class="share-link" (click)="generateCertificateUrl()" *ngIf="pdfPath.length > 0">
            Or Share on: <img src="../../../assets/icons/linkedin-dark.svg" alt="LinkedIn" />
          </div>
        </div>
        <div class="pdf_viwer">
          <pdf-viewer [src]="pdfPath" [original-size]="false" [render-text]="true" style="display: block; width: 90%; margin: auto;">
          </pdf-viewer>
        </div>
      </div>
    </div>
  </div>

  <ng-template #hideTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Attended the Internship?</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>
        <a (click)="onLogin()" routerLink="/user/login">Click here</a> to login <br />
        OR,<br />
        Enter the email that you used to register for the Internship below
      </p>
      <form #ReqInfo="ngForm" (ngSubmit)="getCertificate('Temp',ReqInfo.valid)" autocomplete="off">
        <div class="form-row--1">
          <div class="form-elem">
            <input type="email" name="UserMail" [class.is-inv--input]="
              (UserMail.invalid && UserMail.touched) ||
              (UserMail.pristine && ReqInfo.submitted)
            " placeholder="Email *" aria-describedby="emailHelpBlock" [(ngModel)]="UserMailId" #UserMail="ngModel"
              required email />
            <div class="form-fill--bar" [class.is-inv]="
              (UserMail.invalid && UserMail.touched) ||
              (UserMail.pristine && ReqInfo.submitted)
            "></div>
            <small class="form-error--text" *ngIf="
              (UserMail.errors?.required && UserMail.touched) ||
              (UserMail.pristine && ReqInfo.submitted)
            ">
              Email is Required
            </small>
            <small class="form-error--text" *ngIf="UserMail.errors?.email && UserMail.touched">
              Email is Invalid
            </small>
          </div>
        </div>
        <div class="Ng-temp-Main">
          <div class="Ng-temp-Button">
            <div class="common-div">
              <button class="custom-btn" type="submit">Generate</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </ng-template>

  <ng-template #verifyTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-primary">
        Authenticate a certificate
      </h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide();certificateId=null;">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #verifyFrom="ngForm" (ngSubmit)="checkValidity()" autocomplete="off">
        <div class="form-row--1">
          <div class="form-elem">
            <input type="email" name="certId" [class.is-inv--input]="
                (certId.invalid && certId.touched) ||
                (certId.pristine && verifyFrom.submitted)
              " placeholder="Certificate Id *" aria-describedby="emailHelpBlock" [(ngModel)]="certificateId" #certId="ngModel"
              required />
            <div class="form-fill--bar" [class.is-inv]="
                (certId.invalid && certId.touched) ||
                (certId.pristine && verifyFrom.submitted)
              "></div>
            <small class="form-error--text" *ngIf="
                (certId.errors?.required && certId.touched) ||
                (certId.pristine && verifyFrom.submitted)
              ">
              Certificate ID is required
            </small>
          </div>
        </div>
        <div class="Ng-temp-Main">
          <div class="Ng-temp-Button">
            <div class="common-div">
              <button class="custom-btn" type="submit">Verify</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </ng-template>

  <ng-template #errorTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-danger">Oops!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Could not find your certificate.</p>
    </div>
  </ng-template>
</div>