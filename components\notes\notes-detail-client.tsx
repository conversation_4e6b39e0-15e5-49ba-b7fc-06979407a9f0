/* eslint-disable @typescript-eslint/no-unused-vars */
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  Share2,
  Facebook,
  Twitter,
  Linkedin,
  MessageSquare
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import {
  Notes,
  Comment,
  NotesData,
  CommentSubmission
} from '@/types/notes-types'
import { NotesService } from '@/lib/client-services/notes-service.client'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible'

interface NotesDetailClientProps {
  initialNote: Notes | null
  noteId: string
}

export default function NotesDetailClient({
  initialNote,
  noteId
}: NotesDetailClientProps) {
  const router = useRouter()

  const [noteToRead, setNoteToRead] = useState<Notes | null>(initialNote)
  const [comments, setComments] = useState<Comment[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showCopyAlert, setShowCopyAlert] = useState(false)
  const [myComment, setMyComment] = useState('')
  const [replyComments, setReplyComments] = useState<{ [key: string]: string }>(
    {}
  )
  const [userAvatars, setUserAvatars] = useState<{ [key: string]: string }>({})
  const [openReplies, setOpenReplies] = useState<{ [key: string]: boolean }>({})
  const [showReplies, setShowReplies] = useState<{ [key: string]: boolean }>({})

  const shareNoteUrl = typeof window !== 'undefined' ? window.location.href : ''
  const userEmail =
    typeof window !== 'undefined' ? sessionStorage.getItem('QMail') : ''

  useEffect(() => {
    if (!initialNote && noteId) {
      fetchNoteDetail()
    }
    fetchComments()
    fetchUserAvatar(userEmail || '')
  }, [noteId])

  const fetchNoteDetail = async () => {
    setIsLoading(true)
    try {
      const note = await NotesService.getNotesForReading(noteId)
      setNoteToRead({
        ...note,
        posted_on: new Date(note.posted_on).toLocaleDateString()
      })
    } catch (error) {
      console.error('Error fetching note detail:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchComments = async () => {
    try {
      const fetchedComments = await NotesService.getNotesComments(noteId)

      // Process comments and fetch avatars
      const processedComments = fetchedComments.map((comment) => ({
        ...comment,
        created_at: new Date(comment.created_at).toDateString(),
        replies: comment.replies.map((reply) => ({
          ...reply,
          created_at: new Date(reply.created_at).toDateString()
        }))
      }))

      setComments(processedComments)

      // Fetch avatars for all users
      const uniqueEmails = new Set<string>()
      processedComments.forEach((comment) => {
        uniqueEmails.add(comment.email)
        comment.replies.forEach((reply) => uniqueEmails.add(reply.email))
      })

      uniqueEmails.forEach((email) => fetchUserAvatar(email))
    } catch (error) {
      console.error('Error fetching comments:', error)
    }
  }

  const fetchUserAvatar = async (email: string) => {
    if (!email || userAvatars[email]) return

    try {
      const avatarData = await NotesService.getUserProfileAvatar(email)
      setUserAvatars((prev) => ({
        ...prev,
        [email]: avatarData.path
      }))
    } catch (error) {
      console.error('Error fetching user avatar:', error)
      setUserAvatars((prev) => ({
        ...prev,
        [email]: '/default-avatar.png'
      }))
    }
  }

  const handleSubmitComment = async () => {
    if (!myComment.trim() || !userEmail) return

    setIsLoading(true)
    try {
      const commentData: CommentSubmission = {
        text: myComment,
        email: userEmail
      }

      const commentrsp: any = await NotesService.postNoteComment(
        noteId,
        commentData
      )
      setMyComment('')
      await fetchComments()
    } catch (error) {
      console.error('Error posting comment:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitReply = async (commentId: string) => {
    const replyText = replyComments[commentId]
    if (!replyText?.trim() || !userEmail) return

    setIsLoading(true)
    try {
      const replyData: CommentSubmission = {
        text: replyText,
        email: userEmail
      }

      await NotesService.postNoteCommentReply(noteId, commentId, replyData)
      setReplyComments((prev) => ({ ...prev, [commentId]: '' }))
      setOpenReplies((prev) => ({ ...prev, [commentId]: false }))
      await fetchComments()
    } catch (error) {
      console.error('Error posting reply:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const shareOnPlatform = (platform: string) => {
    let url = ''

    switch (platform) {
      case 'whatsapp':
        url = `https://wa.me/?text=Hey!,%20Checkout%20this%20article%20from%20Quant%20Masters:%0A${noteToRead?.notes_name}%0A%40%0A${shareNoteUrl}`
        break
      case 'linkedin':
        url = `https://www.linkedin.com/sharing/share-offsite/?url=${shareNoteUrl}`
        break
      case 'facebook':
        url = `https://www.facebook.com/sharer/sharer.php?u=${shareNoteUrl}`
        break
      case 'twitter':
        url = `https://twitter.com/home?status=${shareNoteUrl}`
        break
    }

    if (url) {
      window.open(url, '_blank')
    }
  }

  const shareDirectLink = async () => {
    try {
      await navigator.clipboard.writeText(shareNoteUrl)
      setShowCopyAlert(true)
      setTimeout(() => setShowCopyAlert(false), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
    }
  }

  const takeToNotesHome = () => {
    router.push('/dashboard/notes')
  }

  if (isLoading && !noteToRead) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="text-center">Please wait...</div>
      </div>
    )
  }

  if (!noteToRead) {
    return (
      <div className="container mx-auto py-8 px-4 text-center">
        <p className="text-gray-600">Note not found.</p>
        <Button onClick={takeToNotesHome} className="mt-4">
          Back to Notes
        </Button>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto max-w-4xl">
        {showCopyAlert && (
          <Alert className="mb-4">
            <AlertDescription>URL Copied!</AlertDescription>
          </Alert>
        )}

        {/* Navigation */}
        <div className="mb-6">
          <Button
            variant="outline"
            onClick={takeToNotesHome}
            className="flex items-center"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Notes
          </Button>
        </div>

        {/* Main Paper Container */}
        <Card className="bg-white shadow-lg">
          <CardContent className="p-8 md:p-12">
            {/* Article Header */}
            <div className="mb-8">
              <h1 className="text-4xl font-bold mb-4 text-gray-900 leading-tight">
                {noteToRead.notes_name}
              </h1>

              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 pb-6 border-b border-gray-200">
                <p className="text-gray-600">
                  <strong>Posted On:</strong> {noteToRead.posted_on}
                </p>

                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700">
                    Share:
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareOnPlatform('facebook')}
                  >
                    <Facebook className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareOnPlatform('twitter')}
                  >
                    <Twitter className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareOnPlatform('linkedin')}
                  >
                    <Linkedin className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => shareOnPlatform('whatsapp')}
                  >
                    <MessageSquare className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" onClick={shareDirectLink}>
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Reading Area */}
            <div className="reading-area prose prose-lg max-w-none mb-8 text-gray-800 leading-relaxed">
              <ReactMarkdown>{noteToRead.notes_content}</ReactMarkdown>
            </div>

            {/* Embed Video */}
            {noteToRead.notes_video && (
              <div className="embed-area mb-8 text-center">
                <div className="relative w-full max-w-3xl mx-auto">
                  <div className="aspect-video">
                    <iframe
                      src={`https://www.youtube.com/embed/${noteToRead.notes_video}`}
                      frameBorder="0"
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full rounded-lg shadow-md"
                    />
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Comments Section - Separate Card */}
        <Card className="bg-white shadow-lg mt-8">
          <CardContent className="p-8">
            <h2 className="text-2xl font-bold mb-6 text-gray-900">
              {comments.length} Comments
            </h2>

            {/* New Comment */}
            <Card className="mb-6 bg-gray-50">
              <CardHeader>
                <CardTitle className="text-lg">Add a Comment</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  <Avatar>
                    <AvatarImage src={userAvatars[userEmail || '']} />
                    <AvatarFallback>U</AvatarFallback>
                  </Avatar>
                  <Textarea
                    placeholder="Add your comment..."
                    value={myComment}
                    onChange={(e: any) => setMyComment(e.target.value)}
                    className="bg-white"
                  />
                </div>
                <div className="flex justify-end mt-2 space-x-2">
                  <Button variant="outline" onClick={() => setMyComment('')}>
                    Clear
                  </Button>
                  <Button
                    onClick={handleSubmitComment}
                    disabled={isLoading || !myComment.trim()}
                  >
                    Comment
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Existing Comments */}
            {comments.map((comment) => (
              <Card
                key={comment.comment_id}
                className="mb-6 bg-white border border-gray-200"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarImage src={userAvatars[comment.email]} />
                      <AvatarFallback>
                        {comment.created_by.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-base">
                        {comment.created_by}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        {comment.created_at}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-800 mb-3">{comment.text}</p>
                  <div className="flex items-center space-x-4">
                    <Collapsible>
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" size="sm">
                          Reply
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="mt-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <div className="flex gap-4 mb-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={userAvatars[userEmail || '']} />
                              <AvatarFallback>U</AvatarFallback>
                            </Avatar>
                            <Textarea
                              placeholder="Add your reply..."
                              value={replyComments[comment.comment_id] || ''}
                              onChange={(e: any) =>
                                setReplyComments({
                                  ...replyComments,
                                  [comment.comment_id]: e.target.value
                                })
                              }
                              className="bg-white"
                            />
                          </div>
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                setReplyComments({
                                  ...replyComments,
                                  [comment.comment_id]: ''
                                })
                              }
                            >
                              Cancel
                            </Button>
                            <Button
                              size="sm"
                              onClick={() =>
                                handleSubmitReply(comment.comment_id)
                              }
                              disabled={
                                !replyComments[comment.comment_id]?.trim()
                              }
                            >
                              Submit
                            </Button>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  </div>

                  {/* Display Replies */}
                  {comment.replies && comment.replies.length > 0 && (
                    <div className="mt-4 pl-8 border-l-2 border-gray-200">
                      {comment.replies.map((reply, index) => (
                        <div key={index} className="mb-4 last:mb-0">
                          <div className="flex items-center gap-3 mb-2">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={userAvatars[reply.email]} />
                              <AvatarFallback>
                                {reply.created_by.charAt(0).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="text-sm font-medium">
                                {reply.created_by}
                              </p>
                              <p className="text-xs text-gray-500">
                                {reply.created_at}
                              </p>
                            </div>
                          </div>
                          <p className="text-gray-700 text-sm ml-11">
                            {reply.text}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
