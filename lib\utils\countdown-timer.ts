// lib/utils/countdown-timer.ts

export interface TimeObject {
  minutes: number
  seconds: number
}

export class CountDownTimer {
  duration: number
  granularity: number
  tickFns: ((minutes: number, seconds: number) => void)[]
  hardStop: boolean
  running: boolean
  timerInterval: NodeJS.Timeout | null

  constructor(duration: number, granularity: number = 1000) {
    this.duration = duration
    this.granularity = granularity
    this.tickFns = []
    this.hardStop = false
    this.running = false
    this.timerInterval = null
  }

  /**
   * Function to start our custom timer
   */
  start() {
    if (this.running) {
      return
    }

    this.running = true
    this.hardStop = false

    let diff: number
    let obj: TimeObject

    const start = Date.now()

    const timer = () => {
      diff = this.duration - (((Date.now() - start) / 1000) | 0)

      if (diff > 0) {
        if (this.hardStop) {
          if (this.timerInterval) {
            clearTimeout(this.timerInterval)
            this.timerInterval = null
          }
          return
        }
        this.timerInterval = setTimeout(timer, this.granularity)
      } else {
        diff = 0
        this.running = false
        if (this.timerInterval) {
          clearTimeout(this.timerInterval)
          this.timerInterval = null
        }
      }

      obj = CountDownTimer.parse(diff)
      this.tickFns.forEach((fn) => {
        fn.call(this, obj.minutes, obj.seconds)
      })
    }

    timer()
  }

  onTick(fn: (minutes: number, seconds: number) => void) {
    if (typeof fn === 'function') {
      this.tickFns.push(fn)
    }
    return this
  }

  expired() {
    return !this.running
  }

  static parse(seconds: number): TimeObject {
    return {
      minutes: (seconds / 60) | 0,
      seconds: seconds % 60 | 0
    }
  }

  stop() {
    this.hardStop = true
    this.running = false
    this.tickFns = []
    if (this.timerInterval) {
      clearTimeout(this.timerInterval)
      this.timerInterval = null
    }
  }
}
