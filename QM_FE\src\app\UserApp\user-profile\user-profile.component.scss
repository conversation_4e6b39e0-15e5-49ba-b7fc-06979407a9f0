// Added by guru<PERSON>
.Main-containt {
  width: 90%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin: 5em;
  margin-top: 1em;
  position: relative;
  z-index: 10;
  font: 17px "Montserrat", "sans-serif";

  .edit-profile {
    width: 90%;
    margin-top: 1em;
    margin-left: 5em;
    font-size: larger;
    font-style: normal;
    font-family: "Montserrat" "serif";
  }

  .left-sect {
    background-color: #fff;
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: flex-start;

    .left-bar {
      width: 20%;

      .user-options {
        margin-top: 2em;

        .option-link {
          cursor: pointer;
          padding: 0.5rem;
          text-align: center;
          border-radius: 5px;
          color: #000;
          background-color: #eee;
          margin-bottom: 20px;
          transition: all 0.3s ease-out;

          &:hover {
            color: #fff;
            background-color: #2c3e50;
            transition: all 0.2s ease-in;
          }
        }

        .selected {
          background-color: #aeb6bf;
        }
      }
    }

    .detail-bar {
      width: 70%;
      min-height: 70vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      border-left: 2px solid #aeb6bf;
      margin-bottom: 1rem;

      p,
      small {
        width: 90%;
      }

      small {
        margin-bottom: 1rem;
      }

      .user-avatar--wrap {
        padding-top: 2em;

        img.profile-avatar {
          height: 150px;
          width: 150px;
          border-radius: 50%;
        }

        button {
          position: relative;
          bottom: 45px;
          left: 100px;
          height: 70px;
          width: 70px;
          cursor: pointer;
          border: none;
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0);

          img {
            width: 100%;
            height: 100%;
          }
        }
      }

      form {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        .elem-custom-cta {
          display: flex;
          align-items: center;
        }

        .form-row--1 {
          display: flex;
          justify-content: space-between;
          width: 90%;
          margin-top: 1em;

          .form-elem {
            position: relative;
            width: 100%;
          }
          .form-input {
            width: 82.5%;
          }

          svg {
            position: absolute;
            top: 20%;
            left: 80%;
            width: 30px;
            height: 30px;
          }
        }

        .form-row--2 {
          display: flex;
          width: 90%;
          margin-top: 1em;

          .form-elem {
            width: 100%;

            input {
              width: 97.5%;
            }
          }

          i {
            font-style: normal;
            font-weight: bold;
            color: #0b6fb1;
          }
        }

        .form-row--2--btn {
          display: flex;
          width: 90%;
          margin-top: 1em;

          .form-elem-input {
            width: 87%;
          }
          .form-elem-btn {
            width: 10%;
            .cust-btn-small {
              width: 100%;
              height: 50px;
              background-color: #e88224;
              border: none;
              border-radius: 4px;
              color: #fff;
              padding: 8px;
              box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
            }
          }
          i {
            font-style: normal;
            font-weight: bold;
            color: #0b6fb1;
          }
        }
        .form-row--3 {
          display: flex;
          justify-content: space-between;
          width: 89%;
          margin-top: 1em;

          & .form-elem {
            width: 100%;
          }
        }

        input,
        select {
          display: block;
          height: 50px;
          width: 95%;
          padding: 3px 5px;
          border: solid 1.5px #707070;
          border-radius: 5px;
          transition: all 0.3s ease;

          &:focus {
            border: solid 1.5px #0b6fb1;
            transition: all 0.3s ease;
          }

          &:focus + .placeholder-text {
            top: -75px;
            font-size: 13px;
            transition: all 0.3s ease;
          }
        }

        .placeholder-text {
          position: relative;
          top: -56px;
          left: 10px;
          padding: 3px;
          font-size: 17px;
          background-color: #fff;
          transition: all 0.4s ease;
        }

        .custom-btn {
          width: 97.5%;
          height: 40px;
          background-color: #e88224;
          border: none;
          border-radius: 4px;
          color: #fff;
          // margin: 2em auto 3em auto;
          margin-top: 1rem;
          margin-bottom: 1rem;
          padding: 8px;
          box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
        }

        .custom-btn:active:after {
          transition: 0s;
          opacity: 0.7;
          clip-path: circle(0% at 0% 0%);
        }

        .custom-btn::after {
          content: "";
          display: block;
          position: relative;
          top: -32px;
          left: -8px;
          height: 40px;
          width: 150px;
          background-color: #e88224;
          opacity: 0;
          clip-path: circle(150% at 0% 0%);
          transition: all 0.4s ease-in;
        }

        .form-error--text {
          font-size: 80%;
          color: #dc3545;
        }

        .is-inv {
          background-color: #dc3545;
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .Main-containt {
    margin: 3em auto;

    .edit-profile {
      margin-left: 0;
    }

    .user-avatar--wrap {
      width: 50%;
    }

    .left-sect {
      width: 90%;
      display: flex;
      flex-direction: column;

      form {
        .form-row--1 {
          flex-direction: column;
          width: 100% !important;
          .form-elem {
            width: 100%;
            margin-top: 0.5em;

            input {
              width: 100%;
            }
          }
          .form-input {
            width: 100% !important;
          }
        }

        .form-row--2 {
          width: 100% !important;
          .form-elem {
            input {
              width: 100%;
            }
          }
        }

        .form-row--3 {
          width: 100% !important;
          flex-direction: column;

          .form-elem {
            margin-bottom: 1em;

            input,
            select {
              width: 100%;
            }
          }
        }

        .form-row--2--btn {
          width: 100% !important;
          .form-elem-input {
            width: 100% !important;
          }
          .form-elem-btn {
            width: 25% !important;
          }
        }
        .custom-btn {
          margin-top: 0rem !important;
        }
      }
      .left-bar {
        border-bottom: 2px solid #aeb6bf;
        width: 100%;
        .user-options {
          margin-top: 0.5em;
        }
      }
      .detail-bar {
        border-left: 0px;
        width: 100%;

        p,
        small {
          text-align: center;
        }
      }
    }
  }
}
