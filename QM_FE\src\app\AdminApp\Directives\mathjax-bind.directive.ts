import { Directive, ElementRef, Input, OnChanges, SimpleChanges, ViewChild, HostListener } from '@angular/core';

import { MathJaxDirective } from 'ngx-mathjax';

@Directive({
  selector: '[appMathjaxBind]'
})
export class MathjaxBindDirective {

  @Input('appMathjaxBind') expression: string;
  @ViewChild('math', { read: MathJaxDirective }) mathJax?: MathJaxDirective;

  constructor(private elem: ElementRef) { }

  @HostListener('ngModelChange', ['$event']) doStuff(value): void {
    const script = document.createElement('script');
    script.type = 'math/tex';
    script.innerHTML = '';
    script.innerHTML = this.expression;

    this.elem.nativeElement.appendChild(script);
    this.mathJax.MathJaxTypeset();
  }

}
