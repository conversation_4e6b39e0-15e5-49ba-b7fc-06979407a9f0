// types/competitive-paper-types.ts

export interface CompetitiveGroup {
  group_id: string
  group_name: string
}

export interface CompetitivePapers {
  paper_id: string
  paper_name: string
  group_id: string
  level: string
  status: string
  show_ans: string
  once_ans: string
  public: string
  created_at: string
  time_lim: string
  no_of_ques: number
}

export interface CompetitiveQuestion {
  paper_id: string
  question_no: string
  question_origin: string
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  opt_5: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: number
  options: string[]
}
