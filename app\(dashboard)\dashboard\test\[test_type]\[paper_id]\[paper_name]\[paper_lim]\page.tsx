import TestContainer from '@/components/test/test-container'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

interface TestPageProps {
  params: Promise<{
    test_type: string
    paper_id: string
    paper_name: string
    paper_lim: string
  }>
}

export default async function TestPage({ params }: TestPageProps) {
  const paramsData = await params
  const cookieStore = await cookies()
  const email = cookieStore.get('QMA_USR')?.value
  const token = cookieStore.get('QMA_TOK')?.value

  // Redirect to login if not authenticated
  if (!email || !token) {
    redirect('/user/login?redirect=/dashboard')
  }

  // Parse parameters
  const testType = parseInt(paramsData.test_type, 10)
  const paperId = paramsData.paper_id
  const paperName = decodeURIComponent(paramsData.paper_name)
  const paperLim = parseInt(paramsData.paper_lim, 10) / (1000 * 60) // Convert to minutes

  return (
    <main className="container mx-auto px-4 py-8 min-h-screen bg-gray-50">
      <TestContainer
        testType={testType}
        paperId={paperId}
        paperName={paperName}
        paperLim={paperLim}
        userEmail={email}
      />
    </main>
  )
}
