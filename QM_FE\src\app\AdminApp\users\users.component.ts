import {
  Component,
  OnInit,
  TemplateRef,
  OnDestroy,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';

import * as $ from 'jquery';

import { StatsService } from '../../Services/stats.service';
import { AdminViewService } from '../../Services/admin-view.service';
import { PaymentService } from '../../Services/payment.service';
import { UserService } from '../../Services/user.service';

import { NewUser } from '../../Models/NewUser';
import { UserLoginTrack } from 'src/app/Models/UserLoginTrack';

@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class UsersComponent implements OnInit, OnDestroy {
  @ViewChild('UserAccessTemplate') public uATemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('userDetsTemplate') public udTemplate: TemplateRef<any>;
  @ViewChild('userSuccessTemplate') public usTemplate: TemplateRef<any>;
  @ViewChild('userMapTemplate') public mapTemplate: TemplateRef<any>;
  @ViewChild('certificateTemplate') public certTemplate: TemplateRef<any>;
  @ViewChild('errTemplate') public eTemplate: TemplateRef<any>;

  public showIndicator = false;
  public regCount = '0';
  public purCount = '0';
  public wkCount = '0';

  public globalSettingPurchased = true;

  public globalPurchasedBoxText = '';

  public globalDisplayAllSelected = false;
  public globalDisplayPurchasedSelected = false;
  public globalDisplayLastWeekSelected = false;

  public usersList: NewUser[];
  public displayUsers: NewUser[];
  public loginTrack: UserLoginTrack[];
  public certificationTrack: [];

  public pageTrackNumber = 1;

  public selectedLat: number;
  public selectedLng: number;

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    class: 'access-modal',
  };

  public pagePer: number;

  public bulkGrant = false;
  public bulkGrantEmails = '';
  public grantAcessEmail = '';
  public grantAcessLevel = '';
  public grantAccessName = '';
  public grantUserNFMsg = false;
  public bulkGrantResults = [];

  public userSearchEmail = '';
  public searchedUser: NewUser;
  public searchedCertUser = {
    "email" : "",
    "name" : "",
    // "display_id" : "",
    "category" : null
  };
  public userDetailsEdit = false;
  public userSearchType = '';
  public errMsg = '';

  constructor(
    private statsService: StatsService,
    private adminViewService: AdminViewService,
    private paymentService: PaymentService,
    private userService: UserService,
    public modalService: BsModalService
  ) {}

  ngOnInit() {
    this.showIndicator = true;

    if ($(window).width() <= 440) {
      this.pagePer = 7;
    } else {
      this.pagePer = 15;
    }

    this.statsService.retStatsRegistered().subscribe(
      (response) => {
        this.regCount = JSON.parse(JSON.stringify(response))
          .text.toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        this.statsService.retStatsPurchased().subscribe(
          (responsePur) => {
            this.purCount = JSON.parse(JSON.stringify(responsePur))
              .text.toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            this.globalPurchasedBoxText = 'Total Purchases';

            this.statsService.retStatsLastWeek().subscribe(
              (respLastWk) => {
                this.wkCount = JSON.parse(JSON.stringify(respLastWk))
                  .text.toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                this.showIndicator = false;
              },
              (errorLastWk) => {
                this.showIndicator = false;
              }
            );
          },
          (errorPur) => {
            this.showIndicator = false;
          }
        );
      },
      (error) => {
        this.showIndicator = false;
      }
    );

    // this.getAllUsers();
  }

  getPurchasedUsers() {
    this.showIndicator = true;
    this.loginTrack = [];
    this.certificationTrack = [];
    if (this.globalSettingPurchased) {
      this.adminViewService.getAllPurchasedList(true).subscribe(
        (response) => {
          this.usersList = response;
          this.displayUsers = this.usersList.slice(0, this.pagePer);
          this.globalPurchasedBoxText = 'Total Purchases';
          this.globalDisplayPurchasedSelected = true;
          this.globalDisplayAllSelected = this.globalDisplayLastWeekSelected =
            false;
        },
        (error) => {
          this.showIndicator = false;
        }
      );

      this.statsService.retStatsPurchased().subscribe(
        (respStats) => {
          this.purCount = JSON.parse(JSON.stringify(respStats))
            .text.toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          this.showIndicator = false;
        },
        (errorStat) => {
          this.showIndicator = false;
        }
      );
    } else {
      this.adminViewService.getAllPurchasedList(false).subscribe(
        (response) => {
          this.usersList = response;
          this.displayUsers = this.usersList.slice(0, this.pagePer);
          this.globalPurchasedBoxText = 'Total Not Purchased';
          this.globalDisplayPurchasedSelected = true;
          this.globalDisplayAllSelected = this.globalDisplayLastWeekSelected =
            false;

          this.statsService.retStatsNotPurchased().subscribe(
            (respStats) => {
              this.purCount = JSON.parse(JSON.stringify(respStats))
                .text.toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              this.showIndicator = false;
            },
            (errorStat) => {
              this.showIndicator = false;
            }
          );
        },
        (error) => {
          this.showIndicator = false;
        }
      );
    }
  }

  getAllUsersOfLastWeek() {
    this.showIndicator = true;
    this.loginTrack = [];
    this.certificationTrack = [];
    this.adminViewService.getAllRegisteredLastWeek().subscribe(
      (response) => {
        this.usersList = response;
        this.displayUsers = this.usersList.slice(0, this.pagePer);
        this.globalDisplayLastWeekSelected = true;
        this.globalDisplayAllSelected = this.globalDisplayPurchasedSelected =
          false;

        this.showIndicator = false;
      },
      (error) => {
        this.showIndicator = false;
      }
    );
  }

  showPermissionBox() {
    this.bulkGrant = false;
    this.openModal(this.uATemplate);
  }

  showBulkGrant() {
    this.bulkGrant = true;
    this.openModal(this.uATemplate);
  }

  getAcess(isValid: boolean) {
    if (!isValid) {
      return;
    }

    this.paymentService.getUserAccess(this.grantAcessEmail).subscribe(
      (response) => {
        const respObj = JSON.parse(JSON.stringify(response));

        try {
          const level = parseInt(respObj.msg, 10);

          this.grantUserNFMsg = false;

          if (level === 1) {
            this.grantAcessLevel = 'Admin';
          } else if (level === 2) {
            this.grantAcessLevel = 'Purchased Basic';
          } else if (level === 4) {
            this.grantAcessLevel = 'Purchased Premium';
          } else if (level === 3) {
            this.grantAcessLevel = 'Registered';
          } else if (level === 5) {
            this.grantAcessLevel = 'Admin Read';
          }

          const selectedUser = this.usersList.find(
            (x) => x.email === this.grantAcessEmail
          );

          this.grantAccessName =
            selectedUser.f_name + ' ' + selectedUser.l_name;
        } catch (err) {
          console.warn('Website had an OOpsie!');
        }
      },
      (error) => {
        const respErr = JSON.parse(JSON.stringify(error));

        if (respErr.error.msg === 'not found') {
          this.grantAcessLevel = '';
          this.grantUserNFMsg = true;
        }
      }
    );
  }

  elevateToPurchased() {
    this.paymentService.grantUserAccess(this.grantAcessEmail).subscribe(
      (response) => {
        const respObj = JSON.parse(JSON.stringify(response));

        if (respObj.msg === 'granted') {
          this.modalRef.hide();
          this.openModal(this.sTemplate);
          this.getAcess(true);
          this.clearData();
        } else if (respObj.msg === 'not found') {
          this.grantUserNFMsg = true;
          this.clearData();
        }
      },
      (error) => {
        console.log('error :>> ', error);
        this.clearData();
      }
    );
  }

  elevateToPurchasedPremium() {
    this.paymentService.grantUserAccessPremium(this.grantAcessEmail).subscribe(
      (response) => {
        const respObj = JSON.parse(JSON.stringify(response));

        if (respObj.msg === 'granted') {
          this.modalRef.hide();
          this.openModal(this.sTemplate);
          this.getAcess(true);
          this.clearData();
        } else if (respObj.msg === 'not found') {
          this.grantUserNFMsg = true;
          this.clearData();
        }
      },
      (error) => {
        console.log('error :>> ', error);
        this.clearData();
      }
    );
  }

  revokeToRegistered() {
    this.paymentService.revokeUserAccess(this.grantAcessEmail).subscribe(
      (response) => {
        const respObj = JSON.parse(JSON.stringify(response));

        if (respObj.msg === 'granted') {
          this.modalRef.hide();
          this.openModal(this.sTemplate);
          this.getAcess(true);
          this.clearData();
        } else if (respObj.msg === 'not found') {
          this.grantUserNFMsg = true;
          this.clearData();
        }
      },
      (error) => {
        console.log('error :>> ', error);
      }
    );
  }

  updateBulkAcess(isValid: boolean) {
    if (!isValid) {
      return;
    }

    this.showIndicator = true;
    let emails = this.bulkGrantEmails.split(',');
    if (emails.length > 0) {
      emails = emails.map(x => x.trim());

      this.paymentService.grantBulkUserAccess(emails).subscribe(response => {
        const resp = JSON.parse(JSON.stringify(response));

        for (const obj of resp) {
          this.bulkGrantResults.push(obj);
        }
        this.modalRef.hide();
        this.openModal(this.usTemplate);
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
      });
    }
  }

  searchForUser(isValid: boolean) {
    if (!isValid) {
      return;
    }

    this.showIndicator = true;
    this.adminViewService.getUserDetails(this.userSearchEmail).subscribe(response => {
      this.searchedUser = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });

    this.openModal(this.udTemplate);
  }

  addNewCertificate() {
    this.searchedCertUser = {
      "email" : "",
      "name" : "",
      // "display_id" : "",
      "category" : null
    }
    this.openModal(this.certTemplate);
  }

  saveUSerDetails(isValid: boolean) {
    if (!isValid) {
      return;
    }

    this.showIndicator = true;
    this.userService
      .setUserProfileDetails(this.searchedUser.email, this.searchedUser)
      .subscribe(
        (response) => {
          this.getAllUsers('ByClick');
          this.showIndicator = false;

          this.modalRef.hide();
          this.openModal(this.usTemplate);
        },
        (error) => {
          this.showIndicator = false;
        }
      );
  }
  saveUSerCertDetails(isValid: boolean) {
    if (!isValid) {
      return;
    }
    this.showIndicator = true;
    const reqBody = {
      'email' : this.searchedCertUser['email'],
      // 'display_id' : this.searchedCertUser['display_id'],
      'name': this.searchedCertUser['name'],
    };
    
    this.userService
      .postCertificationDetail(reqBody,this.searchedCertUser['category'])
      .subscribe(
        (response) => {
          this.getCertificationData('ByCall');
          this.showIndicator = false;
          this.userSearchEmail = '';
          this.modalRef.hide();
          this.openModal(this.usTemplate);
        },
        (error) => {
          this.showIndicator = false;
          this.modalRef.hide();
          this.errMsg = error.error.msg;
          this.openModal(this.eTemplate);
        }
      );
  }

  getAllUsers(actionType) {
    this.showIndicator = true;
    this.loginTrack = [];
    this.certificationTrack = [];
    this.userSearchType = 'userDetail';

    if (actionType === 'ByClick') {
      this.pageTrackNumber = 1;
    }

    this.adminViewService.getAllRegisteredList(this.pageTrackNumber, this.pagePer).subscribe(
      (response) => {
        this.usersList = response;
        this.displayUsers = this.usersList.slice(0, this.pagePer);
        this.globalDisplayAllSelected = true;
        this.globalDisplayPurchasedSelected = false;
        this.globalDisplayLastWeekSelected = false;
        this.showIndicator = false;
      },
      (error) => {
        this.showIndicator = false;
      }
    );
  }

  getUserLoginData(actionType) {
    this.showIndicator = true;
    this.loginTrack = [];
    this.certificationTrack = [];
    this.usersList = [];
    if (actionType === 'ByClick') {
      this.pageTrackNumber = 1;
    }
    this.adminViewService.getUserLoginTrackData(this.pageTrackNumber).subscribe(
      (response) => {
        this.showIndicator = false;

        if (response.length > 0) {
          this.loginTrack = response;
        }
      },
      (error) => {
        this.showIndicator = false;
      }
    );
  }

  getCertificationData(actionType) {
    this.showIndicator = true;
    this.loginTrack = [];
    this.certificationTrack = [];
    this.usersList = [];
    this.userSearchType = 'certificate';
    if (actionType === 'ByClick') {
      this.pageTrackNumber = 1;
    }
    this.userService.getUserListForCertificate(this.pageTrackNumber).subscribe(
      (response) => {
        this.showIndicator = false;

        if (response.length > 0) {
          this.certificationTrack = this.fnSelectCategory(response);
        }
      },
      (error) => {
        this.showIndicator = false;
      }
    );
  }

  fnSelectCategory(category){
    for (var i=0; i < category.length; i++){
      if(category[i].eligible == 1)
        category[i].display_Category = "Excellence"
      if(category[i].eligible == 2)
        category[i].display_Category = "Achievement"
      if(category[i].eligible == 3)
        category[i].display_Category = "Complition"
    }
    return category;
  }

  navigatePageFirst(actionFrom) {
    this.pageTrackNumber = 1;

    if (actionFrom === 'Login') this.getUserLoginData('ByCall');
    else if (actionFrom === 'Cert') this.getCertificationData('ByCall');
    else if (actionFrom === 'User') this.getAllUsers('ByCall');
  }

  navigatePageRight(actionFrom) {
    this.pageTrackNumber += this.pagePer;

    if (actionFrom === 'Login') this.getUserLoginData('ByCall');
    else if (actionFrom === 'Cert')  this.getCertificationData('ByCall');
    else if (actionFrom === 'User') this.getAllUsers('ByCall');
  }

  navigatePageLeft(actionFrom) {
    this.pageTrackNumber -= this.pagePer;

    if (this.pageTrackNumber < 0) this.pageTrackNumber = 0;

    if (actionFrom === 'Login') this.getUserLoginData('ByCall');
    else if (actionFrom === 'Cert')  this.getCertificationData('ByCall');
    else if (actionFrom === 'User') this.getAllUsers('ByCall');
  }

  openMap(lat: number, lng: number) {
    this.selectedLat = lat;
    this.selectedLng = lng;

    this.openModal(this.mapTemplate);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  closeModal() {
    if (typeof this.modalRef !== 'undefined') {
      this.modalRef.hide();
      this.clearData();
    }
  }

  clearData() {
    this.grantAccessName = '';
    this.grantAcessEmail = '';
    this.grantAcessLevel = '';
  }

  ngOnDestroy() {
    this.closeModal();
  }
}
