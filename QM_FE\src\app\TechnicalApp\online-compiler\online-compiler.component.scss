.c-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  max-width: 1200px;
  min-height: 50vh;
  padding: 1rem;
  margin: 2rem auto;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 5px 30px 0 rgba(125, 121, 125, 0.1);

  .compiler-box {
    width: 80%;

    .code-editor {
      border: 1px solid #eeeeee;
    }

    .editor-container {
      width: 100%;
      height: 400px;

      .monaco-editor {
        height: 50%;
        border: 1px solid #000;
      }
    }

    .editor-config {
      background-color: #3498DB;
      color: #fff;
      margin-top: -10px;
      padding: 7px 15px 5px 15px;

      label {
        font-size: 12px;
        margin-right: 10px;
      }

      select {
        cursor: pointer;
        margin-right: 1rem;
        background-color: #3498DB;
        color: #fff;
        font-size: 12px;
        border: none;

        &:hover {
          background-color: #2E86C1;
        }

        option {
          background-color: #2E86C1;
          font-size: 12px;
          color: #fff;
        }
      }
    }

    .exec-btn {
      padding: 10px 5px;
      margin-top: 1rem;
      border: none;
      background-color: #82E0AA;
      box-shadow: 3px 5px 1px 0 rgba(121, 125, 124, 0.2);
      transform: scale(1);
      transition: all 0.3s ease-out;

      &:hover {
        background-color: #58D68D;
        transform: scale(1.05);
        transition: all 0.1s ease-in;
      }
    }

    p {
      margin-top: 1rem;

      b {
        padding: 3px;
        border-radius: 5px;
      }

      .lots {
        background-color: #A3E4D7;
      }

      .some {
        background-color: #F9E79F;
      }

      .few {
        background-color: #F5B7B1;
      }
    }

    h5:nth-of-type(2) {
      margin-top: 2rem;
    }

    .input-box {
      height: 200px;
      border: 1px solid #eee;
    }

    h5:last-of-type {
      margin-top: 3rem;
    }

    .output-box {
      height: 300px;
      border: 1px solid #eee;

      .perf-metrics {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 5px 10px;
        margin-bottom: 1rem;
        background-color: #EAECEE;

        small {
          margin-right: 0.5rem;
        }
      }

      .success {
        color: #fff;
        background-color: #2ECC71;
      }

      .error {
        background-color: #EC7063;
      }

      p {
        margin-left: 15px;
      }
    }
  }
}

@media (max-width: 440px) {
  .c-wrap {

    .compiler-box {
      width: 100%;
    }
  }
}