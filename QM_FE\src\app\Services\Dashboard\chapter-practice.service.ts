import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { SuperGroup } from '../../Models/Dashboard/Chapters/SuperGroup';
import { Group } from 'src/app/Models/Dashboard/Chapters/Group';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';

@Injectable({
  providedIn: 'root'
})
export class ChapterPracticeService {

  private superGroupUrl = 'https://api.quantmasters.in/test/progression/practice/super-groups';
  private groupUrl      = 'https://api.quantmasters.in/test/progression/practice/groups';
  private groupPaperUrl = 'https://api.quantmasters.in/test/progression/practice/group';

  private adminPaperUrl = 'https://api.quantmasters.in/admin/paper/progression/practice/upload/test';
  private adminQuesUrl  = 'https://api.quantmasters.in/admin/paper/progression/practice/upload/question';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getSuperGroups(): Observable<SuperGroup[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<SuperGroup[]>(this.superGroupUrl, httpOps);
  }

  getGroups(superGrpId: string): Observable<Group[]> {
    this.setSecurityToken();

    const groupUrl = this.groupUrl + '/' + superGrpId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<Group[]>(groupUrl, httpOps);
  }

  getPapersOfAGroup(groupId: string): Observable<ChapterPaper[]> {
    this.setSecurityToken();

    const groupPaperUrl = this.groupPaperUrl + '/' + groupId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<ChapterPaper[]>(groupPaperUrl, httpOps);
  }

  updatePaper(paper: ChapterPaper): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.adminPaperUrl, paper, httpOps);
  }

  updateQuestion(question: Object): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(this.adminQuesUrl, question, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
