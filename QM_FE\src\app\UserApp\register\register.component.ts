import { Component, OnInit, ViewChild, HostListener, TemplateRef, ChangeDetectorRef, ElementRef } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { Subscription, combineLatest } from 'rxjs';

import { RegisterService } from '../../Services/register.service';

import { NewUser } from '../../Models/NewUser';
import { BranchCourse } from '../../Models/Static/BranchCourse';

declare let gtag: Function;

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent implements OnInit {

  public bsValue = new Date();
  public modalRef: BsModalRef;
  public subscriptions: Subscription[] = [];

  public regError: string;
  public conf_password: string;

  public showIndicator = false;

  public newUser: NewUser = {
    f_name: null,
    l_name: '',
    dob: null,
    phone_no: null,
    email: null,
    password: null,
    inst_name: null,
    section: null,
    qual: null,
    branch: null,
    usn: '',
    yop: null,
    subscribed: true
  };

  public branches: BranchCourse;

  public displayBranches: { bName: string, subName: string[] } = { bName: 'Branch', subName: [] };

  public bsConfig = {
    dateInputFormat: 'DD/MM/YYYY',
    containerClass: 'theme-dark-blue',
  };

  @ViewChild(BsDatepickerDirective) datepicker: BsDatepickerDirective;
  @ViewChild('successTemplate') public template: TemplateRef<any>;
  @ViewChild('errorTemplate') public eTemplate: TemplateRef<any>;

  @ViewChild('passwordInp') password: ElementRef | undefined;

  constructor(public router: Router,
              public registerService: RegisterService,
              public modalService: BsModalService,
              public changeDetection: ChangeDetectorRef) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    this.branches = new BranchCourse();

    const $passwordShow = document.getElementById('passwordShow') as HTMLElement;

    $passwordShow.addEventListener('mousedown', (e) => {
      if (this.password.nativeElement.type === 'password') {
        this.password.nativeElement.type = 'text';
        $passwordShow.classList.add('focused');
      }
    });

    $passwordShow.addEventListener('touchstart', (e) => {
      if (this.password.nativeElement.type === 'password') {
        this.password.nativeElement.type = 'text';
        $passwordShow.classList.add('focused');
      }
    });

    $passwordShow.addEventListener('mouseup', (e) => {
      if (this.password.nativeElement.type === 'text') {
        this.password.nativeElement.type = 'password';
        $passwordShow.classList.remove('focused');
      }
    });

    $passwordShow.addEventListener('touchend', (e) => {
      if (this.password.nativeElement.type === 'text') {
        this.password.nativeElement.type = 'password';
        $passwordShow.classList.remove('focused');
      }
    });
  }

  filterSelectedBranch(value: string) {
    this.displayBranches = this.branches.data.find(x => x.bName === value);
  }

  register(isValid: boolean) {
    this.showIndicator = true;

    if (!isValid) {
      this.showIndicator = false;
      return;
    }

    const dob = new Date(this.newUser.dob);
    const month = dob.getMonth() < 9 ? '0' + (dob.getMonth() + 1) : (dob.getMonth() + 1);
    const date = dob.getDate() < 10 ? '0' + dob.getDate() : dob.getDate();
    const dobStr = [dob.getFullYear(), month , date].join('-');

    this.newUser.dob = dobStr;
    this.registerService.registerUser(this.newUser).subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      if (respText.text === 'User Registered!') {
        sessionStorage.setItem('logged', 'true');
        sessionStorage.setItem('QUser', respText.user);
        sessionStorage.setItem('QMSESS_ID', respText.sess);
        sessionStorage.setItem('QMA_TOK', respText.token);
        sessionStorage.setItem('QMail', respText.email);

        this.openModal(this.template);
      }

      this.showIndicator = false;
    }, error => {
      if (error.error === 'Email already Registered!') {
        this.regError = error.error;
        this.openModal(this.eTemplate);
      } else if (error.error === 'SWW') {
        this.regError = 'Something went wrong!';
        this.openModal(this.eTemplate);
      }

      this.showIndicator = false;
    });
  }

  openModal(template: TemplateRef<any>) {
    const _combine = combineLatest(
      this.modalService.onHidden
    ).subscribe(() => this.changeDetection.markForCheck());

    this.subscriptions.push(
      this.modalService.onHidden.subscribe(() => {
        this.router.navigate(['/home']);
        this.unsubscribe();
      })
    );

    this.subscriptions.push(_combine);

    this.modalRef = this.modalService.show(template);
  }

  unsubscribe() {
    this.subscriptions.forEach((subscription: Subscription) => {
      subscription.unsubscribe();
    });

    this.subscriptions = [];
  }

  @HostListener('window:scroll')
  onScrollEvent() {
    this.datepicker.hide();
  }
}
