// components/student-exp/tracking-service.ts

import axios, { AxiosResponse } from 'axios'

// Type definition for VideoUsage similar to the Angular model
export interface VideoUsage {
  videoId: string
  userId: string
  userName: string
  email: string
  videoName: string
  startTime: Date
  endTime: Date
  watchDuration: number
  totalDuration: number
  percentageWatched: number
}

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  videoUsageTrackingUrl: `${API_BASE_URL}/track/video/usage`
}

/**
 * Get JWT token from localStorage
 */
const getJwtToken = (): string => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('QMA_TOK') || ''
  }
  return ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class TrackingService {
  /**
   * Submit video tracking record
   */
  static async submitVideoTrackRecord(
    trackingData: VideoUsage
  ): Promise<string> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<string> = await axios.post(
        ENDPOINTS.videoUsageTrackingUrl,
        trackingData,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error submitting video tracking data:', error)
      throw error
    }
  }

  /**
   * Get session data for a specific video
   */
  static async getVideoSessionData(
    videoId: string,
    userId: string
  ): Promise<any> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.videoUsageTrackingUrl}/${videoId}/user/${userId}`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching session data for video ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get overall user video usage statistics
   */
  static async getUserVideoStats(userId: string): Promise<any> {
    const token = getJwtToken()
    const url = `${API_BASE_URL}/track/user/${userId}/stats`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching user video statistics:`, error)
      throw error
    }
  }

  /**
   * Submit batch tracking data (for offline sync)
   */
  static async submitBatchTrackingData(batchData: VideoUsage[]): Promise<any> {
    const token = getJwtToken()
    const url = `${API_BASE_URL}/track/video/batch`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        { records: batchData },
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error submitting batch tracking data:', error)
      throw error
    }
  }

  /**
   * Track engagement events (play, pause, seek, etc.)
   */
  static async trackEngagementEvent(eventData: {
    videoId: string
    userId: string
    eventType: string
    timestamp: Date
    position: number
  }): Promise<any> {
    const token = getJwtToken()
    const url = `${API_BASE_URL}/track/video/event`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        eventData,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error tracking engagement event:', error)
      throw error
    }
  }
}
