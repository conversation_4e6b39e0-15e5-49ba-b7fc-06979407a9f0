// lib/server/chapter-practice-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { SuperGroup, Group, ChapterPaper } from '@/types/chapter-practice-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  superGroupUrl: `${API_BASE_URL}/test/progression/practice/super-groups`,
  groupUrl: `${API_BASE_URL}/test/progression/practice/groups`,
  groupPaperUrl: `${API_BASE_URL}/test/progression/practice/group`,
  adminPaperUrl: `${API_BASE_URL}/admin/paper/progression/practice/upload/test`,
  adminQuesUrl: `${API_BASE_URL}/admin/paper/progression/practice/upload/question`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerChapterPracticeService {
  /**
   * Get all super groups
   */
  static async getSuperGroups(): Promise<SuperGroup[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.superGroupUrl,
        createAuthHeaders(token)
      )

      // Filter out specific super group as in the Angular code
      const superGroups = response.data as SuperGroup[]
      return superGroups.filter(
        (group) =>
          group.super_group_id !== '8e3208cb-fae1-333f-9ff7-3e63044f6126'
      )
    } catch (error) {
      console.error('Error fetching super groups:', error)
      throw error
    }
  }

  /**
   * Get groups for a super group
   */
  static async getGroups(superGrpId: string): Promise<Group[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.groupUrl}/${superGrpId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching groups:', error)
      throw error
    }
  }

  /**
   * Get papers for a group
   */
  static async getPapersOfAGroup(groupId: string): Promise<ChapterPaper[]> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.groupPaperUrl}/${groupId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching papers:', error)
      throw error
    }
  }

  /**
   * Update paper
   */
  static async updatePaper(paper: ChapterPaper): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.put(
        ENDPOINTS.adminPaperUrl,
        paper,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error updating paper:', error)
      throw error
    }
  }

  /**
   * Update question
   */
  static async updateQuestion(question: object): Promise<string> {
    if (!checkServerSideLoginStatus()) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.put(
        ENDPOINTS.adminQuesUrl,
        question,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error updating question:', error)
      throw error
    }
  }
}
