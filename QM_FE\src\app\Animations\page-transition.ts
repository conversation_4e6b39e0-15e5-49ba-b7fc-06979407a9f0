import { trigger, query, style, animate, transition, animateChild, group } from '@angular/animations';

export const pageTransitionAnimation =
  trigger('routerAnimations', [
    transition('LeftPage => RightPage', [
      style({ position: 'relative' }),
      query(':enter, :leave', [
        style({
          position: 'absolute',
          left: 0,
          width: '100%',
          opacity: 1
        }),
      ]),
      query(':enter', [
        style({ transform: 'translateX(5%)', opacity: 0 })
      ]),
      query(':leave', animateChild()),
      group([
        query(':leave', [
          animate('600ms ease', style({ transform: 'translateX(-5%)', opacity: 0 }))
        ]),
        query(':enter', [
          animate('600ms ease', style({ transform: 'translateX(0%)', opacity: 1 }))
        ])
      ]),
      query(':enter', animateChild()),
    ]),
    transition('RightPage => LeftPage', [
      style({ position: 'relative' }),
      query(':enter, :leave', [
        style({
          position: 'absolute',
          left: 0,
          width: '100%',
          opacity: 1
        }),
      ]),
      query(':enter', [
        style({ transform: 'translateX(-10%)', opacity: 1 })
      ]),
      query(':leave', animateChild()),
      group([
        query(':leave', [
          animate('500ms ease', style({ transform: 'translateX(10%)', opacity: 0 })),
        ]),
        query(':enter', [
          animate('500ms ease', style({ transform: 'translateX(0%)', opacity: 1 }))
        ])
      ]),
      query(':enter', animateChild()),
    ])
  ]);
