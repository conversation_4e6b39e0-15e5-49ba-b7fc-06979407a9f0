.footer {
    width: 100%;
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 2fr;
    padding: 0 10em;
    margin-bottom: 6em;

    .ftr-sect {
        a {
            margin-left: 25px;
            margin-bottom: 3em;

            &:first-of-type {
                margin-left: 0;
            }

            img {
                height: 40px;
                width: 40px;
            }
        }

        p:first-of-type {
            font-weight: bold;
        }

        div {
            margin-top: 3em;
            color: #707070;
        }
    }

    .ftr-sect--2 {
        justify-self: center;
        
        p {
            margin-bottom: 2em;
            color: #707070;
        }

        p:first-of-type {
            font-weight: bold;
            color: #000;
        }

        &:nth-of-type(4) {
            p:nth-of-type(5) {
                margin-top: 4em;
                margin-bottom: 1em;
                font-weight: bold;
                color: #000;
            }
        }

        &:last-of-type {
            margin-left: 6em;
        }

        a {
            text-decoration: none;
            color: #707070;
        }
    }

    form {
        input {
            height: 40px;
            padding: 3px 5px;
            border: solid 3px #0B6FB1;
            border-radius: 5px;
            margin-bottom: 1.5em;
            
            &::placeholder {
                opacity: 0;
            }

            &:focus + .placeholder-text {
                top: -85px;
                font-size: 13px;
                transition: all 0.3s ease;
            }
        }

        .placeholder-text {
            position: relative;
            top: -66px;
            left: 10px;
            padding: 3px;
            font-size: 17px;
            background-color: #fff;
            transition: all 0.4s ease;
        }

        button {
            height: 40px;
            padding: 5px 15px;
            color: #fff;
            background-color: #0B6FB1;
            border: none;
            border-radius: 3px;
            transition: all 0.4s ease;

            &:hover {
                background-color: #E88224;
                transition: all 0.4s ease;
            }
        }
    }
}

@media (max-width: 440px) {
    .footer {
        grid-template-columns: 1fr;
        justify-items: center;
        padding: 0 2em;

        .ftr-sect {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 1em;

            a {
                margin: 0 0 2em 0;

                &:last-of-type {
                    margin: 0;
                }
            }

            p,
            div {
                text-align: center;
            }
        }

        .ftr-sect--2 {
            margin: 1em 0;

            &:last-of-type {
                margin-left: 0;
            }

            p {
                text-align: center;
            }
        }

        form {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .placeholder-text {
                top: -30px;
            }

            .inp-box {
                input {
                    width: 100%;
                    margin: 0;

                    &:focus + .placeholder-text {
                        top: -55px;
                        font-size: 13px;
                        transition: all 0.3s ease;
                    }
                }
            }
        }
    }
}