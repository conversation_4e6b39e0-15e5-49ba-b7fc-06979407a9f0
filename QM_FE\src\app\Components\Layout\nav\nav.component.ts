import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

import { LoginService } from '../../../Services/login.service';

import * as $ from 'jquery';

@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html',
  styleUrls: ['./nav.component.scss']
})
export class NavComponent implements OnInit {

  public username: string;
  public logged: string;

  constructor(public router: Router,
              public loginService: LoginService) { }

  ngOnInit() {
    let that = this;
    setTimeout(function(){
      if (sessionStorage.getItem('QUser') && sessionStorage.getItem('logged')) {
        that.username = sessionStorage.getItem('QUser');
        that.logged   = sessionStorage.getItem('logged');
      }
    },1000);
  }

  logout() {
    this.loginService.logoutUser(sessionStorage.getItem('QMail')).subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      if (respText.text === 'Logged Out') {
        sessionStorage.clear();
        this.router.navigate(['/user/login']);
      }
    }, error => {});
  }

  switchNav() {
    $( '.mob-nav' ).toggleClass('is-open');
  }

  switchSubNav(num: number) {

    switch (num) {
      case 1:
        $( '.sub-nav--links--2' ).toggleClass('is-sub--open');
        break;
      case 2:
        $( '.sub-nav--links--1' ).toggleClass('is-sub--open');
        break;
      case 3:
        $( '.sub-nav--links' ).toggleClass('is-sub--open');
        break;
      case 4:
        $( '.sub-nav--links--4' ).toggleClass('is-sub--open');
        break;
    }
  }
}
