import { useState, useRef } from 'react'
import { Upload, Download, AlertCircle, CheckCircle, X } from 'lucide-react'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import LoadingIndicator from '@/components/shared/indicator'
import {
  bulkUploadQuestions,
  generateExcelTemplate,
  type BulkUploadResult,
  type ValidationError
} from '@/lib/server-actions/bulk-upload'

interface BulkUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  paperId: string
  paperType: number
  onUploadComplete: (result: BulkUploadResult) => void
}

export function BulkUploadDialog({
  open,
  onOpenChange,
  paperId,
  paperType,
  onUploadComplete
}: BulkUploadDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>(
    []
  )
  const [showErrors, setShowErrors] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        toast.error('Please select an Excel file (.xlsx or .xls)')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB')
        return
      }

      setSelectedFile(file)
      setValidationErrors([])
      setShowErrors(false)
    }
  }

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first')
      return
    }

    setUploading(true)
    setUploadProgress(0)

    try {
      // Convert file to ArrayBuffer
      const fileBuffer = await selectedFile.arrayBuffer()

      // Start upload with progress simulation
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => Math.min(prev + 10, 90))
      }, 200)

      // Perform bulk upload
      const result = await bulkUploadQuestions(paperId, paperType, fileBuffer)

      clearInterval(progressInterval)
      setUploadProgress(100)

      // Handle result
      if (result.success) {
        toast.success(result.message)
        onUploadComplete(result)
        handleClose()
      } else {
        setValidationErrors(result.errors)
        setShowErrors(true)
        toast.error(result.message)
      }
    } catch (error) {
      toast.error('Upload failed. Please try again.')
      console.error('Bulk upload error:', error)
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const handleDownloadTemplate = async () => {
    try {
      const templateBuffer = await generateExcelTemplate()
      const blob = new Blob([templateBuffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })

      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'questions-template.xlsx'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      toast.success('Template downloaded successfully')
    } catch (error) {
      toast.error('Failed to download template')
      console.error('Template download error:', error)
    }
  }

  const handleClose = () => {
    setSelectedFile(null)
    setValidationErrors([])
    setShowErrors(false)
    setUploadProgress(0)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    onOpenChange(false)
  }

  const removeFile = () => {
    setSelectedFile(null)
    setValidationErrors([])
    setShowErrors(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <Dialog open={open} onOpenChange={!uploading ? onOpenChange : undefined}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Bulk Upload Questions
          </DialogTitle>
          <DialogDescription>
            Upload multiple questions at once using an Excel file. Download the
            template to get started.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template Section */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Step 1: Download Template
            </Label>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={handleDownloadTemplate}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download Excel Template
              </Button>
              <span className="text-sm text-muted-foreground">
                Use this template to format your questions correctly
              </span>
            </div>
          </div>

          {/* Upload File Section */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">
              Step 2: Upload Your File
            </Label>
            <div className="space-y-3">
              <Input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                disabled={uploading}
                className="cursor-pointer"
              />

              {selectedFile && (
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium">
                      {selectedFile.name}
                    </span>
                    <span className="text-xs text-gray-500">
                      ({Math.round(selectedFile.size / 1024)} KB)
                    </span>
                  </div>
                  {!uploading && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                      className="h-6 w-6 p-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Uploading...</Label>
                <span className="text-sm text-muted-foreground">
                  {uploadProgress}%
                </span>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {/* Validation Errors */}
          {showErrors && validationErrors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>
                Validation Errors ({validationErrors.length})
              </AlertTitle>
              <AlertDescription>
                <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
                  {validationErrors.slice(0, 10).map((error, index) => (
                    <div key={index} className="text-xs">
                      Row {error.row}, {error.field}: {error.message}
                    </div>
                  ))}
                  {validationErrors.length > 10 && (
                    <div className="text-xs text-muted-foreground">
                      ...and {validationErrors.length - 10} more errors
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Instructions */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important Instructions</AlertTitle>
            <AlertDescription className="text-sm space-y-1">
              <div>• Use the provided template with exact column headers</div>
              <div>• All questions must have at least 4 options</div>
              <div>• Correct option should be a number (1-5)</div>
              <div>
                • Option 5 is optional unless it&apos;s the correct answer
              </div>
              <div>• Maximum file size: 5MB</div>
              <div>• Supported formats: .xlsx, .xls</div>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={uploading}>
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
            className="flex items-center gap-2"
          >
            {uploading && <LoadingIndicator isLoading={uploading} />}
            {uploading ? 'Uploading...' : 'Upload Questions'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
