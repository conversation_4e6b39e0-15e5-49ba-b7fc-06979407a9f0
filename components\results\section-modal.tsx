'use client'

import React from 'react'
import { SectionMarks } from '@/types/results-types'
import { Button } from '@/components/ui/button'
import { X } from 'lucide-react'

interface SectionModalProps {
  isOpen: boolean
  onClose: () => void
  sectionMarks: SectionMarks[]
  paperName: string
  answerDate: string
}

export default function SectionModal({
  isOpen,
  onClose,
  sectionMarks,
  paperName,
  answerDate
}: SectionModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="modal-header flex justify-between items-center p-6 border-b border-gray-200">
          <h4 className="text-xl font-semibold text-gray-700">
            {paperName}: Section-Wise Breakdown
          </h4>
          <Button
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="p-2 hover:bg-gray-100 rounded-full"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Modal Body */}
        <div className="modal-body p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <h6 className="text-right text-gray-600 mb-4">
            Answered on: {answerDate}
          </h6>

          {sectionMarks.length > 0 ? (
            <div className="marks-table">
              {/* Table Headers */}
              <div className="headers grid grid-cols-3 gap-4 p-4 bg-blue-600 text-white font-bold rounded-t-lg">
                <p className="text-center">Section Name</p>
                <p className="text-center">Marks</p>
                <p className="text-center">Percentage</p>
              </div>

              {/* Table Content */}
              <div className="content-rows">
                {sectionMarks.map((marks, index) => (
                  <div
                    key={index}
                    className={`
                      content-row grid grid-cols-3 gap-4 p-4 text-center
                      ${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'}
                    `}
                  >
                    <p className="text-gray-800 font-medium">
                      {marks.section_name}
                    </p>
                    <p className="text-gray-700">{marks.marks}</p>
                    <p className="text-blue-600 font-medium">
                      {marks.percentage}%
                    </p>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-600">
                No section-wise data available for this paper.
              </p>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="modal-footer p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <Button onClick={onClose} variant="outline" className="px-6">
              Close
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
