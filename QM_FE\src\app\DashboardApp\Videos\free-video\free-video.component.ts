import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { StreamingService } from 'src/app/Services/streaming.service';

import { StudentVideoList } from '../../../Models/Streaming/StudentVideoList';

declare let gtag: Function;

@Component({
  selector: 'app-free-video',
  templateUrl: './free-video.component.html',
  styleUrls: ['./free-video.component.scss']
})
export class FreeVideoComponent implements OnInit {

  public showIndicator = false;

  public videoList: StudentVideoList[];

  constructor(public router: Router,
    public activatedRoute: ActivatedRoute,
    public streamingService: StreamingService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.streamingService.getTrialVideoList().subscribe(response => {
      that.videoList = response;

      for (const video of this.videoList) {
        video.thumbnail = 'https://quantmasters.in/' + video.thumbnail;
      }

      that.showIndicator = false;
    }, error => {

    });
  }

  startStream(videoId: string, videoType: number) {
    // navigate
    this.router.navigate(['stream', videoId, videoType], { relativeTo: this.activatedRoute });
  }
}

