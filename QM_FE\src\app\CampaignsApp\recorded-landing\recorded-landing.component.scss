.landing-2-wrap {
  --clr-blue-primary: #0A6FB0;
  --clr-ylw-primary: #FBBC05;
  --clr-ylw-secondary: #F59920;
  --clr-grey-1: #707070;
  --clr-grey-2: #686868;

  position: relative;
  width: 100%;
  max-width: 1220px;
  height: 100%;
  margin: auto;
  font: normal normal 16px 'Product Sans', sans-serif;

  .logo-text {
    width: 20%;
    height: 80px;

    a {
      height: 40px;
      color: #0B6FB1;
      text-decoration: none;
      position: relative;
      font: bold normal 1.5rem 'Product Sans', sans-serif;;
      z-index: 2;

      img {
        height: 100%;
        width: 35px;
        margin-bottom: 8px;
      }
    }
  }

  .mode-switch {
    position: absolute;
    top: 1rem;
    right: 0;
    width: 10%;
    height: 30px;

    label, .toggle {
      height: 2rem;
      border-radius: 100px;
    }

    label {
      width: 120px;
      background-color: rgba(0,0,0,.1);
      border-radius: 100px;
      position: relative;
      cursor: pointer;
    }

    .toggle {
      position: absolute;
      width: 50%;
      background-color: #F49820;
      box-shadow: 0 2px 15px rgba(0,0,0,.15);
      transition: transform .3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .names {
      font-size: 90%;
      font-weight: bolder;
      width: 100%;
      height: 100%;
      position: absolute;
      display: flex;
      justify-content: space-around;
      align-items: center;
      user-select: none;

      p {
        margin: 0;
      }
    }
  }

  .heading {
    text-align: center;
    font-family: 'Lato', sans-serif;
    font-weight: 700;
    font-size: 1.7rem;
    margin-bottom: 2rem;
  }

  // Marked for removal
  .bouncy-2 {
    animation: bouncy 5s infinite linear;
    position: relative;

    &:last-of-type {
      animation-direction: reverse;
    }
  }

  // Marked for removal
  @keyframes bouncy {
    0% {
      top: 0em
    }
    40% {
      top: 0em
    }
    43% {
      top: -0.9em
    }
    46% {
      top: 0em
    }
    48% {
      top: -0.4em
    }
    50% {
      top: 0em
    }
    100% {
      top: 0em;
    }
  }

  .sect-1 {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;

    .sect-left {
      width: 50%;

      h2 {
        font-size: 3rem;
        color: var(--clr-blue-primary);
        margin-bottom: 0;
        margin-top: 1rem;
        font-weight: 900;

        span {
          background: #FF0000;  /* fallback for old browsers */
          background: -webkit-linear-gradient(to right, #FF0000, #D31027);  /* Chrome 10-25, Safari 5.1-6 */
          background: linear-gradient(to right, #FF0000, #D31027); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
  
      h3 {
        font-size: 2.5rem;
        color: #565656;
        margin-bottom: 0;
        font-weight: 900;
      }

      .extra-info {

        h4 {
          font-size: 2rem;
          color: var(--clr-ylw-primary);
          margin-top: 2rem;
          margin-bottom: 0;
          font-weight: 900;
        }

        .extra-info--2 {
          display: flex;
          margin-top: 1rem;

          .sect-card {
            display: flex;
            justify-content: center;
            width: 78.5%;
            padding: 0.6rem 1rem;
            color: #F2E205;
            background-color: #270AA8;
            box-shadow: 0px 5px 15px #0000003f;
            border-radius: 1rem;

            p {
              margin: 0;
              font-size: 1.3rem;
              font-weight: 900;
            }
          }

          .card-spl {
            color: #fff;
            background-color: rgb(241, 139, 80);
          }
        }

        .google-info {
          display: flex;
          align-items: center;
          max-width: 30rem;
          padding: 1.4rem;
          margin-top: 1.5rem;
          border-radius: 1rem;
          box-shadow: 0 5px 15px #0000002c;
          background-color: #fff;

          p {
            margin-bottom: 0;
            margin-left: 1rem;
            font-size: 2.562rem;
            color: var(--clr-ylw-primary);

            span {
              font-size: 1.812rem;
              color: var(--clr-grey-1);
            }

            small {
              margin-left: 0.25rem;
              font-size: 1rem;
              color: var(--clr-grey-1);
            }
          }

          img {
            height: 50px;
            width: 221px;
          }
        }

        .upcoming-info {
          margin-top: 2.5rem;

          p {
            margin-bottom: 0;
            font-size: 1.5rem;
            font-weight: 900;
            color: var(--clr-blue-primary);

            b {
              display: block;
              color: var(--clr-ylw-primary);
            }
          }

          .info-card {
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding: 1.4rem;
            margin-top: 0.5rem;
            box-shadow: 0px 0px 18px #0000003f;
            border-radius: 1em;
            opacity: 1;
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);

            p {
              text-align: center;
              font-size: 1rem;
              color: var(--clr-grey-2);
            }
          }
        }
      }
    }

    .sect-right {
      display: grid;
      place-items: center;
      position: relative;

      &::before {
        position: absolute;
        content: '';
        z-index: -1;
        background: no-repeat 120% / 85% url('../../../assets/icons/workshop/sect-1bg-right.svg');
        background-clip: border-box;
        height: 570px;
        width: 100%;
      }
      
      img {
        // Temp styles for sanity
        height: 70%;
      }
    }
  }

  .shape-1-left {
    position: absolute;
    top: 7%;
    left: -48%;
    z-index: -1;
  }

  .sect-2 {
    max-width: 1440px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 0.5em;
    position: relative;
    margin-bottom: 2em;

    .sect-card {
      padding: 1rem 2rem;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, .05) 0% 0% no-repeat padding-box;
      box-shadow: 0px 5px 15px #0000003f;
      border-radius: 15px;
      backdrop-filter: blur(28px);
      -webkit-backdrop-filter: blur(28px);

      img {
        max-height: 3.18rem;
        max-width: 10.625rem;
      }

      p {
        text-align: center;
        margin-bottom: 0;
        margin-top: 1rem;
        font-size: 1.3rem;
        font-weight: 700;
        color: #1ABC9C;

        span {
          font-size: 1.7rem;
          font-weight: 900;
          color: #0E6655;
        }
      }
    }

    .blob-3 {
      position: absolute;
      right: -3%;
      top: -30%;
      z-index: -1;
    }
  }

  .sect-3 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 5em;
    position: relative;

    &:nth-of-type(4) {
      margin-top: 8em;
    }

    .sect-heading {
      font-size: 2.5rem;
      font-weight: 900;
      color: var(--clr-blue-primary);
      text-align: center;
      margin-bottom: 2rem;
    }

    .sect-sub-heading {
      color: var(--clr-grey-1);
      text-align: center;
      width: 80%;
      margin-bottom: 2rem;
    }

    .sect-sub-heading--large {
      font-size: 1.875rem;
      font-weight: 900;
      color: var(--clr-grey-1);
      text-align: center;
    }

    .sect-cards {
      width: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      background: no-repeat center / contain url('../../../assets/icons/workshop/sect-3-center.png');

      .sect-card {
        display: flex;
        justify-content: center;
        width: 80%;
        padding: 0.6rem 1rem;
        margin-bottom: 1rem;
        background-color: #fff;
        background: rgba(255, 255, 255, .05) 0% 0% no-repeat padding-box;
        box-shadow: 0px 5px 15px #0000003f;
        border-radius: 15px;
        backdrop-filter: blur(28px);
        -webkit-backdrop-filter: blur(28px);

        &:nth-of-type(2n) {
          place-self: end;
        }

        &:last-of-type {
          background-color: #1ABC9C;
          color: #fff;
        }

        img {
          height: 3.75rem;
          width: 3.75rem;
          margin-right: 0.5rem;
          border-radius: 50%;
        }

        .sect-body {
          display: grid;
          place-items: center;
          padding: 2em;

          .sect-name {
            margin: 0;
            text-align: center;
            font-size: 1.3rem;
            font-weight: 900;

            span {
              background: #FF0000;  /* fallback for old browsers */
              background: -webkit-linear-gradient(to right, #FF0000, #D31027);  /* Chrome 10-25, Safari 5.1-6 */
              background: linear-gradient(to right, #FF0000, #D31027); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
              -webkit-background-clip: text;
              background-clip: text;
              -webkit-text-fill-color: transparent;
            }
          }

          .sect-content {
            max-width: 22rem;
          }
        }
      }
    }

    .blob-4,
    .blob-5 {
      z-index: -1;
      position: absolute;
    }

    .blob-4 {
      right: 90%;
      top: 75%;
    }

    .blob-5 {
      right: -50%;
      bottom: 10%;
    }

    .sect-modules {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 1em;
      place-items: center;

      .sect-module {
        cursor: pointer;
        max-width: 17.5rem;
        padding: 0.4rem 1.5rem;
        background-color: #fff;
        border: 1px solid var(--clr-grey-1);
        border-radius: 1rem;
        position: relative;

        box-shadow: 0 0 0 0 rgb(12, 10, 69);
        animation: pulse-blue 2s infinite;

        p {
          color: var(--clr-grey-1);
          text-align: center;
          margin-bottom: 0;
          font-size: 1.562rem;
        }

        .arrow-down {
          opacity: 0;
          position: absolute;
          left: calc(50% - 20px);
          bottom: -40%;
        }
      }

      @keyframes pulse-blue {
        0% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(52, 172, 224, 0.7);
        }
        
        70% {
          transform: scale(1);
          box-shadow: 0 0 0 10px rgba(52, 172, 224, 0);
        }
        
        100% {
          transform: scale(0.95);
          box-shadow: 0 0 0 0 rgba(52, 172, 224, 0);
        }
      }

      .selected-module {
        background: transparent linear-gradient(126deg, #0A6FB0 0%, #1B1749 100%);
        
        p {
          color: #FFF;
        }

        .arrow-down {
          opacity: 1;
          width: 0; 
          height: 0; 
          border-left: 20px solid transparent;
          border-right: 20px solid transparent;
          border-top: 20px solid #1B1749;
        }
      }
    }

    .sect--content {
      display: flex;
      align-items: center;
      justify-content: space-around;
      min-height: 20rem;
      width: 100%;
      padding: 2rem 0;
      margin-top: 4rem;
      background: rgba(255, 255, 255, .05) 0% 0% no-repeat padding-box;
      box-shadow: 0px 0px 18px #0000003f;
      border-radius: 15px;
      opacity: 1;
      backdrop-filter: blur(30px);
      -webkit-backdrop-filter: blur(30px);

      .content-left {
        display: flex;
        justify-content: space-between;
      }

      ul { 
        margin-right: 1rem;

        li {
          text-decoration: none;
          font-size: 1rem;
          color: var(--clr-grey-1);
          list-style: none;
          padding: 1rem;
          position: relative;

          &::before {
            position: absolute;
            content: '';
            left: -20%;
            top: 3%;
            background: no-repeat center / contain url('../../../assets/icons/workshop/ticck-mark.png');
            background-clip: border-box;
            height: 40px;
            width: 40px;
          }

          span {
            font-weight: 900;
            background: #FF0000;  /* fallback for old browsers */
            background: -webkit-linear-gradient(to right, #FF0000, #D31027);  /* Chrome 10-25, Safari 5.1-6 */
            background: linear-gradient(to right, #FF0000, #D31027); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }

    .blob-6,
    .blob-7,
    .grid-1 {
      z-index: -1;
      position: absolute;
    }

    .blob-6 {
      right: 90%;
      top: 30%;
    }

    .grid-1 {
      left: calc(50% - 222px);
      top: 90%;
    }

    .blob-7 {
      left: 95%;
      top: 27%;
    }

    .others-placed {
      margin-top: 2rem;
      width: 102%;

      .carousel-inner {
        padding: 1em;
      }

      .animated {
        margin-right: 0.5em;
        -webkit-animation-duration: 1s;
        animation-duration: 1s;
        -webkit-animation-fill-mode: both;
        animation-fill-mode: both;
      }
  
      @-webkit-keyframes fadeIn {
        0% {opacity: 0;}
        100% {opacity: 1;}
      }
  
      @keyframes fadeIn {
        0% {opacity: 0;}
        100% {opacity: 1;}
      }
      
      .fade-in {
        -webkit-animation-name: fadeIn;
        animation-name: fadeIn;
      }

      .placed-card {
        min-width: 24rem;
        height: 240px;
        display: flex;
        justify-content: space-between;
        padding: 1rem;
        background-color: #fff;
        box-shadow: 0px 0px 18px #0000003f;
        border-radius: 1em;

        .placed-left {
          width: 50%;
          display: flex;
          flex-direction: column;
          justify-content: space-around;

          img {
            width: 7.6875rem;
            height: 3.25rem;
          }

          .name {
            font-weight: 900;
            color: var(--clr-blue-primary);
          }

          .package {
            width: 40%;
            padding: 0.3rem;
            margin: 0;
            background: #FFFFFF 0% 0% no-repeat padding-box;
            box-shadow: 0px 0px 18px #00000028;
            border-radius: 1rem;
            color: var(--clr-ylw-secondary);
            text-align: center;
          }
        }

        img.student-img {
          height: 6.875rem;
          width: 6.875rem;
          border-radius: 50%;
          place-self: flex-end;
          margin-left: 0.3rem;
        }
      }
    }

    .grid-4 {
      position: absolute;
      top: 50%;
      left: 90%;
      z-index: -1;
    }

    .sect-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      justify-items: center;
      width: 100%;

      .trainer-info {
        display: flex;
        flex-direction: column;
        align-items: center;

        &:nth-of-type(4) {
          grid-row: 2;
          grid-column: 1 / span 2;
        }

        &:nth-of-type(5) {
          grid-row: 2;
          grid-column: 2 / span 2;
        }

        .trainer-pic {
          position: relative;

          img.pic {
            height: 15.625rem;
            width: 15.625rem;
            border-radius: 50% 50% 48% 52% / 35% 31% 69% 65%;
          }

          .image-blob {
            position: absolute;
    
            &:first-of-type {
              top: 15%;
              left: 90%;
            }
    
            &:last-of-type {
              top: 70%;
              right: 70%;
            }
          }
        }

        .trainer-name {
          font-weight: 900;
          font-size: 1.875rem;
          color: var(--clr-ylw-secondary);
          margin-top: 1rem;
        }

        .trainer-intro {
          font-size: 1.2rem;
        }

        ul {
          padding: 0;

          li {
            list-style: none;
            color: var(--clr-grey-1);
            text-align: center;
            padding: 0.4rem;
          }
        }
      }

      button {
        grid-column: 2;
        display: block;
        margin: auto;
      }
    }

    .grid-5 {
      margin-top: 5rem;
    }

    .video-grid--1 {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      justify-items: center;
      width: 100%;

      .video {
        position: relative;

        iframe {
          border-radius: 1rem;
        }

        .video-title {
          width: 70%;
          position: relative;
          left: 15%;
          padding: 0.5rem;
          color: #fff;
          text-align: center;
          font-weight: 900;
          font-size: 1.1rem;
          border-radius: 2.5rem;
          background: transparent linear-gradient(101deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 18px #00000017;
        }

        .video-text {
          font-size: 1.5rem;
          color: var(--clr-grey-1);
          text-align: center;
        }
      }
    }

    .qm-faq--list .qm-faqs {
      max-width: 70%;
      padding: 1em;
      margin: 2rem auto;
      background-color: #1ABC9C;
      color: #fff;
      border-radius: 0.15rem;
      box-shadow: 1px 2px 15px rgba(172, 255, 251, 0.76);

      .qm-faq--header {
        display: flex;
        justify-content: space-between;

        h5 {
          margin: 0;
        }

        button {
          background-color: #1ABC9C;
          border: none;
          outline: none;

          svg {
            transform: rotate(0deg);
            transition: all 0.4s ease-in;
          }
        }

        .is-open {
          svg {
            transform: rotate(180deg);
            transition: all 0.3s ease-out;
          }
        }
      }

      .qm-faq--item {
        .faq-answer {
          margin-top: 1rem;

          p {
            padding-left: 1em;
            max-width: 90%;
          }
        }
      }
    }
  }

  .sect-5 {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 5em;
    position: relative;

    .sect-5--right {
      margin-bottom: 2em;

      .register-box {
        max-height: 61.375rem;
        padding: 1rem 2rem;
        position: relative;
        background: rgba(0, 0, 0, 0.05) 0% 0% no-repeat padding-box;
        box-shadow: 0px 0px 18px #0000003f;
        border-radius: 15px;
        opacity: 1;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);

        .price-box {
          p {
            text-align: center;
            font-size: 1rem;
            color: var(--clr-grey-1);
            margin-bottom: 1rem;
  
            &:last-of-type {
              display: flex;
              flex-direction: column;
              font-size: 3rem;
              color: var(--clr-ylw-primary);
              margin-bottom: 2rem;
            }

            small {
              display: inline-block;
              font-size: 1rem;
              font-weight: 900;
              color: var(--clr-grey-2);
            }
          }
        }

        button {
          position: absolute;
          left: calc(50% - 110px);
        }
      }

      .offer-box {
        margin-top: 4rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          height: 475px;
          width: 450px;
          margin-bottom: 2em;
        }

        p {
          text-align: center;
          font-size: 1rem;
          text-align: center;
          font-weight: 900;
          color: var(--clr-grey-1);

          span {
            padding: 2px;
            border-radius: 5px;
            cursor: pointer;
            color: #0B5345;
            background-color: #2BE7C2;
            font-weight: 900;
          }
        }
      }
    }

    .blob-8,
    .grid-2,
    .grid-3 {
      position: absolute;
      z-index: -1;
    }

    .grid-2 {
      left: 90%;
    }

    .grid-3 {
      top: 90%;
      right: 90%;
    }

    .blob-8 {
      left: 90%;
      top: 60%;
    }
  }

  .spl-box {
    background: #41295a;  /* fallback for old browsers */
    background: -webkit-linear-gradient(to right, #2F0743, #41295a);  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to right, #2F0743, #41295a); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

    h4 {
      color: #fff;

      span {
        color: #000;
      }
    }
  }

  .qm-ring--btn {
    min-width: 200px;
    min-height: 40px;
    font-family: 'Nunito', sans-serif;
    font-size: 22px;
    text-transform: uppercase;
    letter-spacing: 1.3px;
    font-weight: 700;
    color: #313133;
    background: #EB984E;
    background: linear-gradient(90deg, rgb(245, 176, 65) 0%, rgb(230, 126, 34) 100%);
    border: none;
    border-radius: 1000px;
    box-shadow: 12px 12px 24px rgba(202, 111, 30, 0.64);
    transition: all 0.3s ease-in-out 0s;
    cursor: pointer;
    outline: none;
    position: relative;
    padding: 10px;
    margin: 0rem 0 1rem 0;
  }
  
  .qm-ring--btn::before {
    content: '';
    border-radius: 1000px;
    min-width: calc(200px + 30px);
    min-height: calc(40px + 25px);
    border: 6px solid #F5B041;
    box-shadow: 0 0 40px rgba(255, 153, 0, 0.64);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all .3s ease-in-out 0s;
  }
  
  .qm-ring--btn:hover, .qm-ring--btn:focus {
    color: #313133;
    transform: translateY(-6px);
  }
  
  .qm-ring--btn:hover::before, .qm-ring--btn:focus::before {
    opacity: 1;
  }
  
  .qm-ring--btn::after {
    content: '';
    width: 30px; 
    height: 30px;
    border-radius: 100%;
    border: 6px solid #F5B041;
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ring 1.5s infinite;
  }
  
  .qm-ring--btn:hover::after, .qm-ring--btn:focus::after {
    animation: none;
    display: none;
  }
  
  @keyframes ring {
    0% {
      width: 30px;
      height: 30px;
      opacity: 1;
    }
    100% {
      width: 200px;
      height: 200px;
      opacity: 0;
    }
  }

  .primary-btn {
    height: 3.5rem;
    min-width: 12.5rem;
    border: none;
    border-radius: 0.625rem;
    background: transparent linear-gradient(110deg, #F8DB1F 0%, #E28A26 100%) 0% 0% no-repeat padding-box;
    transform: scale(1);
    transition: all 0.4s ease-out;

    &:hover {
      transform: scale(1.1);
      transition: all 0.2s ease-in;
    }
  }

  .btn-small {
    height: 2.5rem;
    min-width: 7.5rem;
  }

  .secondary-btn {
    height: 2.5rem;
    min-width: 7.5rem;
    border: none;
    border-radius: 0.625rem;
    color: #fff;
    background: transparent linear-gradient(225deg, var(--unnamed-color-0a6fb0) 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
    background: transparent linear-gradient(225deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
    transform: scale(1);
    transition: all 0.4s ease-out;

    &:hover {
      transform: scale(1.1);
      transition: all 0.2s ease-in;
    }
  }

  .ring-btn {
    display: block;
    min-width: 200px;
    min-height: 40px;
    font-family: 'Nunito', sans-serif;
    font-size: 22px;
    text-transform: uppercase;
    letter-spacing: 1.3px;
    font-weight: 700;
    color: #313133;
    background: #EB984E;
    background: linear-gradient(90deg, rgb(245, 176, 65) 0%, rgb(230, 126, 34) 100%);
    border: none;
    border-radius: 1000px;
    box-shadow: 12px 12px 24px rgba(202, 111, 30, 0.64);
    transition: all 0.3s ease-in-out 0s;
    cursor: pointer;
    outline: none;
    position: relative;
    padding: 10px;
    margin: 3rem auto 1rem auto;
    z-index: 2;
  }
  
  .ring-btn::before {
    content: '';
    border-radius: 1000px;
    min-width: calc(390px + 20px);
    min-height: calc(40px + 25px);
    border: 6px solid #F5B041;
    box-shadow: 0 0 40px rgba(255, 153, 0, 0.64);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all .3s ease-in-out 0s;
  }
  
  .ring-btn:hover, .ring-btn:focus {
    color: #313133;
    transform: translateY(-6px);
  }
  
  .ring-btn:hover::before, .ring-btn:focus::before {
    opacity: 1;
  }
  
  .ring-btn::after {
    content: '';
    width: 30px; 
    height: 30px;
    border-radius: 100%;
    border: 6px solid #F5B041;
    position: absolute;
    z-index: -1;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ring 1.5s infinite;
  }
  
  .ring-btn:hover::after, .ring-btn:focus::after {
    animation: none;
    display: none;
  }

  .ring-btn:nth-of-type(1)::before {
    min-width: calc(345px + 20px);
  }
  
  @keyframes ring {
    0% {
      width: 30px;
      height: 30px;
      opacity: 1;
    }
    100% {
      width: 200px;
      height: 200px;
      opacity: 0;
    }
  }

  .ftr {
    margin-top: 4em;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .icons {
      margin-top: 2em;
      display: flex;
      justify-content: space-between;
      width: 20%;

      h6 {
        font-size: 26px;
      }

      .icon {
        display: grid;
        place-items: center;
        padding: 0.1rem;
        background: transparent linear-gradient(111deg, #0C7DA9 0%, #16366D 100%) 0% 0% no-repeat padding-box;
        border-radius: 50%;
        height: 40px;
        width: 40px;

        a {
          img {
            height: 20px;
            width: 20px;
          }
        }
      }
    }

    .copy-text {
      font-size: 17px;
      font-weight: lighter;
      margin-top: 2em;
      margin-bottom: 0.5em;
      color: var(--clr-grey-1);
    }
  }

  .qm-contact--box {
    cursor: pointer;
    max-width: 17rem;
    position: fixed;
    display: flex;
    top: 85%;
    left: 90%;
    align-items: center;
    justify-content: center;
    padding: 0.5em 1em;
    background: rgba(0, 0, 0, 0.05) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 18px #0000003f;
    border-radius: 15px;
    opacity: 1;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);

    &:hover {
      transform: scale(1.05);
      transition: all 0.2s ease;
    }

    p {
      margin: 0;
      font-weight: 900;
    }

    img {
      height: 3rem;
      width: 3rem;
    }
  }
}

.reg-modal {
  font: 'Product Sans' 'sans-serif';

  .modal-body {
    .form-error--text {
      display: block;
      font-size: 12px;
      color: #dc3545;
    }

    .secondary-btn {
      height: 2.5rem;
      min-width: 7.5rem;
      border: none;
      border-radius: 0.625rem;
      color: #fff;
      background: transparent linear-gradient(225deg, var(--unnamed-color-0a6fb0) 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      background: transparent linear-gradient(225deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      transform: scale(1);
      transition: all 0.4s ease-out;
  
      &:hover {
        transform: scale(1.1);
        transition: all 0.2s ease-in;
      }
    }

    input,
    select {
      display: block;
      height: 40px;
      width: 100%;
      padding: 3px 5px;
      border: solid 1.5px #707070;
      font-size: 18px;
      border-radius: 5px;
      transition: all 0.3s ease;

      &:focus {
        border: solid 1.5px #0B6FB1;
        transition: all 0.3s ease;
      }

      &:focus + .placeholder-text {
          top: -75px;
          font-size: 13px;
          transition: all 0.3s ease;
      }
    }

    .form-elem {
      margin-bottom: 1em;
    }

    .placeholder-text {
      position: relative;
      top: -56px;
      left: 10px;
      padding: 3px;
      font-size: 17px;
      background-color: #fff;
      transition: all 0.4s ease;
    }

    #passwordHelpBlock,
    #emailHelpBlock {
      margin-top: 10px;
      font-size: 12px;
      color: #6c757d;
      width: 90%;
    }
  }
}

.blur-bg {
  filter: blur(5px);
}

/** 
 * DARK THEME STYLES
 */
.dark-theme {

  .mode-switch p {
    color: #E5E7E9;
  }

  .logo-text a {
    filter: saturate(60%);
  }

  .sect-1 {
    h2, h3, h4 {
      filter: saturate(60%);
    }

    .sect-left .extra-info {

      .google-info {
        background-color: #34323d;
      }
      
      .upcoming-info .info-card {
        background-color: #34323d;

        p {
          color: #E5E7E9;
        }
      }
    }

    .sect-left h3 {
      color: #E5E7E9;
    }

    .sect-right img {
      filter: saturate(60%);
    }
  }

  .sect-2 {
    h3 {
      color: #E5E7E9;
    }

    .company-grid .company {
      filter: saturate(60%);
      background-color: #E5E7E9;
    }
  }

  .sect-3 {

    .sect-heading {
      filter: saturate(80%);
    }

    .sect-sub-heading--large,
    .sect-sub-heading {
      color: #E5E7E9;
    }

    .sect-cards .sect-card,
    .others-placed .placed-card {
      background-color: #34323d;

      p {
        color: #E5E7E9;
      }

      .placed-left .package {
        background-color: #3b3946;
      }
    }

    .sect--content ul li,
    .sect-grid .trainer-info ul li,
    .video-grid--1 .video .video-text {
      color: #E5E7E9;
    }

    .sect-modules .sect-module {
      border-color: #34323d;
    }

    .qm-faq--list .qm-faqs {
      filter: saturate(60%);
      box-shadow: 1px 2px 15px rgba(172, 255, 251, 0.26);
      color: #E5E7E9;
    }
  }

  .sect-5 {

    .sect-5--left {
      .sect-sub-heading--1 {
        color: #E5E7E9;
      }
    }
  
    .sect-5--right {
    
      .register-box {
        .list-conent ul li,
        .price-box p {
          color: #E5E7E9;
        }
      }
    }
  }

  .ftr {
    color: #E5E7E9;

    .icons a img {
      padding: 1px;
      border-radius: 5px;
    }
  }

  .qm-contact--box {
    background-color: #34323d8e;

    p {
      color: #E5E7E9;
    }
  }

  .modal-content {
    background-color: #34323d;

    h5 {
      color: #E5E7E9;
    }

    .modal-body {
      .item-tab {
        color: #E5E7E9;
      }

      form {
        input,
        select {
          background-color: #37363B;
          color: #E5E7E9;
          border: solid 0.5px #E5E7E9;
        }
      }

      .bubbly-button {
        filter: saturate(60%);
      }
    }
  }
}

@media (max-width: 440px) {
  .landing-2-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .mode-switch {
      position: initial;
      width: initial;
    }

    .logo-text {
      width: 100%;
      text-align: center;
    }

    .sect-1 {
      width: 100%;
      flex-direction: column;

      .sect-left {
        width: 100%;

        .extra-info {
          h4 {
            text-align: center;
          }

          .extra-info--2,
          .google-info {
            flex-direction: column;
            align-items: center;
          }

          .extra-info--2 {
            width: 95%;
            margin: auto;
            margin-top: 1em;

            .sect-card {
              width: initial;

              .sect-name {
                text-align: center;
              }
            }

            .sect-cards {
              grid-template-columns: 1fr;
              background-image: none;
            }

            p {
              width: 100%;
              text-align: left;
            }
          }
        }
      }

      .upcoming-info {
        .info-card {
          flex-direction: column;
          margin-left: auto;
          margin-right: auto;

          p {
            margin-top: 0.5em;
          }

          button {
            margin-top: 1rem;
          }
        }
      }

      .sect-right {
        width: 100%;
        min-height: 31.25rem;
        margin-bottom: 3rem;

        img {
          height: 80%;
          width: 100%;
          margin-top: 5rem;
        }

        &::before {
          width: 100%;
        }
      }

      h2, h3, p {
        text-align: center;
      }
    }

    .sect-2 {
      margin: 0;
      grid-template-columns: 1fr;

      .sect-card {
        padding: 2rem 3rem;
      }
    }

    .sect-3 {
      width: 95%;

      .sect-cards {
        grid-template-columns: 1fr;
        background-image: none;

        .sect-card {
          width: 100%;
        }
      }

      .sect-modules {
        grid-template-columns: 1fr 1fr;
        gap: 2em;

        .sect-module .arrow-down {
          display: none;
        }
      }

      .sect--content {
        flex-direction: column;

        .content-left {
          flex-direction: column;

          ul li::before {
            left: -14%;
          }
        }

        .content-right {
          width: 95%;

          img {
            width: 100%;
          }
        }
      }

      .others-placed .placed-card {
        width: 100%;
        min-width: initial;

        .placed-left {
          width: initial;

          .package {
            width: 60%;
          }
        }
      }

      .sect-grid {
        grid-template-columns: 1fr;

        .trainer-info:nth-of-type(4),
        .trainer-info:nth-of-type(5) {
          grid-row: initial;
          grid-column: initial;
        }

        button {
          grid-row: initial;
          grid-column: initial;
        }
      }

      .video-grid--1 {
        grid-template-columns: 1fr;
        align-items: center;
        width: 95%;

        .video {
          width: 100%;

          iframe {
            width: 100%;
          }
        }
      }

      .qm-faq--list .qm-faqs {
        max-width: 90%;
      }
    }

    .sect-5 {
      flex-direction: column;
      align-items: center;

      .sect-5--left {
        align-items: center;

        .sect-sub-heading--1,
        .sect-heading {
          text-align: center;
        }

        &::before {
          top: 1px;
        }

        img {
          margin-top: 2rem;
          margin-bottom: 3rem;
        }
      }

      .sect-5--right {
        width: 95%;

        .register-box {
          max-height: initial;

          .list-conent {
            grid-template-columns: 1fr;
          }

          .price-box {
            flex-direction: column;
            align-items: center;

            p:last-of-type {
              margin-top: 0;
            }
          }
        }

        .offer-box {
          img {
            width: 100%;
            height: 400px;
          }
        }
      }
    }

    .shape-1-left {
      display: none;
    }

    .down-arrow {
      display: none;
    }

    .ftr {
      margin-top: 2em;

      h5 {
        text-align: center;
      }

      .icons {
        margin-top: 0.5em;
        width: 70%;
      }

      .copy-text {
        margin-top: 1em;
        text-align: center;
        font-size: 15px;
      }
    }

    .qm-contact--box {
      top: 90%;
      left: 10%;
    }
  }
}