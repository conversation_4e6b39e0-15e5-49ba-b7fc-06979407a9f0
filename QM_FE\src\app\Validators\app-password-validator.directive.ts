import { Directive } from '@angular/core';
import { Validator, AbstractControl, NG_VALIDATORS } from '@angular/forms';

@Directive({
  selector: '[appPasswordValidator]',
  providers: [{ provide: NG_VALIDATORS, useExisting: AppPasswordValidatorDirective, multi: true }]
})
export class AppPasswordValidatorDirective implements Validator {

  constructor() { }

  validate(control: AbstractControl): { [key: string]: any } | null {
    if (!/[A-Z]/.test(control.value)) {
      return { 'passwordCaps': true };
    }

    return null;
  }

}
