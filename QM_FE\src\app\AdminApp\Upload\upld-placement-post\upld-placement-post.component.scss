.updl-placement--wrap {
  width: 95%;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 1px 1px 15px rgba(0, 0, 0, 0.16);

  .posts-wrap {
    display: grid;
    grid-template-columns: 1fr;

    button {
      justify-self: end;
      margin-bottom: 2rem;
    }

    .posts-table {
      h5 {
        margin-bottom: 1rem;
      }
      .job-list-pagination {
        padding-left: 20px;
      }

      .job-entry {
        margin-bottom: 3rem;

        form {
          .form-elem {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;

            label {
              font-weight: 700;
              margin-right: 10px;
              width: 10%;
            }

            input[class^="text-link"] {
              flex-grow: 0.6;
            }

            textarea {
              flex-grow: 2;

              &[class^="text-title"] {
                flex-grow: 0.6;
              }
            }

            button {
              margin-bottom: 0;
              margin-left: 15px;
            }

            .img-updl--btn {
              border: none;
              background-color: rgba(0, 0, 0, 0);

              img {
                height: 45px;
                width: 45px;
              }
            }
            .img-updl--btn1 {
              border: none;
              background-color: rgba(0, 0, 0, 0);
              justify-self: start;
              margin-left: 0;
              img {
                height: 40px;
                width: 40px;
              }
            }
          }

          .form-elem--1 {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
  }
}

.custom-btn {
  height: 40px;
  width: 130px;
  font-size: 17px;
  font-weight: normal;
  border: none;
  border-radius: 4px;
  color: #fff;
  background-color: #2E4053;
  padding: 5px 10px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

.custom-btn:active:after {
  transition: 0s;
  opacity: 0.7;
  clip-path: circle(0% at 0% 0%);
}

.custom-btn::after {
  content: "";
  display: block;
  position: relative;
  top: -30px;
  height: 40px;
  background-color: #9FA8DA;
  opacity: 0;
  clip-path: circle(150% at 0% 0%);
  transition: all 0.4s ease-in;
}

.create-modal {
  form {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;

    .form-elem {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
      width: 100%;

      &:last-of-type {
        margin-top: 2rem;
      }

      label {
        font-weight: 700;
        margin-right: 10px;
        width: 25%;
      }

      input, textarea {
        flex-grow: 2;
      }
    }

    .form-error--text {
      font-size: 80%;
      color: #dc3545;
      margin-left: 105px;
      margin-bottom: 1rem;
    }

    .is-inv--input {
      border: 1px solid #dc3545;
      background-color: rgb(255, 200, 194);
    }
  }
}

@media (max-width: 440px) {
  .updl-placement--wrap {
    margin: auto;

    h5 {
      text-align: center;
    }
    
    .posts-wrap {
      place-items: center;

      .posts-table {
        width: 100%;

        h5 {
          text-align: center;
        }

        .job-entry {
          width: 95%;
          margin-right: 0;

          form {
            .form-elem {
              display: grid;

              button[type="button"] {
                margin-top: 0.5rem;
                margin-left: 0;
                justify-self: start;
              }

              .img-updl--btn {
                justify-self: start;
                margin-left: 0;
              }

              label {
                width: 100%;
              }

              input,
              input[class^="text-link"],
              textarea,
              textarea[class^="text-title"] {
                flex-grow: 2;
              }
            }

            .form-elem--1 {
              display: flex;
            }
          }
        }
      }
    }
  }
}