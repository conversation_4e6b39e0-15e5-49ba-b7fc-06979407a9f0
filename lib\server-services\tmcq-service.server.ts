// lib/server-services/tmcq-service.server.ts
import { cookies } from 'next/headers'
import axios from 'axios'
import { TMCQGroup } from '@/types/technical-mcq-types'

const API_BASE_URL = 'https://api.quantmasters.in/v3/tmcq'

const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

const createAuthHeaders = (token: string) => ({
  headers: {
    Authorization: `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})

export class TMCQServerService {
  static async getTMCQGroups(): Promise<TMCQGroup[]> {
    try {
      const token = await getServerSideToken()
      const response = await axios.get(
        `${API_BASE_URL}/group`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching TMCQ groups:', error)
      return []
    }
  }
}
