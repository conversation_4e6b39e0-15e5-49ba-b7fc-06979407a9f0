import { Component, OnInit, OnD<PERSON>roy, ViewChild, TemplateRef } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { ActivatedRoute, Router } from '@angular/router';
import { CertificationService } from 'src/app/Services/Certification/Certification.service';

@Component({
  selector: 'app-certification',
  templateUrl: './certification.component.html',
  styleUrls: ['./certification.component.scss'],
})
export class CertificationComponent implements OnInit, OnDestroy {
  @ViewChild('hideTemplate') public hTemplate: TemplateRef<any>;
  @ViewChild('verifyTemplate') public verifyTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public eTemplate: TemplateRef<any>;

  public showIndicator = false;
  public modalRef: BsModalRef;
  public UserMailId = '';
  public certificateId = '';
  public certId = '';
  public certAuthText = null;
  public showCopyAlert = false;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false,
  };
  public copyAlert = {
    type: 'info',
    msg: 'Url Copied!',
    timeout: 20000,
    isOpen: false
  };

  public pdfPath = '';

  constructor(
    public router: Router,
    public activatedRoute: ActivatedRoute,
    public modalService: BsModalService,
    public certificationService: CertificationService
  ) {}

  ngOnInit() {
    setTimeout(() => {
      if (
        !sessionStorage.getItem('logged') &&
        !this.router.url.match(/^\/dashboard\/test\/7\/*/)
      ) {
        this.openModal(this.hTemplate);
      }
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  onLogin() {
    this.modalRef.hide();
    localStorage.setItem('QPage', 'IC');
  }


  getCertificate(ActionFrom, isValid) {
    const that = this;
    if (
      !sessionStorage.getItem('logged') &&
      !this.router.url.match(/^\/dashboard\/test\/7\/*/) &&
      ActionFrom === 'User'
    ) {
      this.UserMailId = '';
      this.openModal(this.hTemplate);
    } else if (ActionFrom === 'User') {
      this.showIndicator = true;
      this.UserMailId = sessionStorage.getItem('QMail');
      this.certificationService.getCertification(this.UserMailId).subscribe(response => {
        that.pdfPath = response.url;
        that.certId = response.cert_code;
        that.showIndicator = false;
      }, error => {
        that.showIndicator = false;
        that.openModal(that.eTemplate);
      });
    } else if (ActionFrom === 'Temp') {
      if (!isValid) {
        return;
      }
      this.showIndicator = true;
      this.certificationService.getCertification(this.UserMailId).subscribe(response => {
        this.pdfPath = response.url;
        that.certId = response.cert_code;
        this.modalRef.hide();
        this.showIndicator = false;
      }, error => {
        this.modalRef.hide();
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    }
  }

  verifyCertificate() {
    this.pdfPath = '';
    this.openModal(this.verifyTemplate);
  }

  checkValidity() {

    this.showIndicator = true;
    this.certificationService.verifyCertificate(this.certificateId).subscribe(response => {
      this.showIndicator = false;
      this.modalRef.hide();
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.msg && resp.msg === 'Can generate') {
        this.certAuthText = 'You are Eligible for a certificate, hit Get/Generate you certificate';
      } else {
        if (this.pdfPath === '') {
          this.certificationService.getCertification(resp.email).subscribe(resp2 => {
            this.pdfPath = resp2.url;
            this.certId = resp2.cert_code;
            this.modalRef.hide();
            this.showIndicator = false;
          }, error => {
            this.modalRef.hide();
            this.showIndicator = false;
            this.openModal(this.eTemplate);
          });
        }
      }
    }, error => {
      this.showIndicator = false;
      this.modalRef.hide();
      this.certificateId = null;
      this.openModal(this.eTemplate);
    });
  }

  downloadCertificate() {
    window.open(this.pdfPath, '_blank');
  }

  generateCertificateUrl() {

    const certUrl = `https://quantmasters.in/certification/view/${this.certId}`,
          that    = this;

    navigator['clipboard'].writeText(certUrl).then(() => {
      that.showCopyAlert = true;
    }, () => {});
  }

  takeToFAQs() {

    this.router.navigate(['../faq'], {  relativeTo: this.activatedRoute });
  }

  onClosed() {
    this.showCopyAlert = false;
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}
