.forgot-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: 'Montserrat', sans-serif;

  .forgot-card {
    width: 40%;
    margin-top: 4em;
    background-color: #fff;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);

    .instr {
      width: 80%;
      margin: 1em auto;
      text-align: center;

      p:first-child {
        font-size: 28px;
        color: royalblue;
      }


    }

    form {
      width: 90%;
      margin: 2em auto 3em auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      label {
        width: 100%;
      }

      input {
        padding: 1em;
        width: 100%;
        height: 3em;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 5px;
        box-shadow: inset 1px 0.5px 5px rgba(0, 0, 0, 0.25);
      }

      p {
        margin-top: 1em;
        text-align: center;
      }

      .custom-btn {
        cursor: pointer;
        width: 230px;
        height: 40px;
        background-color: #E88224;
        border: none;
        border-radius: 4px;
        color: #fff;
        margin: 2em auto 3em auto;
        padding: 8px;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
      }
  
      .custom-btn:active:after {
        transition: 0s;
        opacity: 0.7;
        clip-path: circle(0% at 0% 0%);
      }
  
      .custom-btn::after {
        content: "";
        display: block;
        position: relative;
        top: -32px;
        left: -8px;
        height: 40px;
        width: 130px;
        background-color: #E88224;
        opacity: 0;
        clip-path: circle(150% at 0% 0%);
        transition: all 0.4s ease-in;
      }

      // button {
      //   height: 50px;
      //   width: 200px;
      //   padding: 0.6em;
      //   color: #fff;
      //   margin-top: 2em;
      //   border: none;
      //   border-radius: 5px;
      //   background-color: rgb(0, 32, 175);
      //   box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.15);
      // }

      // button:active:after {
      //   transition: 0s;
      //   opacity: 0.7;
      //   clip-path: circle(0% at 0% 0%);
      // }

      // button::after {
      //   content: "";
      //   display: block;
      //   position: relative;
      //   top: -33px;
      //   left: -10px;
      //   height: 50px;
      //   width: 200px;
      //   background-color:rgb(255, 238, 192);
      //   opacity: 0;
      //   clip-path: circle(150% at 0% 0%);
      //   transition: all 0.4s ease-in;
      // }

      .is-inv {
        border-color: #dc3545;
      }
    }

  }
}

@media (max-width: 440px) {
  .forgot-wrap {
    .forgot-card {
      width: 90%;
    }
  }
}