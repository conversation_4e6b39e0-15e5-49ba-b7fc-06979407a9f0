// Test Excel file generation to verify functionality
import { generateExcelTemplate } from '@/lib/server-actions/bulk-upload'

export async function testExcelGeneration() {
  try {
    console.log('Testing Excel template generation...')
    const templateBuffer = await generateExcelTemplate()
    console.log('✅ Excel template generated successfully')
    console.log('Template size:', templateBuffer.byteLength, 'bytes')
    return true
  } catch (error) {
    console.error('❌ Excel template generation failed:', error)
    return false
  }
}

// Example usage - call this in a component to test
export default testExcelGeneration
