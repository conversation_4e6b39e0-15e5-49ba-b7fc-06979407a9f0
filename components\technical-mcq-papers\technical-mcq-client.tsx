// components/technical-mcq-papers/technical-mcq-client.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Folder, FileText, ArrowLeft, ArrowRight, Clock } from 'lucide-react'
import { TMCQClientService } from '@/lib/client-services/tmcq-service.client'
import type {
  TMCQGroup,
  TMCQSubGroup,
  ChapterPaper
} from '@/types/technical-mcq-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface TechnicalMCQClientProps {
  initialGroups: TMCQGroup[]
}

export default function TechnicalMCQClient({
  initialGroups
}: TechnicalMCQClientProps) {
  const router = useRouter()
  const [groups] = useState<TMCQGroup[]>(initialGroups)
  const [subGroups, setSubGroups] = useState<TMCQSubGroup[]>([])
  const [papers, setPapers] = useState<ChapterPaper[]>([])
  const [selectedTopic, setSelectedTopic] = useState('')
  const [selectedSubTopic, setSelectedSubTopic] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleGetSubGroups = async (groupId: string, groupName: string) => {
    setIsLoading(true)
    setSelectedTopic(groupName)
    const subgroups = await TMCQClientService.getTMCQSubGroups(groupId)
    setSubGroups(subgroups)
    setPapers([])
    setIsLoading(false)
  }

  const handleGetPapers = async (subGroupId: string, subGroupName: string) => {
    setIsLoading(true)
    setSelectedSubTopic(subGroupName)
    const papers = await TMCQClientService.getPapersOfAGroup(subGroupId)
    setPapers(papers)
    setIsLoading(false)
  }

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    timeLim: number
  ) => {
    router.push(`/dashboard/test/11/${paperId}/${paperName}/${timeLim}`)
  }

  const handleBackToGroups = () => {
    setSubGroups([])
    setPapers([])
    setSelectedTopic('')
    setSelectedSubTopic('')
  }

  const handleBackToSubGroups = () => {
    setPapers([])
    setSelectedSubTopic('')
  }

  if (isLoading) {
    return <div className="text-center py-10">Loading...</div>
  }

  const renderContent = () => {
    if (papers.length > 0) {
      return (
        <div>
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={handleBackToSubGroups}>
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to {selectedTopic}
            </Button>
            <h2 className="text-xl font-semibold ml-4">{selectedSubTopic}</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {papers.map((paper) => (
              <Card key={paper.paper_id} className="h-full flex flex-col">
                <CardHeader>
                  <CardTitle>{paper.paper_name}</CardTitle>
                  <CardDescription className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>
                      {paper.time_lim
                        ? `${paper.time_lim / 60000} Minutes`
                        : 'No Time Limit'}
                    </span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-1">
                    <FileText className="h-4 w-4" />
                    <span>No. of Questions: {paper.no_of_ques}</span>
                  </div>
                </CardContent>
                <CardFooter className="mt-auto">
                  <Button
                    onClick={() =>
                      handleBeginTest(
                        paper.paper_id,
                        paper.paper_name,
                        paper.time_lim
                      )
                    }
                    className="w-full"
                  >
                    Begin Test
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      )
    }

    if (subGroups.length > 0) {
      return (
        <div>
          <div className="flex items-center mb-6">
            <Button variant="ghost" onClick={handleBackToGroups}>
              <ArrowLeft className="mr-2 h-4 w-4" /> All Topics
            </Button>
            <h2 className="text-xl font-semibold ml-4">{selectedTopic}</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {subGroups.map((subgroup) => (
              <Card
                key={subgroup.sub_group_id}
                className="cursor-pointer hover:shadow-md transition-shadow"
                onClick={() =>
                  handleGetPapers(
                    subgroup.sub_group_id,
                    subgroup.sub_group_name
                  )
                }
              >
                <CardHeader>
                  <CardTitle className="text-lg">
                    {subgroup.sub_group_name}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2" />
                    <span>View Papers</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {groups.map((group) => (
          <Card
            key={group.group_id}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => handleGetSubGroups(group.group_id, group.group_name)}
          >
            <CardHeader>
              <CardTitle className="text-lg">{group.group_name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <Folder className="h-4 w-4 mr-2" />
                <span>Browse Papers</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-2xl font-bold">Technical MCQ Practice Papers</h1>
        <p className="text-gray-600 mt-2">Select a topic to begin practicing</p>
      </div>

      {renderContent()}
    </div>
  )
}
