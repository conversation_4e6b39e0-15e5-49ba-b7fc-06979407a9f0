'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { ChevronDown } from 'lucide-react'
import { SuperGroup, Group, ChapterPaper } from '@/types/chapter-practice-types'
import {
  getGroups,
  getPapersOfAGroup
} from '@/lib/client-services/chapter-practice-client'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import PaperList from './paper-list'

interface ChapterPracticeListProps {
  initialSuperGroups: SuperGroup[]
  initialGroups: Group[]
  initialSelectedGroupId: string
}

export default function ChapterPracticeList({
  initialSuperGroups,
  initialGroups,
  initialSelectedGroupId
}: ChapterPracticeListProps) {
  const router = useRouter()
  const [superGroups] = useState<SuperGroup[]>(initialSuperGroups)
  const [groups, setGroups] = useState<Group[]>(initialGroups)
  const [selectedGroupId, setSelectedGroupId] = useState<string>(
    initialSelectedGroupId
  )
  const [papersMap, setPapersMap] = useState<Record<string, ChapterPaper[]>>({})
  const [collapsedGroups, setCollapsedGroups] = useState<
    Record<string, boolean>
  >({})
  const [loading, setLoading] = useState<boolean>(false)

  // Initialize collapsed state for groups and fetch initial papers
  useEffect(() => {
    const initialCollapsedState = groups.reduce(
      (acc, group) => {
        acc[group.group_id] = true // Start with all collapsed
        return acc
      },
      {} as Record<string, boolean>
    )
    setCollapsedGroups(initialCollapsedState)

    // Fetch papers for initial groups
    if (groups.length > 0) {
      fetchPapersForGroups(groups)
    }
  }, []) // Empty dependency array to run only once on mount

  // Save selected super group to localStorage
  useEffect(() => {
    if (selectedGroupId) {
      localStorage.setItem('qmChapterGroupId', selectedGroupId)
      const index = superGroups.findIndex(
        (g) => g.super_group_id === selectedGroupId
      )
      if (index >= 0) {
        localStorage.setItem('qmChapterGroupIdx', index.toString())
      }
    }
  }, [selectedGroupId, superGroups])

  // Fetch papers for all groups
  const fetchPapersForGroups = async (groupsToFetch: Group[]) => {
    setLoading(true)
    const newPapersMap: Record<string, ChapterPaper[]> = { ...papersMap }
    const validGroups: Group[] = []

    for (const group of groupsToFetch) {
      try {
        const papers = await getPapersOfAGroup(group.group_id)
        if (papers.length > 0) {
          newPapersMap[group.group_id] = papers
          validGroups.push(group)
        }
      } catch (error) {
        console.error(
          `Error fetching papers for group ${group.group_id}:`,
          error
        )
      }
    }

    setPapersMap(newPapersMap)
    setGroups(validGroups)
    setLoading(false)
  }

  // Handle super group selection
  const handleSelectSuperGroup = async (
    superGroupId: string,
    index: number
  ) => {
    if (superGroupId === selectedGroupId) return
    console.log(`Selected super group: ${superGroupId} at index: ${index}`)
    setLoading(true)
    setSelectedGroupId(superGroupId)

    try {
      const newGroups = await getGroups(superGroupId)
      setGroups(newGroups)

      // Reset collapsed states
      const initialCollapsedState = newGroups.reduce(
        (acc, group) => {
          acc[group.group_id] = true
          return acc
        },
        {} as Record<string, boolean>
      )
      setCollapsedGroups(initialCollapsedState)

      // Fetch papers for the new groups
      fetchPapersForGroups(newGroups)
    } catch (error) {
      console.error('Error fetching groups:', error)
      setLoading(false)
    }
  }

  // Toggle collapse state for a group
  const toggleCollapsed = (groupId: string) => {
    setCollapsedGroups((prev) => ({
      ...prev,
      [groupId]: !prev[groupId]
    }))
  }

  // Navigate to test page
  const beginTest = (paperId: string, paperName: string, timeLim: string) => {
    router.push(
      `/dashboard/test/6/${paperId}/${encodeURIComponent(paperName)}/${timeLim}`
    )
  }

  // Navigate to plans page
  const takeToPlans = () => {
    router.push('/plans')
  }

  return (
    <div className="w-full">
      {/* Super groups tabs */}
      <div className="flex overflow-x-auto space-x-2 pb-4 mb-4 border-b">
        {superGroups.map((superGroup, i) => (
          <Button
            key={superGroup.super_group_id}
            variant={
              selectedGroupId === superGroup.super_group_id
                ? 'default'
                : 'outline'
            }
            onClick={() => handleSelectSuperGroup(superGroup.super_group_id, i)}
            className="whitespace-nowrap"
          >
            {superGroup.super_group_name}
          </Button>
        ))}
      </div>

      {loading && (
        <div className="flex justify-center py-8">Please wait...</div>
      )}

      {!loading && groups.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-500">No groups found</p>
        </div>
      )}

      {/* Groups and papers */}
      <div className="space-y-4">
        {groups.map((group) => (
          <Card
            key={group.group_id}
            className="overflow-hidden border-gray-200"
          >
            <div
              onClick={() => toggleCollapsed(group.group_id)}
              className="flex justify-between items-center p-4 bg-gray-100 cursor-pointer hover:bg-gray-200 transition-colors"
            >
              <h3 className="text-lg font-medium">{group.group_name}</h3>
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">
                  Number of Papers: {papersMap[group.group_id]?.length || 0}
                </span>
                <ChevronDown
                  className={`transition-transform duration-200 ${!collapsedGroups[group.group_id] ? 'rotate-180' : ''}`}
                  size={20}
                />
              </div>
            </div>

            {!collapsedGroups[group.group_id] && (
              <PaperList
                papers={papersMap[group.group_id] || []}
                beginTest={beginTest}
                takeToPlans={takeToPlans}
              />
            )}
          </Card>
        ))}
      </div>
    </div>
  )
}
