// components/student-exp/streaming-service.ts

import axios, { AxiosResponse } from 'axios'
import { LoginRefresh } from '@/lib/cookies'
import {
  VideoGroupType,
  VideoSubGroupType,
  VideoListType,
  StudentVideoListType,
  ApiResponse
} from '@/types/video-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  preLoginUrl: `${API_BASE_URL}/prelogin/videos/all`,
  videoGroupUrl: `${API_BASE_URL}/learn/videos/groups`,
  videoSubGroupUrl: `${API_BASE_URL}/learn/videos/subgroups`,
  videoListUrl: `${API_BASE_URL}/learn/videos/all`,
  videoLinkUrl: `${API_BASE_URL}/learn/videos/`,
  ratingAndCommentsUrl: `${API_BASE_URL}/learn/video/`,
  studentVideoListUrl: `${API_BASE_URL}/testimony/videos/all`,
  studentVideoLinkUrl: `${API_BASE_URL}/testimony/videos/`,
  reviewVideoListUrl: `${API_BASE_URL}/review/videos/all`,
  reviewVideoLinkUrl: `${API_BASE_URL}/review/videos/`,
  trialVideoListUrl: `${API_BASE_URL}/learn/sample/videos/all`,
  trialVideoLinkUrl: `${API_BASE_URL}/learn/sample/videos/`,
  workshopVideoListUrl: `${API_BASE_URL}/learn/workshop/videos/all`,
  workshopVideoLinkUrl: `${API_BASE_URL}/learn/workshop/videos/`
}

/**
 * Get JWT token from localStorage
 */
const getJwtToken = (): string => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('QMA_TOK') || ''
  }
  return ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class StreamingService {
  /**
   * Get pre-login video list (doesn't require authentication)
   */
  static async getPreLoginVideoList(): Promise<VideoListType[]> {
    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        ENDPOINTS.preLoginUrl
      )
      return response.data
    } catch (error) {
      console.error('Error fetching pre-login video list:', error)
      throw error
    }
  }

  /**
   * Get video groups
   */
  static async getVideoGroups(): Promise<VideoGroupType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<VideoGroupType[]> = await axios.get(
        ENDPOINTS.videoGroupUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video groups:', error)
      throw error
    }
  }

  /**
   * Get video sub-groups
   */
  static async getVideoSubGroups(): Promise<VideoSubGroupType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<VideoSubGroupType[]> = await axios.get(
        ENDPOINTS.videoSubGroupUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video sub-groups:', error)
      throw error
    }
  }

  /**
   * Get video list
   */
  static async getVideoList(): Promise<VideoListType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        ENDPOINTS.videoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching video list:', error)
      throw error
    }
  }

  /**
   * Get videos of a sub-group
   */
  static async getVideosOfASubGroup(
    subGroupId: string
  ): Promise<VideoListType[]> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.videoSubGroupUrl}/${subGroupId}/list`

    try {
      const response: AxiosResponse<VideoListType[]> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching videos of sub-group ${subGroupId}:`, error)
      throw error
    }
  }

  /**
   * Get video link
   */
  static async getVideoLink(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.videoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get video notes
   */
  static async getVideoNotes(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.videoLinkUrl}${videoId}/dl-notes`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching video notes for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get video comments list
   */
  static async getVideoCommentsList(videoId: string): Promise<any> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comments`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching video comments for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get testimony video list
   */
  static async getTestimonyVideoList(): Promise<StudentVideoListType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.studentVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching testimony video list:', error)
      throw error
    }
  }

  /**
   * Get testimony video link
   */
  static async getTestimonyVideoLink(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.studentVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(
        `Error fetching testimony video link for ${videoId}:`,
        error
      )
      throw error
    }
  }

  /**
   * Get review video list
   */
  static async getReviewVideoList(): Promise<StudentVideoListType[]> {
    if (!LoginRefresh.fnCheckUserLoginStatus()) {
      throw new Error('User not logged in')
    }

    const token = getJwtToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.reviewVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching review video list:', error)
      throw error
    }
  }

  /**
   * Get review video link
   */
  static async getReviewVideoLink(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.reviewVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching review video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get trial video list
   */
  static async getTrialVideoList(): Promise<StudentVideoListType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.trialVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching trial video list:', error)
      throw error
    }
  }

  /**
   * Get trial video link
   */
  static async getTrialVideoLink(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.trialVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching trial video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get workshop video list
   */
  static async getWorkshopVideoList(): Promise<StudentVideoListType[]> {
    const token = getJwtToken()

    try {
      const response: AxiosResponse<StudentVideoListType[]> = await axios.get(
        ENDPOINTS.workshopVideoListUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching workshop video list:', error)
      throw error
    }
  }

  /**
   * Get workshop video link
   */
  static async getWorkshopVideoLink(videoId: string): Promise<string> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.workshopVideoLinkUrl}${videoId}/link`

    try {
      const response: AxiosResponse<string> = await axios.get(url, {
        ...createAuthHeaders(token),
        responseType: 'text'
      })
      return response.data
    } catch (error) {
      console.error(`Error fetching workshop video link for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post video rating
   */
  static async postVideoRating(
    email: string,
    videoId: string,
    rating_given: number
  ): Promise<ApiResponse<any>> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/rating`

    try {
      const response: AxiosResponse<ApiResponse<any>> = await axios.post(
        url,
        { email, rating_given },
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting rating for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Get student video rating
   */
  static async getStudentVideoRating(
    videoId: string,
    email: string
  ): Promise<any> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/rating/${email}/check`

    try {
      const response: AxiosResponse<any> = await axios.get(
        url,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching student rating for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post video comment
   */
  static async postVideoComment(
    videoId: string,
    payload: { email: string; name: string; comment_text: string }
  ): Promise<any> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comment`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        payload,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting comment for ${videoId}:`, error)
      throw error
    }
  }

  /**
   * Post comment reply
   */
  static async postCommentReply(
    videoId: string,
    commentId: string,
    payload: { email: string; name: string; reply_text: string }
  ): Promise<any> {
    const token = getJwtToken()
    const url = `${ENDPOINTS.ratingAndCommentsUrl}${videoId}/comment/${commentId}/reply`

    try {
      const response: AxiosResponse<any> = await axios.post(
        url,
        payload,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error posting reply to comment ${commentId}:`, error)
      throw error
    }
  }
}
