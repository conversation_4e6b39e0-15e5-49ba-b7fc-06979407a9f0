<div class="videos-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Comprehensive list of videos broken down on a topic to topic basis for ease of learning.</p>
    </div>
  </div>
  <div class="video-header">
    <div *ngFor="let group of videoGroup; let i = index">
      <p [class.is-selected]="selectedGroup[i]" (click)="selectGroup(group.group_id, i)">{{ group.group_name }}</p>
    </div>
  </div>
  <div class="video-group--list">
    <div class="video-group" *ngFor="let subGroup of selectedGroupSubGroups; let i = index">
      <div class="group-name--bar">
        <h5>{{ subGroup.sub_group_name }}</h5>
        <div>
          <span>Number of Topics: {{ getNoOfTopics(subGroup.sub_group_id) }}</span>
          <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseVideoList">
            <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
              <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                transform="translate(-3.375 -3.375)" fill="#fff" />
            </svg>
          </button>
        </div>
      </div>
      <div class="videos" id="collapseVideoList" [collapse]="isCollapsed[i]" [isAnimated]="true">
        <div class="video-wrap">
          <div class="video" *ngFor="let video of filterBySubGroup(subGroup.sub_group_id)">
            <div class="video-content">
              <div class="locked-resource" (click)="takeToPlans()" *ngIf="video.public == 0" title="Premium Feature">
                <img src="../../../assets/icons/lock.svg" />
              </div>
              <div class="thmb-wrap">
                <img src="{{ video.thumbnail }}" />
              </div>
              <div class="video-name--bar">
                <h6>{{ video.video_name }}</h6> 
                <div class="video-rating">
                  <rating [(ngModel)]="video.rating" [max]="5" [readonly]="true"></rating>
                  <p>{{ video.length }}</p>
                </div>
              </div>
              <div>
                <button (click)="startStream(video.video_id, 1, video.rating)">Play Video</button>
                <button *ngIf="video.notes_link !== ''" (click)="getVideoNotes(video.video_id)">Download Notes</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>