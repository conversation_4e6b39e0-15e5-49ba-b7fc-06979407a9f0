// types/chapter-practice-types.ts
export interface SuperGroup {
  super_group_id: string
  super_group_name: string
}

export interface Group {
  group_id: string
  group_name: string
}

export interface ChapterPaper {
  paper_id: string
  paper_name: string
  group_id: string
  sub_group_id: string
  level: string
  no_of_ques: string
  status: string
  show_ans: string
  public: string
  once_ans: string
  created_at: string
  time_lim: string
  type: number
  neg_marks: string
  rand_ques: string
}
