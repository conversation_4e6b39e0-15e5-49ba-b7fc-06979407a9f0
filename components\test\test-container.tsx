'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import {
  Question,
  CompetitiveQuestion,
  CompanyQuestion,
  SectionData,
  PaperAdditionData
} from '@/types/test-types'

import { Button } from '@/components/ui/button'
import {
  prepareQuestions,
  shuffleQuestions,
  calculateSectionMarks
} from '@/lib/utils/question-utils'
import { ArrowLeft, Loader2 } from 'lucide-react'
import QuestionDisplay from './question-display'
import TestTimer from './test-timer'
import QuestionTracker from './question-tracker'
import { ConfirmStartModal, MarksModal, WarnModal } from './test-modal'
import { CountDownTimer } from '@/lib/utils/countdown-timer'
import { TestService } from '@/lib/client-services/test.service'

interface TestContainerProps {
  testType: number
  paperId: string
  paperName: string
  paperLim: number
  userEmail: string
}

const TestContainer: React.FC<TestContainerProps> = ({
  testType,
  paperId,
  paperName,
  paperLim,
  userEmail
}) => {
  // State management
  const [loading, setLoading] = useState(true)
  const [questions, setQuestions] = useState<Question[]>([])
  const [competitiveQuestions, setCompetitiveQuestions] = useState<
    CompetitiveQuestion[]
  >([])
  const [companyQuestions, setCompanyQuestions] = useState<CompanyQuestion[]>(
    []
  )
  const [modelQuestions, setModelQuestions] = useState<any[]>([])
  const [additionData, setAdditionData] = useState<PaperAdditionData[]>([])
  const [isCollapsed, setIsCollapsed] = useState<boolean[]>([])
  const [showAns, setShowAns] = useState(true)
  const [negMarks, setNegMarks] = useState(false)
  const [bPaperSubmitted, setBPaperSubmitted] = useState(false)
  const [marks, setMarks] = useState(0)
  const [sectionTrack, setSectionTrack] = useState<SectionData[]>([])
  const [noQues, setNoQues] = useState(0)
  const [answerId, setAnswerId] = useState('')
  const [isSuperUser, setIsSuperUser] = useState(false)
  const [showAllExpl, setShowAllExpl] = useState(false)
  const [minutes, setMinutes] = useState(0)
  const [seconds, setSeconds] = useState(0)
  const [blurQuestions, setBlurQuestions] = useState(paperLim > 0)
  const [showConfirmStartModal, setShowConfirmStartModal] = useState(
    paperLim > 0
  )
  const [showWarnModal, setShowWarnModal] = useState(false)
  const [showMarksModal, setShowMarksModal] = useState(false)
  const [showPopoutTimer, setShowPopoutTimer] = useState(false)

  const countDownTimerRef = useRef<CountDownTimer | null>(null)
  const router = useRouter()

  // Check if user is super user
  useEffect(() => {
    const superUsers = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ]
    setIsSuperUser(superUsers.includes(userEmail))
  }, [userEmail])

  // Load questions based on test type
  useEffect(() => {
    console.log(answerId)
    const fetchQuestions = async () => {
      try {
        let response
        switch (testType) {
          case 1: // Chapter wise papers
            response = await TestService.getChapterQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setAdditionData(response.paper_addition_data || [])
            }
            break

          case 2: // Competitive questions
            response = await TestService.getCompetitiveQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = response.questions.map(
                (q: CompetitiveQuestion) => {
                  q.bAnswered = false
                  q.options = [
                    q.opt_1,
                    q.opt_2,
                    q.opt_3,
                    q.opt_4,
                    q.opt_5
                  ].filter((opt) => opt)
                  q.selected_ans = -1
                  return q
                }
              )
              setCompetitiveQuestions(preparedQuestions)
              setAdditionData(response.paper_addition_data || [])
            }
            break

          case 3: // Company questions
            response = await TestService.getCompanyQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = response.questions.map(
                (q: CompanyQuestion) => {
                  q.bAnswered = false
                  q.options = [q.opt_1, q.opt_2, q.opt_3, q.opt_4].filter(
                    (opt) => opt
                  )
                  q.selected_ans = -1
                  return q
                }
              )
              setCompanyQuestions(preparedQuestions)
              setAdditionData(response.paper_addition_data || [])
            }
            break

          case 4: // Model questions
            response = await TestService.getModelQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = response.questions.map((q: any) => {
                q.bAnswered = false
                q.options = [
                  q.option_1,
                  q.option_2,
                  q.option_3,
                  q.option_4
                ].filter((opt) => opt)
                q.selected_ans = -1
                return q
              })
              setModelQuestions(preparedQuestions)
              setShowAns(response.paper_data.show_ans === '1')
              setAdditionData(response.paper_addition_data || [])
            }
            break

          case 5:
          case 7: // Trial papers
            response = await TestService.getTrialQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setShowAns(response.paper_config.show_ans === '1')
              setAdditionData(response.paper_addition_data || [])

              // Disable right-click and text selection for trial papers
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          case 8: // Open competitive
            response = await TestService.getOpenCompetitiveQuestions(paperId)
            if (response.questions) {
              let preparedQuestions = prepareQuestions(response.questions)
              preparedQuestions = preparedQuestions.map((q) => {
                q.original_qno = q.question_no
                return q
              })

              setQuestions(preparedQuestions)
              setShowAns(response.paper_config.show_ans === '1')
              setNegMarks(response.paper_config.neg_marks === '1')
              setAdditionData(response.paper_addition_data || [])

              // Randomize questions if enabled
              if (
                response.paper_config.rand_ques === '1' &&
                response.paper_addition_data
              ) {
                setQuestions(
                  shuffleQuestions(preparedQuestions, true, null, null)
                )
              }

              // Disable right-click and text selection
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          case 9: // Section wise papers
            response = await TestService.getSectionWiseQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setShowAns(response.paper_config.show_ans === '1')
              setAdditionData(response.paper_addition_data || [])

              // Disable right-click and text selection
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          case 10: // AFCAT papers
            response = await TestService.getAfcatQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setShowAns(response.paper_config.show_ans === '1')
              setAdditionData(response.paper_addition_data || [])

              // Disable right-click and text selection
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          case 11: // TMCQ
            response = await TestService.getTMCQQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setShowAns(response.paper_config.show_ans === '1')
              setAdditionData(response.paper_addition_data || [])

              // Disable right-click and text selection
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          case 13:
          case 14: // Competitive paper questions
            response = await TestService.getCompetitivePaperQuestions(paperId)
            if (response.questions) {
              const preparedQuestions = prepareQuestions(response.questions)
              setQuestions(preparedQuestions)
              setAdditionData(response.paper_addition_data || [])

              // Disable right-click and text selection
              if (typeof window !== 'undefined') {
                document.addEventListener('contextmenu', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('selectstart', (e) =>
                  e.preventDefault()
                )
                document.addEventListener('copy', (e) => e.preventDefault())
              }
            }
            break

          default:
            break
        }
      } catch (error) {
        console.error('Error fetching questions:', error)
      } finally {
        setLoading(false)

        // Initialize collapsed state for all questions
        const initialCollapsedState = []
        const questionCount = Math.max(
          questions.length,
          competitiveQuestions.length,
          companyQuestions.length,
          modelQuestions.length
        )

        for (let i = 0; i < questionCount + 50; i++) {
          initialCollapsedState.push(true)
        }
        setIsCollapsed(initialCollapsedState)
      }
    }

    fetchQuestions()

    // Cleanup function
    return () => {
      if (countDownTimerRef.current) {
        countDownTimerRef.current.stop()
      }

      if (typeof window !== 'undefined') {
        document.removeEventListener('contextmenu', (e) => e.preventDefault())
        document.removeEventListener('selectstart', (e) => e.preventDefault())
        document.removeEventListener('copy', (e) => e.preventDefault())
      }
    }
  }, [testType, paperId])

  // Handle pop-out timer
  useEffect(() => {
    const handleScroll = () => {
      if (typeof window !== 'undefined') {
        const timerEl = document.querySelector('.time-box')
        if (timerEl) {
          const timerRect = timerEl.getBoundingClientRect()
          setShowPopoutTimer(timerRect.top < 0)
        }
      }
    }

    if (paperLim > 0 && typeof window !== 'undefined') {
      if (window.innerWidth <= 440) {
        setShowPopoutTimer(true)
      }
      window.addEventListener('scroll', handleScroll)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('scroll', handleScroll)
      }
    }
  }, [paperLim])

  // Start test timer
  const startTest = () => {
    setBlurQuestions(false)
    setShowConfirmStartModal(false)

    // Start timer
    countDownTimerRef.current = new CountDownTimer(paperLim * 60, 1000)

    const timeObj = CountDownTimer.parse(paperLim * 60)
    formatTime(timeObj.minutes, timeObj.seconds)

    countDownTimerRef.current.onTick(formatTime)
    countDownTimerRef.current.onTick(checkTime)
    countDownTimerRef.current.start()
  }

  // Format time for display
  const formatTime = (minutes: number, seconds: number) => {
    setMinutes(minutes)
    setSeconds(seconds)
  }

  // Check if time is up
  const checkTime = (minutes: number, seconds: number) => {
    if (minutes === 0 && seconds === 0) {
      evaluateAnswers(true)
    }
  }

  // Toggle explanation collapse
  const toggleCollapse = (index: number) => {
    const newCollapsed = [...isCollapsed]
    newCollapsed[index] = !newCollapsed[index]
    setIsCollapsed(newCollapsed)
  }

  // Handle option selection
  const handleOptionSelect = (questionNo: string, optionIndex: number) => {
    if (questions.length > 0) {
      setQuestions((prevQuestions) => {
        return prevQuestions.map((q) => {
          if ((q.question_no || q.ques_no) === questionNo) {
            return { ...q, bAnswered: true, selected_ans: optionIndex }
          }
          return q
        })
      })
    } else if (competitiveQuestions.length > 0) {
      setCompetitiveQuestions((prevQuestions) => {
        return prevQuestions.map((q) => {
          if (q.question_no === questionNo) {
            return { ...q, bAnswered: true, selected_ans: optionIndex }
          }
          return q
        })
      })
    } else if (companyQuestions.length > 0) {
      setCompanyQuestions((prevQuestions) => {
        return prevQuestions.map((q) => {
          if (q.question_no === questionNo) {
            return { ...q, bAnswered: true, selected_ans: optionIndex }
          }
          return q
        })
      })
    } else if (modelQuestions.length > 0) {
      setModelQuestions((prevQuestions) => {
        return prevQuestions.map((q) => {
          if (q.question_no === questionNo) {
            return { ...q, bAnswered: true, selected_ans: optionIndex }
          }
          return q
        })
      })
    }
  }

  // Scroll to a specific question
  const scrollToQuestion = (questionNo: string) => {
    if (typeof window !== 'undefined') {
      const element = document.getElementById(`qm-q--${questionNo}`)
      if (element) {
        window.scrollTo({ top: element.offsetTop - 30, behavior: 'smooth' })
      }
    }
  }

  // Evaluate answers and submit to server
  const evaluateAnswers = async (override: boolean) => {
    // Check if all questions are answered
    let allAnswered = true
    let currentQuestions: any[] = []

    if (questions.length > 0) {
      currentQuestions = questions
    } else if (competitiveQuestions.length > 0) {
      currentQuestions = competitiveQuestions
    } else if (companyQuestions.length > 0) {
      currentQuestions = companyQuestions
    } else if (modelQuestions.length > 0) {
      currentQuestions = modelQuestions
    }

    if (!override) {
      allAnswered = currentQuestions.every((q) => q.bAnswered)

      if (!allAnswered) {
        setShowWarnModal(true)
        return
      }
    }

    setLoading(true)

    // Calculate marks
    let calculatedMarks = 0
    setNoQues(currentQuestions.length)

    // For questions with section breakdown
    if (questions.length > 0) {
      const { sectionMarks, sectionTrack } = calculateSectionMarks(
        questions,
        additionData,
        paperId,
        negMarks
      )

      // Calculate total marks
      for (const question of questions) {
        if (question.selected_ans === parseInt(question.correct_opt, 10)) {
          calculatedMarks += negMarks ? 2 : 1
        } else if (question.selected_ans !== -1 && negMarks) {
          calculatedMarks -= 1
        }
      }

      setSectionTrack(sectionTrack)

      // Submit to server
      try {
        let response

        switch (testType) {
          case 1:
            response = await TestService.submitChapterMarks(
              userEmail,
              paperId,
              calculatedMarks
            )
            break
          case 7:
            response = await TestService.submitTrialAnswers(
              userEmail,
              paperId,
              calculatedMarks
            )
            if (response.text === '200') {
              setAnswerId(response.asnwer_id)

              // Submit section breakdown if available
              if (sectionTrack.length > 0) {
                const reqBody = Object.keys(sectionMarks).map((key) => ({
                  question_no: key.split(':')[1],
                  marks: sectionMarks[key]
                }))

                await TestService.submitSectionBreakdown(
                  userEmail,
                  paperId,
                  response.asnwer_id,
                  reqBody
                )
              }
            }
            break
          case 8:
            response = await TestService.submitOpenCompetitiveAnswers(
              userEmail,
              paperId,
              calculatedMarks
            )
            if (response.text === 'Answer Submitted') {
              setAnswerId(response.asnwer_id)

              // Submit section breakdown if available
              if (sectionTrack.length > 0) {
                const reqBody = Object.keys(sectionMarks).map((key) => ({
                  question_no: key.split(':')[1],
                  marks: sectionMarks[key]
                }))

                await TestService.submitSectionBreakdown(
                  userEmail,
                  paperId,
                  response.asnwer_id,
                  reqBody
                )
              }
            }
            break
          case 9:
          case 10:
            response = await TestService.submitSectionWiseAnswers(
              userEmail,
              paperId,
              calculatedMarks
            )
            if (response.text === 'Answer Submitted') {
              setAnswerId(response.asnwer_id)

              // Submit section breakdown if available
              if (sectionTrack.length > 0) {
                const reqBody = Object.keys(sectionMarks).map((key) => ({
                  question_no: key.split(':')[1],
                  marks: sectionMarks[key]
                }))

                await TestService.submitSectionBreakdown(
                  userEmail,
                  paperId,
                  response.asnwer_id,
                  reqBody
                )
              }
            }
            break
          case 11:
            response = await TestService.submitTMCQMarks(
              paperId,
              userEmail,
              calculatedMarks
            )
            break
          case 13:
          case 14:
            response = await TestService.submitCompetitiveMarks(
              paperId,
              userEmail,
              calculatedMarks
            )
            break
          default:
            break
        }
      } catch (error) {
        console.error('Error submitting answers:', error)
      }
    } else if (competitiveQuestions.length > 0) {
      // Calculate marks for competitive questions
      for (const question of competitiveQuestions) {
        if (question.selected_ans === parseInt(question.correct_opt, 10)) {
          calculatedMarks++
        }
      }

      try {
        await TestService.submitCompetitiveMarks(
          userEmail,
          paperId,
          calculatedMarks
        )
      } catch (error) {
        console.error('Error submitting competitive answers:', error)
      }
    } else if (companyQuestions.length > 0) {
      // Calculate marks for company questions
      for (const question of companyQuestions) {
        if (question.selected_ans === parseInt(question.correct_opt, 10)) {
          calculatedMarks++
        }
      }

      try {
        await TestService.submitCompanyMarks(
          userEmail,
          paperId,
          calculatedMarks
        )
      } catch (error) {
        console.error('Error submitting company answers:', error)
      }
    } else if (modelQuestions.length > 0) {
      // Calculate marks for model questions
      for (const question of modelQuestions) {
        if (question.selected_ans === parseInt(question.correct_opt, 10)) {
          calculatedMarks++
        }
      }

      try {
        await TestService.submitModelAnswers(
          userEmail,
          paperId,
          calculatedMarks
        )
      } catch (error) {
        console.error('Error submitting model answers:', error)
      }
    }

    setMarks(calculatedMarks)
    setBPaperSubmitted(true)
    setLoading(false)
    setShowMarksModal(true)

    // Stop timer
    if (countDownTimerRef.current) {
      countDownTimerRef.current.stop()
    }
  }

  const goBack = () => {
    router.back()
  }

  // Determine which questions to display
  const displayQuestions = () => {
    if (questions.length > 0) {
      return questions
    } else if (competitiveQuestions.length > 0) {
      return competitiveQuestions
    } else if (companyQuestions.length > 0) {
      return companyQuestions
    } else if (modelQuestions.length > 0) {
      return modelQuestions
    }
    return []
  }

  const currentQuestions = displayQuestions()

  return (
    <div className="test-container">
      {loading && (
        <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      <div className="header bg-white p-4 shadow rounded-md mb-4">
        <div className="flex justify-between items-center">
          <Button variant="ghost" onClick={goBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>

          <div className="title flex-1 text-center">
            <h1 className="text-xl font-bold">{paperName}</h1>
          </div>

          {paperLim > 0 && testType !== 6 && (
            <div className="time-box">
              <TestTimer minutes={minutes} seconds={seconds} />
            </div>
          )}
        </div>
      </div>

      <div className={`test-content ${blurQuestions ? 'filter blur-md' : ''}`}>
        <div className="test-area space-y-6">
          {currentQuestions.map((question, index) => (
            <QuestionDisplay
              key={question.question_no || question.ques_no || index}
              question={question}
              index={index}
              additionData={additionData}
              bPaperSubmitted={bPaperSubmitted}
              showAns={showAns}
              isCollapsed={isCollapsed}
              toggleCollapse={toggleCollapse}
              handleOptionSelect={handleOptionSelect}
              showAllExpl={showAllExpl}
              testType={testType}
              negMarks={negMarks}
            />
          ))}
        </div>
      </div>

      {testType !== 6 && (
        <>
          <QuestionTracker
            questions={currentQuestions}
            scrollToQuestion={scrollToQuestion}
            isSuperUser={isSuperUser}
            showAllExpl={showAllExpl}
            toggleShowAllExpl={() => setShowAllExpl(!showAllExpl)}
            marksInfo={
              bPaperSubmitted
                ? {
                    marks,
                    noQues,
                    negMarks,
                    sectionTrack
                  }
                : undefined
            }
            showPopoutTimer={showPopoutTimer}
          />

          <div className="footer fixed bottom-0 left-0 w-full bg-white p-4 shadow-lg border-t">
            <div className="container mx-auto flex justify-center">
              <Button
                disabled={bPaperSubmitted}
                onClick={() => evaluateAnswers(false)}
                className="w-full max-w-md"
              >
                Submit
              </Button>
            </div>
            <div className="py-2 text-center text-sm text-gray-500">
              &copy; {new Date().getFullYear()} Quant Masters. All Rights
              Reserved.
            </div>
          </div>
        </>
      )}

      {/* Popout timer for mobile */}
      {showPopoutTimer && paperLim > 0 && testType !== 6 && (
        <TestTimer minutes={minutes} seconds={seconds} isPopout />
      )}

      {/* Modals */}
      <WarnModal
        isOpen={showWarnModal}
        onClose={() => setShowWarnModal(false)}
        onConfirm={() => {
          setShowWarnModal(false)
          evaluateAnswers(true)
        }}
      />

      <MarksModal
        isOpen={showMarksModal}
        onClose={() => setShowMarksModal(false)}
        marks={marks}
        totalMarks={negMarks ? noQues * 2 : noQues}
        sectionTrack={sectionTrack}
      />

      <ConfirmStartModal
        isOpen={showConfirmStartModal}
        onClose={goBack}
        onStart={startTest}
        paperLim={paperLim}
        showAns={showAns}
        negMarks={negMarks}
      />
    </div>
  )
}

export default TestContainer
