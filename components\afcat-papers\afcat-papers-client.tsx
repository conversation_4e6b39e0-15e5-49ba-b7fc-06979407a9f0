'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Clock, FileText, ArrowR<PERSON> } from 'lucide-react'
import { ChapterPaper } from '@/types/afcat-paper-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface AfcatPapersClientProps {
  initialPapers: ChapterPaper[]
}

export default function AfcatPapersClient({
  initialPapers
}: AfcatPapersClientProps) {
  const router = useRouter()
  const papers = initialPapers
  const [isLoading, setIsLoading] = useState(false)

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    timeLim: number
  ) => {
    setIsLoading(true)
    router.push(`/dashboard/test/10/${paperId}/${paperName}/${timeLim}`)
  }

  const navigateToPlans = () => {
    router.push('/plans')
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading papers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="afcat-papers-wrap">
      {papers.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-gray-600 mb-4">
            No AFCAT papers available at the moment.
          </p>
          <Button onClick={navigateToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {papers.map((paper) => (
            <Card key={paper.paper_id} className="h-full flex flex-col">
              <CardHeader>
                <CardTitle>{paper.paper_name}</CardTitle>
                <CardDescription className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    {paper.time_lim
                      ? `${paper.time_lim / 60000} Minutes`
                      : 'No Time Limit'}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-1">
                  <FileText className="h-4 w-4" />
                  <span>No. of Questions: {paper.no_of_ques}</span>
                </div>
              </CardContent>
              <CardFooter className="mt-auto">
                <Button
                  onClick={() =>
                    handleBeginTest(
                      paper.paper_id,
                      paper.paper_name,
                      paper.time_lim
                    )
                  }
                  className="w-full"
                >
                  Begin Test
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
