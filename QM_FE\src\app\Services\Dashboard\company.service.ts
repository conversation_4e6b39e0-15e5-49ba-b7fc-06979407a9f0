import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { CompanyPapers } from '../../Models/Dashboard/Company/CompanyPapers';

@Injectable({
  providedIn: 'root'
})
export class CompanyService {

  private papersUrl = 'https://api.quantmasters.in/test/company/papers';
  private submitUrl = 'https://api.quantmasters.in/test/company/submit/marks';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getAllPapers(): Observable<CompanyPapers[]> {
    this.setSecurityToken();

    const groupPaperUrl = this.papersUrl;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompanyPapers[]>(groupPaperUrl, httpOps);
  }

  submitMarks(email: string, paperId: string, marks: number): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const data = {
      email: email,
      paper_id: paperId,
      marks: marks
    };

    return this.http.post<string>(this.submitUrl, data, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
