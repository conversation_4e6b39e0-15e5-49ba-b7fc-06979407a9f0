import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium transition-transform text-sm rounded-lg text-center disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          'text-white bg-gradient-to-br from-blue-500 to-sky-600 shadow-md shadow-gray-300 hover:scale-[1.02]',
        destructive:
          'text-white bg-gradient-to-br from-red-500 to-red-700 shadow-md shadow-gray-300 hover:scale-[1.02]',
        outline:
          'text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200',
        secondary:
          'text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:ring-gray-200',
        ghost: 'text-gray-700 hover:bg-gray-100 hover:text-gray-900',
        link: 'text-sky-600 underline-offset-4 hover:underline',
        dark: 'text-white bg-gradient-to-br from-gray-700 to-gray-900 shadow-md shadow-gray-300 hover:scale-[1.02]',
        success:
          'text-white bg-gradient-to-br from-green-500 to-green-700 shadow-md shadow-gray-300 hover:scale-[1.02]',
        warning:
          'text-white bg-gradient-to-br from-yellow-500 to-yellow-700 shadow-md shadow-gray-300 hover:scale-[1.02]',
        info: 'text-white bg-gradient-to-br from-blue-500 to-blue-700 shadow-md shadow-gray-300 hover:scale-[1.02]'
      },
      size: {
        default: 'px-5 py-2.5',
        sm: 'px-3 py-2 text-xs',
        lg: 'px-6 py-3',
        icon: 'size-9 p-2'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : 'button'

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
