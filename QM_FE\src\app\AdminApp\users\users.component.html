<div class="user-wrap">
    <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
    <div class="sect-1">
        <div class="info-box" [class.selected]="globalDisplayAllSelected" (click)="getAllUsers('ByClick')">
            <h2>{{ regCount }}</h2>
            <p>Total Registrations</p>
        </div>
        <div class="info-box" [class.selected]="globalDisplayPurchasedSelected" (click)="getPurchasedUsers()">
            <h2>{{ purCount }}</h2>
            <p>{{ globalPurchasedBoxText }}</p>
        </div>
        <div class="action-icon" [class.selected]="globalDisplayPurchasedSelected"
            (click)="globalSettingPurchased = !globalSettingPurchased">
            <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 146 86.3"
                style="enable-background:new 0 0 146 86.3;" xml:space="preserve">
                <style type="text/css">
                    .st00 {
                        fill: #FFFFFF;
                        stroke: #000000;
                        stroke-miterlimit: 10;
                    }

                    .st11 {
                        fill: #000000;
                        stroke: #000000;
                        stroke-miterlimit: 10;
                    }

                    .st22 {
                        fill: #F94343;
                        stroke: #F94343;
                        stroke-miterlimit: 10;
                    }
                </style>
                <polygon id="XMLID_13_" class="st00" points="69,0 69,0 69,0 69,0 69,0 69,0 " />
                <g id="XMLID_14_">
                    <polygon id="XMLID_16_" class="st11" points="0.5,5.3 68.8,45.4 0.5,85.4 	" />
                    <polygon id="XMLID_15_" class="st22" points="145.5,85.4 77.2,45.4 145.5,5.3 	" />
                </g>
            </svg>
        </div>
        <div class="info-box" [class.selected]="globalDisplayLastWeekSelected" (click)="getAllUsersOfLastWeek()">
            <h2>{{ wkCount }}</h2>
            <p>Last Week Registrations</p>
        </div>
        <div class="additional-controls-box">
            <button class="custom-btn grant-btn" (click)="showPermissionBox()" type="button">Grant Access</button>
            <button class="custom-btn grant-btn" (click)="showBulkGrant()" type="button">Bulk Grant Access</button>
            <button class="custom-btn grant-btn" (click)="getUserLoginData('ByClick')" type="button">Get Login
                Data</button>
            <button class="custom-btn grant-btn" (click)="getCertificationData('ByClick')"
                type="button">Certification</button>
        </div>
    </div>
    <div class="sect-15">
        <form #searchForm="ngForm" (ngSubmit)="searchForUser(searchForm.valid)"
            *ngIf="usersList && usersList.length > 0">
            <h6>User Search</h6>
            <div class="form-elem">
                <input name="userEmail" type="text" [(ngModel)]="userSearchEmail" #userEmail="ngModel"
                    placeholder="email"
                    [class.is-inv]="userEmail.invalid && userEmail.touched || (userEmail.errors?.required && searchForm.submitted)"
                    required email />
                <small class="form-error--text"
                    *ngIf="userEmail.errors?.required && userEmail.touched || (userEmail.pristine && searchForm.submitted)">
                    Email is Required
                </small>
                <small class="form-error--text" *ngIf="userEmail.errors?.email && searchForm.touched">
                    Email is Invalid
                </small>
                <button class="custom-btn search-btn" type="submit">Search</button>
            </div>
        </form>
        <div *ngIf="certificationTrack && certificationTrack.length > 0">
            <button type="button" (click)="addNewCertificate()" class="custom-btn grant-btn">Add User</button>
        </div>
        <div class="page-ctrls" *ngIf="loginTrack && loginTrack.length > 0">
            <p>Page: </p>
            <button type="button" (click)="navigatePageFirst('Login')">First</button>
            <button type="button" (click)="navigatePageLeft('Login')">Prev</button>
            <button type="button" (click)="navigatePageRight('Login')">Next</button>
        </div>
        <div class="page-ctrls ctrls-cert" *ngIf="certificationTrack && certificationTrack.length > 0">
            <p>Page: </p>
            <button type="button" (click)="navigatePageFirst('Cert')">First</button>
            <button type="button" (click)="navigatePageLeft('Cert')">Prev</button>
            <button type="button" (click)="navigatePageRight('Cert')">Next</button>
        </div>
        <div class="page-ctrls ctrls-user" *ngIf="usersList && usersList.length > 0">
            <p>Page: </p>
            <button type="button" (click)="navigatePageFirst('User')">First</button>
            <button type="button" (click)="navigatePageLeft('User')">Prev</button>
            <button type="button" (click)="navigatePageRight('User')">Next</button>
        </div>
    </div>
    <div class="sect-2" *ngIf="usersList && usersList.length > 0">
        <div class="users-headers">
            <h6>Name</h6>
            <h6>Email</h6>
            <h6>Phone</h6>
            <h6>D.O.B</h6>
            <div class="sub-header">
                <h6>College/Institute</h6>
                <div class="sub-sub-header">
                    <h6>Qualification</h6>
                    <h6>Year of Passing</h6>
                </div>
            </div>
            <h6>USN</h6>
            <h6>Registred On</h6>
        </div>
        <div class="users-data" *ngFor="let user of displayUsers">
            <p>{{ user.f_name }} {{ user.l_name }}</p>
            <p>{{ user.email }}</p>
            <p>{{ user.phone_no }}</p>
            <p>{{ user.dob }}</p>
            <div class="sub-data">
                <p>{{ user.inst_name }}</p>
                <div class="sub-sub-data">
                    <p>{{ user.qual }}</p>
                    <p>{{ user.yop }}</p>
                </div>
            </div>
            <p>{{ user.usn }}</p>
            <p>{{ user.created_at.split("T")[0] }}</p>
        </div>
    </div>

    <div class="sect-2" *ngIf="loginTrack && loginTrack.length > 0">
        <div class="users-headers track-headers">
            <h6>Email</h6>
            <h6>IP address</h6>
            <h6>Browser</h6>
            <h6>OS</h6>
            <h6>Platform</h6>
            <h6>Latitude</h6>
            <h6>Longitude</h6>
            <h6>Plot</h6>
        </div>
        <div class="users-data track-data" *ngFor="let data of loginTrack">
            <p>{{ data.email }}</p>
            <p>{{ data.last_ip }}</p>
            <p>{{ data.browser }}</p>
            <p>{{ data.os }}</p>
            <p>{{ data.platform }}</p>
            <p>{{ data.latitude }}</p>
            <p>{{ data.longitude }}</p>
            <button (click)="openMap(data.latitude, data.longitude)">Map</button>
        </div>
    </div>

    <div class="sect-2" *ngIf="certificationTrack && certificationTrack.length > 0">
        <div class="users-headers cert-headers">
            <h6>Email</h6>
            <h6>Certificate ID</h6>
            <h6>Category</h6>
            <h6>Filename</h6>
            <h6>Geanted On</h6>
        </div>
        <div class="users-data cert-data" *ngFor="let data of certificationTrack">
            <p>{{ data.email }}</p>
            <p>{{ data.display_id }}</p>
            <p>{{ data.display_Category}}</p>
            <p>{{ data.file_loc }}</p>
            <p>{{ data.updated_at }}</p>
        </div>
    </div>

    <ng-template #UserAccessTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-info">Grant User Access</h4>
            <button type="button" class="close pull-right" aria-label="Close"
                (click)="modalRef.hide(); grantAcessEmail = ''; grantAccessName = ''; grantAcessLevel = ''">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <div *ngIf="bulkGrant">
                <form autocomplete="off" #bulkAccessForm="ngForm" (ngSubmit)="updateBulkAcess(bulkAccessForm.valid)">
                    <div class="form-row--1 row-spl">
                        <div class="form-elem">
                            <label for="emails">Enter comma separated emails below</label>
                            <textarea name="emails" placeholder="<EMAIL>,<EMAIL>"
                                [(ngModel)]="bulkGrantEmails" #emails="ngModel" required></textarea>
                            <small class="form-error--text"
                                *ngIf="emails.errors?.required && emails.touched || (emails.pristine && bulkAccessForm.submitted)">
                                At Least one email is Required
                            </small>
                        </div>
                        <div class="form-elem"><button type="submit">Grant Access</button></div>
                    </div>
                </form>
            </div>
            <div *ngIf="!bulkGrant">
                <form autocomplete="off" #accessForm="ngForm" (ngSubmit)="getAcess(accessForm.valid)">
                    <div class="form-row--1">
                        <div class="form-elem">
                            <input type="email" name="email" placeholder="Email *" aria-describedby="emailHelpBlock"
                                [(ngModel)]="grantAcessEmail" #email="ngModel" required email />
                            <small class="form-error--text"
                                *ngIf="email.errors?.required && email.touched || (email.pristine && accessForm.submitted)">
                                Email is Required
                            </small>
                            <small class="form-error--text" *ngIf="email.errors?.email && email.touched">
                                Email is Invalid
                            </small>
                        </div>
                        <div class="form-row--1">
                            <button type="submit" class="custom-btn register-btn">
                                View Access
                            </button>
                        </div>
                    </div>
                </form>
                <div class="notFound-wrap" *ngIf="grantUserNFMsg">
                    <p>Email Not Found, Kindly ask them to register first.</p>
                </div>
                <div class="user-table" *ngIf="grantAcessLevel != ''">
                    <div class="header">
                        <h6>Email</h6>
                        <h6>Name</h6>
                        <h6>Access Level</h6>
                        <h6></h6>
                    </div>
                    <div class="content">
                        <p>{{ grantAcessEmail }}</p>
                        <p>{{ grantAccessName }}</p>
                        <p>{{ grantAcessLevel }}</p>
                        <p></p>
                    </div>
                    <!-- <button *ngIf="grantAcessLevel === 'Registered' || grantAcessLevel === 'Purchased Premium'" (click)="elevateToPurchased()" class="custom-btn level-btn">
            Grant Basic Access
          </button> -->
                    <button *ngIf="grantAcessLevel === 'Registered' || grantAcessLevel === 'Purchased Basic'"
                        (click)="elevateToPurchasedPremium()" class="custom-btn level-btn premium-btn">
                        Grant Premium Access
                    </button>
                    <button
                        *ngIf="grantAcessLevel !== 'Registered' && grantAcessLevel !== 'Admin' && grantAcessLevel !== 'Admin Read'"
                        (click)="revokeToRegistered()" class="custom-btn level-btn revoke-btn"
                        title="Careful: Backlash may happen">
                        Revoke Access!
                    </button>
                </div>
            </div>
        </div>
    </ng-template>

    <ng-template #successTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-success">Success!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p>User Access Granted!</p>
        </div>
    </ng-template>

    <ng-template #errTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-danger">Oopsie!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p>{{ errMsg }}</p>
        </div>
    </ng-template>

    <ng-template #userDetsTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-primary">User Details</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form #userDetsForm="ngForm" (ngSubmit)="saveUSerDetails(userDetsForm.valid)">
                <div class="form-group">
                    <button class="img-updl--btn1" type="button" (click)="userDetailsEdit = !userDetailsEdit">
                        <img src="../../../assets/push-icons/edit-form.svg" />
                    </button>
                </div>
                <div class="form-group">
                    <label for="fName">First Name: </label>
                    <div class="form-elem">
                        <input type="text" name="fName" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.f_name"
                            #fName="ngModel"
                            [class.is-inv]="fName.invalid && fName.touched || (fName.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="fName.errors?.required && fName.touched || (fName.pristine && userDetsForm.submitted)">
                            First Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="lName">Last Name: </label>
                    <div class="form-elem">
                        <input type="text" name="lName" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.l_name"
                            #lName="ngModel"
                            [class.is-inv]="lName.invalid && lName.touched || (lName.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="lName.errors?.required && lName.touched || (lName.pristine && userDetsForm.submitted)">
                            Last Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="dob">DoB: </label>
                    <div class="form-elem">
                        <input type="text" name="dob" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.dob"
                            #dob="ngModel"
                            [class.is-inv]="dob.invalid && dob.touched || (dob.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="dob.errors?.required && dob.touched || (dob.pristine && userDetsForm.submitted)">
                            DoB is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="phone">Phone No: </label>
                    <div class="form-elem">
                        <input type="text" name="phone" [disabled]="!userDetailsEdit"
                            [(ngModel)]="searchedUser.phone_no" #phone="ngModel"
                            [class.is-inv]="phone.invalid && phone.touched || (phone.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="phone.errors?.required && phone.touched || (phone.pristine && userDetsForm.submitted)">
                            Phone Number is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="uEmail">Email: </label>
                    <div class="form-elem">
                        <input type="text" name="uEmail" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.email"
                            #uEmail="ngModel"
                            [class.is-inv]="uEmail.invalid && uEmail.touched || (uEmail.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="uEmail.errors?.required && uEmail.touched || (uEmail.pristine && userDetsForm.submitted)">
                            Email is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="instName">Institute Name: </label>
                    <div class="form-elem">
                        <input type="text" name="instName" [disabled]="!userDetailsEdit"
                            [(ngModel)]="searchedUser.inst_name" #instName="ngModel"
                            [class.is-inv]="instName.invalid && instName.touched || (instName.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="instName.errors?.required && instName.touched || (instName.pristine && userDetsForm.submitted)">
                            Institute Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="qual">Qualification: </label>
                    <div class="form-elem">
                        <input type="text" name="qual" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.qual"
                            #qual="ngModel"
                            [class.is-inv]="qual.invalid && qual.touched || (qual.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="qual.errors?.required && qual.touched || (qual.pristine && userDetsForm.submitted)">
                            Qualification is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="usn">USN: </label>
                    <input type="text" name="usn" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.usn"
                        #usn="ngModel" />
                </div>
                <div class="form-group">
                    <label for="yop">Year of Passing: </label>
                    <div class="form-elem">
                        <input type="text" name="yop" [disabled]="!userDetailsEdit" [(ngModel)]="searchedUser.yop"
                            #yop="ngModel"
                            [class.is-inv]="yop.invalid && yop.touched || (yop.errors?.required && userDetsForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="yop.errors?.required && yop.touched || (yop.pristine && userDetsForm.submitted)">
                            Year of Passing is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <button class="custom-btn search-btn" type="submit" [disabled]="!userDetailsEdit">Save</button>
                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #userSuccessTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-success">Success!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p *ngIf="bulkGrantResults.length == 0">Data Saved Successfully!</p>
            <div *ngIf="bulkGrantResults.length > 0">
                <p *ngFor="let result of bulkGrantResults">{{ result }}</p>
            </div>
        </div>
    </ng-template>

    <ng-template #userMapTemplate>
        <!--<agm-map [latitude]="selectedLat" [longitude]="selectedLng">
          <agm-marker [latitude]="selectedLat" [longitude]="selectedLng"></agm-marker>
        </agm-map> -->
    </ng-template>

    <ng-template #certificateTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-primary">User Details</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form #userCertForm="ngForm" (ngSubmit)="saveUSerCertDetails(userCertForm.valid)">
                <div></div>
                <div class="form-group">
                    <label for="uEmail">Email: </label>
                    <div class="form-elem">
                        <input type="text" name="uEmail" [(ngModel)]="searchedCertUser.email" #uEmail="ngModel"
                            [class.is-inv]="uEmail.invalid && uEmail.touched || (uEmail.errors?.required && userCertForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="uEmail.errors?.required && uEmail.touched || (uEmail.pristine && userCertForm.submitted)">
                            Email is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="name">Candidate Name: </label>
                    <div class="form-elem">
                        <input type="text" name="name" [(ngModel)]="searchedCertUser.name"
                            #name="ngModel"
                            [class.is-inv]="name.invalid && name.touched || (name.errors?.required && userCertForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="name.errors?.required && name.touched || (name.pristine && userCertForm.submitted)">
                            Name is Required
                        </small>
                    </div>
                </div>
                <!-- <div class="form-group">
                    <label for="display_id">Display Id: </label>
                    <div class="form-elem">
                        <input type="text" name="display_id" [(ngModel)]="searchedCertUser.display_id"
                            #display_id="ngModel"
                            [class.is-inv]="display_id.invalid && display_id.touched || (display_id.errors?.required && userCertForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="display_id.errors?.required && display_id.touched || (display_id.pristine && userCertForm.submitted)">
                            Display id is Required
                        </small>
                    </div>
                </div> -->
                <div class="form-group">
                    <label for="Category">Category: </label>
                    <div class="form-elem">
                        <select name="Category" (change)="selectCategory()" [(ngModel)]="searchedCertUser.category"
                            #Category="ngModel" class="custom-select form-control" required
                            [class.is-inv]="Category.invalid && Category.touched || (Category.errors?.required && userCertForm.submitted)">
                            <option [ngValue]="null">-- Please choose category--</option>
                            <option value="1">Excellence</option>
                            <option value="2">Achievement</option>
                            <option value="3">Complition</option>
                        </select>
                        <small class="form-error--text"
                            *ngIf="Category.errors?.required && Category.touched || (Category.pristine && userCertForm.submitted)">
                            Category is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <button class="custom-btn search-btn" type="submit">Save</button>
                </div>
            </form>
        </div>
    </ng-template>
</div>
