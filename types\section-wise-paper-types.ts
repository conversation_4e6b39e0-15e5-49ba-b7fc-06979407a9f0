export interface ChapterPaper {
  paper_id: string
  paper_name: string
  group_id: string
  sub_group_id: string
  level: string
  no_of_ques: string
  status: string
  show_ans: string
  public: string
  once_ans: string
  created_at: string
  time_lim: number
  type: number
  neg_marks: string
  rand_ques: string
}

export interface SectionWiseMarks {
  email: string
  paper_id: string
  marks: number
}

export interface AnswerSubmission {
  email: string
  paper_id: string
  marks: number
}

export interface SectionAnswerSubmission {
  email: string
  paper_id: string
  answer_id: string
  data: any
}

export interface ExplanationData {
  question_no: string
  explanation: string
}

export interface QuestionUpdateData {
  paper_id: string
  question: object
}

export interface PaperUpdateData {
  paper_id: string
  paper: object
}
