import './globals.css'
import { Inter } from 'next/font/google'
import type { Metadata } from 'next'

// Components
import Navigation from '@/components/layout/nav'
import Footer from '@/components/layout/footer'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'QuantMasters',
  description: 'Quantitative analysis and learning platform'
}

export default function RootLayout({
  children
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-gray-50`}>
        {/* Navigation will be conditionally rendered based on the route in the nav component */}
        <Navigation />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  )
}
