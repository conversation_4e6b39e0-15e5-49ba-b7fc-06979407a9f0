import { Component, OnInit, OnDestroy, ViewChild, TemplateRef, ViewEncapsulation } from '@angular/core';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';

import { PlacementPostsService } from '../../../Services/Dashboard/placement-posts.service';

import { JobPost } from '../../../Models/Dashboard/JobPost';
import { JobDetail } from '../../../Models/Dashboard/JobDetail';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';

@Component({
  selector: 'app-upld-placement-post',
  templateUrl: './upld-placement-post.component.html',
  styleUrls: ['./upld-placement-post.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class UpldPlacementPostComponent implements OnInit, OnDestroy {

  @ViewChild(BsDatepickerDirective) datepicker: BsDatepickerDirective;
  @ViewChild('createNewTemplate') public cTemplate: TemplateRef<any>;
  @ViewChild('succTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('errTemplate') public eTemplate: TemplateRef<any>;
  @ViewChild('imgUpdlTemplate') public imgTemplate: TemplateRef<any>;

  public showIndicator = false;
  public errMsg = '';

  public allPosts: JobPost[];
  public displayRecords: JobPost[];
  public allPostLegth: number;
  public newPost: JobPost;
  public newDetail: JobDetail;
  public postsCount = 0;
  public exitingImages = [];
  public selectedImage: File;
  public selectedId: number;
  public newId: number;

  public bsValue: Date;
  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    class: 'create-modal',
    isAnimated: 'true'
  };
  public bsDateConfig = {
    dateInputFormat: 'DD/MM/YYYY h:mm:ss',
    containerClass: 'theme-dark-blue',
  };

  public inputDisabled = [];
  public successText = '';

  constructor(private placementPostsService: PlacementPostsService,
    private modalService: BsModalService) { }

  ngOnInit() {

    this.showIndicator = true;
    this.getAllPosts();
    this.getAllImages();
  }

  openAddNewPost() {
    this.newPost = new JobPost();
    this.newDetail = new JobDetail();
    this.openModal(this.cTemplate);
  }

  getAllPosts() {
    this.placementPostsService.getAllPostsAdmin().subscribe(response => {

      this.allPosts = response;
      this.allPosts.sort((a, b) => {
        const aDate = new Date(a.posted_on),
          bDate = new Date(b.posted_on);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });

      this.allPosts.forEach((x, idx, arr) => {
        this.inputDisabled.push(true);
        const x_logo = x.logo_link.split('/');
        x.logo_name = x_logo[x_logo.length - 1];
        arr[idx] = x;
      });

      this.displayRecords = this.allPosts.slice(0, 5);
      this.allPostLegth = this.allPosts.length;

      this.postsCount = this.allPosts.length + 1;
      this.showIndicator = false;
    }, error => {
      this.errMsg = 'Looks like something went wrong. Please try again later.';
      this.openModal(this.eTemplate);
      this.showIndicator = false;
    });
  }

  getAllImages() {
    this.placementPostsService.getImages().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.exitingImages = resp;
    });
  }

  previewImage(imageName: string) {

    const imgUrl = 'https://quantmasters.in/assets/companyLogos/' + imageName;

    if (imageName !== '') {
      window.open(imgUrl, '_blank');
    } else {
      this.errMsg = 'Looks like no image is uploaded yet :C';
      this.openModal(this.eTemplate);
    }
  }

  postNewPost(isValid: boolean) {

    if (!isValid) {
      return;
    }

    this.showIndicator = true;
    this.placementPostsService.createPost(this.newPost).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.text === 'Created') {
        this.newId = parseInt(resp.id, 10);

        this.newDetail.id = this.newId;
        this.placementPostsService.createDetail(this.newDetail).subscribe(response2 => {
          const resp2 = JSON.parse(JSON.stringify(response2));

          if (resp2.text === 'Also Created') {
            this.modalRef.hide();
            this.getAllPosts();
            this.getAllImages();
            this.successText = 'Created Successfully..!';
            this.openModal(this.sTemplate);
          }
        }, error => {
          this.modalRef.hide();
          this.openModal(this.eTemplate);
          this.showIndicator = false;
        });
      }
    }, error => {
      this.modalRef.hide();
      this.openModal(this.eTemplate);
      this.showIndicator = false;
    });
  }

  fnSaveModifiedData(postData: JobPost, index: number) {
    postData.logo_link = 'https://www.quantmasters.in/assets/companyLogos/' + postData.logo_name;
    delete(postData.logo_name);

    this.showIndicator = true;
    this.placementPostsService.modifyPost(postData).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.text === 'Updated') {

        postData.detail.id = postData.id;
        this.placementPostsService.modifyPostDetail(postData.detail).subscribe(response2 => {
          const respw = JSON.parse(JSON.stringify(response2));

          if (respw.text === 'Also Updated') {
            this.inputDisabled[index] = true;
            this.successText = 'Modified Successfully!';
            this.getAllPosts();
            this.getAllImages();
            this.showIndicator = false;
            this.openModal(this.sTemplate);
          }
        }, error => {
          this.showIndicator = false;
          this.openModal(this.eTemplate);
        });
      }
    }, error => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
    });
  }

  onClickDeleteJobPost(id: number, index: number) {

    this.showIndicator = true;
    this.placementPostsService.deletePost(id).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.text === 'Deleted') {
        this.getAllPosts();
        this.getAllImages();
        this.modalRef.hide();
        this.successText = 'Deleted Successfully!';
        this.showIndicator = false;
        this.openModal(this.sTemplate);
      }
    }, error => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
    });
  }

  fileSelect(event: any, id: number) {
    this.selectedImage = event.target.files[0];
    this.selectedId = id;

    this.openModal(this.imgTemplate);
  }

  uploadFile() {
    this.modalRef.hide();

    this.showIndicator = true;
    this.placementPostsService.setImage(this.selectedId, this.selectedImage).subscribe(response => {
      this.selectedImage = null;

      const resp = JSON.parse(JSON.stringify(response));

      if (resp.msg === 'Uploaded') {
        this.getAllPosts();
        this.getAllImages();
        this.successText = 'Uploaded Successfully!';
        this.openModal(this.sTemplate);
      }
    }, error => {
      const resp = JSON.parse(JSON.stringify(error));

      this.selectedImage = null;
      this.errMsg = resp.error.err;
      this.openModal(this.eTemplate);

      this.showIndicator = false;
    });
  }

  cancelUpload() {
    this.selectedImage = null;
    this.modalRef.hide();
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  closeModal() {
    if (typeof this.modalRef !== 'undefined') {
      this.modalRef.hide();
    }
  }

  ngOnDestroy() {
    this.closeModal();
  }
  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayRecords = this.allPosts.slice(startItem, endItem);
  }
}
