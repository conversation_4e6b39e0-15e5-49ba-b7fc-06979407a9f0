import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FAQAppRoutingModule } from './faq-app-routing.module';

import { SharedNavModule } from '../Components/Utilities/shared-nav/shared-nav.module';
import { SharedFooterModule } from '../Components/Utilities/shared-footer/shared-footer.module';
import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { FaqBaseComponent } from './faq-base/faq-base.component';
import { FaqTopicsComponent } from '../FAQApp/faq-topics/faq-topics.component';

@NgModule({
  declarations: [
    FaqBaseComponent,
    FaqTopicsComponent
  ],
  imports: [
    CommonModule,
    FAQAppRoutingModule,
    SharedNavModule,
    SharedFooterModule,
    SharedIndicatorModule
  ]
})
export class FAQAppModule { }
