'use client'

import React from 'react'
import { Button } from '@/components/ui/button'
import {
  Question,
  CompetitiveQuestion,
  CompanyQuestion
} from '@/types/test-types'

interface QuestionTrackerProps {
  questions: (Question | CompetitiveQuestion | CompanyQuestion)[]
  scrollToQuestion: (questionNo: string) => void
  isSuperUser?: boolean
  showAllExpl: boolean
  toggleShowAllExpl: () => void
  marksInfo?: {
    marks: number
    noQues: number
    negMarks: boolean
    sectionTrack: { section_name: string; marks: number }[]
  }
  showPopoutTimer: boolean
}

const QuestionTracker: React.FC<QuestionTrackerProps> = ({
  questions,
  scrollToQuestion,
  isSuperUser = false,
  showAllExpl,
  toggleShowAllExpl,
  marksInfo,
  showPopoutTimer
}) => {
  return (
    <div className="tracking-wrap mt-6 p-4 bg-white rounded-md shadow">
      {isSuperUser && (
        <Button variant="outline" className="mb-4" onClick={toggleShowAllExpl}>
          {showAllExpl ? 'Hide All Explanations' : 'Show All Explanations'}
        </Button>
      )}

      {showPopoutTimer && (
        <div className="time-box-2 mb-4">
          <p className="text-sm">Time Remaining</p>
          <h5 className="timeRem text-lg font-bold"></h5>
        </div>
      )}

      <div className="tracking grid grid-cols-5 gap-2 sm:grid-cols-8 md:grid-cols-10">
        {questions.map((qn) => (
          <div
            key={qn.question_no || (qn as Question).ques_no}
            className="item cursor-pointer"
            onClick={() =>
              scrollToQuestion(qn.question_no || (qn as Question).ques_no)
            }
          >
            <p
              className={`item-inner p-2 text-center font-medium rounded-md ${
                qn.bAnswered
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-200 text-gray-800'
              }`}
            >
              {qn.question_no || (qn as Question).ques_no}
            </p>
          </div>
        ))}
      </div>

      <div className="tracking-legend mt-4 space-y-2">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-sm bg-gray-200"></div>
          <p className="text-sm">Unanswered</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-sm bg-green-500"></div>
          <p className="text-sm">Answered</p>
        </div>

        {marksInfo && (
          <>
            <p className="text-sm mt-4">
              Your marks:{' '}
              <span className="font-bold">
                {marksInfo.marks} /{' '}
                {marksInfo.negMarks ? marksInfo.noQues * 2 : marksInfo.noQues}
              </span>
            </p>

            {marksInfo.sectionTrack && marksInfo.sectionTrack.length > 0 && (
              <div className="section-marks--wrap mt-2">
                <p className="text-sm font-medium">Section Wise Breakdown</p>
                <div className="space-y-1 mt-1">
                  {marksInfo.sectionTrack.map((sectionData, idx) => (
                    <p key={idx} className="text-sm">
                      {sectionData.section_name}:{' '}
                      <span className="font-bold">{sectionData.marks}</span>
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default QuestionTracker
