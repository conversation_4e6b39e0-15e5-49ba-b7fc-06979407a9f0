'use client'

import { useState, useRef, useEffect } from 'react'
import { Calendar, Edit2, Eye, EyeOff } from 'lucide-react'
import { NewUser, BRANCH_COURSE_DATA } from '@/types/user-types'
import { UserProfileService } from '@/lib/client-services/user-profile-service.client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'

interface UserProfileClientProps {
  initialUser: NewUser
  initialAvatarPath: string | null
}

type TabType = 'profile' | 'password'

export default function UserProfileClient({
  initialUser,
  initialAvatarPath
}: UserProfileClientProps) {
  const [activeTab, setActiveTab] = useState<TabType>('profile')
  const [isLoading, setIsLoading] = useState(false)
  const [userInfo, setUserInfo] = useState<NewUser>(initialUser)
  const [avatarPath, setAvatarPath] = useState<string | null>(initialAvatarPath)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPass: '',
    password: '',
    conf_password: ''
  })
  const [showPasswordFields, setShowPasswordFields] = useState(false)
  const [passwordFieldsDisabled, setPasswordFieldsDisabled] = useState(false)
  const [resetKey, setResetKey] = useState('')
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  // Branch options based on selected course
  const [branchOptions, setBranchOptions] = useState<string[]>([])

  // Modals
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [showSuccessModal, setShowSuccessModal] = useState(false)
  const [showErrorModal, setShowErrorModal] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [successMessage, setSuccessMessage] = useState('')

  // Form validation
  const [errors, setErrors] = useState<Record<string, string>>({})

  const fileInputRef = useRef<HTMLInputElement>(null)

  // Update branch options when course changes
  useEffect(() => {
    if (userInfo.qual) {
      const selectedCourse = BRANCH_COURSE_DATA.data.find(
        (course) => course.bName === userInfo.qual
      )
      setBranchOptions(selectedCourse?.subName || [])

      // Reset branch if it's not valid for the new course
      if (selectedCourse && !selectedCourse.subName.includes(userInfo.branch)) {
        setUserInfo((prev) => ({ ...prev, branch: '' }))
      }
    }
  }, [userInfo.qual])

  const validateField = (name: string, value: string) => {
    const newErrors = { ...errors }

    switch (name) {
      case 'f_name':
        if (!value.trim()) {
          newErrors[name] = 'First Name is required'
        } else if (!/^[a-zA-Z][a-zA-Z ]*$/.test(value)) {
          newErrors[name] = 'First Name can only contain letters'
        } else {
          delete newErrors[name]
        }
        break
      case 'l_name':
        if (value && !/^[a-zA-Z]*$/.test(value)) {
          newErrors[name] = 'Last Name can only contain letters'
        } else {
          delete newErrors[name]
        }
        break
      case 'phone_no':
        if (!value) {
          newErrors[name] = 'Please enter your phone number'
        } else if (!/^\d{10}$/.test(value.toString())) {
          newErrors[name] = 'Mobile number must contain 10 digits'
        } else {
          delete newErrors[name]
        }
        break
      case 'email':
        if (!value) {
          newErrors[name] = 'Email is required'
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          newErrors[name] = 'Email is invalid'
        } else {
          delete newErrors[name]
        }
        break
      case 'dob':
        if (!value) {
          newErrors[name] = 'Date of Birth is required'
        } else {
          delete newErrors[name]
        }
        break
      case 'inst_name':
        if (!value.trim()) {
          newErrors[name] = 'Please select your College'
        } else {
          delete newErrors[name]
        }
        break
      case 'qual':
        if (!value) {
          newErrors[name] = 'Please select your latest course'
        } else {
          delete newErrors[name]
        }
        break
    }

    setErrors(newErrors)
  }

  const handleInputChange = (name: string, value: string | number) => {
    setUserInfo((prev) => ({ ...prev, [name]: value }))
    validateField(name, value.toString())
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setShowUploadModal(true)
    }
  }

  const handleUploadAvatar = async () => {
    if (!selectedFile) return

    setIsLoading(true)
    setShowUploadModal(false)

    try {
      await UserProfileService.uploadAvatar(selectedFile)
      const newAvatarPath = await UserProfileService.getUserAvatar()
      setAvatarPath(newAvatarPath)
      setSuccessMessage('Profile image updated successfully')
      setShowSuccessModal(true)
    } catch (error: any) {
      setErrorMessage(error.response?.data?.err || 'Failed to upload image')
      setShowErrorModal(true)
    } finally {
      setSelectedFile(null)
      setIsLoading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleUpdateProfile = async () => {
    // Validate all required fields
    const requiredFields = [
      'f_name',
      'phone_no',
      'email',
      'dob',
      'inst_name',
      'qual',
      'yop'
    ]
    let hasErrors = false

    requiredFields.forEach((field) => {
      const value = userInfo[field as keyof NewUser]?.toString() || ''
      validateField(field, value)
      if (!value.trim()) {
        hasErrors = true
      }
    })

    if (hasErrors || Object.keys(errors).length > 0) {
      setErrorMessage('Please fill all the details correctly')
      setShowErrorModal(true)
      return
    }

    setIsLoading(true)

    try {
      await UserProfileService.updateUserProfile(userInfo)
      setSuccessMessage('Profile details updated successfully')
      setShowSuccessModal(true)
    } catch (error: any) {
      console.error('Profile update error:', error)
      setErrorMessage(error.message || 'Failed to update profile')
      setShowErrorModal(true)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCheckCurrentPassword = async () => {
    if (!passwordData.currentPass) {
      setErrorMessage('Current password is required')
      setShowErrorModal(true)
      return
    }

    setIsLoading(true)

    try {
      const result = await UserProfileService.validateCurrentPassword(
        passwordData.currentPass
      )
      setResetKey(result.key)
      setShowPasswordFields(true)
      setPasswordFieldsDisabled(true)
    } catch (error: any) {
      console.error('Password validation error:', error)
      setErrorMessage(error.message || 'Please enter correct password')
      setShowErrorModal(true)
      setShowPasswordFields(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdatePassword = async () => {
    if (!passwordData.password || !passwordData.conf_password) {
      setErrorMessage('Please fill all password fields')
      setShowErrorModal(true)
      return
    }

    if (passwordData.password !== passwordData.conf_password) {
      setErrorMessage('Passwords do not match')
      setShowErrorModal(true)
      return
    }

    if (passwordData.password.length < 8) {
      setErrorMessage('Password must be at least 8 characters long')
      setShowErrorModal(true)
      return
    }

    if (!/[A-Z]/.test(passwordData.password)) {
      setErrorMessage('Password must contain at least one capital letter')
      setShowErrorModal(true)
      return
    }

    setIsLoading(true)

    try {
      await UserProfileService.saveNewPassword(
        passwordData.conf_password,
        resetKey
      )
      setPasswordData({ currentPass: '', password: '', conf_password: '' })
      setShowPasswordFields(false)
      setPasswordFieldsDisabled(false)
      setResetKey('')
      setSuccessMessage('Password updated successfully')
      setShowSuccessModal(true)
    } catch (error: any) {
      console.log(error)
      setErrorMessage('Failed to update password')
      setShowErrorModal(true)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDateForInput = (dateStr: string) => {
    if (!dateStr) return ''
    const [day, month, year] = dateStr.split('/')
    return `${year}-${month}-${day}`
  }

  const formatDateFromInput = (dateStr: string) => {
    if (!dateStr) return ''
    const [year, month, day] = dateStr.split('-')
    return `${day}/${month}/${year}`
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* {isLoading && <div className="text-center py-10">Please wait...</div>} */}

      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">Edit Profile</h1>

        {/* Tab Navigation */}
        <div className="flex mb-8 border-b">
          <button
            className={`px-6 py-3 font-medium border-b-2 transition-colors ${
              activeTab === 'profile'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('profile')}
          >
            Profile Details
          </button>
          <button
            className={`px-6 py-3 font-medium border-b-2 transition-colors ${
              activeTab === 'password'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => setActiveTab('password')}
          >
            Password
          </button>
        </div>

        {/* Profile Details Tab */}
        {activeTab === 'profile' && (
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Avatar Section */}
              <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                  <img
                    src={avatarPath || '/assets/push-icons/avatar-def.png'}
                    alt="Avatar"
                    className="w-24 h-24 rounded-full object-cover border-4 border-gray-200"
                  />
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="absolute bottom-0 right-0 bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors"
                    title="Edit Profile Avatar"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                />
              </div>

              {/* Form Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* First Name */}
                <div className="space-y-2">
                  <Label htmlFor="f_name">First Name *</Label>
                  <Input
                    id="f_name"
                    type="text"
                    placeholder="First Name"
                    value={userInfo.f_name || ''}
                    onChange={(e) =>
                      handleInputChange('f_name', e.target.value)
                    }
                    className={errors.f_name ? 'border-red-500' : ''}
                  />
                  {errors.f_name && (
                    <p className="text-sm text-red-500">{errors.f_name}</p>
                  )}
                </div>

                {/* Last Name */}
                <div className="space-y-2">
                  <Label htmlFor="l_name">Last Name</Label>
                  <Input
                    id="l_name"
                    type="text"
                    placeholder="Last Name"
                    value={userInfo.l_name || ''}
                    onChange={(e) =>
                      handleInputChange('l_name', e.target.value)
                    }
                    className={errors.l_name ? 'border-red-500' : ''}
                  />
                  {errors.l_name && (
                    <p className="text-sm text-red-500">{errors.l_name}</p>
                  )}
                </div>

                {/* Date of Birth */}
                <div className="space-y-2">
                  <Label htmlFor="dob">Date of Birth *</Label>
                  <div className="relative">
                    <Input
                      id="dob"
                      type="date"
                      value={formatDateForInput(userInfo.dob || '')}
                      onChange={(e) =>
                        handleInputChange(
                          'dob',
                          formatDateFromInput(e.target.value)
                        )
                      }
                      className={errors.dob ? 'border-red-500' : ''}
                    />
                    <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  </div>
                  {errors.dob && (
                    <p className="text-sm text-red-500">{errors.dob}</p>
                  )}
                </div>

                {/* Phone Number */}
                <div className="space-y-2">
                  <Label htmlFor="phone_no">Mobile Number *</Label>
                  <Input
                    id="phone_no"
                    type="tel"
                    placeholder="Mobile Number"
                    value={userInfo.phone_no || ''}
                    onChange={(e) =>
                      handleInputChange('phone_no', e.target.value)
                    }
                    className={errors.phone_no ? 'border-red-500' : ''}
                  />
                  {errors.phone_no && (
                    <p className="text-sm text-red-500">{errors.phone_no}</p>
                  )}
                </div>

                {/* Email */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Email"
                    value={userInfo.email || ''}
                    disabled
                    className="bg-gray-100"
                  />
                </div>

                {/* Institution Name */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="inst_name">College or School Name *</Label>
                  <Input
                    id="inst_name"
                    type="text"
                    placeholder="Your College or School Name"
                    value={userInfo.inst_name || ''}
                    onChange={(e) =>
                      handleInputChange('inst_name', e.target.value)
                    }
                    className={errors.inst_name ? 'border-red-500' : ''}
                  />
                  {errors.inst_name && (
                    <p className="text-sm text-red-500">{errors.inst_name}</p>
                  )}
                </div>

                {/* Course */}
                <div className="space-y-2">
                  <Label htmlFor="qual">Course *</Label>
                  <Select
                    value={userInfo.qual || ''}
                    onValueChange={(value) => handleInputChange('qual', value)}
                  >
                    <SelectTrigger
                      className={errors.qual ? 'border-red-500' : ''}
                    >
                      <SelectValue placeholder="Select Course" />
                    </SelectTrigger>
                    <SelectContent>
                      {BRANCH_COURSE_DATA.data.map((course) => (
                        <SelectItem key={course.bName} value={course.bName}>
                          {course.bName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.qual && (
                    <p className="text-sm text-red-500">{errors.qual}</p>
                  )}
                </div>

                {/* Branch */}
                <div className="space-y-2">
                  <Label htmlFor="branch">Branch</Label>
                  <Select
                    value={userInfo.branch || ''}
                    onValueChange={(value) =>
                      handleInputChange('branch', value)
                    }
                    disabled={!userInfo.qual || branchOptions.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select Branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branchOptions.map((branch) => (
                        <SelectItem key={branch} value={branch}>
                          {branch}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* USN */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="usn">USN / Roll Number</Label>
                  <Input
                    id="usn"
                    type="text"
                    placeholder="USN"
                    value={userInfo.usn || ''}
                    onChange={(e) => handleInputChange('usn', e.target.value)}
                  />
                </div>
              </div>

              {/* Update Button */}
              <div className="flex justify-center pt-6">
                <Button
                  onClick={handleUpdateProfile}
                  disabled={isLoading}
                  className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isLoading ? 'Updating...' : 'Update Profile'}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Password Tab */}
        {activeTab === 'password' && (
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Password */}
              <div className="space-y-2">
                <Label htmlFor="currentPass">Current Password *</Label>
                <div className="relative">
                  <Input
                    id="currentPass"
                    type={showPasswords.current ? 'text' : 'password'}
                    placeholder="Enter current password"
                    value={passwordData.currentPass}
                    onChange={(e) =>
                      setPasswordData((prev) => ({
                        ...prev,
                        currentPass: e.target.value
                      }))
                    }
                    disabled={passwordFieldsDisabled}
                  />
                  <button
                    type="button"
                    onClick={() =>
                      setShowPasswords((prev) => ({
                        ...prev,
                        current: !prev.current
                      }))
                    }
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.current ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* Check Password Button */}
              {!showPasswordFields && (
                <div className="flex justify-center">
                  <Button
                    onClick={handleCheckCurrentPassword}
                    disabled={isLoading || !passwordData.currentPass}
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    {isLoading ? 'Checking...' : 'Verify Current Password'}
                  </Button>
                </div>
              )}

              {/* New Password Fields */}
              {showPasswordFields && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="password">New Password *</Label>
                    <div className="relative">
                      <Input
                        id="password"
                        type={showPasswords.new ? 'text' : 'password'}
                        placeholder="Enter new password"
                        value={passwordData.password}
                        onChange={(e) =>
                          setPasswordData((prev) => ({
                            ...prev,
                            password: e.target.value
                          }))
                        }
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowPasswords((prev) => ({
                            ...prev,
                            new: !prev.new
                          }))
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.new ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="conf_password">
                      Confirm New Password *
                    </Label>
                    <div className="relative">
                      <Input
                        id="conf_password"
                        type={showPasswords.confirm ? 'text' : 'password'}
                        placeholder="Confirm new password"
                        value={passwordData.conf_password}
                        onChange={(e) =>
                          setPasswordData((prev) => ({
                            ...prev,
                            conf_password: e.target.value
                          }))
                        }
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setShowPasswords((prev) => ({
                            ...prev,
                            confirm: !prev.confirm
                          }))
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPasswords.confirm ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">
                      Password Requirements:
                    </h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• At least 8 characters long</li>
                      <li>• Must contain at least one capital letter</li>
                    </ul>
                  </div>

                  <div className="flex justify-center">
                    <Button
                      onClick={handleUpdatePassword}
                      disabled={isLoading}
                      className="px-8 py-2  bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      {isLoading ? 'Updating...' : 'Update Password'}
                    </Button>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Upload Modal */}
      <Dialog open={showUploadModal} onOpenChange={setShowUploadModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Profile Image</DialogTitle>
            <DialogDescription>
              Are you sure you want to upload this image as your profile
              picture?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowUploadModal(false)}>
              Cancel
            </Button>
            <Button onClick={handleUploadAvatar}>Upload</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <Dialog open={showSuccessModal} onOpenChange={setShowSuccessModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Success</DialogTitle>
            <DialogDescription>{successMessage}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setShowSuccessModal(false)}>OK</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Error Modal */}
      <Dialog open={showErrorModal} onOpenChange={setShowErrorModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error</DialogTitle>
            <DialogDescription>{errorMessage}</DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button onClick={() => setShowErrorModal(false)}>OK</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
