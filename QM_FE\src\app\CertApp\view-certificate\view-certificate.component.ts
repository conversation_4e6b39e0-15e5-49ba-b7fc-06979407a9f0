import { Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

import { CertificationService } from '../../Services/Certification/Certification.service';

@Component({
  selector: 'app-view-certificate',
  templateUrl: './view-certificate.component.html',
  styleUrls: ['./view-certificate.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ViewCertificateComponent implements OnInit {

  public showIndicator = false;
  public certificateId = '';
  public pdfUrl = '';
  public recieverName = '';

  constructor(private route: ActivatedRoute,
    private certificationService: CertificationService) { }

  ngOnInit() {
    this.showIndicator = true;
    this.route.paramMap.subscribe(params => {
      this.certificateId = params.get('certificateId');

      this.certificationService.verifyCertificate(this.certificateId).subscribe(response => {
        const resp = JSON.parse(JSON.stringify(response));

        this.recieverName = resp.name;
        if (!resp.msg) {
          this.certificationService.getCertification(resp.email).subscribe(resp2 => {
            this.pdfUrl = resp2.url;
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        }
      }, error => {
        this.showIndicator = false;
      });
    });
  }

}
