// app/notes/page.tsx
import { Metadata } from 'next'
import { ServerNotesService } from '@/lib/server-services/notes-service.server'
import NotesHomeClient from '@/components/notes/notes-home-client'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Study Notes | Quant Masters',
  description:
    'Access comprehensive study materials and notes for quantitative analysis and competitive exams'
}

export default async function NotesPage() {
  try {
    // Fetch groups on the server
    const groups = await ServerNotesService.getSuperGroups()

    return (
      <div className="container mx-auto py-8 px-4">
        <div className="page-header mb-8">
          <div className="title">
            <h1 className="text-3xl font-bold text-center mb-2">Study Notes</h1>
            <p className="text-center text-gray-600">
              Access comprehensive study materials and notes for quantitative
              analysis and competitive exams
            </p>
          </div>
        </div>

        {/* Pass the fetched papers to the client component */}
        <NotesHomeClient initialGroups={groups} />

        <div className="copy-content mt-16 text-center text-sm text-gray-500">
          <p>
            &copy; {new Date().getFullYear()} Quant Masters. All Rights
            Reserved.
          </p>
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error loading notes data:', error)

    // Return error state
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Study Notes</h1>
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <p className="text-red-800 mb-4">
              Unable to load notes. Please try again later.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }
}
