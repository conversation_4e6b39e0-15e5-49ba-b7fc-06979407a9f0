// lib/server-services/section-wise-papers.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { ChapterPaper } from '@/types/section-wise-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in/v2'
const ENDPOINTS = {
  papersUrl: `${API_BASE_URL}/test/sectionWise`,
  papersAdmUrl: `${API_BASE_URL}/admin/test/sectionWise`,
  afcatUrl: `${API_BASE_URL}/test/afcat`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerSectionWisePapersService {
  /**
   * Get section-wise papers
   */
  static async getPapers(): Promise<ChapterPaper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.papersUrl}/papers`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching section-wise papers:', error)
      return []
    }
  }

  /**
   * Get questions for a specific paper
   */
  static async getQuestions(paperId: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.papersUrl}/paper/${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching questions for paper ${paperId}:`, error)
      throw error
    }
  }

  /**
   * Get AFCAT papers
   */
  static async getAfcatPapers(): Promise<ChapterPaper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.afcatUrl}/papers`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching AFCAT papers:', error)
      return []
    }
  }

  /**
   * Get AFCAT questions for a specific paper
   */
  static async getAfcatQuestions(paperId: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.afcatUrl}/paper/${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching AFCAT questions for paper ${paperId}:`,
        error
      )
      throw error
    }
  }
}
