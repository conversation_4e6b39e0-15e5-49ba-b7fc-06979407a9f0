// types/admin-types.ts
export interface PaperType {
  id: number
  name: string
  service: string
  hasTopicDropdown: boolean
  hasSubtopicDropdown: boolean
  hasStatusToggle: boolean
  canDelete: boolean
  canCreate: boolean
  topicLabel?: string
  subtopicLabel?: string
}

export interface Paper {
  paper_id: string
  paper_name: string
  no_of_ques: string
  time_lim: string // in milliseconds
  status: string // "0" = hidden, "1" = shown
  show_ans: string // "0" = don't show, "1" = show answers
  once_ans: string // "0" = can retake, "1" = one attempt only
  neg_marks?: string // negative marking
  rand_ques?: string // randomize questions
  created_at?: string
  group_id?: string
  sub_group_id?: string
  level?: string
  public?: string
  type?: number
}

export interface SuperGroup {
  super_group_id: string
  super_group_name: string
}

export interface Group {
  group_id: string
  group_name: string
  super_group_id: string
}

export interface TMCQGroup {
  group_id: string
  group_name: string
}

export interface TMCQSubGroup {
  group_id: string
  sub_group_id: string
  sub_group_name: string
}

export interface Topic {
  id: string
  name: string
}

export interface Subtopic {
  id: string
  name: string
  parent_id: string
}

export interface CreatePaperRequest {
  paper_name: string
  no_of_ques: number
  time_lim: number
  show_ans: string
  once_ans: string
  neg_marks?: string
  rand_ques?: string
  group_id?: string
  sub_group_id?: string
}

export interface FilterState {
  type?: string
  topic?: string
  subtopic?: string
}

export interface ApiError {
  message: string
  code: string
  details?: any
}

export const PAPER_TYPES: PaperType[] = [
  {
    id: 1,
    name: 'Chapter-Wise Papers',
    service: 'chapters',
    hasTopicDropdown: true,
    hasSubtopicDropdown: true,
    hasStatusToggle: false,
    canDelete: false,
    canCreate: false,
    topicLabel: 'Super Group',
    subtopicLabel: 'Group'
  },
  {
    id: 4,
    name: 'Mock Papers',
    service: 'tests',
    hasTopicDropdown: false,
    hasSubtopicDropdown: false,
    hasStatusToggle: false,
    canDelete: false,
    canCreate: false
  },
  {
    id: 5,
    name: 'Trial Papers',
    service: 'open-tests',
    hasTopicDropdown: false,
    hasSubtopicDropdown: false,
    hasStatusToggle: true,
    canDelete: true,
    canCreate: false
  },
  {
    id: 7,
    name: 'Company Papers',
    service: 'company',
    hasTopicDropdown: false,
    hasSubtopicDropdown: false,
    hasStatusToggle: true,
    canDelete: true,
    canCreate: false
  },
  {
    id: 6,
    name: 'Chapter-Wise Practice',
    service: 'chapter-practice',
    hasTopicDropdown: true,
    hasSubtopicDropdown: true,
    hasStatusToggle: false,
    canDelete: false,
    canCreate: false,
    topicLabel: 'Super Group',
    subtopicLabel: 'Group'
  },
  {
    id: 8,
    name: 'Weekly Competitive',
    service: 'admin-view',
    hasTopicDropdown: false,
    hasSubtopicDropdown: false,
    hasStatusToggle: true,
    canDelete: false,
    canCreate: false
  },
  {
    id: 9,
    name: 'Section-Wise Papers',
    service: 'section-wise',
    hasTopicDropdown: false,
    hasSubtopicDropdown: false,
    hasStatusToggle: false,
    canDelete: false,
    canCreate: false
  },
  {
    id: 11,
    name: 'Technical MCQs',
    service: 'tmcq',
    hasTopicDropdown: true,
    hasSubtopicDropdown: true,
    hasStatusToggle: true,
    canDelete: true,
    canCreate: true,
    topicLabel: 'Topic',
    subtopicLabel: 'SubTopic'
  },
  {
    id: 13,
    name: 'Verbal Practice',
    service: 'competitive',
    hasTopicDropdown: true,
    hasSubtopicDropdown: false,
    hasStatusToggle: true,
    canDelete: true,
    canCreate: true,
    topicLabel: 'Topic'
  },
  {
    id: 14,
    name: 'Technical Practice',
    service: 'competitive',
    hasTopicDropdown: true,
    hasSubtopicDropdown: false,
    hasStatusToggle: true,
    canDelete: true,
    canCreate: true,
    topicLabel: 'Topic'
  }
]

export function getPaperTypeConfig(typeId: number): PaperType {
  return PAPER_TYPES.find((type) => type.id === typeId) || PAPER_TYPES[0]
}
