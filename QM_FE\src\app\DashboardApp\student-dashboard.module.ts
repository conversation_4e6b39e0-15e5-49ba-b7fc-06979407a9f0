import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { CollapseModule } from 'ngx-bootstrap/collapse';
import { RatingModule } from 'ngx-bootstrap/rating';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';

import { MarkdownModule } from 'ngx-markdown';

import { VgCoreModule } from 'videogular2/compiled/core';
import { VgControlsModule } from 'videogular2/compiled/controls';
import { VgOverlayPlayModule } from 'videogular2/compiled/overlay-play';
import { VgStreamingModule } from 'videogular2/compiled/streaming';
import { VgBufferingModule } from 'videogular2/compiled/buffering';

import { MathJaxModule } from 'ngx-mathjax';

import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { StudentDashboardRoutingModule } from './student-dashboard.routing.module';

import { DashboardComponent } from './dashboard/dashboard.component';
import { LeftNavComponent } from './Layout/left-nav/left-nav.component';

import { VideoListComponent } from './Videos/video-list/video-list.component';
import { VideoStreamComponent } from './Videos/video-stream/video-stream.component';
import { StudentReviewComponent } from './Videos/student-review/student-review.component';
import { ChapterPaperComponent } from './ChapterPapers/chapter-paper.component';
import { ModelPaperComponent } from './ModelPapers/model-paper.component';
import { CompetitivePaperComponent } from './CompetitivePapers/competitive-paper.component';
import { CompanyPaperComponent } from './CompanyPapers/company-paper.component';
import { TestsComponent } from './tests/tests.component';
import { StudentTestimonialComponent } from './Videos/student-testimonial/student-testimonial.component';
import { FreeVideoComponent } from './Videos/free-video/free-video.component';
import { FreePaperComponent } from './FreePapers/free-paper.component';
import { WorkshopVideosComponent } from './Videos/workshop-videos/workshop-videos.component';
import { ChapterPracticePapersComponent } from './ChapterPracticePapers/chapter-practice-papers.component';
import { OpenCompetitivePaperComponent } from './OpenCompetitivePapers/open-competitive-paper.component';
import { SectionWisePapersComponent } from './SectionWisePapers/section-wise-papers.component';
import { AfcatPapersComponent } from './AfcatPapers/afcat-papers.component';
import { CompanyTestsListComponent } from './OpenCompanyPapers/company-tests-list/company-tests-list.component';
import { CompanyPapersComponent } from './OpenCompanyPapers/company-papers/company-papers.component';
import { PaperListComponent } from './TechMCQ/paper-list/paper-list.component';
import { TechNotesComponent } from './tech-notes/tech-notes.component';
import { WPPracticePapersComponent } from "./WPPracticePapers/papers-list/wppractice-papers.component";

@NgModule({
  declarations: [
    DashboardComponent,
    LeftNavComponent,
    VideoListComponent,
    VideoStreamComponent,
    ChapterPaperComponent,
    ModelPaperComponent,
    CompetitivePaperComponent,
    CompanyPaperComponent,
    TestsComponent,
    StudentReviewComponent,
    StudentTestimonialComponent,
    FreeVideoComponent,
    FreePaperComponent,
    WorkshopVideosComponent,
    ChapterPracticePapersComponent,
    OpenCompetitivePaperComponent,
    SectionWisePapersComponent,
    AfcatPapersComponent,
    CompanyTestsListComponent,
    CompanyPapersComponent,
    PaperListComponent,
    TechNotesComponent,
    WPPracticePapersComponent
  ],
  imports: [
    CommonModule,
    SharedIndicatorModule,
    FormsModule,
    StudentDashboardRoutingModule,
    MarkdownModule.forRoot(),
    CollapseModule.forRoot(),
    RatingModule.forRoot(),
    TabsModule.forRoot(),
    VgCoreModule,
    VgControlsModule,
    VgOverlayPlayModule,
    VgStreamingModule,
    VgBufferingModule,
    MathJaxModule.forRoot({
      version: '2.7.5',
      config: 'TeX-AMS_HTML',
      hostname: 'cdnjs.cloudflare.com'
    }),
    BsDropdownModule.forRoot()
  ]
})
export class StudentDashboardModule { }
