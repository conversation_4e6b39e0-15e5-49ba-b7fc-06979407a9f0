import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';

import { PasswordService } from '../../../Services/password.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit {

  public e_mail: string;
  public userDob: Date;
  public bsValue = new Date();
  public modalRef: BsModalRef;
  public bsConfig = {
    dateInputFormat: 'DD/MM/YYYY',
    containerClass: 'theme-dark-blue',
  };

  public showIndicator = false;

  @ViewChild('successTemplate') public template: TemplateRef<any>;
  @ViewChild('genErrorTemplate') public errTemplate: TemplateRef<any>;
  @ViewChild(BsDatepickerDirective) datepicker: BsDatepickerDirective;

  public resetErr = '';
  public emailErr = false;

  constructor(public router: Router,
              public modalService: BsModalService,
              public passwordService: PasswordService) { }

  ngOnInit() {
  }

  handleForgotPW(isValid: boolean) {
    this.showIndicator = true;

    if (!isValid) {
      this.showIndicator = false;
      return;
    }

    // this.passwordService.validateDOBRequest(this.e_mail, dob).subscribe(response => {
    //   this.router.navigate(['password/reset/rst_l/', 0, this.e_mail]);
    // }, error => {
    //   this.openModel(this.errTemplate);
    // });

    this.passwordService.sendPasswordResetMail(this.e_mail).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.msg === 'mail sent') {
        this.openModel(this.template);
        this.showIndicator = false;
      }
    }, error => {
      if (error.error) {

        const resText = JSON.parse(JSON.stringify(error.error));

        if (resText.msg === 'Email not found') {
          this.emailErr = true;
        } else {
          this.resetErr = 'Something went wrong, please try again';
        }

        this.openModel(this.errTemplate);
        this.showIndicator = false;
      }
    });
  }

  openModel(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

}
