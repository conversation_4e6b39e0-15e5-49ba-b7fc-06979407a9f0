import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { FreeVideoComponent } from './free-video.component';

describe('FreeVideoComponent', () => {
  let component: FreeVideoComponent;
  let fixture: ComponentFixture<FreeVideoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ FreeVideoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FreeVideoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
