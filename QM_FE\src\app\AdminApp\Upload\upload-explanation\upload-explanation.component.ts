import { <PERSON>mponent, On<PERSON>nit, On<PERSON><PERSON>roy, Template<PERSON>ef, ViewChild } from '@angular/core';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { ChaptersService } from '../../../Services/Dashboard/chapters.service';
import { TestsService } from '../../../Services/tests.service';
import { Tests2Service } from '../../../Services/Dashboard/tests-2.service';
import { ChapterPracticeService } from '../../../Services/Dashboard/chapter-practice.service';
import { OpenTestsService } from '../../../Services/open-tests.service';

import { SuperGroup } from 'src/app/Models/Dashboard/Chapters/SuperGroup';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';
import { Group } from 'src/app/Models/Dashboard/Chapters/Group';
import { Question } from 'src/app/Models/Dashboard/Chapters/Question';
import { CompanyService } from 'src/app/Services/Dashboard/company.service';
import { SectionWisePapersService } from '../../../Services/Dashboard/section-wise-papers.service';

import { Paper } from '../../../Models/Paper';
import { Questions } from '../../../Models/Questions';

@Component({
  selector: 'app-upload-explanation',
  templateUrl: './upload-explanation.component.html',
  styleUrls: ['./upload-explanation.component.scss']
})
export class UploadExplanationComponent implements OnInit, OnDestroy {

  @ViewChild('successTemplate') public STemplate: TemplateRef<any>;
  @ViewChild('imgUpdlTemplate') public updlTemplate: TemplateRef<any>;

  public showIndicator = false;

  public selectedType = '-1';
  public selectedSubType = '-1';
  public selectedChapter = '-1';
  public selectedPaper = '-1';
  public selectedQuestion = '-1';
  public slectedUploadType = '-1';

  public selectedImage: File;

  public currentExplanation = '';

  public subTypes: SuperGroup[];
  public subSubTypes: Group[];
  public chapterPapers: ChapterPaper[];
  public modelPapers: Paper[];

  public chapterQuestions: Question[];
  public modelQuestions: Questions[];

  public imageUpdl = false;

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    class: 'access-modal'
  };

  public markdownText = null;

  constructor(private chaptersService: ChaptersService,
    private modelTestsService: TestsService,
    private tests2Service: Tests2Service,
    private chapterPracticeService: ChapterPracticeService,
    private openTestsService: OpenTestsService,
    private comanyService: CompanyService,
    private sectionWiseService: SectionWisePapersService,
    public modalService: BsModalService) { }

  ngOnInit() {
  }

  paperTyepSelected() {
    this.showIndicator = true;
    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.chaptersService.getSuperGroups().subscribe((response) => {
          this.subTypes = response;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 2:
          this.openTestsService.getFreeCompanyPapers().subscribe((response) => {
            const respObj = JSON.parse(JSON.stringify(response));

            this.modelPapers = respObj;
            this.chapterPapers = null;
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 3:
        this.modelTestsService.getPapers().subscribe((response) => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelPapers = respObj;
          this.chapterPapers = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 4:
        this.chapterPracticeService.getSuperGroups().subscribe(response => {
          this.subTypes = response;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 5:
        this.openTestsService.getPapers().subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelPapers = respObj;
          this.chapterPapers = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 6:
        this.sectionWiseService.getPapers().subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelPapers = respObj;
          this.chapterPapers = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
      default:
        this.showIndicator = false;

    }
  }

  paperSubTypeSelected() {
    this.showIndicator = true;

    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.chaptersService.getGroups(this.selectedSubType).subscribe(response => {
          this.subSubTypes = response;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 4:
        this.chapterPracticeService.getGroups(this.selectedSubType).subscribe(response => {
          this.subSubTypes = response;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
    }
  }

  paperChapterSelected() {
    this.showIndicator = true;

    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.chaptersService.getPapersOfAGroup(this.selectedChapter).subscribe(response => {
          this.chapterPapers = response;
          this.modelPapers = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 4:
        this.chapterPracticeService.getPapersOfAGroup(this.selectedChapter).subscribe(response => {
          this.chapterPapers = response;
          this.modelPapers = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
    }
  }

  paperSelected() {
    this.showIndicator = true;
    // setting selected question and type to upload to -1
    this.selectedQuestion = '-1';
    this.slectedUploadType = '-1';
    this.markdownText = null;
    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.tests2Service.getQuestions(this.selectedPaper).subscribe(response => {
          this.chapterQuestions = response.questions;
          this.modelQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 2:
        this.openTestsService.getQuestions(this.selectedPaper).subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelQuestions = respObj;
          this.chapterQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 3:
        this.modelTestsService.getQuestions(this.selectedPaper).subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelQuestions = respObj.questions;
          this.chapterQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 4:
        this.tests2Service.getQuestions(this.selectedPaper).subscribe(response => {
          this.chapterQuestions = response.questions;
          this.modelQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 5:
        this.openTestsService.getQuestions(this.selectedPaper).subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelQuestions = respObj;
          this.chapterQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 6:
        this.sectionWiseService.getQuestions(this.selectedPaper).subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelQuestions = respObj.questions;
          this.chapterQuestions = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
    }
  }

  questionSelected(sFlag) {
    // setting upload type to null-> only when user select
    if (sFlag) {
      this.slectedUploadType = '-1';
      this.markdownText = null;
    }

    // tslint:disable-next-line: triple-equals
    switch (parseInt(this.selectedType, 10)) {
      case 0:
        // tslint:disable-next-line: triple-equals
        this.currentExplanation = this.chapterQuestions.filter(x => x.ques_no == this.selectedQuestion)[0].explanation;
        break;
      case 2:
        // tslint:disable-next-line: triple-equals
        this.currentExplanation = this.modelQuestions.filter(x => x.question_no == this.selectedQuestion)[0].explanation;
        break;
      case 3:
        // tslint:disable-next-line: triple-equals
        this.currentExplanation = this.modelQuestions.filter(x => x.question_no == this.selectedQuestion)[0].explanation;
        break;
      case 4:
        // tslint:disable-next-line: triple-equals
        this.currentExplanation = this.chapterQuestions.filter(x => x.ques_no == this.selectedQuestion)[0].explanation;
        break;
      case 5:
        // tslint:disable-next-line: triple-equals
        this.currentExplanation = this.modelQuestions.filter(x => x.question_no == this.selectedQuestion)[0].explanation;
        break;
    }
  }

  updateExplanation() {
    this.showIndicator = true;

    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.tests2Service.addQuestionExplanation('1', this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 2:
        this.openTestsService.addExplanation(this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 3:
        this.modelTestsService.addExplanation(this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 4:
        this.tests2Service.addQuestionExplanation('1', this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 5:
        this.openTestsService.addExplanation(this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 6:
        this.sectionWiseService.addExplanationText(this.selectedPaper, this.selectedQuestion, this.currentExplanation)
          .subscribe(response => {
            const respObj = JSON.parse(JSON.stringify(response));

            if (respObj.msg === 'updated') {
              this.openModal(this.STemplate);
            }
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
    }
  }

  updateImageQuestion() {
    this.showIndicator = true;
    this.tests2Service.addImageExplanationForQuestion(this.selectedType, this.selectedPaper, this.selectedQuestion, this.selectedImage)
      .subscribe(response => {
        const respObj = JSON.parse(JSON.stringify(response));
        this.closeModal();
        if (respObj.msg === 'Question Updated') {
          this.openModal(this.STemplate);
        }
        this.fnGetSelectedChapterData();
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
      });
  }

  fileSelect(event: any) {
    this.selectedImage = event.target.files[0];

    this.openModal(this.updlTemplate);
  }

  uploadFile() {
    this.showIndicator = true;
    if (this.slectedUploadType === 'forAnswer') {

      const selectType = parseInt(this.selectedType, 10);

      if (selectType === 0 || selectType === 4) {
        this.tests2Service.addImageExplanation('1', this.selectedPaper, this.selectedQuestion, this.selectedImage)
          .subscribe(response => {

            const restObj = JSON.parse(JSON.stringify(response));

            if (restObj.msg === 'updated') {
              this.modalRef.hide();
              this.openModal(this.STemplate);
            }

            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
      } else if (selectType === 5 || selectType === 2) {
        this.tests2Service.addImageExplanation('5', this.selectedPaper, this.selectedQuestion, this.selectedImage)
          .subscribe(response => {

            const restObj = JSON.parse(JSON.stringify(response));

            if (restObj.msg === 'updated') {
              this.modalRef.hide();
              this.openModal(this.STemplate);
            }

            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
      } else if (selectType === 3) {
				
			}
    } else if (this.slectedUploadType === 'forQuestion') {
      this.markdownText = null;
      this.updateImageQuestion();
    }
  }

  onSelectUploadFor(oEvent) {
    if (oEvent === 'forQuestion') {
      this.imageUpdl = true;
      if (this.chapterQuestions) {
        this.fnGetSelectedQuestionDetails(this.chapterQuestions);
      } else if (this.modelQuestions) {
        this.fnGetSelectedQuestionDetails(this.modelQuestions);
      }
    } else if (oEvent === 'forAnswer') {
      this.imageUpdl = false;
    } else {
      this.markdownText = null;
    }
  }

  fnGetSelectedChapterData() {
    const that = this;
    this.openTestsService.getQuestions(this.selectedPaper).subscribe(response => {
      const respObj = JSON.parse(JSON.stringify(response));

      that.modelQuestions = respObj;
      that.chapterQuestions = null;
      that.markdownText = null;
      that.markdownText = that.modelQuestions.filter(x => x.question_no === that.selectedQuestion)[0].question;

      that.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  fnGetSelectedQuestionDetails(response) {
    const that = this;
    let aSelectedQuestion = [];
    aSelectedQuestion = response.filter(function (oQue, i) {
      if (oQue.question_no) {

        return oQue.question_no === Number(that.selectedQuestion);
      } else {
        return oQue.ques_no === Number(that.selectedQuestion);
      }
    });
    if (aSelectedQuestion.length > 0) {

      this.markdownText = aSelectedQuestion[0].question;
    }
  }

  cancelUpload() {
    this.selectedImage = null;
    this.modalRef.hide();
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  closeModal() {
    if (typeof this.modalRef !== 'undefined') {
      this.modalRef.hide();
    }
  }

  ngOnDestroy() {
    this.closeModal();
  }
}
