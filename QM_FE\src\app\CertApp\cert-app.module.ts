import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';
import { SharedNavModule } from '../Components/Utilities/shared-nav/shared-nav.module';
import { CertAppRoutingModule } from './cert-app-routing.module';

import { ModalModule } from 'ngx-bootstrap/modal';
import { AlertModule } from 'ngx-bootstrap/alert';
import { CollapseModule } from 'ngx-bootstrap/collapse';

import { FormsModule } from '@angular/forms';

import { CertificationComponent } from './certification/certification.component';
import { ViewCertificateComponent } from './view-certificate/view-certificate.component';
import { CertificationFaqComponent } from './certification-faq/certification-faq.component';

import { PdfViewerModule } from 'ng2-pdf-viewer';

@NgModule({
  declarations: [
    CertificationComponent,
    ViewCertificateComponent,
    CertificationFaqComponent
  ],
  imports: [
    CommonModule,
    CertAppRoutingModule,
    PdfViewerModule,
    SharedIndicatorModule,
    SharedNavModule,
    ModalModule,
    AlertModule,
    CollapseModule,
    FormsModule
  ]
})
export class CertAppModule { }
