// components/admin/tests/paper-type-grid.tsx
import Link from 'next/link'
import { PaperType } from '@/types/admin-types'
import { cn } from '@/lib/utils'

interface PaperTypeGridProps {
  paperTypes: PaperType[]
}

export default function PaperTypeGrid({ paperTypes }: PaperTypeGridProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {paperTypes.map((type) => {
        const typeName = type.name.toLowerCase().replace(/\s+/g, '-')

        return (
          <Link
            key={type.id}
            href={`/admin/tests/papers/type/${typeName}`}
            className={cn(
              'p-4 rounded-lg border-2 transition-all duration-200 text-left hover:shadow-md',
              'border-gray-200 hover:border-blue-300 hover:bg-blue-50 block'
            )}
          >
            <h3 className="font-semibold text-gray-900 mb-1">{type.name}</h3>
            <p className="text-sm text-gray-600">
              {type.hasTopicDropdown && type.hasSubtopicDropdown
                ? `Requires ${type.topicLabel} & ${type.subtopicLabel}`
                : type.hasTopicDropdown
                  ? `Requires ${type.topicLabel}`
                  : 'Direct access'}
            </p>
            <div className="mt-2 flex flex-wrap gap-2">
              {type.canCreate && (
                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                  Can Create
                </span>
              )}
              {type.canDelete && (
                <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                  Can Delete
                </span>
              )}
              {type.hasStatusToggle && (
                <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                  Status Toggle
                </span>
              )}
            </div>
          </Link>
        )
      })}
    </div>
  )
}
