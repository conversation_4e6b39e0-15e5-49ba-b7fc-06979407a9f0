// components/admin/tests/paper-type-selector.tsx
import { memo } from 'react'
import { PaperType } from '@/types/admin-types'
import { cn } from '@/lib/utils'

interface PaperTypeSelectorProps {
  paperTypes: PaperType[]
  selected: number | null
  onSelect: (typeId: number) => void
}

const PaperTypeSelector = memo(function PaperTypeSelector({
  paperTypes,
  selected,
  onSelect
}: PaperTypeSelectorProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {paperTypes.map((type) => (
        <button
          key={type.id}
          onClick={() => onSelect(type.id)}
          className={cn(
            'p-4 rounded-lg border-2 transition-all duration-200 text-left hover:shadow-md',
            selected === type.id
              ? 'border-blue-500 bg-blue-50 shadow-md'
              : 'border-gray-200 hover:border-gray-300'
          )}
        >
          <h3 className="font-semibold text-gray-900 mb-1">{type.name}</h3>
          <p className="text-sm text-gray-600">
            {type.hasTopicDropdown && type.hasSubtopicDropdown
              ? `Requires ${type.topicLabel} & ${type.subtopicLabel}`
              : type.hasTopicDropdown
                ? `Requires ${type.topicLabel}`
                : 'Direct access'}
          </p>
          <div className="mt-2 flex flex-wrap gap-2">
            {type.canCreate && (
              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                Can Create
              </span>
            )}
            {type.canDelete && (
              <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
                Can Delete
              </span>
            )}
            {type.hasStatusToggle && (
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                Status Toggle
              </span>
            )}
          </div>
        </button>
      ))}
    </div>
  )
})

export default PaperTypeSelector
