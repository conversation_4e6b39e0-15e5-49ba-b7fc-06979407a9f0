'use client'

import {
  Video,
  MessageSquare,
  BookOpen,
  Book,
  FileText,
  ListChecks,
  FileQuestion,
  FileCheck,
  ClipboardList,
  Award,
  Trophy,
  Layers,
  Plane,
  Briefcase,
  Home,
  ChevronDown,
  ChevronRight,
  Settings,
  HelpCircle,
  StickyNote
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState } from 'react'
import Image from 'next/image'

interface SidebarItem {
  title: string
  icon: React.ReactNode
  href: string
  isSpecial?: boolean
  children?: SidebarItem[]
}

const Sidebar: React.FC = () => {
  const pathname = usePathname()
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) =>
      prev.includes(title)
        ? prev.filter((item) => item !== title)
        : [...prev, title]
    )
  }

  const sidebarSections = [
    {
      title: 'Dashboard',
      items: [
        {
          title: 'Overview',
          icon: <Home className="w-5 h-5" />,
          href: '/dashboard'
        }
      ]
    },
    {
      title: 'Interviews & Testimonials',
      items: [
        {
          title: 'Company Interviews',
          icon: <Video className="w-5 h-5" />,
          href: '/dashboard/student-review'
        },
        {
          title: 'Testimonials',
          icon: <MessageSquare className="w-5 h-5" />,
          href: '/dashboard/student-testimonials'
        }
      ]
    },
    {
      title: 'Notes & Resources',
      items: [
        {
          title: 'Session Notes',
          icon: <StickyNote className="w-5 h-5" />,
          href: '/dashboard/notes'
        },
        {
          title: 'Technical Notes',
          icon: <BookOpen className="w-5 h-5" />,
          href: '/dashboard/technical-notes'
        },
        {
          title: 'Verbal Notes',
          icon: <Book className="w-5 h-5" />,
          href: '/dashboard/verbal-notes'
        }
      ]
    },
    {
      title: 'Practice & Assessments',
      items: [
        {
          title: 'Trial Papers',
          icon: <FileText className="w-5 h-5" />,
          href: '/dashboard/trial-papers'
        },
        {
          title: 'Chapter-wise Practice',
          icon: <ListChecks className="w-5 h-5" />,
          href: '/dashboard/chapter-practice-papers'
        },
        {
          title: 'Technical MCQs',
          icon: <FileQuestion className="w-5 h-5" />,
          href: '/dashboard/technical-mcq-papers'
        },
        {
          title: 'Mock Papers',
          icon: <FileCheck className="w-5 h-5" />,
          href: '/dashboard/model-papers'
        },
        {
          title: 'Chapter-Wise Assessment',
          icon: <ClipboardList className="w-5 h-5" />,
          href: '/dashboard/chapter-papers'
        },
        {
          title: 'Competitive',
          icon: <Award className="w-5 h-5" />,
          href: '/dashboard/competitive-papers'
        },
        {
          title: 'Weekly Competitive',
          icon: <Trophy className="w-5 h-5" />,
          href: '/dashboard/open-competitive-papers'
        },
        {
          title: 'Section Wise Papers',
          icon: <Layers className="w-5 h-5" />,
          href: '/dashboard/section-wise-papers'
        },
        {
          title: 'AFCAT Papers',
          icon: <Plane className="w-5 h-5" />,
          href: '/dashboard/afcat-papers'
        },
        {
          title: 'Company Papers',
          icon: <Briefcase className="w-5 h-5" />,
          href: '/dashboard/company-papers'
        }
      ]
    }
  ]

  const renderSidebarItem = (item: SidebarItem, depth = 0) => {
    const isActive = pathname === item.href
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.title)

    return (
      <div key={item.title}>
        <Link
          href={item.href}
          onClick={(e) => {
            if (hasChildren) {
              e.preventDefault()
              toggleExpanded(item.title)
            }
          }}
          className={`
            flex items-center py-2.5 px-4 text-sm font-normal rounded-lg transition-all duration-200
            ${depth > 0 ? 'ml-6' : ''}
            ${
              isActive
                ? 'bg-gradient-to-r from-sky-50 to-blue-50 text-sky-700 font-medium'
                : 'text-gray-700 hover:bg-gray-200'
            }
          `}
        >
          <div
            className={`
            ${isActive ? 'icon-box-white' : 'icon-box-white'}
            mr-3
          `}
          >
            {item.icon}
          </div>
          <span className="flex-1">{item.title}</span>
          {hasChildren && (
            <span className="ml-auto">
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </span>
          )}
        </Link>
        {hasChildren && isExpanded && (
          <div className="mt-1">
            {item.children!.map((child) => renderSidebarItem(child, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <aside
      id="sidebar"
      className="flex flex-col w-64 h-full bg-white border-r border-gray-200"
      aria-label="Sidebar"
    >
      <div className="hidden lg:flex items-center px-4 py-4 border-b border-gray-200">
        <Image
          src="/img/logo.png"
          alt="QuantMasters"
          width={120}
          height={40}
          className="h-8 w-auto"
          priority
        />
      </div>

      <div className="flex flex-col flex-1 overflow-y-auto">
        <div className="flex flex-col flex-1 px-3 py-4 space-y-1">
          {sidebarSections.map((section, index) => (
            <div key={section.title}>
              {index > 0 && (
                <>
                  <div className="border-t border-gray-200 my-4" />
                  <p className="px-3 mb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    {section.title}
                  </p>
                </>
              )}
              {section.items.map((item) => renderSidebarItem(item))}
            </div>
          ))}

          <div className="border-t border-gray-200 my-4" />

          <div className="space-y-1">
            <Link
              href="/dashboard/user/profile"
              className={`
                flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200
                ${
                  pathname === '/dashboard/user/profile'
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <div className="flex items-center justify-center w-6 h-6 mr-3">
                <Settings className="w-4 h-4" />
              </div>
              <span>Settings</span>
            </Link>
            <a
              href="https://help.quantmasters.in"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200"
            >
              <div className="flex items-center justify-center w-6 h-6 mr-3">
                <HelpCircle className="w-4 h-4" />
              </div>
              <span>Help & Support</span>
            </a>
          </div>
        </div>

        {/* Bottom section */}
        <div className="flex-shrink-0 border-t border-gray-200 p-4">
          <p className="text-xs text-gray-500 text-center">
            © 2024 QuantMasters
          </p>
        </div>
      </div>
    </aside>
  )
}

export default Sidebar
