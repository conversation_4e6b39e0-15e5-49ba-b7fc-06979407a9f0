// components/admin/tests/create-paper-modal.tsx
'use client'

import { useState } from 'react'
import { X } from 'lucide-react'
import {
  Paper,
  CreatePaperRequest,
  getPaperTypeConfig
} from '@/types/admin-types'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'sonner'

interface CreatePaperModalProps {
  isOpen: boolean
  paperType: number | null
  selectedTopic?: string
  selectedSubtopic?: string
  onClose: () => void
  onSuccess: (paper: Paper) => void
}

export default function CreatePaperModal({
  isOpen,
  paperType,
  selectedTopic,
  selectedSubtopic,
  onClose,
  onSuccess
}: CreatePaperModalProps) {
  const [formData, setFormData] = useState<CreatePaperRequest>({
    paper_name: '',
    no_of_ques: 10,
    time_lim: 600000, // 10 minutes in milliseconds
    show_ans: '1',
    once_ans: '0',
    neg_marks: '0',
    rand_ques: '0',
    group_id: selectedTopic,
    sub_group_id: selectedSubtopic
  })
  const [loading, setLoading] = useState(false)

  if (!isOpen || !paperType) return null

  const config = getPaperTypeConfig(paperType)

  if (!config.canCreate) {
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.paper_name.trim()) {
      toast.error('Paper name is required')
      return
    }

    if (formData.no_of_ques <= 0) {
      toast.error('Number of questions must be greater than 0')
      return
    }

    setLoading(true)

    try {
      const paperData = {
        ...formData,
        time_lim: formData.time_lim * 60000 // Convert minutes to milliseconds
      }

      const newPaper = await AdminPapersClientService.createPaper(
        paperData,
        paperType
      )
      onSuccess(newPaper)

      // Reset form
      setFormData({
        paper_name: '',
        no_of_ques: 10,
        time_lim: 600000,
        show_ans: '1',
        once_ans: '0',
        neg_marks: '0',
        rand_ques: '0',
        group_id: selectedTopic,
        sub_group_id: selectedSubtopic
      })
    } catch (error) {
      console.error('Error creating paper:', error)
      toast.error('Failed to create paper')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (
    field: keyof CreatePaperRequest,
    value: string | number
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Create New Paper</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Paper Name */}
          <div className="space-y-2">
            <Label htmlFor="paper_name">Paper Name *</Label>
            <Input
              id="paper_name"
              value={formData.paper_name}
              onChange={(e) => handleInputChange('paper_name', e.target.value)}
              placeholder="Enter paper name"
              required
            />
          </div>

          {/* Number of Questions */}
          <div className="space-y-2">
            <Label htmlFor="no_of_ques">Number of Questions *</Label>
            <Input
              id="no_of_ques"
              type="number"
              min="1"
              value={formData.no_of_ques}
              onChange={(e) =>
                handleInputChange('no_of_ques', parseInt(e.target.value))
              }
              required
            />
          </div>

          {/* Time Limit */}
          <div className="space-y-2">
            <Label htmlFor="time_lim">Time Limit (minutes)</Label>
            <Input
              id="time_lim"
              type="number"
              min="0"
              value={Math.round(formData.time_lim / 60000)}
              onChange={(e) =>
                handleInputChange('time_lim', parseInt(e.target.value) * 60000)
              }
              placeholder="0 for no time limit"
            />
          </div>

          {/* Show Answers */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="show_ans"
              checked={formData.show_ans === '1'}
              onCheckedChange={(checked) =>
                handleInputChange('show_ans', checked ? '1' : '0')
              }
            />
            <Label htmlFor="show_ans">Show answers after completion</Label>
          </div>

          {/* One Attempt Only */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="once_ans"
              checked={formData.once_ans === '1'}
              onCheckedChange={(checked) =>
                handleInputChange('once_ans', checked ? '1' : '0')
              }
            />
            <Label htmlFor="once_ans">Allow only one attempt</Label>
          </div>

          {/* Negative Marking */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="neg_marks"
              checked={formData.neg_marks === '1'}
              onCheckedChange={(checked) =>
                handleInputChange('neg_marks', checked ? '1' : '0')
              }
            />
            <Label htmlFor="neg_marks">Enable negative marking</Label>
          </div>

          {/* Randomize Questions */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="rand_ques"
              checked={formData.rand_ques === '1'}
              onCheckedChange={(checked) =>
                handleInputChange('rand_ques', checked ? '1' : '0')
              }
            />
            <Label htmlFor="rand_ques">Randomize questions</Label>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-6">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create Paper'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
