// types/paper-types.ts
export interface ChapterPaper {
  paper_id: string
  paper_name: string
  time_lim: number
  no_of_ques: number
}

export interface Question {
  question_id: string
  question_no: string
  question_text: string
  option_a: string
  option_b: string
  option_c: string
  option_d: string
  option_e: string
  correct_option: string
  explanation: string
}

export interface AnswerSubmission {
  email: string
  paper_id: string
  marks: number
  answered_on: string
}
