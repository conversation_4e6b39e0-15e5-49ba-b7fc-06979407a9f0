import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { CompetitiveGroup } from 'src/app/Models/Dashboard/Competitive/CompetitiveGroup';
import { CompetitivePapers } from 'src/app/Models/Dashboard/Competitive/CompetitivePapers';
import { CompetitiveQuestion } from 'src/app/Models/Dashboard/Competitive/CompetitiveQuestion';

@Injectable({
  providedIn: 'root'
})
export class CompetitiveService {

  private groupUrl      = 'https://api.quantmasters.in/test/competitive/groups';
  private papersUrl     = 'https://api.quantmasters.in/test/competitive/papers';
  private groupPaperUrl = 'https://api.quantmasters.in/test/competitive/group';
  private submitUrl     = 'https://api.quantmasters.in/test/competitive/submit/marks';
  private questionUrl   = 'https://api.quantmasters.in/test/competitive/paper/new';

  private vpUrls        = 'https://api.quantmasters.in/v2/verbal/practice';
  private tpUrls        = 'https://api.quantmasters.in/v2/technical/practice';
  private admUrls       = 'https://api.quantmasters.in/admin/paper/competitive';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getGroups(): Observable<CompetitiveGroup[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompetitiveGroup[]>(this.groupUrl, httpOps);
  }

  getDetailsOfAGroup(groupId: string): Observable<CompetitiveGroup> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompetitiveGroup>(`${this.groupUrl}/${groupId}/detail`, httpOps);
  }

  getAllPapers(): Observable<CompetitivePapers[]> {
    this.setSecurityToken();

    const groupPaperUrl = this.papersUrl;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompetitivePapers[]>(groupPaperUrl, httpOps);
  }

  getPapersOfAGroup(groupId: string): Observable<CompetitivePapers[]> {
    this.setSecurityToken();

    const groupPaperUrl = this.groupPaperUrl + '/' + groupId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompetitivePapers[]>(groupPaperUrl, httpOps);
  }

  getMarksForAStudent(email: string): Observable<string> {
    this.setSecurityToken();

    const url = `${this.papersUrl}/practice/${email}/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    return this.http.get<string>(url, httpOps);
  }

  submitMarks(paperId: string, email: string, marks: number): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const data = {
      email: email,
      paper_id: paperId,
      marks: marks
    };

    return this.http.post<string>(this.submitUrl, data, httpOps);
  }

  getVerbalPracticeGroups(): Observable<string[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string[]>(`${this.vpUrls}/groups`, httpOps);
  }

  getTechnicalPracticeGroups(): Observable<string[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string[]>(`${this.tpUrls}/groups`, httpOps);
  }

  createNewVerbalPracticeGroup(group: CompetitiveGroup): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(`${this.vpUrls}/groups`, group, httpOps);
  }

  createNewCompetitivePaper(paper: CompetitivePapers): Observable<CompetitivePapers> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<CompetitivePapers>(`${this.admUrls}/upload/test`, paper, httpOps);
  }

  updateCompetitivePaper(paper: CompetitivePapers): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(`${this.admUrls}/update/test`, paper, httpOps);
  }

  deleteCompetitivePaper(paperId: string): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.delete<string>(`${this.admUrls}/update/test/${paperId}`, httpOps);
  }

  /**
   * Get a Competitive Paper's Questions, also used for adding
   * Verbal Practice and Technical Practice Papers.
   * 
   * @param paperId Guid of the paper
   * @returns The added question
   */
   getCompetitivePaperQuestions(paperId: string): Observable<String[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<String[]>(`${this.questionUrl}/${paperId}`, httpOps);
  }

  /**
   * Create a new Competitive Paper Question, also used for adding
   * Verbal Practice and Technical Practice Papers.
   * 
   * @param question The Question Object
   * @returns The added question
   */
  createCompetitivePaperQuestion(question: Object): Observable<Object> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<Object>(`${this.admUrls}/upload/question`, question, httpOps);
  }

  /**
   * Create a new Competitive Paper Question, also used for adding
   * Verbal Practice and Technical Practice Papers.
   * 
   * @param question The Question Object
   * @returns The added question
   */
   updateCompetitivePaperQuestion(question: Object): Observable<Object> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<Object>(`${this.admUrls}/update/question`, question, httpOps);
  }

  deleteCompetitivePaperQuestion(paperId: string, quesNo: string): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.delete<string>(`${this.admUrls}/update/test/${paperId}/${quesNo}`, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
