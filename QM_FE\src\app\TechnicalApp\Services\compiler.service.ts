import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class CompilerService {

  private compilerUrl = 'https://api.quantmasters.in/track/compiler/';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getEntryForClient(email: string): Observable<string> {

    const url = this.compilerUrl + email;

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getCountForClient(email: string): Observable<string> {

    const url = this.compilerUrl + email + '/usage';

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  setCountForClient(email: string): Observable<string> {

    const url = this.compilerUrl + email + '/decrease';

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.put<string>(url, null, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
