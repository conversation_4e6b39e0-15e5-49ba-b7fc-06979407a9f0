'use client'

import Image from 'next/image'
import { useState } from 'react'
import { achieversGroups } from './achievers-list-data'

// Initial display limit per section
const INITIAL_DISPLAY_COUNT = 6
// Number of additional items to show when "Show More" is clicked
const LOAD_MORE_COUNT = 6

export default function AchieversListComponent() {
  // Create a state object to track visible items count for each year
  const [visibleCounts, setVisibleCounts] = useState<Record<string, number>>(
    () => {
      // Initialize with the default count for each year
      const initialCounts: Record<string, number> = {}
      Object.keys(achieversGroups).forEach((year) => {
        initialCounts[year] = INITIAL_DISPLAY_COUNT
      })
      return initialCounts
    }
  )

  // Function to handle "Show More" button click for a specific year
  const handleShowMore = (year: string) => {
    setVisibleCounts((prevCounts) => ({
      ...prevCounts,
      [year]: prevCounts[year] + LOAD_MORE_COUNT
    }))
  }

  return (
    <div className="py-16 space-y-28">
      {Object.entries(achieversGroups).map(([year, achievers]) => (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" key={year}>
          <h2 className="text-3xl font-bold text-gray-900 mb-10 text-center">
            {year}
          </h2>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Only display the limited number of achievers */}
            {achievers.slice(0, visibleCounts[year]).map((achiever, index) => (
              <div
                key={index}
                className="bg-white rounded-lg overflow-hidden shadow-lg transition-all duration-400 hover:shadow-xl shad"
              >
                <div className="relative h-64 aspect-square p-4">
                  <Image
                    src={achiever.image}
                    alt={achiever.name}
                    fill
                    className="object-cover rounded-full aspect-square border-amber-500 border-12"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {achiever.name}
                  </h3>
                  <p className="text-blue-600 mb-3">{achiever.company}</p>
                  {/* <p className="text-gray-700 mb-4">{achiever.testimonial}</p>
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <p className="text-sm font-medium text-blue-800">
                                Achievement: {achiever.achievement}
                              </p>
                            </div> */}
                </div>
              </div>
            ))}
          </div>

          {/* Show the "Show More" button only if there are more items to show */}
          {visibleCounts[year] < achievers.length && (
            <div className="mt-10 text-center">
              <button
                onClick={() => handleShowMore(year)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md font-medium shadow-lg inline-block transition-all"
              >
                Show More {year} Achievers
              </button>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
