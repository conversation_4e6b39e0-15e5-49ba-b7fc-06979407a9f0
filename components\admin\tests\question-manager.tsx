import { useState } from 'react'
import { Plus, Edit, Trash2, <PERSON>, <PERSON>Off, Upload } from 'lucide-react'
import { toast } from 'sonner'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog'
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import LoadingIndicator from '@/components/shared/indicator'
import { BulkUploadQuestions } from '@/components/admin/bulk-upload-questions'

interface QuestionManagerProps {
  paperId: string
  paperType: number
  questions: any[]
  onQuestionsUpdate: (questions: any[]) => void
}

interface QuestionFormData {
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  opt_5?: string
  correct_opt: number
  explanation?: string
}

export default function QuestionManager({
  paperId,
  paperType,
  questions,
  onQuestionsUpdate
}: QuestionManagerProps) {
  const [showAddModal, setShowAddModal] = useState(false)
  const [showBulkUpload, setShowBulkUpload] = useState(false)
  const [editingQuestion, setEditingQuestion] = useState<any>(null)
  const [formData, setFormData] = useState<QuestionFormData>({
    question: '',
    opt_1: '',
    opt_2: '',
    opt_3: '',
    opt_4: '',
    opt_5: '',
    correct_opt: 1,
    explanation: ''
  })
  const [loading, setLoading] = useState(false)
  const [expandedQuestions, setExpandedQuestions] = useState<Set<number>>(
    new Set()
  )

  const resetForm = () => {
    setFormData({
      question: '',
      opt_1: '',
      opt_2: '',
      opt_3: '',
      opt_4: '',
      opt_5: '',
      correct_opt: 1,
      explanation: ''
    })
    setEditingQuestion(null)
  }

  const handleAddQuestion = async () => {
    if (!formData.question || !formData.opt_1 || !formData.opt_2) {
      toast.error('Please fill in required fields')
      return
    }

    setLoading(true)
    try {
      const questionData = {
        ...formData,
        question_no: questions.length + 1
      }
      const newQuestion = await AdminPapersClientService.addQuestion(
        paperId,
        questionData,
        paperType
      )
      onQuestionsUpdate([...questions, newQuestion])
      setShowAddModal(false)
      resetForm()
      toast.success('Question added successfully')
    } catch {
      toast.error('Failed to add question')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateQuestion = async () => {
    if (!editingQuestion) return

    setLoading(true)
    try {
      await AdminPapersClientService.updateQuestion(
        paperId,
        editingQuestion.question_no,
        formData,
        paperType
      )
      const updated = questions.map((q) =>
        q.question_no === editingQuestion.question_no
          ? { ...q, ...formData }
          : q
      )
      onQuestionsUpdate(updated)
      setEditingQuestion(null)
      resetForm()
      toast.success('Question updated successfully')
    } catch {
      toast.error('Failed to update question')
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteQuestion = async (questionNo: number) => {
    if (!confirm('Are you sure you want to delete this question?')) return

    setLoading(true)
    try {
      await AdminPapersClientService.deleteQuestion(
        paperId,
        questionNo,
        paperType
      )
      const filtered = questions.filter((q) => q.question_no !== questionNo)
      // Renumber questions
      const renumbered = filtered.map((q, index) => ({
        ...q,
        question_no: index + 1
      }))
      onQuestionsUpdate(renumbered)
      toast.success('Question deleted successfully')
    } catch {
      toast.error('Failed to delete question')
    } finally {
      setLoading(false)
    }
  }

  const startEdit = (question: any) => {
    setEditingQuestion(question)
    setFormData({
      question: question.question,
      opt_1: question.opt_1,
      opt_2: question.opt_2,
      opt_3: question.opt_3,
      opt_4: question.opt_4,
      opt_5: question.opt_5 || '',
      correct_opt: question.correct_opt,
      explanation: question.explanation || ''
    })
  }

  const toggleExpanded = (questionNo: number) => {
    const newExpanded = new Set(expandedQuestions)
    if (newExpanded.has(questionNo)) {
      newExpanded.delete(questionNo)
    } else {
      newExpanded.add(questionNo)
    }
    setExpandedQuestions(newExpanded)
  }

  const QuestionForm = () => (
    <div className="space-y-4">
      <div>
        <Label htmlFor="question">Question *</Label>
        <Textarea
          id="question"
          value={formData.question}
          onChange={(e) =>
            setFormData({ ...formData, question: e.target.value })
          }
          placeholder="Enter the question"
          className="min-h-[100px]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="opt_1">Option 1 *</Label>
          <Input
            id="opt_1"
            value={formData.opt_1}
            onChange={(e) =>
              setFormData({ ...formData, opt_1: e.target.value })
            }
            placeholder="Option 1"
          />
        </div>
        <div>
          <Label htmlFor="opt_2">Option 2 *</Label>
          <Input
            id="opt_2"
            value={formData.opt_2}
            onChange={(e) =>
              setFormData({ ...formData, opt_2: e.target.value })
            }
            placeholder="Option 2"
          />
        </div>
        <div>
          <Label htmlFor="opt_3">Option 3</Label>
          <Input
            id="opt_3"
            value={formData.opt_3}
            onChange={(e) =>
              setFormData({ ...formData, opt_3: e.target.value })
            }
            placeholder="Option 3"
          />
        </div>
        <div>
          <Label htmlFor="opt_4">Option 4</Label>
          <Input
            id="opt_4"
            value={formData.opt_4}
            onChange={(e) =>
              setFormData({ ...formData, opt_4: e.target.value })
            }
            placeholder="Option 4"
          />
        </div>
        <div>
          <Label htmlFor="opt_5">Option 5</Label>
          <Input
            id="opt_5"
            value={formData.opt_5}
            onChange={(e) =>
              setFormData({ ...formData, opt_5: e.target.value })
            }
            placeholder="Option 5 (optional)"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="correct_opt">Correct Answer *</Label>
        <Select
          value={formData.correct_opt.toString()}
          onValueChange={(value: string) =>
            setFormData({ ...formData, correct_opt: parseInt(value) })
          }
        >
          <SelectTrigger id="correct_opt">
            <SelectValue placeholder="Select correct option" />
          </SelectTrigger>
          <SelectContent>
            {[1, 2, 3, 4, 5].map((opt) => (
              <SelectItem key={opt} value={opt.toString()}>
                Option {opt}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="explanation">Explanation</Label>
        <Textarea
          id="explanation"
          value={formData.explanation}
          onChange={(e) =>
            setFormData({ ...formData, explanation: e.target.value })
          }
          placeholder="Enter explanation (optional)"
          className="min-h-[80px]"
        />
      </div>
    </div>
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Questions</h3>
          <p className="text-sm text-gray-600">
            {questions.length} question{questions.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Question
          </Button>
          <Button variant="outline" onClick={() => setShowBulkUpload(true)}>
            <Upload className="h-4 w-4 mr-2" />
            Bulk Upload
          </Button>
        </div>
      </div>

      {/* Questions List */}
      {questions.length === 0 ? (
        <Card className="border-dashed">
          <CardContent className="text-center py-12">
            <p className="text-gray-500 mb-4">No questions yet</p>
            <Button onClick={() => setShowAddModal(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Question
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {questions.map((question) => (
            <Card key={question.question_no}>
              <CardHeader>
                <div className="flex justify-between w-full items-start">
                  <div className="flex-1">
                    <CardTitle className="text-base">
                      Question {question.question_no}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {question.question}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => toggleExpanded(question.question_no)}
                    >
                      {expandedQuestions.has(question.question_no) ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => startEdit(question)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-red-600 hover:text-red-700"
                      onClick={() => handleDeleteQuestion(question.question_no)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              <Collapsible open={expandedQuestions.has(question.question_no)}>
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {[1, 2, 3, 4, 5].map((i) => {
                        const option = question[`opt_${i}`]
                        if (!option) return null
                        const isCorrect = question.correct_opt === i
                        return (
                          <div
                            key={i}
                            className={`p-2 rounded ${isCorrect ? 'bg-green-50 border border-green-200' : 'bg-gray-50'}`}
                          >
                            <span className="font-medium">{i}.</span> {option}
                            {isCorrect && (
                              <span className="ml-2 text-green-600 text-sm">
                                (Correct)
                              </span>
                            )}
                          </div>
                        )
                      })}
                      {question.explanation && (
                        <div className="mt-4 p-3 bg-blue-50 rounded">
                          <p className="text-sm font-medium text-blue-900">
                            Explanation:
                          </p>
                          <p className="text-sm text-blue-700 mt-1">
                            {question.explanation}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))}
        </div>
      )}

      {/* Add/Edit Question Modal */}
      <Dialog
        open={showAddModal || !!editingQuestion}
        onOpenChange={(open) => {
          if (!open) {
            setShowAddModal(false)
            setEditingQuestion(null)
            resetForm()
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingQuestion ? 'Edit Question' : 'Add New Question'}
            </DialogTitle>
            <DialogDescription>
              {editingQuestion
                ? `Editing question ${editingQuestion.question_no}`
                : 'Fill in the details to add a new question'}
            </DialogDescription>
          </DialogHeader>

          <QuestionForm />

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowAddModal(false)
                setEditingQuestion(null)
                resetForm()
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={
                editingQuestion ? handleUpdateQuestion : handleAddQuestion
              }
              disabled={loading}
            >
              {loading && <LoadingIndicator isLoading={loading} />}
              {editingQuestion ? 'Update' : 'Add'} Question
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Upload Questions Modal */}
      <Dialog
        open={showBulkUpload}
        onOpenChange={(open) => {
          if (!open) {
            setShowBulkUpload(false)
          }
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Upload Questions</DialogTitle>
            <DialogDescription>
              Upload questions in bulk using an Excel file with predefined
              format.
            </DialogDescription>
          </DialogHeader>

          <BulkUploadQuestions
            paperId={paperId}
            paperType={paperType}
            onUploadComplete={() => {
              setShowBulkUpload(false)
              // Refresh questions list
              AdminPapersClientService.getPaperQuestions(paperId, paperType)
                .then((updatedQuestions) => {
                  onQuestionsUpdate(updatedQuestions)
                  toast.success('Questions list refreshed')
                })
                .catch(() => {
                  toast.error('Failed to refresh questions list')
                })
            }}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
