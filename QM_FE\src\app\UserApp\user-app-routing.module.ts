import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { UserProfileComponent } from './user-profile/user-profile.component';
import { ResultsComponent } from './results/results.component';
import { ForgotPasswordComponent } from '../Components/Utilities/forgot-password/forgot-password.component';
import { PasswordRecoveryComponent } from '../Components/Utilities/password-recovery/password-recovery.component';

const routes: Routes = [
  {
    path: 'login',
    component: LoginComponent
  },
  {
    path: 'register',
    component: RegisterComponent
  }, {
    path: 'profile',
    component: UserProfileComponent
  }, {
    path: 'results',
    component: ResultsComponent
  },
  {
    path: 'password/forgot',
    component: ForgotPasswordComponent
  },
  {
    path: 'password/reset/rst_l/:reset_key/:email',
    component: PasswordRecoveryComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class UserAppRoutingModule { }
