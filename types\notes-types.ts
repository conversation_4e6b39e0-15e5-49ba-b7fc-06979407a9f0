// types/notes-types.ts

export interface SuperGroup {
  super_id: string
  super_name: string
  super_seq: number
}

export interface NotesGroup {
  super_id: string
  super_name: string
  super_seq: number
  group_id: string
  group_name: string
  group_seq: number
}

export interface Notes {
  group_id: string
  notes_id: string
  notes_name: string
  posted_on: string
  notes_content: string
  notes_video?: string
  public?: number
}

export interface Comment {
  comment_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user_avatar?: string
  replies: CommentReply[]
}

export interface CommentReply {
  reply_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user_avatar?: string
}

export interface CommentSubmission {
  text: string
  email: string
}

export interface NotesData {
  thisNoteId: string
  selectedSupGroup: string
  selectedGroup: string
  nextNoteId?: string
  nextNoteName?: string
  prevNoteId?: string
  prevNoteName?: string
}
