'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Clock, FileText, ArrowR<PERSON> } from 'lucide-react'
import { Paper } from '@/types/model-paper-types'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface ModelPapersClientProps {
  initialPapers: Paper[]
}

export default function ModelPapersClient({
  initialPapers
}: ModelPapersClientProps) {
  const router = useRouter()
  const [papers] = useState<Paper[]>(initialPapers)
  const [isLoading, setIsLoading] = useState(false)

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    paperLim: number
  ) => {
    setIsLoading(true)
    router.push(`/dashboard/test/4/${paperId}/${paperName}/${paperLim}`)
  }

  const navigateToPlans = () => {
    router.push('/plans')
  }

  // const isPublicPaper = (paper: Paper) => parseInt(paper.public, 10) === 1

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading test...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="paper-wrap">
      {papers.length === 0 ? (
        <div className="text-center py-20">
          <div className="mb-6">
            <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 text-lg">No model papers.</p>
            <p className="text-gray-500 text-sm mt-2">
              Check back later for new practice papers.
            </p>
          </div>
          <Button onClick={navigateToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {papers.map((paper) => (
            <Card key={paper.paper_id} className="h-full flex flex-col">
              <CardHeader>
                <CardTitle>{paper.paper_name}</CardTitle>
                <CardDescription className="flex items-center space-x-1">
                  <Clock className="h-4 w-4" />
                  <span>
                    {paper.time_lim
                      ? `${paper.time_lim / 60000} Minutes`
                      : 'No Time Limit'}
                  </span>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-1">
                  <FileText className="h-4 w-4" />
                  <span>No. of Questions: {paper.no_of_ques}</span>
                </div>
              </CardContent>

              {paper.paper_desc && (
                <CardContent>
                  <span className={`text-sm text-gray-700`}>
                    {' '}
                    {paper.paper_desc}{' '}
                  </span>
                </CardContent>
              )}
              <CardFooter className="mt-auto">
                <Button
                  onClick={() =>
                    handleBeginTest(
                      paper.paper_id,
                      paper.paper_name,
                      paper.time_lim
                    )
                  }
                  className="w-full"
                >
                  Begin Test
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
