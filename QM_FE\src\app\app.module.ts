import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { AppRoutingModule } from './app-routing.module';

import { SharedNavModule } from './Components/Utilities/shared-nav/shared-nav.module';
import { SharedFooterModule } from './Components/Utilities/shared-footer/shared-footer.module';
import { SharedIndicatorModule } from './Components/Utilities/shared-indicator/shared-indicator.module';

import { ModalModule } from 'ngx-bootstrap/modal';
import { AlertModule } from 'ngx-bootstrap/alert';

import { CookieService } from 'ngx-cookie-service';

import { AppComponent } from './app.component';
import { HomeComponent } from './WebApp/home/<USER>';

import { PlansComponent } from './WebApp/plans/plans.component';
import { CheckoutComponent } from './WebApp/checkout/checkout.component';
import { AchieversComponent } from './WebApp/achievers/achievers.component';
import { LinksComponent } from './WebApp/links/links.component';
import { PrivacyPolicyComponent } from './WebApp/privacy-policy/privacy-policy.component';

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    PlansComponent,
    CheckoutComponent,
    AchieversComponent,
    LinksComponent,
    PrivacyPolicyComponent
  ],
  imports: [
    BrowserModule,
    SharedNavModule,
    SharedFooterModule,
    SharedIndicatorModule,
    AppRoutingModule,
    HttpClientModule,
    FormsModule,
    ReactiveFormsModule,
    BrowserAnimationsModule,
    ModalModule.forRoot(),
    AlertModule.forRoot()
  ],
  providers: [CookieService],
  bootstrap: [AppComponent]
})
export class AppModule { }
