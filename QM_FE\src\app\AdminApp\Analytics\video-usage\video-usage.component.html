<div class="video-usage-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="sect-1">
    <div class="info-box">
      <form #basicSearchForm="ngForm" (submit)="filterRecordsList($event)">
        <h6>Basic Search</h6>
        <div class="form-elem">
          <input type="text" name="basicInput" class="basic-search" [(ngModel)]="basicSearchVal" />
          <button type="submit" class="custom-btn">Search</button>
        </div>
      </form>
      <form #videoSearchForm="ngForm" (submit)="filterResultsListUsingFields($event)">
        <h6>Advanced Search</h6>
        <div class="form-fields">
          <div class="form-elem">
            <label for="name-inp">Name:</label>
            <input type="text" name="name-inp" [(ngModel)]="userName" />
          </div>
          <div class="form-elem">
            <label for="email-inp">Email:</label>
            <input type="text" name="email-inp" [(ngModel)]="email" />
          </div>
          <div class="form-elem">
            <label for="videoName">Video Name:</label>
            <input type="text" name="videoName" [(ngModel)]="videoName" />
          </div>
          <div class="form-elem">
            <label for="streamDate">Streamed On:</label>
            <input type="text" name="streamDate" [(ngModel)]="streamDate" />
          </div>
        </div>
        <div class="form-fields">
          <button type="submit" class="custom-btn">Filter</button>
          <button type="button" class="custom-btn clr-btn" (click)="clearFilters($event)">Clear All</button>
        </div>
      </form>
    </div>
    <div class="info-box ops-box">
      <h6>Sort Data</h6>
      <div class="icon-wrap" (click)="openSorter()">
        <svg id="Capa_1" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
          xmlns="http://www.w3.org/2000/svg">
          <g>
            <path
              d="m487 65.55v380.9c0 36.14-29.41 65.55-65.55 65.55h-330.9c-36.14 0-65.55-29.41-65.55-65.55v-380.9c0-36.14 29.41-65.55 65.55-65.55h330.9c36.14 0 65.55 29.41 65.55 65.55z"
              fill="#F9A825" />
            <path
              d="m487 65.55v380.9c0 36.14-29.41 65.55-65.55 65.55h-165.45v-512h165.45c36.14 0 65.55 29.41 65.55 65.55z"
              fill="#FFFDE7" />
            <path
              d="m343.997 447c-8.623 0-6.845-.635-58.604-52.394-14.024-14.021 7.17-35.256 21.213-21.213l22.394 22.394v-279.574l-22.394 22.394c-14.023 14.023-35.257-7.171-21.213-21.213 53.091-53.091 49.931-51.452 56.787-52.283 9.8-1.2 8.825.682 60.426 52.283 14.02 14.02-7.168 35.257-21.213 21.213l-22.393-22.394v279.574l22.394-22.394c9.478-9.478 25.606-2.634 25.606 10.607 0 8.297.09 6.123-52.394 58.606-1.822 2.187-6.185 4.394-10.609 4.394z"
              fill="#5B2C6F" />
            <g fill="#5B2C6F">
              <path
                d="m185.237 77.591c-5.681-15.22-27.601-15.82-33.919-.38-.118.292 2.305-6.066-53.877 141.45-2.949 7.742.937 16.408 8.679 19.356 7.742 2.951 16.408-.938 19.356-8.679l9.705-25.482h65.642l9.601 25.439c2.267 6.007 7.974 9.708 14.036 9.708 10.413 0 17.751-10.446 14.031-20.3zm-38.63 96.265 21.546-56.57 21.349 56.57z" />
              <path
                d="m216.819 417h-68.387l78.718-120.812c6.493-9.966-.68-23.188-12.568-23.188h-90.874c-8.284 0-15 6.716-15 15s6.716 15 15 15h63.197l-78.717 120.812c-6.494 9.966.678 23.188 12.567 23.188h96.064c8.284 0 15-6.716 15-15s-6.715-15-15-15z" />
            </g>
          </g>
        </svg>
      </div>
    </div>
  </div>
  <div class="sect-2">
    <div class="users-headers">
      <h6>Name</h6>
      <h6>Email</h6>
      <h6>Video Name</h6>
      <h6>Stream Time</h6>
      <h6>Streamed On</h6>
    </div>
    <div class="users-data" *ngFor="let record of displayRecords">
      <p>{{ record.user_name }}</p>
      <p>{{ record.email }}</p>
      <p>{{ record.video_name }}</p>
      <p>{{ record.total_length }}</p>
      <p>{{ record.display_date }}</p>
    </div>
    <pagination *ngIf="numFilteredResults == 0" [boundaryLinks]="true" [totalItems]="fullListLength" [rotate]="true" [maxSize]="7" [itemsPerPage]="15"
      (pageChanged)="pageChanged($event)"></pagination>
    <pagination *ngIf="numFilteredResults > 0" [boundaryLinks]="true" [totalItems]="numFilteredResults" [rotate]="true"
      [maxSize]="7" [itemsPerPage]="15" (pageChanged)="pageFilterChanged($event)"></pagination>
  </div>
  <ng-template #sortTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-warning">Sorters</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #sortForm="ngForm">
        <div class="form-row">
          <h6>Sort Field</h6>
          <div class="form-elem" (click)="selectSorter(1)">
            <label for="name">Name</label>
            <input type="radio" id="name" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(2)">
            <label for="email">Email</label>
            <input type="radio" id="email" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(3)">
            <label for="video">Video Name</label>
            <input type="radio" id="video" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(4)">
            <label for="date">Streamed On</label>
            <input type="radio" id="date" name="sort-field" />
          </div>
        </div>
        <div class="form-row">
          <h6>Sort Order</h6>
          <div class="form-elem" (click)="selectAsc()">
            <label for="asc-btn">Ascending</label>
            <input type="radio" id="asc-btn" name="order" />
          </div>
          <div class="form-elem" (click)="selectDesc()">
            <label for="desc-btn">Descending</label>
            <input type="radio" id="desc-btn" name="order" />
          </div>
        </div>
        <div class="form-row">
          <button (click)="sortList()">Go</button>
        </div>
      </form>
    </div>
  </ng-template>
</div>