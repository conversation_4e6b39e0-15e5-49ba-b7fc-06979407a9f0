<div class="c-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="compiler-box">
    <h5>Code Editor</h5>
    <ngx-monaco-editor style="height: 400px;" class="code-editor" [options]="editorOptions" [(ngModel)]="code"></ngx-monaco-editor>
    <div class="editor-config">
      <label for="lang">Language: </label>
      <select [(ngModel)]="selectedLang" (change)="handleLangChange()">
        <option value="c">C</option>
        <option value="cpp">C++</option>
        <option value="python2">Python 2</option>
        <option value="python3">Python 3</option>
        <!-- <option value="java">Java</option> -->
      </select>
      <label for="theme">Theme: </label>
      <select name="theme" [(ngModel)]="selectedTheme" (change)="handleThemeChange()">
        <option value="vs">Visual Studio</option>
        <option value="vs-dark" selected>Visual Studio-Dark</option>
        <option value="hc-black">High Contrast-Black</option>
      </select>
    </div>
    <button class="exec-btn" (click)="executeCode()">Run Code</button>
    <p>Compiler Runs Left: <b [ngClass]="{'lots': compilerTodayCount >= 7, 'some': compilerTodayCount >= 4 && compilerTodayCount < 7, 'few': compilerTodayCount <= 3}">{{ compilerTodayCount }}</b></p>
    <h5>Input</h5>
    <div class="input-box">
      <ngx-monaco-editor style="height: 200px;" class="code-editor" [options]="inputEditorOptions" [(ngModel)]="inputText"></ngx-monaco-editor>
    </div>
    <h5>Output</h5>
    <div class="output-box">
      <div class="perf-metrics" [ngClass]="{'error': compilerStateError, 'success': op.statusCode == 200}">
        <small><b>Status:</b> {{ compilerStateError ? 'Failed' : op.statusCode == 200 ? 'Executed' : 'None' }}</small>
        <small><b>CPU:</b> {{ op.cpuTime }} ms</small><br />
        <small><b>Memory:</b> {{ op.memory }} b</small>
      </div>
      <p>{{ op.output }}</p>
    </div>
  </div>
  <ng-template #errTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-warning">Oops, looks like thats it for today</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body text-center">
      <p>Due to technical constraints unfortunalty we have to limit your executions to 10 per day.</p>
      <p>But fret not you'll get fresh 10 new executions tomorrow.</p>
    </div>
  </ng-template>
</div>