import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { PaginationModule } from 'ngx-bootstrap/pagination';

import { UserAppRoutingModule } from './user-app-routing.module';

import { SharedNavModule } from '../Components/Utilities/shared-nav/shared-nav.module';
import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { AppPasswordValidatorDirective } from '../Validators/app-password-validator.directive';
import { PasswordEqualValidatorDirective } from '../Validators/password-equal-validator.directive';

import { LoginComponent } from './login/login.component';
import { RegisterComponent } from './register/register.component';
import { UserProfileComponent } from './user-profile/user-profile.component';
import { ResultsComponent } from './results/results.component';
import { ForgotPasswordComponent } from '../Components/Utilities/forgot-password/forgot-password.component';
import { PasswordRecoveryComponent } from '../Components/Utilities/password-recovery/password-recovery.component';

@NgModule({
  declarations: [
    AppPasswordValidatorDirective,
    PasswordEqualValidatorDirective,
    LoginComponent,
    RegisterComponent,
    UserProfileComponent,
    ResultsComponent,
    ForgotPasswordComponent,
    PasswordRecoveryComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    UserAppRoutingModule,
    BsDatepickerModule.forRoot(),
    PaginationModule.forRoot(),
    SharedNavModule,
    SharedIndicatorModule
  ]
})
export class UserAppModule { }
