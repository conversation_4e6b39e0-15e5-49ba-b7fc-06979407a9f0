'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Clock,
  FileText,
  ArrowR<PERSON>,
  ChevronDown,
  ChevronUp
} from 'lucide-react'
import { SuperGroup, Group, ChapterPaper } from '@/types/chapter-paper-types'
import { ChaptersService } from '@/lib/client-services/chapters-service.client'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface ChapterPapersClientProps {
  initialSuperGroups: SuperGroup[]
}

export default function ChapterPapersClient({
  initialSuperGroups
}: ChapterPapersClientProps) {
  const router = useRouter()

  const [superGroups] = useState<SuperGroup[]>(initialSuperGroups)
  const [paperGroups, setPaperGroups] = useState<Group[]>([])
  const [chapterPapers, setChapterPapers] = useState<ChapterPaper[]>([])
  const [isCollapsed, setIsCollapsed] = useState<boolean[]>([])
  const [selectedGroup, setSelectedGroup] = useState<boolean[]>([])
  const [showIndicator, setShowIndicator] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // Initialize selected group state
    const initialSelectedGroup = new Array(superGroups.length).fill(false)
    setSelectedGroup(initialSelectedGroup)

    // Load saved state from localStorage
    const savedGroupIdx = localStorage.getItem('qmChapterGroupIdx')
    const savedGroupId = localStorage.getItem('qmChapterGroupId')

    if (savedGroupIdx && savedGroupId) {
      const groupIdx = parseInt(savedGroupIdx)
      if (groupIdx < superGroups.length) {
        initialSelectedGroup[groupIdx] = true
        setSelectedGroup([...initialSelectedGroup])
        handleSelectGroup(savedGroupId, groupIdx)
      }
    } else if (superGroups.length > 0) {
      // Select first group by default
      initialSelectedGroup[0] = true
      setSelectedGroup([...initialSelectedGroup])
      handleSelectGroup(superGroups[0].super_group_id, 0)
    }
  }, [superGroups])

  const handleSelectGroup = async (superGrpId: string, groupIdx: number) => {
    setShowIndicator(true)

    // Save to localStorage
    localStorage.setItem('qmChapterGroupIdx', groupIdx.toString())
    localStorage.setItem('qmChapterGroupId', superGrpId)

    try {
      // Update selected group state
      const newSelectedGroup = new Array(superGroups.length).fill(false)
      newSelectedGroup[groupIdx] = true
      setSelectedGroup(newSelectedGroup)

      // Fetch groups for selected super group
      const groups = await ChaptersService.getGroups(superGrpId)
      setPaperGroups(groups)

      // Initialize collapsed state for groups
      setIsCollapsed(new Array(groups.length).fill(true))

      // Fetch papers for all groups
      const allPapers: ChapterPaper[] = []

      for (const group of groups) {
        try {
          const papers = await ChaptersService.getPapersOfAGroup(group.group_id)
          if (papers.length > 0) {
            allPapers.push(...papers)
          }
        } catch (error) {
          console.error(
            `Error fetching papers for group ${group.group_id}:`,
            error
          )
        }
      }

      // Filter out groups with no papers
      const groupsWithPapers = groups.filter((group) =>
        allPapers.some((paper) => paper.group_id === group.group_id)
      )

      setPaperGroups(groupsWithPapers)
      setChapterPapers(allPapers)
      setIsCollapsed(new Array(groupsWithPapers.length).fill(true))
    } catch (error) {
      console.error('Error selecting group:', error)
    } finally {
      setShowIndicator(false)
    }
  }

  const toggleGroupCollapse = (index: number) => {
    const newIsCollapsed = [...isCollapsed]
    newIsCollapsed[index] = !newIsCollapsed[index]
    setIsCollapsed(newIsCollapsed)
  }

  const filterChapterPapers = (groupId: string): ChapterPaper[] => {
    return chapterPapers.filter((paper) => paper.group_id === groupId)
  }

  const handleBeginTest = (
    paperId: string,
    paperName: string,
    paperLim: string
  ) => {
    setIsLoading(true)
    const timeLim = parseInt(paperLim) || 0
    router.push(`/dashboard/test/1/${paperId}/${paperName}/${timeLim}`)
  }

  const navigateToPlans = () => {
    router.push('/plans')
  }

  if (isLoading) {
    return <div className="text-center py-10">Please wait...</div>
  }

  return (
    <div className="chapter-papers-wrap">
      {showIndicator && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      )}

      {/* Super Group Selection */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Select Subject Category</h2>
        <div className="flex flex-wrap gap-2">
          {superGroups.map((superGroup, index) => (
            <Button
              key={superGroup.super_group_id}
              variant={selectedGroup[index] ? 'default' : 'outline'}
              onClick={() =>
                handleSelectGroup(superGroup.super_group_id, index)
              }
              className="transition-colors"
            >
              {superGroup.super_group_name}
            </Button>
          ))}
        </div>
      </div>

      {/* Paper Groups and Papers */}
      {paperGroups.length === 0 && !showIndicator ? (
        <div className="text-center py-10">
          <p className="text-gray-600">
            No chapter papers available for the selected category.
          </p>
          <Button onClick={navigateToPlans} className="mt-4">
            View Our Plans
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {paperGroups.map((group, groupIndex) => {
            const groupPapers = filterChapterPapers(group.group_id)
            if (groupPapers.length === 0) return null

            return (
              <div key={group.group_id} className="border rounded-lg">
                <div
                  className="flex items-center justify-between p-4 cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors"
                  onClick={() => toggleGroupCollapse(groupIndex)}
                >
                  <h3 className="text-lg font-medium">{group.group_name}</h3>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">
                      {groupPapers.length} paper
                      {groupPapers.length !== 1 ? 's' : ''}
                    </span>
                    {isCollapsed[groupIndex] ? (
                      <ChevronDown className="h-4 w-4" />
                    ) : (
                      <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </div>

                {!isCollapsed[groupIndex] && (
                  <div className="p-4 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {groupPapers.map((paper) => (
                        <Card
                          key={paper.paper_id}
                          className="h-full flex flex-col"
                        >
                          <CardHeader>
                            <CardTitle className="text-base">
                              {paper.paper_name}
                            </CardTitle>
                            <CardDescription className="flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>
                                {paper.time_lim && parseInt(paper.time_lim) > 0
                                  ? `${parseInt(paper.time_lim) / 60000} Minutes`
                                  : 'No Time Limit'}
                              </span>
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="space-y-2 text-sm">
                              <div className="flex items-center space-x-1">
                                <FileText className="h-4 w-4" />
                                <span>Questions: {paper.no_of_ques}</span>
                              </div>
                              {paper.level && (
                                <div className="flex items-center space-x-1">
                                  <span className="font-medium">Level:</span>
                                  <span className="capitalize">
                                    {paper.level}
                                  </span>
                                </div>
                              )}
                              {paper.neg_marks && (
                                <div className="flex items-center space-x-1">
                                  <span className="font-medium">
                                    Negative Marks:
                                  </span>
                                  <span>{paper.neg_marks}</span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                          <CardFooter className="mt-auto">
                            <Button
                              onClick={() =>
                                handleBeginTest(
                                  paper.paper_id,
                                  paper.paper_name,
                                  paper.time_lim
                                )
                              }
                              className="w-full"
                            >
                              Begin Test
                              <ArrowRight className="ml-2 h-4 w-4" />
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
