import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ApiAuthService } from '.././api-auth.service';

import { Groups } from '../../Models/Blog/Notes';

@Injectable({
  providedIn: 'root',
})
export class CertificationService {
  private BaseUrl = 'https://api.quantmasters.in';
  private JwtToken: string;

  constructor(
    private http: HttpClient,
    private apiAuthService: ApiAuthService
  ) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }


  getCertification(MailId): Observable<any> {
    // this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    const superGroupUrl = this.BaseUrl + `/v2/internship/certificate/${MailId}`;

    return this.http.get<string>(superGroupUrl, httpOps);
  }

  verifyCertificate(certId: string): Observable<string> {

    const superGroupUrl = this.BaseUrl + `/v2/internship/certificate/${certId}/verify`;

    return this.http.get<string>(superGroupUrl);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }

}
