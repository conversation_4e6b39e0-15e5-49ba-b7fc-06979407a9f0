// lib/server-services/competitive.service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import {
  CompetitiveGroup,
  CompetitivePapers
} from '@/types/competitive-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  groupUrl: `${API_BASE_URL}/test/competitive/groups`,
  papersUrl: `${API_BASE_URL}/test/competitive/papers`,
  groupPaperUrl: `${API_BASE_URL}/test/competitive/group`,
  questionUrl: `${API_BASE_URL}/test/competitive/paper/new`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerCompetitivePapersService {
  /**
   * Get competitive groups
   */
  static async getGroups(): Promise<CompetitiveGroup[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.groupUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching competitive groups:', error)
      return []
    }
  }

  /**
   * Get all competitive papers
   */
  static async getAllPapers(): Promise<CompetitivePapers[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.papersUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching competitive papers:', error)
      return []
    }
  }

  /**
   * Get papers of a specific group
   */
  static async getPapersOfAGroup(
    groupId: string
  ): Promise<CompetitivePapers[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.groupPaperUrl}/${groupId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching papers for group ${groupId}:`, error)
      return []
    }
  }

  /**
   * Get competitive paper questions
   */
  static async getCompetitivePaperQuestions(
    paperId: string
  ): Promise<string[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.questionUrl}/${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching questions for paper ${paperId}:`, error)
      throw error
    }
  }
}
