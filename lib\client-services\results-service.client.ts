import axios from 'axios'
import { StudentMarks, SectionMarks } from '@/types/results-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  indResultsAllUrl: `${API_BASE_URL}/view/results/ind/all`,
  openResultsUrl: `${API_BASE_URL}/v2/test/open`,
  weeklyCompetitiveUrl: `${API_BASE_URL}/v2/test/open/competitive`,
  sectionWiseUrl: `${API_BASE_URL}/v2/test/sectionWise`,
  afcatUrl: `${API_BASE_URL}/v2/test/afcat`,
  companyUrl: `${API_BASE_URL}/v2/test/open/company/papers`,
  technicalMcqUrl: `${API_BASE_URL}/v3/tmcq/paper`,
  wpPracticeUrl: `${API_BASE_URL}/test/competitive/papers/practice`,
  sectionWiseBreakdownUrl: `${API_BASE_URL}/v2/test/open/papers`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Process results data - add display_date and sort by created_at
 */
const processResults = (results: any[]): StudentMarks[] => {
  const processedResults = results.map((result) => {
    const date = new Date(result.created_at)

    // Debug: Log the sw_exists field to see what we're getting
    console.log('Result data:', {
      paper_name: result.paper_name,
      sw_exists: result.sw_exists,
      answer_id: result.answer_id,
      fullResult: result
    })

    return {
      ...result,
      display_date: date.toLocaleString()
    }
  })

  return processedResults.sort((a, b) => {
    const aDate = new Date(a.created_at)
    const bDate = new Date(b.created_at)
    return aDate > bDate ? -1 : aDate < bDate ? 1 : 0
  })
}

export class ResultsService {
  /**
   * Get all individual results for a user
   */
  static async getAllMarks(userEmail: string): Promise<StudentMarks[]> {
    try {
      const data = { email: userEmail }
      const response = await axios.post(
        ENDPOINTS.indResultsAllUrl,
        data,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching all marks:', error)
      throw error
    }
  }

  /**
   * Get trial paper results
   */
  static async getTrialPaperMarks(userEmail: string): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.openResultsUrl}/papers/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching trial paper marks:', error)
      throw error
    }
  }

  /**
   * Get weekly competitive paper results
   */
  static async getWeeklyCompetitivePaperMarks(
    userEmail: string
  ): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.weeklyCompetitiveUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching weekly competitive marks:', error)
      throw error
    }
  }

  /**
   * Get section wise paper results
   */
  static async getSectionWisePaperMarks(
    userEmail: string
  ): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.sectionWiseUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching section wise marks:', error)
      throw error
    }
  }

  /**
   * Get AFCAT paper results
   */
  static async getAfcatPaperMarks(userEmail: string): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.afcatUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching AFCAT marks:', error)
      throw error
    }
  }

  /**
   * Get company paper results
   */
  static async getCompanyPaperMarks(
    userEmail: string
  ): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.companyUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching company paper marks:', error)
      throw error
    }
  }

  /**
   * Get technical MCQ results
   */
  static async getTechnicalMCQMarks(
    userEmail: string
  ): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.technicalMcqUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching technical MCQ marks:', error)
      throw error
    }
  }

  /**
   * Get WP practice results
   */
  static async getWPPracticeMarks(userEmail: string): Promise<StudentMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.wpPracticeUrl}/${userEmail}/marks`,
        createAuthHeaders()
      )
      return processResults(response.data)
    } catch (error) {
      console.error('Error fetching WP practice marks:', error)
      throw error
    }
  }

  /**
   * Get section-wise breakdown for a specific answer
   */
  static async getSectionWiseBreakdown(
    userEmail: string,
    answerId: string
  ): Promise<SectionMarks[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.sectionWiseBreakdownUrl}/${userEmail}/section/${answerId}/marks`,
        createAuthHeaders()
      )

      const sectionMarks = response.data.map((marks: any) => ({
        ...marks,
        section_name: marks.section_name.replace(/[#*]+/g, ''),
        marks: `${marks.marks} / ${marks.total}`,
        percentage: (
          (parseInt(marks.marks, 10) / parseInt(marks.total, 10)) *
          100
        ).toFixed(2)
      }))

      return sectionMarks
    } catch (error) {
      console.error('Error fetching section wise breakdown:', error)
      throw error
    }
  }
}
