<div class="navBox">
    <div class="shape-1">
        <svg xmlns="http://www.w3.org/2000/svg" width="330.024" height="480.605" viewBox="0 0 330.024 480.605">
            <path id="Path_2" data-name="Path 2" d="M312-155S417.655,70.119,246,68,71.622,182.159,25,286,97-182,97-182Z"
                transform="translate(-14.143 182)" fill="#0b6fb1" />
        </svg>
    </div>
    <div class="logo-text">
        <a routerLink="/"><img src="../../../../assets/QM_Logo_No_Text.svg" alt="QLogo" />uantMasters</a>
    </div>
    <div class="nav-links">
        <a class="nav-link" routerLink="/achievers">Our Achievers</a>
        <div class="nav-link-spl">
            <p (click)="switchSubNav(1)">
                Resources
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                        d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                        transform="translate(-3.375 -3.375)" fill="#777" />
                </svg>
            </p>
            <div class="sub-nav--links--2">
                <a class="nav-link" href="https://placements.quantmasters.in">Placements</a>
                <a class="nav-link" routerLink="/certification/view" >Certification</a>
                <!-- <a class="nav-link" routerLink="/blog">Blog</a> -->
            </div>
        </div>
        <div class="nav-link-spl" *ngIf="username">
            <p (click)="switchSubNav(2)">
                Products
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                        d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                        transform="translate(-3.375 -3.375)" fill="#777" />
                </svg>
            </p>
            <div class="sub-nav--links--1">
                <a class="nav-link" *ngIf="username" routerLink="/dashboard">Dashboard</a>
                <a class="nav-link" *ngIf="username" routerLink="/technical/apps">Technical</a>
                <a class="nav-link" *ngIf="username" routerLink="/notes">Notes</a>
            </div>
        </div>
        <div class="nav-link-spl" *ngIf="username">
            <p (click)="switchSubNav(4)">
                Desktop App
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                        d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                        transform="translate(-3.375 -3.375)" fill="#777" />
                </svg>
            </p>
            <div class="sub-nav--links--4">
                <a class="nav-link" *ngIf="username" href="https://firebasestorage.googleapis.com/v0/b/portal-quant-masters.appspot.com/o/quant-masters-desktop.exe?alt=media&token=b2fac42c-0f23-49a3-abed-78a5015138e6" target="_blank">Download</a>
                <a class="nav-link" *ngIf="username" href="https://vamsikrishna-vk.medium.com/how-to-install-quant-masters-desktop-application-on-windows-e2affb43c09d" target="_blank">Install Guide</a>
            </div>
        </div>
        <!-- <a class="nav-link" routerLink="/faqs">FAQs</a> -->
        <!-- <a class="nav-link" routerLink="/plans">Plans</a> -->
        <div class="nav-link-spl">
            <p *ngIf="username" (click)="switchSubNav(3)">
                Hi, {{ username }}
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                        d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                        transform="translate(-3.375 -3.375)" fill="#777" />
                </svg>
            </p>
            <div *ngIf="username" class="sub-nav--links">
                <a class="nav-link" routerLink="/user/profile">Profile</a>
                <a class="nav-link" routerLink="/user/results">My Marks</a>
                <p class="nav-link" (click)="logout()">Logout</p>
            </div>
        </div>
        <a *ngIf="!username" class="nav-link nav-bold" routerLink="/user/login">Login</a>
        <a *ngIf="!username" class="nav-link nav-inv" routerLink="/user/register">Sign Up</a>
    </div>
    <div class="mob-nav" (click)="switchNav()">
        <span class="ham-bar"></span>
    </div>
    <div class="mob-links">
        <a class="mob-link" href="https://placements.quantmasters.in">Placements</a>
        <a class="mob-link" routerLink="/achievers">Our Achievers</a>
        <a class="mob-link" *ngIf="username" routerLink="/dashboard">Dashboard</a>
        <a class="mob-link" *ngIf="username" routerLink="/technical/apps">Technical</a>
        <a class="mob-link" *ngIf="username" routerLink="/notes">Notes</a>
        <!-- <a class="mob-link" routerLink="/plans">Plans</a> -->
        <div class="mob-link-spl">
            <p *ngIf="username" (click)="switchSubNav(3)">
                Hi, {{ username }}
                <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
                    <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                        d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                        transform="translate(-3.375 -3.375)" fill="#777" />
                </svg>
            </p>
            <div *ngIf="username" class="sub-nav--links">
                <a class="mob-link" routerLink="/user/profile">Profile</a>
                <a class="mob-link" routerLink="/user/results">My Marks</a>
            </div>
        </div>
        <!-- <p *ngIf="username">Hi, {{ username }}</p> -->
        <p *ngIf="username" class="mob-link mob-inv" (click)="logout()">Logout</p>
        <a *ngIf="!username" class="mob-link mob-bold" routerLink="/user/login">Login</a>
        <a *ngIf="!username" class="mob-link mob-inv" routerLink="/user/register">Sign Up</a>
    </div>
</div>