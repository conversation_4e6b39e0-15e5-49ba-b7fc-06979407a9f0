<div class="tests-cockpit">
    <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>

    <div class="alrt-box" *ngIf="showCopyAlert">
        <alert [type]="copyAlert.type" [dismissOnTimeout]="copyAlert.timeout" (onClosed)="onClosed()">{{ copyAlert.msg }}
        </alert>
    </div>

    <div class="center-controls" [@fadeIn]>
        <h5 [@hideGreetingMsg]="hideGreeting">{{ selectdGreeting }}</h5>
        <div class="main-ctrls">
            <div class="back-ctrl" [class.show-back]="showBackIcon" (click)="goBack()">
                <img src="../../../../assets/icons/sign-out.svg" alt="back-icon" />
            </div>
            <div class="main-ctrl" [id]="selectedAction[0] ? 'active' : ''" (click)="selectMainAction(0)">
                <!-- <div class="main-ctrl" [id]="selectedAction[0] ? 'active' : ''" (click)="selectMainAction(0)"> -->
                Creating a Paper
            </div>
            <div class="main-ctrl" [id]="selectedAction[1] ? 'active' : ''" (click)="selectMainAction(1)">
                Editing a Paper
            </div>
            <div class="main-ctrl" [id]="selectedAction[2] ? 'active' : ''">
                <!-- <div class="main-ctrl" [id]="selectedAction[2] ? 'active' : ''" (click)="selectMainAction(2)"> -->
                Creating Chapter Groups
            </div>
            <div class="main-ctrl" [id]="selectedAction[3] ? 'active' : ''">
                <!-- <div class="main-ctrl" [id]="selectedAction[3] ? 'active' : ''" (click)="selectMainAction(3)"> -->
                Creating Chapter Super Groups
            </div>
            <div class="create-ctrls" *ngIf="createPaperState || editPaperState">
                <div class="paper-select" (click)="openSelect(1)">
                    <span>{{ selectedPaperTypeMsg }}</span><span>▼</span>
                </div>
                <div class="opts" *ngIf="selectStates[0]">
                    <div class="select-opt" (click)="getTrialPapers()">Trial Papers</div>
                    <div class="select-opt" (click)="getWeeklyCompetitivePapers()">Weekly Competitive Papers</div>
                    <div class="select-opt" (click)="getCompanyPapers()">Company Paper</div>
                    <div class="select-opt" (click)="getTechnicalMCQResources()">Technical MCQs</div>
                    <div class="select-opt" (click)="getSectionWisePapers()">Section-Wise Papers</div>
                    <div class="select-opt" (click)="getVerbalNotesGroups(false)">Verbal Practice Papers</div>
                    <div class="select-opt" (click)="getTechnicalNotesGroups(false)">Technical Practice Papers</div>
										<div class="select-opt" (click)="getMockPapers()">Mock Papers</div>
										<div class="select-opt" (click)="getChapterSuperGroups()">Chapter-Wise Papers</div>
										<div class="select-opt" (click)="getChapterPracticeSuperGroups()">Chapter-Wise Practice Papers</div>
                </div>
            </div>
        </div>
    </div>
    <div class="main-content" *ngIf="showMainContent">

        <div class="paper-metadata" *ngIf="paperGroups || selectedPaperType == 8 || selectedPaperType == 13 || selectedPaperType == 14 || selectedPaperType == 1 || selectedPaperType == 6">
            <div class="mtcq-groups" *ngIf="selectedPaperType == 1">
                <div class="btn-group" dropdown>
                    <label for="topic">Super Group: </label>
                    <select name="topic" id="qmTMCQTopic" [(ngModel)]="selectedTopic" (change)="getChapterGroups()">
                        <option [ngValue]="''">Select</option>
                        <option [ngValue]="group.super_group_id" *ngFor="let group of chapterSuperGroups">{{ group.super_group_name }}
                        </option>
                    </select>
                </div>
                <div class="btn-group" dropdown>
                    <label for="subTopic">Group: </label>
                    <select name="subTopic" id="qmTMCQSubTopic" [(ngModel)]="selectedSubTopic"
                        (change)="getChapterPapers()">
                        <option [ngValue]="''">Select Sub Topic</option>
                        <option [ngValue]="subGroup.group_id" *ngFor="let subGroup of chapterGroups">{{
                            subGroup.group_name }}</option>
                    </select>
                </div>
            </div>

            <div class="mtcq-groups" *ngIf="selectedPaperType == 6">
                <div class="btn-group" dropdown>
                    <label for="topic">Super Group: </label>
                    <select name="topic" id="qmTMCQTopic" [(ngModel)]="selectedTopic" (change)="getChapterPracticeGroups()">
                        <option [ngValue]="''">Select</option>
                        <option [ngValue]="group.super_group_id" *ngFor="let group of chapterSuperGroups">{{ group.super_group_name }}
                        </option>
                    </select>
                </div>
                <div class="btn-group" dropdown>
                    <label for="subTopic">Group: </label>
                    <select name="subTopic" id="qmTMCQSubTopic" [(ngModel)]="selectedSubTopic"
                        (change)="getChapterPracticePapers()">
                        <option [ngValue]="''">Select Sub Topic</option>
                        <option [ngValue]="subGroup.group_id" *ngFor="let subGroup of chapterGroups">{{
                            subGroup.group_name }}</option>
                    </select>
                </div>
            </div>


            <div class="mtcq-groups" *ngIf="selectedPaperType == 11">
                <div class="btn-group" dropdown>
                    <label for="topic">Topic: </label>
                    <select name="topic" id="qmTMCQTopic" [(ngModel)]="selectedTopic" (change)="getTMCQSubGroups()">
                        <option [ngValue]="''">Select Topic</option>
                        <option [ngValue]="group.group_id" *ngFor="let group of tmcqGroups">{{ group.group_name }}
                        </option>
                    </select>
                </div>
                <div class="btn-group" dropdown>
                    <label for="subTopic">SubTopic: </label>
                    <select name="subTopic" id="qmTMCQSubTopic" [(ngModel)]="selectedSubTopic"
                        (change)="getTMCQPapers()">
                        <option [ngValue]="''">Select Sub Topic</option>
                        <option [ngValue]="subGroup.sub_group_id" *ngFor="let subGroup of tmcqSubGroups">{{
                            subGroup.sub_group_name }}</option>
                    </select>
                </div>
            </div>

            <div class="mtcq-groups" *ngIf="selectedPaperType == 13 || selectedPaperType == 14">
                <div class="btn-group" dropdown>
                    <label for="topic">Topic: </label>
                    <select name="topic" id="qmTMCQTopic" [(ngModel)]="selectedTopic" (change)="getPracticePapers()">
                        <option [ngValue]="''">Select Topic</option>
                        <option [ngValue]="group" *ngFor="let group of competitiveGroups">{{ group }}
                        </option>
                    </select>
                </div>
            </div>

            <div class="paper-ctrls">
                <button type="button" class="custom-btn" *ngIf="selectedPaperType == 11 || selectedPaperType == 13 || selectedPaperType == 14"
                    (click)="openCreatePaper()">Create Paper</button>
                <button type="button" class="custom-btn" *ngIf="selectedPaperType == 11 || selectedPaperType == 13 || selectedPaperType == 14"
                    (click)="openCreateGroup()">Create Group / Topic</button>
                <button type="button" class="custom-btn" *ngIf="selectedPaperType == 11"
                    (click)="openCreateSubGroup()">Create Sub Group</button>
                <button type="button" class="custom-btn btn-2" *ngIf="selectedPaperType == 8" (click)="refreshCache()">Refresh Cache</button>
                <button type="button" class="custom-btn btn-2" *ngIf="selectedPaperType == 13 || selectedPaperType == 14" (click)="copyListUrlForWordpress()">Copy List Url</button>
            </div>
        </div>

        <div *ngIf="papers && papers.length == 0">
            <p style="text-align: center;"><b>Looks like no papers have been uploaded yet!</b></p>
        </div>

        <div class="papers-list" *ngIf="papers && papers.length > 0" [@bringInPapers]="papers && papers.length">
            <div class="papers" *ngFor="let paper of displayRecords">
                <p>{{ paper.paper_name }}</p>
                <p>{{ paper.no_of_ques }} Questions</p>
                <p>{{ paper.time_lim }}</p>
                <div class="btn-group" btnRadioGroup [(ngModel)]="paper.status" *ngIf="selectedPaperType !== 13 && selectedPaperType !== 14 && selectedPaperType !== 4 && selectedPaperType !== 1 && selectedPaperType !== 6">
                    <label class="btn" [class.btn-secondary]="paper.status == 1" [class.btn-light]="paper.status == 0"
                        btnRadio="{{ 1 }}" tabindex="0" role="button">Shown</label>
                    <label class="btn" [class.btn-secondary]="paper.status == 0" [class.btn-light]="paper.status == 1"
                        btnRadio="{{ 0 }}" tabindex="0" role="button">Hidden</label>
                </div>
                <img src="../../../../assets/push-icons/diskette.png" alt="Save Paper" title="Save" *ngIf="selectedPaperType !== 13 && selectedPaperType !== 14 && selectedPaperType !== 4 && selectedPaperType !== 1 && selectedPaperType !== 6"
                    (click)="savePaperDetails(paper)" />
                <img src="../../../../assets/push-icons/edit-form.svg" alt="Edit paper" title="Edit"
                    (click)="takeToPaperDetails(paper)" />
                <img src="../../../../assets/push-icons/trash.svg" alt="Delete paper" title="Delete"
                    *ngIf="selectedPaperType !== 4 && selectedPaperType !== 1 && selectedPaperType !== 6" (click)="deletePaper(paper.paper_id)" />
            </div>
        </div>
        <pagination [boundaryLinks]="true" [totalItems]="allPapersLegth" class="job-list-pagination" [rotate]="true"
            [maxSize]="7" [itemsPerPage]="10" (pageChanged)="pageChanged($event)"></pagination>
    </div>

    <ng-template #createPaperTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-primary">Create new Paper!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form #paperCreateForm="ngForm" (ngSubmit)="createNewPaper(paperCreateForm.valid)">
                <div *ngIf="selectedPaperType == 11">
                    <p class="text-primary">Paper will be assinged under the Selected Topic and Selected Sub Topic.</p>
                    <p class="text-danger" *ngIf="selectedSubTopic == ''">Please select a Topic and Sub Topic first.</p>
                </div>
                <div class="form-group">
                    <label for="paperName">Paper Name: </label>
                    <div class="form-elem">
                        <input type="text" name="paperName" [(ngModel)]="newPaper.paper_name" #paperName="ngModel"
                            [class.is-inv]="paperName.invalid && paperName.touched || (paperName.errors?.required && paperCreateForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="paperName.errors?.required && paperName.touched || (paperName.pristine && paperCreateForm.submitted)">
                            Paper Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="noOfQues">Number of Questions: </label>
                    <div class="form-elem">
                        <input type="number" name="noOfQues" [(ngModel)]="newPaper.no_of_ques" #noOfQues="ngModel"
                            [class.is-inv]="noOfQues.invalid && noOfQues.touched || (noOfQues.errors?.required && paperCreateForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="noOfQues.errors?.required && noOfQues.touched || (noOfQues.pristine && paperCreateForm.submitted)">
                            Number of Questions is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <button class="custom-btn search-btn" type="submit">Create</button>
                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #createGroup>
        <div class="modal-header">
            <h4 class="modal-title text-primary">Create new Group / Topic</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form #groupCreateForm="ngForm" (ngSubmit)="createNewGroup(groupCreateForm.valid)">
                <div></div>
                <div class="form-group">
                    <label for="groupName">Group / Topic Name: </label>
                    <div class="form-elem">
                        <input type="text" name="groupName" [(ngModel)]="newTmcqGroup" #groupName="ngModel"
                            [class.is-inv]="groupName.invalid && groupName.touched || (groupName.errors?.required && groupCreateForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="groupName.errors?.required && groupName.touched || (groupName.pristine && groupCreateForm.submitted)">
                            Group / Topic Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <button class="custom-btn search-btn" type="submit">Create</button>
                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #createSubGroup>
        <div class="modal-header">
            <h4 class="modal-title text-primary">Create new SubGroup!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <form #subGroupCreateForm="ngForm" (ngSubmit)="createNewSubGroup(subGroupCreateForm.valid)">
                <div></div>
                <div class="form-group">
                    <label for="Group">Group: </label>
                    <div class="form-elem">
                        <select name="Group" id="qmTMCQGroup" [(ngModel)]="newTmcqGroupId" #Group="ngModel"
                            [class.is-inv]="Group.invalid && Group.touched || (Group.errors?.required && subGroupCreateForm.submitted)"
                            required>
                            <option [ngValue]="''">Select Group</option>
                            <option [ngValue]="group.group_id" *ngFor="let group of tmcqGroups">{{ group.group_name }}
                            </option>
                        </select>
                        <small class="form-error--text"
                            *ngIf="Group.errors?.required && Group.touched || (Group.pristine && subGroupCreateForm.submitted)">
                            SubGroup Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <label for="subGroupName">SubGroup Name: </label>
                    <div class="form-elem">
                        <input type="text" name="subGroupName" [(ngModel)]="newTmcqSubGroup" #subGroupName="ngModel"
                            [class.is-inv]="subGroupName.invalid && subGroupName.touched || (subGroupName.errors?.required && subGroupCreateForm.submitted)"
                            required />
                        <small class="form-error--text"
                            *ngIf="subGroupName.errors?.required && subGroupName.touched || (subGroupName.pristine && subGroupCreateForm.submitted)">
                            SubGroup Name is Required
                        </small>
                    </div>
                </div>
                <div class="form-group">
                    <button class="custom-btn search-btn" type="submit">Create</button>
                </div>
            </form>
        </div>
    </ng-template>

    <ng-template #successTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-success">Success!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p>{{ successMsg }}</p>
        </div>
    </ng-template>

    <ng-template #errorTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-danger">Error!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p>Oops, Somthing went wrong, please try again!</p>
        </div>
    </ng-template>

    <ng-template #refreshTemplate>
        <div class="modal-header">
            <h4 class="modal-title text-success">Success!</h4>
            <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body">
            <p>The cache has been refreshed.</p>
        </div>
    </ng-template>
</div>
