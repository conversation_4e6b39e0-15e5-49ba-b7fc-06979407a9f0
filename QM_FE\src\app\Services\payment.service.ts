import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PaymentService {

  private JwtToken: string;
  private sUrl         = 'https://api.quantmasters.in/product/order';
  private confirmUrl   = 'https://api.quantmasters.in/product/payment/confirm';
  private chechUrl     = 'https://api.quantmasters.in/product/payment/';
  private amountUrl    = 'https://api.quantmasters.in/product/course/quote';
  private wkAmountUrl  = 'https://api.quantmasters.in/product/workshop/quote';
  private plAmountUrl  = 'https://api.quantmasters.in/product/placment/quote';
  private inAmountUrl  = 'https://api.quantmasters.in/product/internship/quote';
  private userUrl      = 'https://api.quantmasters.in/user/';
  private mailUrl      = 'https://api.quantmasters.in/product/placment/payment/contact';
  private contactUrl   = 'https://api.quantmasters.in/product/contact';

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getCourseQuote(): Observable<string> {
    return this.http.get<string>(this.amountUrl);
  }

  getWorkshopQuote(): Observable<string> {
    return this.http.get<string>(this.wkAmountUrl);
  }

  getWorkshopQuote2(): Observable<string> {
    return this.http.get<string>(this.plAmountUrl);
  }

  getInternshipQuote(): Observable<string> {
    return this.http.get<string>(this.inAmountUrl);
  }

  getCampaignContact(): Observable<string> {
    return this.http.get<string>(this.contactUrl);
  }

  getOrderId(amount: number): Observable<string> {
    const data = {
      amount: amount
    };

    this.setSecurityToken();

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.sUrl, data, httpOpts);
  }

  confirmPayment(paymentId: string, amount: string): Observable<string> {

    this.setSecurityToken();

    const data = {
      paymentId: paymentId,
      amount: amount
    };

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(this.confirmUrl, data, httpOpts);
  }

  sendMailPostPayment(email: string, f_name: string, l_name: string, choice: number): Observable<string> {

    const body = {
      email,
      f_name,
      l_name
    };

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };

    return this.http.post<string>(this.mailUrl + '/' + choice, body, httpOpts);
  }

  sendMailPostPaymentInRecorded(email: string, f_name: string, l_name: string, choice: number): Observable<string> {

    const body = {
      email,
      f_name,
      l_name
    };

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json'
      })
    };

    return this.http.post<string>('https://api.quantmasters.in/product/placement/payment/contact/workshop/' + choice, body, httpOpts);
  }

  verifyUser(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.chechUrl + email + '/validate';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  getUserAccess(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.userUrl + email + '/check/access';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  grantUserAccess(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.userUrl + email + '/grant/access';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, httpOpts);
  }

  grantUserAccessPremium(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.userUrl + email + '/grant/access/premium';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, httpOpts);
  }

  grantBulkUserAccess(emails: string[]): Observable<string> {

    this.setSecurityToken();

    const url = `${this.userUrl}grant/access/2`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const body = {
      emails
    };

    return this.http.post<string>(url, body, httpOpts);
  }

  revokeUserAccess(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.userUrl + email + '/revoke/access';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, httpOpts);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
