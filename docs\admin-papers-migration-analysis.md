# Admin Papers Migration Analysis

## Overview
This document analyzes the migration of the admin tests/papers functionality from the Angular app to Next.js, focusing on feature parity and API consistency.

## Paper Types Comparison

### Fully Implemented (✅)
1. **Chapter-Wise Papers (Type 1)**
   - Super Group → Group selectors working correctly
   - No status toggle (as expected)
   - Cannot create/delete (as expected)

2. **Mock Papers (Type 4)**
   - Direct paper loading without dropdowns
   - Read-only operations

3. **Trial/Company Papers (Type 5)**
   - Status toggle functionality
   - Delete capability
   - Direct loading

4. **Technical MCQs (Type 11)**
   - Topic → SubTopic selectors
   - Create paper functionality
   - Delete capability
   - Status toggle

### Partially Implemented (⚠️)
1. **Weekly Competitive (Type 8)**
   - Missing: Refresh cache button
   - Status toggle works

2. **Verbal/Technical Practice (Types 13/14)**
   - Missing: Copy List URL for WordPress integration
   - Create and delete work correctly

## Critical API Inconsistencies

### Angular API Patterns (Legacy)
```
Technical MCQs:
- GET /v3/admin/tmcq/papers/{id}
- PUT /v3/admin/tmcq/paper
- DELETE /v3/admin/tmcq/paper/{id}

Trial/Company:
- GET /test/sample/paper/new/{id} (questions)
- PUT /v2/admin/test/open/paper/{id}
- DELETE /admin/paper/open/update/{id} (v1!)

Section-Wise:
- GET /v2/test/sectionWise/papers
- PUT /v2/admin/test/sectionWise/paper/{id}
```

### Next.js API Patterns (Normalized)
```
Technical MCQs:
- GET /v2/test/tmcq/papers/{id}
- PUT /v2/admin/test/tmcq/paper

Trial/Company:
- GET /v2/test/open/papers
- PUT /v2/admin/test/open/paper/{id}
```

## Missing Features

### 1. Question Management
The Angular app has full CRUD for questions:
- Add new questions
- Edit existing questions
- Delete questions
- Upload images for explanations
- Question validation

Next.js only has a placeholder UI.

### 2. Special Actions
- **Refresh Cache** button for Weekly Competitive papers
- **Copy List URL** for WordPress integration (Types 13/14)
- **Create Group/SubGroup** modals for Technical MCQs

### 3. Paper Creation
Angular supports creating:
- New papers (Types 11, 13, 14)
- New groups/topics
- New subgroups (Type 11 only)

Next.js only supports creating papers.

## Implementation Priority

### High Priority
1. Complete question management functionality
2. Fix API endpoint inconsistencies
3. Add missing buttons (refresh cache, copy URL)

### Medium Priority
1. Create group/subgroup functionality
2. Image upload for questions
3. Question validation logic

### Low Priority
1. UI/UX improvements
2. Performance optimizations
3. Additional paper types

## API Endpoint Recommendations

Standardize all endpoints to v2:
```
GET /v2/test/{service}/papers/{id?}
POST /v2/admin/test/{service}/paper
PUT /v2/admin/test/{service}/paper/{id}
DELETE /v2/admin/test/{service}/paper/{id}

GET /v2/test/{service}/paper/{id}/questions
POST /v2/admin/test/{service}/paper/{id}/question
PUT /v2/admin/test/{service}/paper/{id}/question/{qno}
DELETE /v2/admin/test/{service}/paper/{id}/question/{qno}
```

Where `{service}` = tmcq, open, competitive, sectionwise, chapter, model 