import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { CompetitiveGroup } from 'src/app/Models/Dashboard/Competitive/CompetitiveGroup';
import { CompetitivePapers } from 'src/app/Models/Dashboard/Competitive/CompetitivePapers';
import { CompetitiveService } from 'src/app/Services/Dashboard/competitive.service';

declare let gtag: Function;

@Component({
  selector: 'app-wppractice-papers',
  templateUrl: './wppractice-papers.component.html',
  styleUrls: ['./wppractice-papers.component.scss']
})
export class WPPracticePapersComponent implements OnInit {

  public groupId: string;
  public group: CompetitiveGroup;
  public paperType: string;
  public papers: CompetitivePapers[];

  public showIndicator = false;

  constructor(private router: Router,
              private route: ActivatedRoute,
              private competitiveService: CompetitiveService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;
    this.route.paramMap.subscribe(params => {
      that.groupId = params.get('groupId');

      if (that.groupId.includes('VP')) {
        that.paperType = '13';
      } else {
        that.paperType = '14';
      }

      that.getPapers();
      that.getGroupDetails();
    });
  }

  getPapers() {

    this.showIndicator = true;
    this.competitiveService.getPapersOfAGroup(this.groupId).subscribe(response => {
      this.papers = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getGroupDetails() {

    this.showIndicator = true;
    this.competitiveService.getDetailsOfAGroup(this.groupId).subscribe(response => {
      this.group = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    
    this.router.navigate(['/dashboard/test/', this.paperType, paperId, paperName, paperLim]);
  }
}
