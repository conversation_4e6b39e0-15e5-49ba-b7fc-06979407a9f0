<app-nav></app-nav>
<app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
<div class="notes-wrap">
  <div class="alrt-box" *ngIf="showCopyAlert">
    <alert [type]="copyAlert.type" [dismissOnTimeout]="copyAlert.timeout" (onClosed)="onClosed()">{{ copyAlert.msg }}
    </alert>
  </div>
  <h4>{{ noteToRead.notes_name }}</h4>
  <div class="ctrls-area">
    <p class="posted-on"><b>Posted On:</b> {{ noteToRead.posted_on }}</p>
    <div class="social-share--area">
      <p><b>Share:</b></p>
      <div class="social-share--icon" (click)="shareOnFacebook()"><img src="../../../assets/icons/facebook.svg" /></div>
      <div class="social-share--icon" (click)="shareOnTwitter()"><img src="../../../assets/icons/twitter.svg " /></div>
      <div class="social-share--icon" (click)="shareOnLinkedIn()"><img src="../../../assets/icons/linkedin-dark.svg" /></div>
      <div class="social-share--icon" (click)="shareOnWhatsApp()"><img src="../../../assets/icons/whatsapp.svg" /></div>
      <div class="social-share--icon" (click)="shareDirectLink()"><img src="../../../assets/icons/share-button.svg" />
      </div>
    </div>
  </div>
  <div class="reading-area">
    <markdown [data]="noteToRead.notes_content"></markdown>
  </div>
  <div class="embed-area" *ngIf="embedVideoUrl != ''">
    <iframe width="660" height="415" [src]="embedVideoUrl" frameborder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen></iframe>
  </div>
  <div class="player-legend">
    <div class="user-comment-section">
      <h4>{{ notesCommentList.length }} Comments</h4>
      <div class="user-new-comment">
        <div class="image-div">
          <img class="user-image" src="{{userImage}}" />
        </div>
        <div class="comment-div">
          <textarea id="new-comment-area" ng-trim="false" [(ngModel)]="myComment" class="user-comment"
            placeholder="Add your comments.."></textarea>
          <div class="buttons-div">
            <button title="Clear" (click)="fnOnClickClearComment()">Clear</button>
            <button title="Confrim" (click)="fnOnClickConfrimComment(myComment)">Comment</button>
          </div>
        </div>
      </div>
    </div>

    <div class="video-comments" *ngFor="let cmt of notesCommentList; let i = index">
      <div class="image-div">
        <img class="user-image" src="{{cmt.user_avatar}}" />
      </div>
      <div class="comment-text-div">
        <p class="comment-heder"><u>{{cmt.created_by}} - <span>{{cmt.created_at}}</span></u></p>
        <span class="comments">{{cmt.text}}</span>
        <div class="comment-reply-section">
          <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseVideoList">REPLY</button>
          <button (click)="isCollapsedReplies[i] = !isCollapsedReplies[i]" [class.is-open]="!isCollapsedReplies[i]"
            [attr.aria-expanded]="!isCollapsedReplies" aria-controls="collapseVideoList">View {{cmt.replies.length}}
            Reply</button>
          <!-- <p>View {{cmt.replies.length}} Reply </p> -->
        </div>
        <div class="user-comment-section" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div class="comment-div">
            <textarea [(ngModel)]="commentReply[i]" class="user-comment user-comment--reply"
              placeholder="Add your comments.."></textarea>
            <div class="buttons-div">
              <button title="Clear" (click)="fnOnClickClearReply(i)">Cancel</button>
              <button title="Confrim"
                (click)="fnOnClickConfrimCommentReply(cmt.comment_id,commentReply[i], i)">Reply</button>
            </div>
          </div>
        </div>
        <div [collapse]="isCollapsedReplies[i]" [isAnimated]="true">
          <div class="reply_section_ExtraCSS" *ngFor="let replies of notesCommentList[i].replies; let j = index">
            <div class="image-div">
              <img class="user-image" src="{{replies.user_avatar}}" />
            </div>
            <div class="comment-text-div">
              <p class="comment-heder"><u>{{replies.created_by}} - {{replies.created_at}}</u></p>
              <span class="comments">{{replies.text}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="nav-area">
    <!-- <div class="nav-btn" *ngIf="prevNoteId != ''" (click)="takeToPreviousNote()" title="Goto Previous Note: {{ prevNoteName }}">
      <img src="../../../assets/icons/left-arrow.svg" alt="Goto Previous Note" />
    </div> -->
    <div class="nav-btn" title="Back to Notes List" (click)="takeToNotesHome()">
      <img src="../../../assets/icons/back-arrow.svg" alt="Goto Previous Page" />
    </div>
    <!-- <div class="nav-btn" *ngIf="nextNoteId != ''" (click)="takeToNextNote()"  title="Goto Next Note: {{ nextNoteName }}">
      <img src="../../../assets/icons/right-arrow.svg" alt="Goto Next Note" />
    </div> -->
  </div>
</div>

<ng-template #messageTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left text-info">{{messageHeader}}</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="messageModalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>{{message}}</p>
  </div>
</ng-template>
<app-footer></app-footer>