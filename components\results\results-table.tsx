'use client'

import React, { useState } from 'react'
import {
  StudentMarks,
  SectionMarks,
  PaginationConfig
} from '@/types/results-types'
import { Button } from '@/components/ui/button'
import SectionModal from './section-modal'

interface ResultsTableProps {
  displayMarks: StudentMarks[]
  numDispMarks: number
  currentPage: number
  itemsPerPage: number
  onPageChange: (page: number) => void
  onShowSectionMarks: (
    answerId: string,
    paperName: string,
    answerDate: string
  ) => void
  sectionMarks: SectionMarks[]
  paperName: string
  answerDate: string
  paginatorConfig: PaginationConfig
}

export default function ResultsTable({
  displayMarks,
  numDispMarks,
  currentPage,
  itemsPerPage,
  onPageChange,
  onShowSectionMarks,
  sectionMarks,
  paperName,
  answerDate,
  paginatorConfig
}: ResultsTableProps) {
  const [showModal, setShowModal] = useState(false)

  const totalPages = Math.ceil(numDispMarks / itemsPerPage)

  const handleSectionMarksClick = (
    answerId: string,
    paperName: string,
    answerDate: string
  ) => {
    onShowSectionMarks(answerId, paperName, answerDate)
    setShowModal(true)
  }

  const generatePageNumbers = () => {
    const pages = []
    const maxVisible = paginatorConfig.max
    const showBoundary = paginatorConfig.boundaryLinks

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      const half = Math.floor(maxVisible / 2)
      let start = Math.max(1, currentPage - half)
      const end = Math.min(totalPages, start + maxVisible - 1)

      if (end - start + 1 < maxVisible) {
        start = Math.max(1, end - maxVisible + 1)
      }

      if (showBoundary && start > 1) {
        pages.push(1)
        if (start > 2) pages.push('...')
      }

      for (let i = start; i <= end; i++) {
        pages.push(i)
      }

      if (showBoundary && end < totalPages) {
        if (end < totalPages - 1) pages.push('...')
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (numDispMarks === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">
          No results found. Please select a test type to view your marks.
        </p>
      </div>
    )
  }

  return (
    <div className="data-table">
      {/* Table Headers */}
      <div className="headers grid grid-cols-4 lg:grid-cols-4 gap-2 p-4 bg-gray-600 text-white font-semibold rounded-t-lg">
        <h6 className="text-sm lg:text-base">Paper Name</h6>
        <h6 className="text-sm lg:text-base">Paper Type</h6>
        <h6 className="text-sm lg:text-base">Paper Marks</h6>
        <h6 className="text-sm lg:text-base">Answered On</h6>
      </div>

      {/* Table Data */}
      <div className="table-body">
        {displayMarks.map((result, index) => (
          <div
            key={`${result.paper_id}-${index}`}
            className={`
              table-data grid grid-cols-4 lg:grid-cols-4 gap-2 p-4 border-b border-gray-200
              ${index % 2 === 0 ? 'bg-blue-50' : 'bg-white'}
            `}
          >
            <p
              className="text-sm lg:text-base text-gray-800 truncate"
              title={result.paper_name}
            >
              {result.paper_name}
            </p>
            <p
              className="text-sm lg:text-base text-gray-600 truncate"
              title={result.paper_type}
            >
              {result.paper_type}
            </p>
            <p className="text-sm lg:text-base text-gray-800 font-medium">
              {result.marks}/{result.total_marks}
            </p>
            <p
              className="text-sm lg:text-base text-gray-600 truncate"
              title={result.display_date}
            >
              {result.display_date}
            </p>
            <div className="hidden lg:block">
              <Button
                type="button"
                size="sm"
                onClick={() =>
                  handleSectionMarksClick(
                    result.answer_id || '',
                    result.paper_name,
                    result.display_date || ''
                  )
                }
                className={`
                  text-xs bg-gray-500 hover:bg-gray-600 text-white
                  ${result.sw_exists ? 'visible' : 'invisible'}
                `}
              >
                Section-wise Breakdown
              </Button>
            </div>

            {/* Mobile action button */}
            <div className="lg:hidden col-span-4 mt-2">
              {result.sw_exists && (
                <Button
                  type="button"
                  size="sm"
                  onClick={() =>
                    handleSectionMarksClick(
                      result.answer_id || '',
                      result.paper_name,
                      result.display_date || ''
                    )
                  }
                  className="text-xs bg-gray-500 hover:bg-gray-600 text-white w-full"
                >
                  Section-wise Breakdown
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="pagination flex justify-center items-center gap-2 mt-6 flex-wrap">
          <Button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            variant="outline"
            size="sm"
            className="text-sm"
          >
            Previous
          </Button>

          {generatePageNumbers().map((page, index) => (
            <React.Fragment key={index}>
              {page === '...' ? (
                <span className="px-2 text-gray-500">...</span>
              ) : (
                <Button
                  onClick={() => onPageChange(page as number)}
                  variant={currentPage === page ? 'default' : 'outline'}
                  size="sm"
                  className={`
                    text-sm min-w-[40px]
                    ${
                      currentPage === page
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : 'hover:bg-gray-100'
                    }
                  `}
                >
                  {page}
                </Button>
              )}
            </React.Fragment>
          ))}

          <Button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            variant="outline"
            size="sm"
            className="text-sm"
          >
            Next
          </Button>
        </div>
      )}

      {/* Section Modal */}
      <SectionModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        sectionMarks={sectionMarks}
        paperName={paperName}
        answerDate={answerDate}
      />
    </div>
  )
}
