import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DashboardComponent } from './dashboard/dashboard.component';
import { VideoListComponent } from './Videos/video-list/video-list.component';
import { VideoStreamComponent } from './Videos/video-stream/video-stream.component';
import { StudentReviewComponent } from './Videos/student-review/student-review.component';
import { ChapterPaperComponent } from './ChapterPapers/chapter-paper.component';
import { ModelPaperComponent } from './ModelPapers/model-paper.component';
import { CompetitivePaperComponent } from './CompetitivePapers/competitive-paper.component';
import { CompanyPaperComponent } from './CompanyPapers/company-paper.component';
import { TestsComponent } from './tests/tests.component';
import { StudentTestimonialComponent } from './Videos/student-testimonial/student-testimonial.component';
import { FreeVideoComponent } from './Videos/free-video/free-video.component';
import { FreePaperComponent } from './FreePapers/free-paper.component';
import { WorkshopVideosComponent } from './Videos/workshop-videos/workshop-videos.component';
import { ChapterPracticePapersComponent } from './ChapterPracticePapers/chapter-practice-papers.component';
import { OpenCompetitivePaperComponent } from './OpenCompetitivePapers/open-competitive-paper.component';
import { SectionWisePapersComponent } from './SectionWisePapers/section-wise-papers.component';
import { AfcatPapersComponent } from './AfcatPapers/afcat-papers.component';
import { CompanyTestsListComponent } from './OpenCompanyPapers/company-tests-list/company-tests-list.component';
import { CompanyPapersComponent } from './OpenCompanyPapers/company-papers/company-papers.component';
import { PaperListComponent } from './TechMCQ/paper-list/paper-list.component';
import { TechNotesComponent } from './tech-notes/tech-notes.component';
import { WPPracticePapersComponent } from './WPPracticePapers/papers-list/wppractice-papers.component';

const routes: Routes = [{
  path: '',
  component: DashboardComponent,
  children: [
    // { path: 'videos', component: VideoListComponent },
    { path: 'student-exp', component: StudentReviewComponent },
    { path: 'student-testimonials', component: StudentTestimonialComponent },
    { path: 'trial-videos', component: FreeVideoComponent },
    { path: 'workshops', component: WorkshopVideosComponent },
    { path: 'videos/stream/:video_id/:stream_type/:rating', component: VideoStreamComponent },
    { path: 'student-testimonials/stream/:video_id/:stream_type', component: VideoStreamComponent },
    { path: 'student-exp/stream/:video_id/:stream_type', component: VideoStreamComponent },
    { path: 'trial-videos/stream/:video_id/:stream_type', component: VideoStreamComponent },
    { path: 'workshops/stream/:video_id/:stream_type', component: VideoStreamComponent },
    { path: 'chapter-papers', component: ChapterPaperComponent },
    { path: 'chapter-practice-papers', component: ChapterPracticePapersComponent },
    { path: 'model-papers', component: ModelPaperComponent },
    { path: 'competitive-papers', component: CompetitivePaperComponent },
    // { path: 'company-papers', component: CompanyPaperComponent },
    { path: 'trial-papers', component: FreePaperComponent },
    { path: 'open-competitive-papers', component: OpenCompetitivePaperComponent },
    { path: 'section-wise-papers', component: SectionWisePapersComponent },
    { path: 'afcat-papers', component: AfcatPapersComponent },
    { path: 'test/:test_type/:paper_id/:paper_name/:paper_lim', component: TestsComponent },
    { path: 'company-papers', component: CompanyTestsListComponent },
    { path: 'company-papers/list/:paper_type', component: CompanyPapersComponent },
    { path: 'technical-mcq-papers', component: PaperListComponent },
    { path: 'technical-notes', component: TechNotesComponent },
    { path: 'practice/:groupId/papers', component: WPPracticePapersComponent }
  ]
}];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class StudentDashboardRoutingModule { }
