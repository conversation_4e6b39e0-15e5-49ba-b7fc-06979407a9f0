// Add this to your video-types.ts file
declare global {
  interface Window {
    gtag: (
      command: string,
      target: string,
      config?: Record<string, any>
    ) => void
  }
}

export interface StudentVideoListType {
  video_id: string
  video_name: string
  description: string
  thumbnail: string
  length: string
  display_order: number
  video_type: string // 1-6 based on the Angular implementation
  link: string
  rating?: string // For displaying video rating
}

export interface SelectedVideoType {
  id: string
  type: string // Video type (1-6)
  name?: string // Added to support video name in modal
}

// Comment types based on the Angular implementation
export interface Comment {
  comment_id: string
  video_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user_avatar: string
  replies: CommentReply[]
}

export interface CommentReply {
  reply_id: string
  comment_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user_avatar: string
}

// Request payload types
export interface CommentPayload {
  text: string
  email: string
}

export interface ReplyPayload {
  text: string
  email: string
}

// Video tracking data
export interface VideoTrackingData {
  email: string
  videoId: string
  totalLength: number
  lastWatch: number
}

export interface VideoGroupType {
  group_id: string
  group_name: string
  group_description: string
  image: string
  display_order: number
}
export interface VideoSubGroupType {
  sub_group_id: string
  parent_group_id: string
  sub_group_name: string
  sub_group_description: string
  display_order: number
}

export interface VideoListType {
  video_id: string
  parent_sub_group_id: string
  video_name: string
  description: string
  thumbnail: string
  length: string
  display_order: number
  video_type: string
  link: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}
