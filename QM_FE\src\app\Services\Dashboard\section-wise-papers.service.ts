import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiAuthService } from '../api-auth.service';

@Injectable({
  providedIn: 'root'
})
export class SectionWisePapersService {

  private papersUrl    = 'https://api.quantmasters.in/v2/test/sectionWise';
  private papersAdmUrl = 'https://api.quantmasters.in/v2/admin/test/sectionWise';

  private afcatUrl = 'https://api.quantmasters.in/v2/test/afcat';

  private JwtToken: string;

  constructor(private http: HttpClient,
              private apiAuthService: ApiAuthService) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getPapers(): Observable<string> {

    this.setSecurityToken();

    const url = this.papersUrl + `/papers`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const url = this.papersUrl + `/paper/${paper_id}`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getSectionWiseMarks(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.papersUrl + `/${email}/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  submitAnswers(email: string, paper_id: string, marks: number): Observable<string> {

    this.setSecurityToken();

    const url = this.papersUrl + `/paper/submit`;

    const data = {
      email: email,
      paper_id: paper_id,
      marks: marks
    };

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, data, httpOps);
  }

  submitSectionAnswers(email: string, paper_id: string, answer_id: string, data: any): Observable<string> {

    const url = this.papersUrl + '/paper/' + paper_id + '/submit/' + answer_id + '/' + email + '/sections';

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.post<string>(url, data, httpOps);
  }

  addExplanationText(paperId: string, quesNo: string, explanation: string): Observable<string> {

    const url = this.papersAdmUrl + '/paper/' + paperId + '/explanation';

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type' : 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const reqBody = {
      question_no: quesNo,
      explanation
    };

    return this.http.post<string>(url, reqBody, httpOps);
  }

  updateSectionWisePaper(paperId: string, paper: object): Observable<string> {

    this.setSecurityToken();

    const url = this.papersAdmUrl + '/paper/' + paperId;

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    const protectedBody = this.apiAuthService.generateAuthedBody(
      'PUT',
      '/v2/admin/test/sectionWise/paper/' + paperId,
      paper
    );

    return this.http.put<string>(url, protectedBody, httpOps);
  }

  updateSectionWiseQuestion(paperId: string, question: object): Observable<string> {

    this.setSecurityToken();

    const url = this.papersAdmUrl + '/paper/' + paperId + '/question';

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + this.JwtToken,
      }),
    };

    const protectedBody = this.apiAuthService.generateAuthedBody(
      'PUT',
      '/v2/admin/test/sectionWise/paper/' + paperId + '/question',
      question
    );

    return this.http.put<string>(url, protectedBody, httpOps);
  }

  getAfcatPapers(): Observable<string> {

    this.setSecurityToken();

    const url = this.afcatUrl + `/papers`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getAfcatQuestions(paper_id: string): Observable<string> {

    this.setSecurityToken();

    const url = this.afcatUrl + `/paper/${paper_id}`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getAfcatMarks(email: string): Observable<string> {

    this.setSecurityToken();

    const url = this.afcatUrl + `/${email}/marks`;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
