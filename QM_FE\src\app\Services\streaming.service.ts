import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { VideoList } from '../Models/Streaming/VideoList';
import { StudentVideoList } from '../Models/Streaming/StudentVideoList';
import { VideoGroup } from '../Models/Streaming/VideoGroup';
import { VideoSubGroup } from '../Models/Streaming/VideoSubGroup';

@Injectable({
  providedIn: 'root'
})
export class StreamingService {

  private preLoginUrl      = 'https://api.quantmasters.in/prelogin/videos/all';
  private videoGroupUrl    = 'https://api.quantmasters.in/learn/videos/groups';
  private videoSubGroupUrl = 'https://api.quantmasters.in/learn/videos/subgroups';
  private videoListUrl     = 'https://api.quantmasters.in/learn/videos/all';
  private videoLinkUrl     = 'https://api.quantmasters.in/learn/videos/';

  private ratingAndCommentsUrl = 'https://api.quantmasters.in/learn/video/';

  private studentVideoListUrl = 'https://api.quantmasters.in/testimony/videos/all';
  private studentVideoLinkUrl = 'https://api.quantmasters.in/testimony/videos/';
  private reviewVideoListUrl  = 'https://api.quantmasters.in/review/videos/all';
  private reviewVideoLinkUrl  = 'https://api.quantmasters.in/review/videos/';

  private trialVideoListUrl = 'https://api.quantmasters.in/learn/sample/videos/all';
  private trialVideoLinkUrl = 'https://api.quantmasters.in/learn/sample/videos/';

  private workshopVideoListUrl = 'https://api.quantmasters.in/learn/workshop/videos/all';
  private workshopVideoLinkUrl = 'https://api.quantmasters.in/learn/workshop/videos/';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getPreLoginVideoList(): Observable<VideoList[]> {
    return this.http.get<VideoList[]>(this.preLoginUrl);
  }

  getVideoGroups(): Observable<VideoGroup[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<VideoGroup[]>(this.videoGroupUrl, httpOps);
  }

  getVideoSubGroups(): Observable<VideoSubGroup[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<VideoSubGroup[]>(this.videoSubGroupUrl, httpOps);
  }

  getVideoList(): Observable<VideoList[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<VideoList[]>(this.videoListUrl, httpOps);
  }

  getVideosOfASubGroup(subGroupId: string): Observable<VideoList[]> {
    this.setSecurityToken();

    const url = this.videoSubGroupUrl + '/' + subGroupId + '/list';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<VideoList[]>(url, httpOps);
  }

  getVideoLink(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.videoLinkUrl + videoId  + '/link';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getTestVideoLink(): Observable<HttpResponse<string>> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = 'https://api.quantmasters.in/learn/videos/link';

    return this.http.get<string>(url, { headers, observe: 'response' as 'response', withCredentials: true });
  }

  getTestVideoSign(): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = 'https://api.quantmasters.in/learn/videos/signature';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getVideoNotes(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.videoLinkUrl + videoId  + '/dl-notes';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getVideoCommentsList(videoId: string): Observable<string> {
    this.setSecurityToken();

    const url = this.ratingAndCommentsUrl + videoId + '/comments';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOps);
  }

  getTestimonyVideoList(): Observable<StudentVideoList[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<StudentVideoList[]>(this.studentVideoListUrl, httpOps);
  }

  getTestimonyVideoLink(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.studentVideoLinkUrl + videoId  + '/link';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getReviewVideoList(): Observable<StudentVideoList[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<StudentVideoList[]>(this.reviewVideoListUrl, httpOps);
  }

  getReviewVideoLink(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.reviewVideoLinkUrl + videoId  + '/link';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getTrialVideoList(): Observable<StudentVideoList[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<StudentVideoList[]>(this.trialVideoListUrl, httpOps);
  }

  getTrialVideoLink(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.trialVideoLinkUrl + videoId  + '/link';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  getWorkshopVideoList(): Observable<StudentVideoList[]> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<StudentVideoList[]>(this.workshopVideoListUrl, httpOps);
  }

  getWorkshopVideoLink(videoId: string): Observable<string> {
    this.setSecurityToken();

    const headers = new HttpHeaders({
      'Authorization': 'Bearer ' + this.JwtToken
    });

    const url = this.workshopVideoLinkUrl + videoId  + '/link';

    return this.http.get(url, { headers, responseType: 'text' });
  }

  postVideoRating(email: string, videoId: string, rating_given: number): Observable<string> {

    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const body = {
      email,
      rating_given
    };

    const url = this.ratingAndCommentsUrl + videoId  + '/rating';

    return this.http.post<string>(url, body, httpOps);
  }

  getStudentVideoRating(videoId: string, email: string): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const url = this.ratingAndCommentsUrl + videoId  + '/rating/' + email + '/check';

    return this.http.get<string>(url, httpOps);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }

  fnPostVideoComment(videoId: string, payload: object): Observable<string> {
    this.setSecurityToken();
    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };
    const body = payload;
    const url = this.ratingAndCommentsUrl + videoId + '/comment';
    return this.http.post<string>(url, body, httpOps);
  }

  fnPostCommentReply(videoId: string, commentId: string, payload: object): Observable<string> {
    this.setSecurityToken();
    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const body = payload;
    const url = this.ratingAndCommentsUrl + videoId + '/comment/' + commentId + '/reply';
    return this.http.post<string>(url, body, httpOps);
  }
}
