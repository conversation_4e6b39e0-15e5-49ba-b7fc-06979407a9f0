import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';

import { environment } from 'src/environments/environment.prod';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import * as $ from 'jquery';

import { NewUser } from '../../Models/NewUser';
import { RegisterService } from '../../Services/register.service';
import { PaymentService } from '../../Services/payment.service';
import { WindowRef } from '../../Services/window-ref.service';

declare let fbq: Function;
declare let gtag: Function;

@Component({
  selector: 'app-ml-landing',
  templateUrl: './ml-landing.component.html',
  styleUrls: ['./ml-landing.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [WindowRef]
})
export class MlLandingComponent implements OnInit {

  @ViewChild('regTemplate') public regTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public failTemplate: TemplateRef<any>;
  @ViewChild('buyTemplate') public buyTemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public sucTemplate: TemplateRef<any>;

  public showIndicator = false;
  public dark_theme = false;

  public selectedDesc = 'Internship Program on Machine Learning and Artificial Intelligence 27th August 2022';
  public selectedAmount: number;
  public selContactNum: string;
  public selContactInfo: string;
  public selWhatsappInfo: string;

  public newUser = {
    email: '',
    password: '',
    f_name: '',
    l_name: '',
    dob: '',
    phone_no: '',
    inst_name: '',
    qual: '',
    usn: '',
    yop: '',
    subscribed: true,
    semester: 0,
    college: '',
    branch: '',
    state: '',
    city: ''
  };

  public modalRef: BsModalRef;
  public modalConfig = {
    animated: true,
    keyboard: false,
    backdrop: true,
    ignoreBackdropClick: true,
    class: 'reg-modal'
  };

  public orderId: string;

  constructor(private router: Router,
    private registerService: RegisterService,
    private modalService: BsModalService,
    private paymentService: PaymentService,
    private winRef: WindowRef) {

    this.router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });

        fbq('track', 'PageView', {
          value: 0.1,
          currency: 'INR',
        });
      }
    });
  }

  ngOnInit() {

    this.showIndicator = true;
    this.paymentService.getInternshipQuote().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      this.selectedAmount = respText.amount;

      this.paymentService.getCampaignContact().subscribe(contactResponse => {
        const resText = JSON.parse(JSON.stringify(contactResponse));

        this.selContactNum = resText.phone;
        this.selContactInfo = resText.name + ', ' + resText.desig;
        this.selWhatsappInfo = resText.whatsapp;

        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;

        this.selContactNum = '+91 ************';
        this.selContactInfo = 'Himanshu Sharma, Founder, Managing Director, Quant Masters';
        this.selWhatsappInfo = '919900001452';
      });
    }, error => {
      this.showIndicator = false;
      this.openModal(this.failTemplate);
    });

    $(window).scroll(() => {
      if ($(window).width() < 450) {
        const whatsappButtonTop = $('.qm-contact--box').offset().top;
        const footerTop = $('.qm-ftr').offset().top;

        const disappStyles = {
          transition: 'all 0.2s ease-out',
          opacity: 0
        };

        const reappStyles = {
          transition: 'all 0.2s ease-in',
          opacity: 1
        };

        const whatsappButton = $('.qm-contact--box');

        if (whatsappButtonTop > footerTop) {
          whatsappButton.css(disappStyles);
        } else if (whatsappButtonTop < footerTop) {
          whatsappButton.css(reappStyles);
        }
      }
    });
  }

  openRegForm() {

    this.openModal(this.regTemplate);
    // this.openModal(this.buyTemplate);
  }

  registerQuick(isValid: boolean) {

    if (!isValid) {
      return;
    }

    const newUser = new NewUser();
    newUser.f_name = this.newUser.f_name;
    newUser.l_name = this.newUser.l_name;
    newUser.email = this.newUser.email;
    newUser.password = this.newUser.password;
    newUser.inst_name = this.newUser.college;

    const newRegUser = {
      workshop_id: 'aa8afa43-6106-3928-957b-8cd54c530706',
      name: this.selectedDesc,
      email: this.newUser.email,
      f_name: this.newUser.f_name,
      l_name: this.newUser.l_name,
      phone_no: this.newUser.phone_no.toString(),
      semester: this.newUser.semester,
      branch: this.newUser.branch,
      college: this.newUser.college,
      state: this.newUser.state,
      city: this.newUser.city
    };

    this.showIndicator = true;
    this.modalRef.hide();
    this.registerService.sendWorkshpRegistration(newRegUser).subscribe(resp => {
      const respT = JSON.parse(JSON.stringify(resp));

      if (respT.text === 'rec added') {
        this.openModal(this.buyTemplate);
        this.showIndicator = false;
      } else {
        this.openModal(this.failTemplate);
      }
    }, error => {
      this.openModal(this.failTemplate);
      this.showIndicator = false;
    });
  }

  onCheckout() {

    const that = this;
    this.showIndicator = true;
    this.paymentService.getOrderId(1999 * 100).subscribe(orderResponse => {
      this.orderId = orderResponse;

      fbq('track', 'InitiateCheckout', {
        value: 0.2,
        currency: 'INR',
      });

      const options: any = {
        'key': environment.razorpayKey, // Enter the Key ID generated from the Dashboard
        'amount': this.selectedAmount * 100, // '250000',
        'currency': 'INR',
        'name': 'Quant Masters',
        'description': this.selectedDesc,
        'image': 'https://quantmasters.in/assets/QM_Logo_No_Text.svg',
        'order_id': that.orderId,
        'modal': {
          'escape': false
        },
        'prefill': {
          'name': that.newUser.f_name + ' ' + that.newUser.l_name,
          'email': that.newUser.email,
          'contact': that.newUser.phone_no
        }
      };

      options.handler = ((paymentResponse) => {
        options['payment_response_id'] = paymentResponse.razorpay_payment_id;

        that.paymentService.confirmPayment(paymentResponse.razorpay_payment_id, options.amount).subscribe(ConfirmResponse => {
          const resp = JSON.parse(JSON.stringify(ConfirmResponse));

          if (resp.msg === 'Captured' || resp.msg === 'Already Captured') {
            that.modalRef.hide();

            fbq('track', 'Purchase', {
              value: 2499,
              currency: 'INR',
            });

            that.paymentService.sendMailPostPaymentInRecorded(
              that.newUser.email,
              that.newUser.f_name,
              that.newUser.l_name,
              3)
              .subscribe(response => {

                that.openModal(that.sucTemplate);
                that.showIndicator = false;
              }, error => {
                that.showIndicator = false;
              });
          }
        }, error => {
          const resp = JSON.parse(JSON.stringify(error));
          that.showIndicator = false;
          console.log(resp.msg);
        });
      });

      const rzp = new that.winRef.nativeWindow.Razorpay(options);
      rzp.open();
    }, error => {
      that.showIndicator = false;
    });
  }

  takeToDetails() {

    $([document.documentElement, document.body]).animate({
      scrollTop: $('#qm-enroll--box').offset().top - 40
    }, 700);
  }

  sendToWhatsapp() {
    const whatsAppUrl = 'https://wa.me/' + this.selWhatsappInfo;

    window.open(whatsAppUrl, '_blank');
  }

  switchTheme() {

    this.dark_theme = !this.dark_theme;

    const toggleCssDark = {
      'transform': 'translateX(100%)',
      'background-color': '#34323D'
    };

    const toggleCssNorm = {
      'transform': 'translateX(0)',
      'color': '#fff',
      'background-color': '#F49820'
    };

    if (this.dark_theme) {
      $('.qm-toggle').css(toggleCssDark);
      $('.qm-ml--wrap').addClass('dark-theme');
      $('body').css({ 'background-color': '#26242E' });

      this.modalConfig.class = 'reg-modal dark-theme';
    } else {
      $('.qm-toggle').css(toggleCssNorm);
      $('.qm-ml--wrap').removeClass('dark-theme');
      $('body').css({ 'background-color': '#F8F9F9' });

      this.modalConfig.class = 'reg-modal';
    }
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }
}
