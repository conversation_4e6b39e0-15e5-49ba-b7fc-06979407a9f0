<app-nav></app-nav>
<div class="sect-2">
  <div class="sect-text">
    <h5>Our Student Achievers</h5>
  </div>
  <h4>2023 January Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversJan23">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2022 April & May Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversAprMay22">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2022 Februray Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversFeb22">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2022 January Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversJan22">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2021 November Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversNov21">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2021 September Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achieversSep21">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2021 Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achievers2020">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
  <h4>2020 Batch</h4>
  <div class="achv-grid">
    <div class="achiever" *ngFor="let item of achievers">
      <img src="{{ item.path }}" alt="student-image" />
      <h5>{{ item.name }}</h5>
      <p>{{ item.company }}</p>
    </div>
  </div>
</div>
<svg class="neg-space" xmlns="http://www.w3.org/2000/svg" width="184.715" height="186.713"
  viewBox="0 0 184.715 186.713">
  <ellipse id="Ellipse_1" data-name="Ellipse 1" cx="59" cy="72.5" rx="59" ry="72.5"
    transform="translate(97.024) rotate(42)" fill="#e88224" />
</svg>
<app-footer></app-footer>