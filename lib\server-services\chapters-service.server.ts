// lib/server-services/chapters-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { SuperGroup, Group, ChapterPaper } from '@/types/chapter-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  superGroupUrl: `${API_BASE_URL}/test/progression/super-groups`,
  groupUrl: `${API_BASE_URL}/test/progression/groups`,
  groupPaperUrl: `${API_BASE_URL}/test/progression/group`,
  submitUrl: `${API_BASE_URL}/test/progression/submit/marks`,
  imgUrl: `${API_BASE_URL}/test/progression/paper`,
  adminPaperUrl: `${API_BASE_URL}/admin/paper/progression/upload/test`,
  adminQuesUrl: `${API_BASE_URL}/admin/paper/progression/upload/test/question`,
  adminQuesGetUrl: `${API_BASE_URL}/admin/paper/progression/paper`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerChaptersService {
  /**
   * Get super groups
   */
  static async getSuperGroups(): Promise<SuperGroup[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.superGroupUrl,
        createAuthHeaders(token)
      )

      let superGroups: SuperGroup[] = response.data

      // Remove specific super group (temp code from original)
      superGroups = superGroups.filter(
        (x) => x.super_group_id !== '143dbc04-8493-3b7e-ad2d-4e3983ae4edd'
      )

      return superGroups
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get groups by super group ID
   */
  static async getGroups(superGrpId: string): Promise<Group[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.groupUrl}/${superGrpId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching groups for super group ${superGrpId}:`,
        error
      )
      return []
    }
  }

  /**
   * Get papers of a group
   */
  static async getPapersOfAGroup(groupId: string): Promise<ChapterPaper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.groupPaperUrl}/${groupId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching papers for group ${groupId}:`, error)
      return []
    }
  }
}
