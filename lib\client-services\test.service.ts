// lib/client-services/test.service.ts
import axios from 'axios'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'

/**
 * Get JWT token from cookies/localStorage
 */
const getAuthToken = (): string => {
  return LoginRefresh.getAuthToken() || ''
}

/**
 * Create auth headers with JW<PERSON> token
 */
const createAuthHeaders = () => {
  const token = getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Test types mapping
 * 1: Chapter wise papers
 * 2: Competitive questions
 * 3: Company questions
 * 4: Model questions
 * 5: Open tests
 * 6: Display only questions
 * 7: Trial papers
 * 8: Open competitive questions
 * 9: Section wise papers
 * 10: AFCAT papers
 * 11: TMCQ
 * 13, 14: Competitive paper questions
 */

export const TestService = {
  // Get chapter wise questions
  getChapterQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/progression/paper/new/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching chapter questions:', error)
      throw error
    }
  },

  // Get competitive questions
  getCompetitiveQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/competitive/paper/new/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching competitive questions:', error)
      throw error
    }
  },

  // Get company questions
  getCompanyQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/company/paper/new/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching company questions:', error)
      throw error
    }
  },

  // Get model questions
  getModelQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/question/ret/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching model questions:', error)
      throw error
    }
  },

  // Get trial questions
  getTrialQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching trial questions:', error)
      throw error
    }
  },

  // Get open competitive questions
  getOpenCompetitiveQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/v2/test/open/competitive/paper/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching open competitive questions:', error)
      throw error
    }
  },

  // Get section wise questions
  getSectionWiseQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/v2/test/sectionWise/paper/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching section wise questions:', error)
      throw error
    }
  },

  // Get AFCAT questions
  getAfcatQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/v2/test/afcat/paper/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching AFCAT questions:', error)
      throw error
    }
  },

  // Get TMCQ questions
  getTMCQQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/v3/tmcq/paper/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching TMCQ questions:', error)
      throw error
    }
  },

  // Get competitive paper questions
  getCompetitivePaperQuestions: async (paperId: string) => {
    try {
      const url = `${API_BASE_URL}/test/competitive/paper/new/${paperId}`
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching competitive paper questions:', error)
      throw error
    }
  },

  // Submit answers for chapter wise papers
  submitChapterMarks: async (email: string, paperId: string, marks: number) => {
    try {
      const url = `${API_BASE_URL}/test/progression/submit/marks`
      const data = { email, paper_id: paperId, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting chapter marks:', error)
      throw error
    }
  },

  // Submit answers for competitive papers
  submitCompetitiveMarks: async (
    email: string,
    paperId: string,
    marks: number
  ) => {
    try {
      const url = `${API_BASE_URL}/test/competitive/submit/marks`
      const data = { email, paper_id: paperId, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting competitive marks:', error)
      throw error
    }
  },

  // Submit answers for company papers
  submitCompanyMarks: async (email: string, paperId: string, marks: number) => {
    try {
      const url = `${API_BASE_URL}/test/company/submit/marks`
      const data = { email, paper_id: paperId, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting company marks:', error)
      throw error
    }
  },

  // Submit answers for model tests
  submitModelAnswers: async (email: string, paperId: string, marks: number) => {
    try {
      const today = new Date()
      const date = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`

      const url = `${API_BASE_URL}/test/answer/submit`
      const data = { email, paper_id: paperId, marks, answered_on: date }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting model answers:', error)
      throw error
    }
  },

  // Submit answers for trial papers
  submitTrialAnswers: async (email: string, paperId: string, marks: number) => {
    try {
      const today = new Date()
      const date = `${today.getMonth() + 1}/${today.getDate()}/${today.getFullYear()}`

      const url = `${API_BASE_URL}/test/open/submit`
      const data = { email, paper_id: paperId, marks, answered_on: date }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting trial answers:', error)
      throw error
    }
  },

  // Submit answers for open competitive
  submitOpenCompetitiveAnswers: async (
    email: string,
    paperId: string,
    marks: number
  ) => {
    try {
      const url = `${API_BASE_URL}/v2/test/open/competitive/submit/marks`
      const data = { email, paper_id: paperId, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting open competitive answers:', error)
      throw error
    }
  },

  // Submit answers for section wise papers
  submitSectionWiseAnswers: async (
    email: string,
    paperId: string,
    marks: number
  ) => {
    try {
      const url = `${API_BASE_URL}/v2/test/sectionWise/paper/submit`
      const data = { email, paper_id: paperId, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting section wise answers:', error)
      throw error
    }
  },

  // Submit section wise breakdown
  submitSectionBreakdown: async (
    email: string,
    paperId: string,
    answerId: string,
    sectionsData: any[]
  ) => {
    try {
      let url
      if (paperId.includes('sec')) {
        url = `${API_BASE_URL}/v2/test/sectionWise/paper/${paperId}/submit/${answerId}/${email}/sections`
      } else {
        url = `${API_BASE_URL}/v2/test/open/paper/${paperId}/submit/${answerId}/${email}/sections`
      }
      const response = await axios.post(url, sectionsData, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting section breakdown:', error)
      throw error
    }
  },

  // Submit TMCQ answers
  submitTMCQMarks: async (paperId: string, email: string, marks: number) => {
    try {
      const url = `${API_BASE_URL}/v3/tmcq/paper/marks`
      const data = { paper_id: paperId, email, marks }
      const response = await axios.post(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error submitting TMCQ marks:', error)
      throw error
    }
  }
}
