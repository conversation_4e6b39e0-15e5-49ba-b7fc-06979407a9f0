$black: #1e1e1e;
$grey: #cccccc;
$white: #ffffff;

.expl-wrapper {
  width: 100%;

  .ops-wrap {
    width: 98%;
    padding: 15px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 3px 1px 6px rgba(0, 0, 0, 0.15);

    .form-wrap {
      display: flex;
      margin-top: 2em;

      form {
        width: 100%;
        display: flex;

        .form-elem {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin-right: 1em;

          label {
            font-weight: 700;
          }
        }
      }

      .img-updl--toggle {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px;

        p {
          margin: 0;
        }

        span.switcher {
          position: relative;
    
          width: 100px;
          height: 50px;
          border-radius: 25px;
          margin: 20px 0;
          cursor: pointer;

          input {
            appearance: none;
    
            position: relative;
    
            width: 100px;
            height: 50px;
            border-radius: 25px;
    
            background-color: $black;
            outline: none;
    
            font-family: "<PERSON>", sans-serif;
            &:before,
            &:after {
                z-index: 2;
    
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
    
                color: $white;
            }
            &:before {
                content: "ON";
                left: 20px;
            }
            &:after {
                content: "OFF";
                right: 20px;
            }
          }
          label {
            z-index: 1;
            position: absolute;
            top: 10px;
            bottom: 10px;
    
            border-radius: 20px;
          }
          &.switcher-2 {
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.15);

            input {
                transition: background-color 0s 0.3s;
                &:before {
                  color: $black;
                }
                &:after {
                  color: $white;
                }
                &:checked {
                  background-color: $white;
                  & + label {
                      background: $white;
    
                      animation: turn-on 0.3s ease-out;
    
                      @keyframes turn-on {
                        0% {
                            left: 100%;
                        }
                        100% {
                            left: 0%;
                        }
                      }
                  }
                }
                &:not(:checked) {
                  background: $black;
                  & + label {
                      background: $black;
    
                      animation: turn-off 0.3s ease-out;
    
                      @keyframes turn-off {
                        0% {
                            right: 100%;
                        }
                        100% {
                            right: 0%;
                        }
                      }
                  }
                }
            }
            label {
                top: 0px;
    
                width: 200px;
                height: 50px;
                border-radius: 25px;
            }
          }
        }
      }
    }
  }

  .ipopBox {
    display: flex;

    div {
      margin-left: 2em;
      border: solid 1px #707070;
      border-radius: 3px;
      width: 50%;
      height: 200px;
    }
  }

  .img-box {
    button {
      border: none;
      background-color: #fff;
      margin-bottom: 1rem;
    }
  }

  select,
  textarea {
    display: block;
    height: 40px;
    width: 100%;
    padding: 3px 5px;
    border: solid 1px #707070;
    font-size: 15px;
    border-radius: 3px;
    transition: all 0.3s ease;

    &:focus {
      border: solid 1.5px #0B6FB1;
      transition: all 0.3s ease;
    }
  }

  .custom-btn {
    height: 40px;
    font-size: 18px;
    font-weight: normal;
    border: none;
    border-radius: 4px;
    color: #145A32;
    background-color: #58D68D;
    padding: 5px 10px;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
  }
  
  .custom-btn:active:after {
    transition: 0s;
    opacity: 0.7;
    clip-path: circle(0% at 0% 0%);
  }
  
  .custom-btn::after {
    content: "";
    display: block;
    position: relative;
    top: -32px;
    height: 40px;
    width: 150px;
    background-color: #1D8348;
    opacity: 0;
    clip-path: circle(150% at 0% 0%);
    transition: all 0.4s ease-in;
  }

  .data-wrap {
    width: 98%;
    padding: 15px;
    margin-top: 1em;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 3px 1px 6px rgba(0, 0, 0, 0.15);

    textarea {
      width: 50%;
      height: 200px;
      margin-bottom: 1em;
      resize: both;
    }
  }
}