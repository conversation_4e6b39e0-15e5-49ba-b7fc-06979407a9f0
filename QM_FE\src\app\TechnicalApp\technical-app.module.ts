import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { MonacoEditorModule  } from 'ngx-monaco-editor';

import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { TechnicalAppRoutingModule } from './technical-app-routing.module';

import { NavComponent } from './Layout/nav/nav.component';
import { AppSelectComponent } from './Layout/app-select/app-select.component';

import { TechDashboardComponent } from './tech-dashboard/tech-dashboard.component';
import { OnlineCompilerComponent } from './online-compiler/online-compiler.component';

@NgModule({
  declarations: [
    NavComponent,
    AppSelectComponent,
    TechDashboardComponent,
    OnlineCompilerComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    TechnicalAppRoutingModule,
    SharedIndicatorModule,
    MonacoEditorModule.forRoot()
  ]
})
export class TechnicalAppModule { }
