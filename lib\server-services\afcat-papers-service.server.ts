// lib/server-services/afcat-papers-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { ChapterPaper } from '@/types/afcat-paper-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  afcatPapersUrl: `${API_BASE_URL}/v2/test/afcat/papers`,
  afcatQuestionsUrl: `${API_BASE_URL}/v2/test/afcat/paper/`,
  afcatMarksUrl: `${API_BASE_URL}/v2/test/afcat/`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerAfcatPapersService {
  /**
   * Get AFCAT papers
   */
  static async getAfcatPapers(): Promise<ChapterPaper[]> {
    // Optional: redirect if not authenticated
    // if (!await checkServerSideLoginStatus()) {
    //   redirect('/user/login')
    // }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.afcatPapersUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching AFCAT papers:', error)
      return []
    }
  }

  /**
   * Get AFCAT questions for a specific paper
   */
  static async getAfcatQuestions(paperId: string): Promise<any> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.afcatQuestionsUrl}${paperId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(
        `Error fetching AFCAT questions for paper ${paperId}:`,
        error
      )
      throw error
    }
  }
}
