<div class="tmcq-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="page-header">
    <div class="title">
      <p>Technical MCQs</p>
    </div>
  </div>
  <div class="data-wrap">
    <div class="qm-select--wrap">
      <div class="btn-group" dropdown>
        <button id="button-topic" dropdownToggle type="button" class="btn btn-blue dropdown-toggle"
                aria-controls="dropdown-topic">
          {{ selectedTopic ? 'Topic: ' + selectedTopic : 'Select Topic' }} <span class="caret"></span>
        </button>
        <ul id="dropdown-topic" *dropdownMenu class="dropdown-menu"
            role="menu" aria-labelledby="button-topic">
          <li *ngFor="let group of groups" role="menuitem"><a class="dropdown-item" (click)="getSubGroups(group.group_id, group.group_name)">{{ group.group_name }}</a></li>
        </ul>
      </div>
      <div class="btn-group" dropdown>
        <button id="button-topic" dropdownToggle type="button" class="btn btn-blue dropdown-toggle"
                aria-controls="dropdown-sub-topic">
          {{ selectedSubTopic ? 'Sub Topic: ' + selectedSubTopic : 'Select Sub Topic' }} <span class="caret"></span>
        </button>
        <ul id="dropdown-topic" *dropdownMenu class="dropdown-menu"
            role="menu" aria-labelledby="button-sub-topic">
          <li *ngFor="let subGroup of subGroups" role="menuitem"><a class="dropdown-item" (click)="getPapers(subGroup.sub_group_id, subGroup.sub_group_name)">{{ subGroup.sub_group_name }}</a></li>
        </ul>
      </div>
    </div>
    <div class="qm-tmcq--papers" *ngIf="papers && papers.length > 0">
      <div class="qm-paper" *ngFor="let paper of papers">
        <h6>{{ paper.paper_name }}</h6>
        <p>{{paper.time_lim > 0 ? paper.time_lim / (1000 * 60) + " Mins" : "No Time Limit"}}</p>
        <p>No. of Questions: {{ paper.no_of_ques }}</p>
        <button (click)="beginTest(paper.paper_id, paper.paper_name, paper.time_lim)">Begin Test</button>
      </div>
    </div>
  </div>
</div>