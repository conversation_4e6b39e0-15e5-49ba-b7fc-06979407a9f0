// app/(admin)/admin/tests/papers/type/[typeName]/page.tsx
import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { PAPER_TYPES } from '@/types/admin-types'
import AdminBreadcrumb from '@/components/admin/admin-breadcrumb'
import PaperTypeDetailClient from '@/components/admin/tests/paper-type-detail-client'

interface Props {
  params: Promise<{
    typeName: string
  }>
  searchParams: Promise<{
    topic?: string
    subtopic?: string
  }>
}

export default async function PaperTypeDetailPage({
  params,
  searchParams
}: Props) {
  const { typeName } = await params
  const { topic, subtopic } = await searchParams

  // Find paper type by name (URL-friendly name)
  const paperType = PAPER_TYPES.find(
    (type) => type.name.toLowerCase().replace(/\s+/g, '-') === typeName
  )

  if (!paperType) {
    notFound()
  }

  return (
    <div className="px-4 py-6">
      <AdminBreadcrumb
        items={[
          { label: 'Admin Console', href: '/admin' },
          { label: 'Tests & Papers', href: '/admin/tests/papers' },
          { label: paperType.name, isCurrentPage: true }
        ]}
      />

      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          {paperType.name} Management
        </h1>
        <p className="mt-2 text-gray-600">
          Manage {paperType.name.toLowerCase()} and their configurations
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <PaperTypeDetailClient
          paperType={paperType}
          initialTopic={topic}
          initialSubtopic={subtopic}
        />
      </Suspense>
    </div>
  )
}

export async function generateStaticParams() {
  return PAPER_TYPES.map((type) => ({
    typeName: type.name.toLowerCase().replace(/\s+/g, '-')
  }))
}
