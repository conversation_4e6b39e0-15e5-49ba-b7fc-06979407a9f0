import { Component, OnInit, ViewChild, TemplateRef, ViewEncapsulation } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';

import * as $ from 'jquery';

import { AdminViewService } from '../../../Services/admin-view.service';
import { VideoWatchTrack } from '../../../Models/Tracking/VideoWatchTrack';

@Component({
  selector: 'app-video-usage',
  templateUrl: './video-usage.component.html',
  styleUrls: ['./video-usage.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class VideoUsageComponent implements OnInit {

  @ViewChild('sortTemplate') public sortTemplate: TemplateRef<any>;

  public allRecords: VideoWatchTrack[];
  public displayRecords: VideoWatchTrack[];
  public filteredRecords: VideoWatchTrack[];

  public fullListLength = 0;
  public numFilteredResults = 0;

  public showIndicator = false;

  public basicSearchVal = '';
  public userName = '';
  public email = '';
  public videoName = '';
  public streamDate = '';

  public sortField: number;
  public sortAsc: boolean;

  public modalRef: BsModalRef;
  public config = {
    animated: true,
    backdrop: true,
    ignoreBackdropClick: false,
    keyboard: true,
    class: 'sorter-model'
  };

  constructor(private adminViewService: AdminViewService,
              public modalService: BsModalService) { }

  ngOnInit() {
    this.showIndicator = true;
    this.adminViewService.getAllVideoWatchTrack().subscribe(response => {
      this.allRecords = response;

      this.allRecords.forEach((record: VideoWatchTrack) => {
        const date = new Date(record.created_at);
        record.display_date = date.toLocaleString();
      });

      this.allRecords = this.allRecords.sort((x, y) => {
        const aDate = new Date(x.created_at),
              bDate = new Date(y.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });

      this.sortField = 4;
      this.sortAsc = true;

      this.displayRecords = this.allRecords.slice(0, 15);
      this.fullListLength = this.allRecords.length;
      this.showIndicator = false;
    }, error => {

    });
  }

  filterRecordsList($event: Event) {
    this.showIndicator = true;
    $event.preventDefault();
    $event.stopPropagation();

    if (this.basicSearchVal !== '') {
      const searchVal = this.basicSearchVal;
      let intFiltered = [];

      this.filteredRecords = [];

      this.filteredRecords = this.customFilter(this.allRecords, 'user_name', searchVal);

      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'email', searchVal);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'email', searchVal);
      }

      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'video_name', searchVal);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'video_name', searchVal);
      }

      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'created_at', searchVal);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'created_at', searchVal);
      }

      this.numFilteredResults = this.filteredRecords.length;
      this.displayRecords = this.filteredRecords.slice(0, 15);
    }

    this.showIndicator = false;
  }

  filterResultsListUsingFields($event: Event) {
    this.showIndicator = true;
    $event.preventDefault();
    $event.stopPropagation();

    let intFiltered = [];

    this.filteredRecords = [];

    if (this.userName !== '') {
      this.filteredRecords = this.customFilter(this.allRecords, 'user_name', this.userName);
    }

    if (this.email !== '') {
      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'email', this.email);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'email', this.email);
      }
    }

    if (this.videoName !== '') {
      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'video_name', this.videoName);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'video_name', this.videoName);
      }
    }

    if (this.streamDate !== '') {
      if (this.filteredRecords.length > 0) {
        intFiltered = this.customFilter(this.filteredRecords, 'created_at', this.streamDate);

        if (intFiltered.length > 0) {
          this.filteredRecords = intFiltered;
        }
      } else {
        this.filteredRecords = this.customFilter(this.allRecords, 'created_at', this.streamDate);
      }
    }

    this.numFilteredResults = this.filteredRecords.length;
    this.displayRecords = this.filteredRecords.slice(0, 15);
    this.showIndicator = false;
  }

  customFilter(targetArray: any[], objProp: string, compareStr: string) {
    return targetArray.filter((x) => {
      if (x.hasOwnProperty(objProp)) {
        const i = x[objProp].search(compareStr);
        return i !== -1 ? true : false;
      }

      return false;
    });
  }

  clearFilters($event: Event) {
    $event.preventDefault();
    $event.stopPropagation();

    this.basicSearchVal = '';
    this.userName = '';
    this.email = '';
    this.videoName = '';
    this.streamDate = '';

    this.numFilteredResults = 0;
    this.filteredRecords = [];
    this.displayRecords = this.allRecords.slice(0, 15);
  }


  openSorter() {
    this.openModal(this.sortTemplate);

    switch (this.sortField) {
      case 1:
        $('#name').attr('checked', true).trigger('click');
        break;
      case 2:
        $('#email').attr('checked', true).trigger('click');
        break;
      case 3:
        $('#video').attr('checked', true).trigger('click');
        break;
      case 4:
        $('#date').attr('checked', true).trigger('click');
        break;
    }

    this.sortAsc ? $('#asc-btn').attr('checked', true).trigger('click') : $('#desc-btn').attr('checked', true).trigger('click');
  }

  selectSorter(num: number) {
    switch (num) {
      case 1:
        $('#name').attr('checked', true).trigger('click');
        this.sortField = 1;
        break;
      case 2:
        $('#email').attr('checked', true).trigger('click');
        this.sortField = 2;
        break;
      case 3:
        $('#video').attr('checked', true).trigger('click');
        this.sortField = 3;
        break;
      case 4:
        $('#date').attr('checked', true).trigger('click');
        this.sortField = 4;
        break;
      default:
        break;
    }
  }

  selectAsc() {
    $('#asc-btn').attr('checked', true).trigger('click');
    this.sortAsc = true;
  }

  selectDesc() {
    $('#desc-btn').attr('checked', true).trigger('click');
    this.sortAsc = false;
  }

  sortList() {
    let filterField = '';
    switch (this.sortField) {
      case 1:
        filterField = 'user_name';
        break;
      case 2:
        filterField = 'email';
        break;
      case 3:
        filterField = 'video_name';
        break;
      case 4:
        filterField = 'created_at';
        break;
    }

    if (this.numFilteredResults > 0) {
      if (filterField === 'created_at') {
        this.filteredRecords.sort((a, b) => {
          const aDate = new Date(a.created_at),
                bDate = new Date(b.created_at);

          return this.sortAsc ? aDate > bDate ? -1 : aDate < bDate ? 1 : 0 : aDate > bDate ? 1 : aDate < bDate ? -1 : 0;
        });
      } else {
        this.filteredRecords.sort((a, b) => {
          return this.sortAsc ? a[filterField] > b[filterField] ? -1 :
            a[filterField] < b[filterField] ? 1 : 0 : a[filterField] > b[filterField] ? 1 :
            a[filterField] < b[filterField] ? -1 : 0;
        });
      }

      this.numFilteredResults = this.filteredRecords.length;
      this.displayRecords = this.filteredRecords.slice(0, 15);
    } else {
      if (filterField === 'created_at') {
        this.allRecords.sort((a, b) => {
          const aDate = new Date(a.created_at),
                bDate = new Date(b.created_at);

          return this.sortAsc ? aDate > bDate ? -1 : aDate < bDate ? 1 : 0 : aDate > bDate ? 1 : aDate < bDate ? -1 : 0;
        });
      } else {
        this.allRecords.sort((a, b) => {
          return this.sortAsc ? a[filterField] > b[filterField] ? 1 :
            a[filterField] < b[filterField] ? -1 : 0 : a[filterField] > b[filterField] ? -1 :
            a[filterField] < b[filterField] ? 1 : 0;
        });
      }

      this.fullListLength = this.allRecords.length;
      this.displayRecords = this.allRecords.slice(0, 15);
    }

    this.modalRef.hide();
  }

  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayRecords = this.allRecords.slice(startItem, endItem);
  }

  pageFilterChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayRecords = this.filteredRecords.slice(startItem, endItem);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }
}
