import { Component, OnInit, ViewChild, TemplateRef, OnDestroy } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';

declare let gtag: Function;

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {

  @ViewChild('hideTemplate')    public hTemplate: TemplateRef<any>;

  public modalRef: BsModalRef;
  public config = {
    backdrop           : true,
    ignoreBackdropClick: true,
    keyboard           : false
  };

  constructor(public router: Router,
              public modalService: BsModalService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    setTimeout(() => {
      if (!sessionStorage.getItem('logged') && !this.router.url.match(/^\/dashboard\/test\/7\/*/)) {
        this.openModal(this.hTemplate);
      }
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}
