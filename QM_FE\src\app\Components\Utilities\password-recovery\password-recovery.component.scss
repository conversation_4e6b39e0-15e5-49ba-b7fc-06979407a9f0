.reset-container {
  width: 100%;
  font-family: 'Montserrat', 'sans-serif';

  .reset-nav {
    height: 80px;
    width: 100%;
    padding: 0.3em 1em;
    background-color: #85C1E9;

    .qm-logo {
      height: 60px;
      width: 60px;
    }
  }

  .reset-form--container {
    width: 400px;
    padding: 1em;
    margin: 2em auto 0 auto;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);

    form {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .form-grp {
        width: 80%;
        margin-bottom: 1em;

        &:nth-of-type(2) {
          margin-bottom: 2em;
        }
      }

      h5 {
        margin-bottom: 2em;
      }

      input {
        padding: 1em;
        width: 100%;
        height: 3em;
        background-color: #fff;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 5px;
        box-shadow: inset 1px 0.5px 5px rgba(0, 0, 0, 0.25);
      }

      .btn-grp {

        .custom-btn {
          width: 230px;
          height: 40px;
          background-color: #E88224;
          border: none;
          border-radius: 4px;
          color: #fff;
          margin: 2em auto 3em auto;
          padding: 8px;
          box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
        }
    
        .custom-btn:active:after {
          transition: 0s;
          opacity: 0.7;
          clip-path: circle(0% at 0% 0%);
        }
    
        .custom-btn::after {
          content: "";
          display: block;
          position: relative;
          top: -32px;
          left: -8px;
          height: 40px;
          width: 130px;
          background-color: #E88224;
          opacity: 0;
          clip-path: circle(150% at 0% 0%);
          transition: all 0.4s ease-in;
        }

        // .submit-btn {
        //   height: 50px;
        //   width: 200px;
        //   padding: 0.6em;
        //   color: #fff;
        //   border: none;
        //   border-radius: 5px;
        //   background-color: rgb(0, 32, 175);
        //   box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.15);
        // }

        // &:hover .btn-wrap {
        //   opacity: 0;
        //   clip-path: circle(100% at 0% 0%);
        //   transition: all 0.4s ease-in;
        // }

        // .submit-btn:active:after {
        //   transition: 0s;
        //   opacity: 0.7;
        //   clip-path: circle(0% at 0% 0%);
        // }
  
        // .submit-btn::after {
        //   content: "";
        //   display: block;
        //   position: relative;
        //   top: -33px;
        //   left: -10px;
        //   height: 50px;
        //   width: 200px;
        //   background-color:rgb(255, 238, 192);
        //   opacity: 0;
        //   clip-path: circle(150% at 0% 0%);
        //   transition: all 0.4s ease-in;
        // }

        .is-inv {
          border-color: #dc3545;
        }
      }
    }
  }
}

@media (max-width: 440px) {
  .reset-container {
    .reset-form--container {
      width: 100%;
      margin-left: 0;
      margin-right: 0;

      form {
        .form-grp {
          width: 95%;
        }      
      }
    }
  }
}