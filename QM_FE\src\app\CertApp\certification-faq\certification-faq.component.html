<div class="qm-cert--wrap">
  <app-nav></app-nav>
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="qm-cert--faq">
    <h4>Frequently Asked Questions</h4>
    <div class="qm-faq--list">
      <div class="qm-faqs" *ngFor="let faq of faqs; let i = index">
        <div class="qm-faq--header">
          <h5>{{ faq.question }}</h5>
          <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseChapterGroup">
            <svg xmlns="http://www.w3.org/2000/svg" width="29.25" height="29.25" viewBox="0 0 29.25 29.25">
              <path id="Icon_ionic-ios-arrow-dropdown-circle" data-name="Icon ionic-ios-arrow-dropdown-circle"
                d="M3.375,18A14.625,14.625,0,1,1,18,32.625a14.926,14.926,0,0,1-6.188-1.369A14.514,14.514,0,0,1,3.375,18ZM23.7,21.052a1.362,1.362,0,0,0,1.92,0,1.341,1.341,0,0,0,.394-.956,1.364,1.364,0,0,0-.4-.963l-6.63-6.609a1.355,1.355,0,0,0-1.87.042l-6.729,6.708a1.357,1.357,0,0,0,1.92,1.92l5.7-5.759Z"
                transform="translate(-3.375 -3.375)" fill="#fff" />
            </svg>
          </button>
        </div>
        <div class="qm-faq--item" id="collapseChapterGroup" id="collapseChapterGroup" [collapse]="isCollapsed[i]"
          [isAnimated]="true">
          <div class="faq-answer" *ngIf="i === 0">
            <p><b>Step-1: </b> Click on the LinkedIn logo after you generate your certificate. This will copy your credentials url.</p>
            <img src="../../../assets/faq/Int1-Step-1.png" alt="Q1 Step 1" />
            <p><b>Step-2:</b> Login to LinkedIn and navigate to your profile details. In the Licenses & Certificates section click + to add the certificate.</p>
            <img src="../../../assets/faq/Int1-Step-2.png" alt="Q1 Step 2" />
            <p><b>Step-3:</b> Here give the certificate a name.</p>
            <img src="../../../assets/faq/Int1-Step-3.png" alt="Q1 Step 3" />
            <p><b>Step-4:</b> Select the Issuing Organization as Quant Masters Technologies Pvt. Ltd.</p>
            <img src="../../../assets/faq/Int1-Step-4.png" alt="Q1 Step 4" />
            <p><b>Step-5:</b> Set the Issue Date and paste the copied url in the Credential URL field. Lastly press save.</p>
            <img src="../../../assets/faq/Int1-Step-5.png" alt="Q1 Step 5" />
            <p>The certificate should now be visible on your profile. Click on See Credentials to view the certificate.</p>
            <img src="../../../assets/faq/Int1-Step-6.png" alt="Q1 Step 6" />
          </div>
          <div class="faq-answer" *ngIf="i === 1">
            <p>Each certificate issued by us has a unique Certificate ID attahced to it. Present in the bottom right corner of the certificate.</p>
            <img src="../../../assets/faq/Int2-Step-1.png" alt="Q2 Intro">
            <p><b>Step-1:</b> In our certification page <a href="https://quantmasters.in/certification/view">here</a>, click on Verify Certificate button.</p>
            <img src="../../../assets/faq/Int2-Step-2.png" alt="Q2 Step 1">
            <p><b>Step-2:</b> Here enter the certificate id and press Verify.</p>
            <img src="../../../assets/faq/Int2-Step-3.png" alt="Q2 Step 2">
            <p>It should display the correct certificate with the candidates name and certificate id.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="qm-ftr">
    <p>&copy; 2022 Quantmasters. All Rights Reserved.</p>
  </div>
</div>