<div class="main-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="button-wrap">
    <div class="common-div">
      <button class="custom-btn" (click)="openAddNewSuperGroup()">Add Super Group</button>
    </div>
    <div class="common-div">
      <button class="custom-btn" (click)="openAddNewGroup()">Add Group to a Super Group</button>
    </div>
    <div class="common-div">
      <button class="custom-btn" (click)="onOpenAdditionalSettings()">Settings</button>
    </div>
  </div>
  <div class="notes-wrap">
    <p>Add Notes</p>
    <!-- <button class="custom-btn" (click)="openAddNewSuperGroup()">Add New Note</button> -->
    <div class="group-wrap">
      <!-- <label for="SuperGroup">Super Group: </label> -->
      <div class="group-select-wrap">
        <select name="SuperGroup" (change)="selectSuperGroup()" [(ngModel)]="sel_super_grp_id" #SuperGroup="ngModel"
          class="custom-select form-control">
          <option [ngValue]="null">-- Please choose Super Group--</option>
          <option *ngFor="let super_grp of superGroup; let i = index" value="{{super_grp.super_id}}">{{
            super_grp.super_name }}</option>
        </select>
      </div>
      <!-- <label for="Group">Group: </label> -->
      <div class="group-select-wrap">
        <select name="Group" (change)="selectGroup()" [(ngModel)]="sel_grp_id" #Group="ngModel"
          class="custom-select form-control">
          <option [ngValue]="null">-- Please choose Group --</option>
          <option *ngFor="let grp of groups; let i = index" value="{{grp.group_id}}">{{ grp.group_name }}</option>
        </select>
      </div>
      <div class="group-select-wrap">
        <select name="Notes" (change)="selectNote()" [(ngModel)]="sel_note_id" #Notes="ngModel"
          class="custom-select form-control" [disabled]="visibleSelectNote">
          <option [ngValue]="null">-- Please choose Note --</option>
          <option value="AddNewNote">-- Add New Note --</option>
          <option *ngFor="let nts of NotesList; let i = index" value="{{nts.notes_id}}">{{ nts.notes_name }}</option>
        </select>
      </div>
    </div>
    <div *ngIf="sel_note_id && sel_note_id != 'AddNewNote'">
      <form #SaveNotes="ngForm" (ngSubmit)="SubmitNoteDetail(SaveNotes.valid)" autocomplete="off">
        <div class="Note-Detail-Top">
          <div class="form-elem">
            <input type="text" name="NoteName"
              [class.is-inv--input]="NoteName.invalid && NoteName.touched || (NoteName.pristine && SaveNotes.submitted)"
              [(ngModel)]="NoteDescInDatailSec" #NoteName="ngModel" required />
            <small class="form-error--text"
              *ngIf="NoteName.errors?.required && NoteName.touched || (NoteName.touched && SaveNotes.submitted)">
              Note Name is Required
            </small>
          </div>
          <div class="common-div">
            <button class="custom-btn ButtonWidthNoteDetail" type="submit">Save</button>
          </div>
          <div class="common-div">
            <button class="custom-btn ButtonWidthNoteDetail validate-btn" type="button" (click)="validateNotesContent()">Validate</button>
          </div>
          <div class="img-box">
            <p class="updl-p">Upload Image</p>
            <form (ngSubmit)="userAvatarSelect.click()" enctype="multipart/form-data">
              <input style="display: none" type="file" (change)="fileSelect($event)" #userAvatarSelect />
              <button type="submit" title="Select Image">
                <img src="../../../assets/push-icons/edit.svg" />
              </button>
            </form>
            <button class="img-updl--btn1" (click)="deleteSelectedNote()" type="button">
              <img class="del-img" src="../../../assets/push-icons/trash.svg" />
            </button>
          </div>
          <div>
            
          </div>
        </div>
      </form>

      <div>
        <angular-markdown-editor textareaId="editor1" rows="28" name="markdownText" [(ngModel)]="markdownText">
        </angular-markdown-editor>
      </div>

      <div class="embed-settings">
        <label for="video">Video Key: </label>
        <input type="text" name="video" [(ngModel)]="noteVideo" #notesVideo="ngModel" />
      </div>
    </div>
  </div>

  <ng-template #AddSuperGroup>
    <div class="modal-header">
      <h4 class="modal-title text-success">Add Super Group</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <form #AddSuperGroupForm="ngForm" (ngSubmit)="SubmitSuperGroup(AddSuperGroupForm.valid)" autocomplete="off">
      <div class="form-row--1">
        <div class="form-elem">
          <input type="text" name="superGroupName" placeholder="Super Group Name *"
            [class.is-inv--input]="superGroupName.invalid && superGroupName.touched || (superGroupName.pristine && AddSuperGroupForm.submitted)"
            [(ngModel)]="NewGroup.super_name" #superGroupName="ngModel" required />
          <small class="form-error--text"
            *ngIf="superGroupName.errors?.required && superGroupName.touched || (superGroupName.pristine && AddSuperGroupForm.submitted)">
            Super Group Name is Required
          </small>
        </div>
        <div class="form-elem">

          <input type="text" name="groupName" placeholder="Group Name *"
            [class.is-inv--input]="groupName.invalid && groupName.touched || (groupName.pristine && AddSuperGroupForm.submitted)"
            [(ngModel)]="NewGroup.group_name" #groupName="ngModel" required />
          <small class="form-error--text"
            *ngIf="groupName.errors?.required && groupName.touched || (groupName.pristine && AddSuperGroupForm.submitted)">
            Group Name is Required
          </small>
        </div>
      </div>

      <div class="Ng-temp-Main">
        <div class="Ng-temp-Button">
          <div class="common-div">
            <button class="custom-btn" type="submit">Save</button>
          </div>
          <div class="common-div">
            <button class="custom-btn" (click)="modalRef.hide()">Close</button>
          </div>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template #AddGroup>
    <div class="modal-header">
      <h4 class="modal-title text-success">Add Group</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <form #AddGroupForm="ngForm" (ngSubmit)="SubmitGroup(AddGroupForm.valid)" autocomplete="off">
      <div class="form-row--1">
        <div class="form-elem">
          <select name="SuperGroup" [(ngModel)]="NewGroup.super_id" #SuperGroup="ngModel"
            class="custom-select form-control" required>
            <option [ngValue]="null">--Please choose Super Group-- </option>
            <option *ngFor="let super_grp of superGroup; let i = index" value="{{super_grp.super_id}}">{{
              super_grp.super_name }}</option>
          </select>
          <small class="form-error--text"
            *ngIf="SuperGroup.errors?.required && SuperGroup.touched || (SuperGroup.pristine && AddGroupForm.submitted)">
            Please choose Super Group
          </small>
        </div>
        <div class="form-elem">

          <input type="text" name="groupName" placeholder="Group Name *"
            [class.is-inv--input]="groupName.invalid && groupName.touched || (groupName.pristine && AddGroupForm.submitted)"
            [(ngModel)]="NewGroup.group_name" #groupName="ngModel" required />
          <small class="form-error--text"
            *ngIf="groupName.errors?.required && groupName.touched || (groupName.pristine && AddGroupForm.submitted)">
            Group Name is Required
          </small>
        </div>
      </div>

      <div class="Ng-temp-Main">
        <div class="Ng-temp-Button">
          <div class="common-div">
            <button class="custom-btn" type="submit">Save</button>
          </div>
          <div class="common-div">
            <button class="custom-btn" (click)="modalRef.hide()">Close</button>
          </div>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template #AddNewNote>
    <div class="modal-header">
      <h4 class="modal-title text-success">Add New Note</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <form #AddNoteForm="ngForm" (ngSubmit)="SubmitNewNote(AddNoteForm.valid)" autocomplete="off">
      <div class="form-row--1">
        <div class="form-elem">
          <input type="text" name="NoteName" placeholder="Note Name *"
            [class.is-inv--input]="NoteName.invalid && NoteName.touched || (NoteName.pristine && AddNoteForm.submitted)"
            [(ngModel)]="NewNoteDesc" #NoteName="ngModel" required />
          <small class="form-error--text"
            *ngIf="NoteName.errors?.required && NoteName.touched || (NoteName.pristine && AddNoteForm.submitted)">
            Note Name is Required
          </small>
        </div>
      </div>

      <div class="Ng-temp-Main">
        <div class="Ng-temp-Button">
          <div class="common-div">
            <button class="custom-btn" type="submit">Save</button>
          </div>
          <div class="common-div">
            <button class="custom-btn" (click)="modalRef.hide()">Close</button>
          </div>
        </div>
      </div>
    </form>
  </ng-template>

  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-success">Success!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="closeModel(sTemplate)">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>{{sSuccessMsg}}</p><br>
    </div>
  </ng-template>

  <ng-template #successNoteTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-success">Success!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Note Saved Successfully!</p>
    </div>
  </ng-template>

  <ng-template #errorTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-danger">Error!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Please try doing again!</p>
    </div>
  </ng-template>

  <ng-template #warningTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-warning">Warning!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="closeWarningTemplate()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <!-- <p>Please select supergroup and group, Before adding Notes!</p> -->
      <p> {{warningtext}}</p>
    </div>
  </ng-template>

  <ng-template #imgUpdlTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Upload Image</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>Only .png, jpg and .jpeg images are supported</p>
      <button class="btn btn-primary mr-4" (click)="uploadFile()">Upload</button>
      <button class="btn btn-warning" (click)="cancelUpload()">Cancel</button>
    </div>
  </ng-template>

  <ng-template #validationTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left" [class.text-success]="validationGood" [class.text-warning]="!validationGood">{{ validationTitle }}</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>{{ validationMsg }}</p>
    </div>
  </ng-template>

  <ng-template #advSettingsTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-info">Settings</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide(); showManageSuperGroups = false;">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <div class="setting-btn" (click)="showManageSuperGroups = true">Super Groups</div>
      <div class="setting-btn" *ngIf="!showManageSuperGroups">Groups</div>
      <div class="settings-table-super" *ngIf="showManageSuperGroups">
        <div class="setting" *ngFor="let supGrp of superGroup; let i = index;">
          <div class="group-name" [class.isSelected]="selectedSupGroup[i]" (click)="selectSuperGroupForSetting(i)">{{ supGrp.group_name }}</div>
        </div>
        <div class="group-btns">
          <div class="group-btn">Move Up</div>
          <div class="group-btn">Move Down</div>
        </div>
      </div>
    </div>
  </ng-template>
</div>