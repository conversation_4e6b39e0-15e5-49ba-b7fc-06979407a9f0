.videos-wrap {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .page-header {
    display: flex;
    height: 2.5rem;
    margin-bottom: 2em;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .videos {
    width: 100%;
    padding: 1em;
    background-color: #E4F9F5;
  
    .video-group-wrap {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 1em;
  
      .video {
        width: 100%;
        background-color: #fff;
        margin-bottom: 25px;
  
        .video-name--bar {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5em;
          background-color: #eeeeee;
  
          h6, p {
            margin: 0;
          }
        }
  
        .video-content {
          width: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-evenly;
          align-items: center;
          padding: 1em;
          background-color: #fff;
          position: relative;
  
          .thmb-wrap {
            width: 100%;
            height: 270px;
            background-color: #fff;
  
            img, iframe {
              width: 100%;
              height: 100%;
            } 
          }
        }
  
        div {
          display: flex;
          align-items: flex-start;
          justify-content: center;
  
          p, button {
            margin-bottom: 0;
            margin-left: 15px;
          }
  
          button {
            margin-top: 1em;
            padding: 0.5em 1em;
            color: #000;
            background-color: #eeeeee;
            border: 1px solid #0B6FB1;
            border-radius: 5px;
            transition: all 0.4s ease-out;
  
            &:hover {
              background-color: #E88224;
              transition: all 0.2s ease-in;
            }
          }
        }
      }
    }
  }
}

.copy-content {
  text-align: right;
  margin-top: 1em;

  p {
    color: #707070;
    margin: 0;
  }
}

@media (max-width: 440px) {
  .videos-wrap {
    .page-header {
      height: 3.5rem;
    }

    .videos {
      .video-group-wrap {
        grid-template-columns: 1fr;

        .video {

          .video-content {
            padding: 0;
            padding-bottom: 10px;

            .thmb-wrap {
              height: 150px;
            }

            div {
              flex-direction: column;
              align-items: center;

              button {
                margin-left: 0;
              }
            }
          }
        }
      }
    }
  }
}