import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { CompilerService } from '../../Services/compiler.service';

import * as $ from 'jquery';

@Component({
  selector: 'app-app-select',
  templateUrl: './app-select.component.html',
  styleUrls: ['./app-select.component.scss']
})
export class AppSelectComponent implements OnInit {

  public showIndicator: boolean;
  public selectedLang: string;
  public entryDenied: boolean;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              private compilerService: CompilerService) { }

  ngOnInit() {
    this.selectedLang = 'c';

    this.showIndicator = true;
    const email = sessionStorage.getItem('QMail');
    this.compilerService.getEntryForClient(email).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      if (resp.msg === 'Ok') {
        this.entryDenied = false;
      }

      this.showIndicator = false;
    }, error => {
      const resp = JSON.parse(JSON.stringify(error));

      if (resp.error.err === 'unp' || resp.error.err === 'sww') {
        this.entryDenied = true;
      }

      this.showIndicator = false;
    });
  }

  takeToApp($event: any, num: number) {

    const srcClass = $event.srcElement.classList[0];
    if (srcClass === 'extra-opts' || srcClass === 'extra-opts--wrap') {
      $( '.options-card' ).toggleClass('is-open');
    } else if (srcClass === 'options-bar' || srcClass === 'app-card' || srcClass === 'app-comp') {
      $( '.options-card' ).toggleClass('is-open');

      switch (num) {
        case 1:
          this.router.navigate(['compiler', this.selectedLang], { relativeTo: this.activatedRoute });
      }
    }
  }

  selectLanguage() {
    $( '.options-card' ).toggleClass('is-open');
    console.log(this.selectedLang);
  }

  openCtxMenu($event: any) {

    $event.preventDefault();

    if ($event.target === $( '.extra-opts--wrap' )) {

      console.log('hi :>> ', $event.target);
    }
  }
}
