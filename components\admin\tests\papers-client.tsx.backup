// components/admin/tests/papers-client.tsx
'use client'

import { useState, useCallback, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'sonner'
import {
  PaperType,
  Paper,
  FilterState,
  getPaperTypeConfig,
  Subtopic
} from '@/types/admin-types'
import { Group } from '@/types/chapter-paper-types'
import { TMCQSubGroup } from '@/types/technical-mcq-types'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import { AdminTopicsClientService } from '@/lib/client-services/admin/topics.client'
import PaperTypeSelector from './paper-type-selector'
import TopicSubtopicSelectors from './topic-subtopic-selectors'
import PapersList from './papers-list'
import CreatePaperModal from './create-paper-modal'
import LoadingIndicator from '@/components/shared/indicator'

interface AdminTestsPapersClientProps {
  initialPaperTypes: PaperType[]
}

// Separate loading states for different operations
interface LoadingStates {
  paperType: boolean
  topics: boolean
  subtopics: boolean
  papers: boolean
  actions: boolean
}

export default function AdminTestsPapersClient({
  initialPaperTypes
}: AdminTestsPapersClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Initialize state from URL params
  const [selectedPaperType, setSelectedPaperType] = useState<number | null>(
    searchParams.get('type') ? Number(searchParams.get('type')) : null
  )
  const [papers, setPapers] = useState<Paper[]>([])
  const [topics, setTopics] = useState<any[]>([])
  const [subtopics, setSubtopics] = useState<any[]>([])
  const [selectedTopic, setSelectedTopic] = useState<string>(
    searchParams.get('topic') || ''
  )
  const [selectedSubtopic, setSelectedSubtopic] = useState<string>(
    searchParams.get('subtopic') || ''
  )

  // Granular loading states
  const [loadingStates, setLoadingStates] = useState<LoadingStates>({
    paperType: false,
    topics: false,
    subtopics: false,
    papers: false,
    actions: false
  })

  const [showCreateModal, setShowCreateModal] = useState(false)

  // Helper to update specific loading state
  const setLoadingState = useCallback(
    (key: keyof LoadingStates, value: boolean) => {
      setLoadingStates((prev) => ({ ...prev, [key]: value }))
    },
    []
  )

  /**
   * Update URL with current filter state
   */
  const updateURL = useCallback(
    (newFilters: FilterState) => {
      const params = new URLSearchParams(searchParams)

      Object.entries(newFilters).forEach(([key, value]) => {
        if (value) {
          params.set(key, value)
        } else {
          params.delete(key)
        }
      })

      router.push(`/admin/tests/papers?${params.toString()}`)
    },
    [router, searchParams]
  )

  /**
   * Handle paper type selection
   */
  const handlePaperTypeSelect = useCallback(
    async (typeId: number) => {
      // Don't reload if same type is selected
      if (selectedPaperType === typeId) return

      setLoadingState('topics', true)
      setSelectedPaperType(typeId)

      // Clear dependent data
      setPapers([])
      setTopics([])
      setSubtopics([])
      setSelectedTopic('')
      setSelectedSubtopic('')

      const config = getPaperTypeConfig(typeId)

      try {
        // Store paper type in sessionStorage for navigation
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('paperType', typeId.toString())
          sessionStorage.setItem('paperState', 'Edit')
        }

        // Load topics if required
        if (config.hasTopicDropdown) {
          let topicsData = []

          switch (typeId) {
            case 1: // Chapter-Wise Papers
            case 6: // Chapter-Wise Practice
              topicsData = await AdminTopicsClientService.getSuperGroups()
              break
            case 11: // Technical MCQs
              topicsData = await AdminTopicsClientService.getTMCQGroups()
              break
            case 13: // Verbal Practice
              topicsData =
                await AdminTopicsClientService.getVerbalPracticeGroups()
              break
            case 14: // Technical Practice
              topicsData =
                await AdminTopicsClientService.getTechnicalPracticeGroups()
              break
          }

          setTopics(topicsData)
        } else {
          // Load papers directly if no topics required
          setLoadingState('papers', true)
          setLoadingState('topics', false)
          const papersData =
            await AdminPapersClientService.getPapersByType(typeId)
          setPapers(papersData)
          setLoadingState('papers', false)
        }

        updateURL({ type: typeId.toString(), topic: '', subtopic: '' })
      } catch (error) {
        console.error('Error selecting paper type:', error)
        toast.error('Failed to load paper type data')
      } finally {
        setLoadingState('topics', false)
      }
    },
    [selectedPaperType, updateURL, setLoadingState]
  )

  /**
   * Handle topic change
   */
  const handleTopicChange = useCallback(
    async (topicId: string) => {
      if (!selectedPaperType || selectedTopic === topicId) return

      setLoadingState('subtopics', true)
      setSelectedTopic(topicId)
      setSelectedSubtopic('')
      setSubtopics([])
      setPapers([])

      const config = getPaperTypeConfig(selectedPaperType)

      try {
        // Store topic in sessionStorage
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('QPaperGroup', topicId)
        }

        if (config.hasSubtopicDropdown && topicId) {
          // Load subtopics
          let subtopicsData: (Group | TMCQSubGroup | Subtopic)[] = []

          switch (selectedPaperType) {
            case 1: // Chapter-Wise Papers
            case 6: // Chapter-Wise Practice
              subtopicsData = await AdminTopicsClientService.getGroups(topicId)
              break
            case 11: // Technical MCQs
              subtopicsData =
                await AdminTopicsClientService.getTMCQSubGroups(topicId)
              break
          }

          setSubtopics(subtopicsData)
        } else if (topicId) {
          // Load papers directly
          setLoadingState('papers', true)
          setLoadingState('subtopics', false)
          const papersData = await AdminPapersClientService.getPapersByType(
            selectedPaperType,
            topicId
          )
          setPapers(papersData)
          setLoadingState('papers', false)
        }

        updateURL({
          type: selectedPaperType.toString(),
          topic: topicId,
          subtopic: ''
        })
      } catch (error) {
        console.error('Error loading topic data:', error)
        toast.error('Failed to load topic data')
      } finally {
        setLoadingState('subtopics', false)
      }
    },
    [selectedPaperType, selectedTopic, updateURL, setLoadingState]
  )

  /**
   * Handle subtopic change
   */
  const handleSubtopicChange = useCallback(
    async (subtopicId: string) => {
      if (
        !selectedPaperType ||
        !selectedTopic ||
        selectedSubtopic === subtopicId
      )
        return

      setLoadingState('papers', true)
      setSelectedSubtopic(subtopicId)

      try {
        // Store subtopic in sessionStorage
        if (typeof window !== 'undefined') {
          sessionStorage.setItem('QPaperSubGroup', subtopicId)
        }

        const papersData = await AdminPapersClientService.getPapersByType(
          selectedPaperType,
          selectedTopic,
          subtopicId
        )
        setPapers(papersData)

        updateURL({
          type: selectedPaperType.toString(),
          topic: selectedTopic,
          subtopic: subtopicId
        })
      } catch (error) {
        console.error('Error loading subtopic data:', error)
        toast.error('Failed to load papers')
      } finally {
        setLoadingState('papers', false)
      }
    },
    [
      selectedPaperType,
      selectedTopic,
      selectedSubtopic,
      updateURL,
      setLoadingState
    ]
  )

  /**
   * Handle paper edit
   */
  const handleEditPaper = useCallback(
    (paper: Paper) => {
      if (!selectedPaperType) return

      // Build query params for paper detail page
      const params = new URLSearchParams({
        type: selectedPaperType.toString(),
        name: paper.paper_name,
        show_ans: paper.show_ans || '0',
        time_lim: paper.time_lim || '0',
        no_of_ques: paper.no_of_ques || '0',
        status: paper.status || '0',
        once_ans: paper.once_ans || '0',
        neg_marks: paper.neg_marks || '0',
        rand_ques: paper.rand_ques || '0'
      })

      router.push(
        `/admin/tests/papers/detail/${paper.paper_id}?${params.toString()}`
      )
    },
    [selectedPaperType, router]
  )

  /**
   * Handle paper deletion
   */
  const handleDeletePaper = useCallback(
    async (paperId: string) => {
      if (!selectedPaperType) return

      const confirmed = window.confirm(
        'Are you sure you want to delete this paper? This action cannot be undone.'
      )
      if (!confirmed) return

      try {
        setLoadingState('actions', true)
        await AdminPapersClientService.deletePaper(paperId, selectedPaperType)

        // Refresh papers list
        setPapers((current) => current.filter((p) => p.paper_id !== paperId))
        toast.success('Paper deleted successfully')
      } catch (error) {
        console.error('Error deleting paper:', error)
        toast.error('Failed to delete paper')
      } finally {
        setLoadingState('actions', false)
      }
    },
    [selectedPaperType, setLoadingState]
  )

  /**
   * Handle paper status toggle
   */
  const handleToggleStatus = useCallback(
    async (paperId: string, newStatus: string) => {
      if (!selectedPaperType) return

      try {
        setLoadingState('actions', true)
        await AdminPapersClientService.togglePaperStatus(
          paperId,
          newStatus,
          selectedPaperType
        )

        // Update papers list
        setPapers((current) =>
          current.map((p) =>
            p.paper_id === paperId
              ? { ...p, status: newStatus === '1' ? '0' : '1' }
              : p
          )
        )

        toast.success(
          `Paper ${newStatus === '1' ? 'hidden' : 'shown'} successfully`
        )
      } catch (error) {
        console.error('Error toggling paper status:', error)
        toast.error('Failed to update paper status')
      } finally {
        setLoadingState('actions', false)
      }
    },
    [selectedPaperType, setLoadingState]
  )

  /**
   * Handle refresh cache for weekly competitive papers
   */
  const handleRefreshCache = useCallback(async () => {
    try {
      setLoadingState('actions', true)
      await AdminPapersClientService.refreshCompetitiveCache()
      toast.success('Cache refreshed successfully')
    } catch (error) {
      console.error('Error refreshing cache:', error)
      toast.error('Failed to refresh cache')
    } finally {
      setLoadingState('actions', false)
    }
  }, [setLoadingState])

  /**
   * Handle copy list URL for practice papers
   */
  const handleCopyListUrl = useCallback(() => {
    if (!selectedTopic) {
      toast.error('Please select a topic first')
      return
    }

    const baseUrl =
      selectedPaperType === 13
        ? 'https://verbal-notes.quantmasters.in'
        : 'https://tech-notes.quantmasters.in'

    const listUrl = `${baseUrl}/papers/${selectedTopic}`
    navigator.clipboard.writeText(listUrl)
    toast.success('URL copied to clipboard!')
  }, [selectedPaperType, selectedTopic])

  /**
   * Handle paper creation success
   */
  const handlePaperCreated = useCallback((newPaper: Paper) => {
    setPapers((current) => [newPaper, ...current])
    setShowCreateModal(false)
    toast.success('Paper created successfully')
  }, [])

  // Memoize the paper type config
  const currentPaperTypeConfig = useMemo(
    () => (selectedPaperType ? getPaperTypeConfig(selectedPaperType) : null),
    [selectedPaperType]
  )

  // Check if any loading state is active
  const isAnyLoading = Object.values(loadingStates).some((state) => state)

  return (
    <div className="admin-papers-container space-y-6">
      {/* Paper Type Selector - Never shows loading */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold mb-4">Select Paper Type</h2>
        <PaperTypeSelector
          paperTypes={initialPaperTypes}
          selected={selectedPaperType}
          onSelect={handlePaperTypeSelect}
        />
      </div>

      {/* Topic/Subtopic Selectors */}
      {selectedPaperType && currentPaperTypeConfig?.hasTopicDropdown && (
        <div className="bg-white rounded-lg shadow p-6 relative">
          {loadingStates.topics && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Loading topics..." />
            </div>
          )}
          {loadingStates.subtopics && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Loading subtopics..." />
            </div>
          )}
          <TopicSubtopicSelectors
            paperType={selectedPaperType}
            topics={topics}
            subtopics={subtopics}
            selectedTopic={selectedTopic}
            selectedSubtopic={selectedSubtopic}
            onTopicChange={handleTopicChange}
            onSubtopicChange={handleSubtopicChange}
            onTopicsUpdate={setTopics}
            onSubtopicsUpdate={setSubtopics}
          />
        </div>
      )}

      {/* Papers List */}
      {(papers.length > 0 || loadingStates.papers) && selectedPaperType && (
        <div className="bg-white rounded-lg shadow p-6 relative">
          {loadingStates.papers && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Loading papers..." />
            </div>
          )}
          {loadingStates.actions && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10 rounded-lg">
              <LoadingIndicator isLoading={true} text="Processing..." />
            </div>
          )}

          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold">Papers</h2>
            {currentPaperTypeConfig?.canCreate && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                disabled={isAnyLoading}
              >
                Create New Paper
              </button>
            )}
          </div>

          <PapersList
            papers={papers}
            paperType={selectedPaperType}
            onEdit={handleEditPaper}
            onDelete={handleDeletePaper}
            onToggleStatus={handleToggleStatus}
            onRefreshCache={handleRefreshCache}
            onCopyUrl={handleCopyListUrl}
          />
        </div>
      )}

      {/* Create Paper Modal */}
      <CreatePaperModal
        isOpen={showCreateModal}
        paperType={selectedPaperType}
        selectedTopic={selectedTopic}
        selectedSubtopic={selectedSubtopic}
        onClose={() => setShowCreateModal(false)}
        onSuccess={handlePaperCreated}
      />
    </div>
  )
}
