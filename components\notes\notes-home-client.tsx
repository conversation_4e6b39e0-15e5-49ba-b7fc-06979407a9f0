'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  Lock,
  ChevronRight,
  Folder,
  Folder<PERSON>pen,
  BookOpen,
  ArrowLeft,
  ArrowRight
} from 'lucide-react'
import { SuperGroup, NotesGroup, Notes, NotesData } from '@/types/notes-types'
import { NotesService } from '@/lib/client-services/notes-service.client'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card'

interface NotesHomeClientProps {
  initialGroups: NotesGroup[]
}

export default function NotesHomeClient({
  initialGroups
}: NotesHomeClientProps) {
  const router = useRouter()

  const [superGroups, setSuperGroups] = useState<SuperGroup[]>([])
  const [displayGroups, setDisplayGroups] = useState<NotesGroup[]>([])
  const [notesOfGroup, setNotesOfGroup] = useState<Notes[]>([])
  const [selectedSupGroup, setSelectedSupGroup] = useState('')
  const [selectedGroup, setSelectedGroup] = useState('')
  const [isLoadingNotes, setIsLoadingNotes] = useState(false)

  useEffect(() => {
    // Process initial groups to extract super groups
    const processedSuperGroups: SuperGroup[] = []
    const superGroupMap = new Map<string, SuperGroup>()

    initialGroups.forEach((group) => {
      if (!superGroupMap.has(group.super_id)) {
        const superGroup: SuperGroup = {
          super_id: group.super_id,
          super_name: group.super_name,
          super_seq: group.super_seq
        }
        superGroupMap.set(group.super_id, superGroup)
        processedSuperGroups.push(superGroup)
      }
    })

    // Sort super groups by sequence
    processedSuperGroups.sort((a, b) => {
      if (a.super_seq < b.super_seq) return -1
      if (a.super_seq > b.super_seq) return 1
      return 0
    })

    setSuperGroups(processedSuperGroups)

    // Check for stored notes data
    const storedNotesData = localStorage.getItem('QNotesData')
    if (storedNotesData) {
      const notesData: NotesData = JSON.parse(storedNotesData)
      onSelectSuperGroup(notesData.selectedSupGroup)
      onSelectGroup(notesData.selectedGroup)
      localStorage.removeItem('QNotesData')
    }
  }, [initialGroups])

  const onSelectSuperGroup = (supId: string) => {
    const filteredGroups = initialGroups.filter((x) => x.super_id === supId)

    // Sort groups by sequence
    filteredGroups.sort((a, b) => {
      if (a.group_seq < b.group_seq) return -1
      if (a.group_seq > b.group_seq) return 1
      return 0
    })

    setDisplayGroups(filteredGroups)
    setSelectedSupGroup(supId)
    setNotesOfGroup([])
    setSelectedGroup('')
  }

  const onSelectGroup = async (grpId: string) => {
    if (!grpId) return

    setSelectedGroup(grpId)
    setIsLoadingNotes(true)

    try {
      const notes = await NotesService.getNotesOfGroup(grpId)

      // Format dates
      const formattedNotes = notes.map((note) => ({
        ...note,
        posted_on: new Date(note.posted_on).toLocaleDateString()
      }))

      setNotesOfGroup(formattedNotes)
    } catch (error) {
      console.error('Error fetching notes:', error)
    } finally {
      setIsLoadingNotes(false)
    }
  }

  const takeToNotesDetail = (noteId: string, idx: number) => {
    const currNote = notesOfGroup[idx].notes_name.replace(/ /g, '-')
    const nextNote =
      idx !== notesOfGroup.length - 1 ? notesOfGroup[idx + 1] : null
    const prevNote = idx !== 0 ? notesOfGroup[idx - 1] : null

    const notesData: NotesData = {
      thisNoteId: notesOfGroup[idx].notes_id,
      selectedSupGroup,
      selectedGroup,
      nextNoteId: nextNote?.notes_id || '',
      nextNoteName: nextNote?.notes_name || '',
      prevNoteId: prevNote?.notes_id || '',
      prevNoteName: prevNote?.notes_name || ''
    }

    localStorage.setItem('QNotesData', JSON.stringify(notesData))
    router.push(`/dashboard/notes/read/${currNote}`)
  }

  const takeToPlans = () => {
    router.push('/plans')
  }

  return (
    <div>
      {/* Navigation */}
      {(selectedSupGroup || selectedGroup) && (
        <div className="breadcrumb mb-6 p-3 bg-gray-100 rounded-lg border">
          <div className="flex items-center text-sm text-gray-600">
            <span className="font-medium">You are here:</span>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span className="text-blue-600 font-medium">
              {superGroups.find((sg) => sg.super_id === selectedSupGroup)
                ?.super_name || 'Categories'}
            </span>
            {selectedGroup && (
              <>
                <ChevronRight className="h-4 w-4 mx-2" />
                <span className="text-gray-600 font-medium">
                  {
                    displayGroups.find((g) => g.group_id === selectedGroup)
                      ?.group_name
                  }
                </span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Super Groups Section */}
      <div className="super-groups mb-12">
        <div className="flex items-center mb-6 pb-2 border-b-2 border-blue-200">
          <Folder className="h-6 w-6 text-blue-600 mr-3" />
          <h4 className="text-2xl font-semibold text-gray-800">Categories</h4>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {superGroups.map((supGrp) => (
            <Card
              key={supGrp.super_id}
              className={`cursor-pointer transition-all hover:shadow-lg border-2 ${
                selectedSupGroup === supGrp.super_id
                  ? 'ring-2 ring-blue-400 border-blue-300 bg-blue-50'
                  : 'border-gray-200 hover:border-blue-300'
              }`}
              onClick={() => onSelectSuperGroup(supGrp.super_id)}
            >
              <CardHeader className="pb-4">
                <CardTitle className="text-center flex items-center justify-center">
                  {selectedSupGroup === supGrp.super_id ? (
                    <FolderOpen className="h-5 w-5 mr-2 text-blue-600" />
                  ) : (
                    <Folder className="h-5 w-5 mr-2 text-gray-600" />
                  )}
                  {supGrp.super_name}
                </CardTitle>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>

      {/* Subgroups Section */}
      {displayGroups.length > 0 && (
        <div className="groups mb-8">
          {/* Section Header with Back Navigation */}
          <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <BookOpen className="h-6 w-6 text-gray-600 mr-3" />
              <div>
                <h4 className="text-xl font-semibold text-gray-800">Topics</h4>
                <p className="text-sm text-gray-600">
                  Select a topic from{' '}
                  {
                    superGroups.find((sg) => sg.super_id === selectedSupGroup)
                      ?.super_name
                  }
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSelectedSupGroup('')
                setDisplayGroups([])
                setNotesOfGroup([])
                setSelectedGroup('')
              }}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Categories
            </Button>
          </div>

          {/* Subgroups Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {displayGroups.map((grp) => (
              <Card
                key={grp.group_id}
                className={`cursor-pointer transition-all hover:shadow-md border ${
                  selectedGroup === grp.group_id
                    ? 'ring-2 ring-blue-400 border-blue-300 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300'
                }`}
                onClick={() => onSelectGroup(grp.group_id)}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-center text-lg flex items-center justify-center">
                    <ChevronRight
                      className={`h-4 w-4 mr-2 transition-transform ${
                        selectedGroup === grp.group_id
                          ? 'rotate-90 text-blue-600'
                          : 'text-gray-500'
                      }`}
                    />
                    {grp.group_name}
                  </CardTitle>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Notes Section */}
      {notesOfGroup.length > 0 && (
        <div className="notes-wrap">
          {/* Notes Header */}
          <div className="flex items-center justify-between mb-6 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center">
              <BookOpen className="h-6 w-6 text-gray-600 mr-3" />
              <div>
                <h4 className="text-xl font-semibold text-gray-800">
                  Available Notes
                </h4>
                <p className="text-sm text-gray-600">
                  {notesOfGroup.length} note
                  {notesOfGroup.length !== 1 ? 's' : ''} in{' '}
                  {
                    displayGroups.find((g) => g.group_id === selectedGroup)
                      ?.group_name
                  }
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setNotesOfGroup([])
                setSelectedGroup('')
              }}
              className="flex items-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Topics
            </Button>
          </div>

          {/* Notes Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {notesOfGroup.map((note, i) => (
              <Card
                key={note.notes_id}
                className="h-full flex flex-col relative hover:shadow-lg transition-shadow border border-gray-200 hover:border-sky-300"
              >
                {note.public === 0 && (
                  <div
                    className="absolute top-2 right-2 z-10 cursor-pointer bg-yellow-500 text-white p-2 rounded-full shadow-md hover:bg-yellow-600 transition-colors"
                    onClick={takeToPlans}
                    title="Premium Feature"
                  >
                    <Lock className="h-4 w-4" />
                  </div>
                )}
                <CardHeader>
                  <CardTitle className="text-lg text-gray-800">
                    {note.notes_name}
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    Posted On: {note.posted_on}
                  </CardDescription>
                </CardHeader>
                <CardContent className="mt-auto">
                  <Button
                    onClick={() => takeToNotesDetail(note.notes_id, i)}
                    className="w-full"
                    disabled={note.public === 0}
                  >
                    Start Reading
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Empty state */}
      {selectedGroup && notesOfGroup.length === 0 && !isLoadingNotes && (
        <div className="text-center py-10">
          <p className="text-gray-600">
            No notes available for this group at the moment.
          </p>
        </div>
      )}

      {/* Loading state */}
      {isLoadingNotes && (
        <div className="text-center py-10">
          <p className="text-gray-600">Loading notes...</p>
        </div>
      )}
    </div>
  )
}
