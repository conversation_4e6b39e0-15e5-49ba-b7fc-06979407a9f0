import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { NewUser } from '../Models/NewUser';

@Injectable({
  providedIn: 'root'
})
export class RegisterService {
  protected regUrl      = 'https://api.quantmasters.in/user/new/add?type=qck&view_type=trial';
  protected emailUrl    = 'https://api.quantmasters.in/user/new/mail';
  protected workshopUrl = 'https://api.quantmasters.in/user/new/workshop';

  constructor(private http: HttpClient) { }

  registerUser(newUser: NewUser): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const userDetails = {
      f_name    : newUser.f_name,
      l_name    : newUser.l_name,
      email     : newUser.email,
      passwd    : newUser.password,
      qual      : newUser.qual,
      inst_name : newUser.inst_name,
      section   : newUser.section,
      usn       : newUser.usn,
      yop       : newUser.yop,
      dob       : newUser.dob,
      phone_no  : newUser.phone_no,
      branch    : newUser.branch,
      subscribed: newUser.subscribed
    };

    return this.http.post<string>(this.regUrl, userDetails, httpOps);
  }

  registerUserQuick(newUser: NewUser, viewType: string): Observable<string> {

    const regUrl = this.regUrl + '?type=qck&view_type=' + viewType;

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const userDetails = {
      email     : newUser.email,
      passwd    : newUser.password,
      phone_no  : newUser.phone_no
    };

    return this.http.post<string>(regUrl, userDetails, httpOps);
  }

  sendWorkshpRegistration(newUser: {}): Observable<string> {

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    return this.http.post<string>(this.workshopUrl, newUser, httpOps);
  }

  sendWorkshopEmail(newUser: {}): Observable<string> {

    const regUrl = this.emailUrl;

    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    return this.http.post<string>(regUrl, newUser, httpOps);
  }

  getCitiesOfState(state: string): Observable<string> {

    const url = 'https://indian-cities-api-nocbegfhqg.now.sh/cities?State=' + state;

    return this.http.get<string>(url);
  }
}
