import AchieversListComponent from '@/components/marketing/achievers-list'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Our Achievers | QuantMasters',
  description: 'Success stories and achievements of QuantMasters students'
}

export default function AchieversPage() {
  return (
    <div className="bg-white">
      {/* Hero section */}
      <div className="bg-gradient-to-r from-blue-800 to-blue-600 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl font-extrabold sm:text-5xl lg:text-6xl mb-6">
            Our Achievers
          </h1>
          <p className="text-xl max-w-3xl mx-auto">
            Meet the professionals who have transformed their careers with
            QuantMasters. Their success stories inspire us to continue our
            mission of creating exceptional quants.
          </p>
        </div>
      </div>

      {/* Stats section */}
      <div className="bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">95%</div>
              <p className="text-gray-700">
                Job placement rate within 6 months
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">42%</div>
              <p className="text-gray-700">
                Average salary increase after certification
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">500+</div>
              <p className="text-gray-700">
                Successful graduates working at top firms
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Achievers grid */}
      <AchieversListComponent />

      {/* CTA section */}
      <div className="bg-blue-900 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold mb-4">
            Join Our Community of Achievers
          </h2>
          <p className="text-xl mb-8">
            We help you make the perfect start to master your career
          </p>
          <div className="space-x-4">
            <a
              href="/user/login"
              className="bg-white text-blue-700 hover:bg-blue-50 px-6 py-3 rounded-md font-medium shadow-lg inline-block"
            >
              Enroll Now
            </a>
            <a
              href="/contact"
              className="bg-transparent border-2 border-sky-700 text-sky-700 hover:bg-white hover:text-blue-700 px-6 py-3 rounded-md font-medium inline-block"
            >
              Learn More
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
