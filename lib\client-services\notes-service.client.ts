// lib/client-services/notes-service.client.ts
import axios from 'axios'
import {
  NotesGroup,
  Notes,
  Comment,
  CommentSubmission
} from '@/types/notes-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  groupsUrl: `${API_BASE_URL}/v2/notes/groups`,
  notesUrl: `${API_BASE_URL}/v3/notes`,
  commentsUrl: `${API_BASE_URL}/v2/notes`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class NotesService {
  /**
   * Get super groups and groups
   */
  static async getSuperGroups(): Promise<NotesGroup[]> {
    try {
      const response = await axios.get(ENDPOINTS.groupsUrl, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get notes for a specific group
   */
  static async getNotesOfGroup(groupId: string): Promise<Notes[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.notesUrl}/${groupId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching notes for group ${groupId}:`, error)
      throw error
    }
  }

  /**
   * Get note detail for reading
   */
  static async getNotesForReading(noteId: string): Promise<Notes> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.notesUrl}/detail/${noteId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching note detail ${noteId}:`, error)
      throw error
    }
  }

  /**
   * Get comments for a note
   */
  static async getNotesComments(noteId: string): Promise<Comment[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.commentsUrl}/${noteId}/comments`
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching comments for note ${noteId}:`, error)
      throw error
    }
  }

  /**
   * Post a comment
   */
  static async postNoteComment(
    noteId: string,
    comment: CommentSubmission
  ): Promise<any> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.commentsUrl}/${noteId}/comment`,
        comment,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error posting comment:', error)
      throw error
    }
  }

  /**
   * Post a comment reply
   */
  static async postNoteCommentReply(
    noteId: string,
    commentId: string,
    reply: CommentSubmission
  ): Promise<any> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.commentsUrl}/${noteId}/comment/${commentId}/reply`,
        reply,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error posting comment reply:', error)
      throw error
    }
  }

  /**
   * Get user profile avatar
   */
  static async getUserProfileAvatar(email: string): Promise<{ path: string }> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/user/profile/avatar/${email}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching user avatar:', error)
      return { path: '/default-avatar.png' }
    }
  }
}
