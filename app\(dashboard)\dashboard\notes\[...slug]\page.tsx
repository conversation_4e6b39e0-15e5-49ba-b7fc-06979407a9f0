// app/notes/[...slug]/page.tsx
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import NotesDetailClientWrapper from './notes-detail-wrapper'
import Link from 'next/link'

interface NotesSlugPageProps {
  params: Promise<{
    slug: string[]
  }>
}

export async function generateMetadata({
  params
}: NotesSlugPageProps): Promise<Metadata> {
  const resolvedParams = await params
  const { slug } = resolvedParams

  // Handle different slug patterns
  if (slug.length >= 2 && slug[0] === 'read') {
    const noteName = decodeURIComponent(slug[1]).replace(/-/g, ' ')
    return {
      title: `${noteName} | Study Notes | Quant Masters`,
      description: `Read comprehensive study notes on ${noteName}`
    }
  }

  return {
    title: 'Study Notes | Quant Masters',
    description: 'Access comprehensive study materials and notes'
  }
}

export default async function NotesSlugPage({ params }: NotesSlugPageProps) {
  const resolvedParams = await params
  const { slug } = resolvedParams

  // Handle routing pattern: /notes/read/[noteName]
  // The noteId will be retrieved from localStorage (NotesData) on the client side

  if (slug.length < 2 || slug[0] !== 'read') {
    notFound()
  }

  try {
    // Since the noteId is stored in localStorage from the notes home page,
    // we'll create a client component that handles retrieving the noteId
    // and fetching the note details.

    return <NotesDetailClientWrapper />
  } catch (error) {
    console.error('Error loading note detail:', error)

    // Return error state
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Note Not Found
          </h1>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <p className="text-yellow-800 mb-4">
              The requested note could not be found or you don&apos;t have
              permission to access it.
            </p>
            <Link
              href="/notes"
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 inline-block"
            >
              Back to Notes
            </Link>
          </div>
        </div>
      </div>
    )
  }
}
