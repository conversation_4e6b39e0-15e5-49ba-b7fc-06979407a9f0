import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { environment } from 'src/environments/environment.prod';

import { Subscription } from 'rxjs';

import * as $ from 'jquery';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { NewUser } from 'src/app/Models/NewUser';
import { RegisterService } from 'src/app/Services/register.service';
import { WindowRef } from 'src/app/Services/window-ref.service';
import { PaymentService } from 'src/app/Services/payment.service';


import { CitiesJson } from '../../../assets/cities.json';

declare let fbq: Function;
declare let gtag: Function;

@Component({
  selector: 'app-recorded-landing',
  templateUrl: './recorded-landing.component.html',
  styleUrls: ['./recorded-landing.component.scss'],
  encapsulation: ViewEncapsulation.None,
  providers: [WindowRef]
})
export class RecordedLandingComponent implements OnInit {

  @ViewChild('regTemplate') public regTemplate: TemplateRef<any>;
  @ViewChild('buyTemplate') public buyTemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public sucTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public failTemplate: TemplateRef<any>;

  public subscriptions: Subscription[] = [];
  public modalRef: BsModalRef;
  public showIndicator = false;
  public modalConfig = {
    animated: true,
    keyboard: false,
    backdrop: true,
    ignoreBackdropClick: true,
    class: 'reg-modal'
  };

  public dark_theme = false;

  public selectedModules = [false, false, false, false, false];
  public selectedContentIdx = 0;
  public modeulesContent = [[
    'Quantitative aptitude',
    'Logical reasoning',
    'Verbal reasoning',
    'Analytical reasoning',
  ], [
    'Basics of programming',
    'Pattern programming',
    'C (from Basics)',
    'C++',
    'Java',
    'Python (from basics)',
    'Data structures (from basics)'
  ], [
    'Communication skills',
    'Presentation skills',
    'Body language',
    'Do\'s and Don\'t'
  ], [
    'In depth training',
    'Resume template & formatting',
    'Personalised resume creation',
    '1-1 Resume review & analysis',
    'Feedbacks & improvisations',
    'Final updated resume',
  ], [
    'Specialised HRs from various MNCs',
    'Complete assessment of candidate personality',
    'Marks awarded on important personality parameters',
    'Detailed feedback report',
    'What to answer?, How to answer?',
    'Handling trap questions',
    'Situation & puzzle based questions',
    'Recording of interview for introspection & improvement',
  ]];
  public moreModsContent = [
    [],
    [
      'Operating systems',
      'Computer networks',
      'Cloud computing',
      'Cloud security',
      'Snippets',
      'Pseudocodes',
      'Competitive coding for product based companies'
    ], [
      'Mock HR interview',
      'Group discussions',
      'Resume writing',
      'LinkedIn profile and network building',
    ]
  ];
  public evenMoreModsContent = [
    [],
    [
      'Company specific coding',
      'Automata',
      'Solutions to previous year papers',
      '<span>Live</span> practice coding sessions',
      'MCQs & preparations materials',
      '<span>Live</span> doubt clearning sessions',
      'Technical training from scratch',
    ],
    []
  ];
  public courseImages = [
    '../../../../assets/icons/workshop/apti-dude.svg',
    '../../../../assets/icons/workshop/technical.svg',
    '../../../../assets/icons/workshop/interview.svg',
    '../../../../assets/icons/workshop/resume-guy.svg',
    '../../../../assets/icons/workshop/hr-dude.svg',
  ];

  public itemsPerSlide = 3;
  public slides2 = [
    {image: '../../assets/testimonials/carousel/car(1).jpeg'},
    {image: '../../assets/testimonials/carousel/car(1).png'},
    {image: '../../assets/testimonials/carousel/car(2).jpeg'},
    {image: '../../assets/testimonials/carousel/car(2).png'},
    {image: '../../assets/testimonials/carousel/car(3).jpeg'},
    {image: '../../assets/testimonials/carousel/car(3).png'}
  ];

  public blurOnEntry = true;

  public noCreateAccount = false;
  public conf_password = '';

  public selectedPlan: number;
  public selectedDesc: string;
  public selectedAmount: number;
  public selContactNum: string;
  public selContactInfo: string;
  public selWhatsappInfo: string;

  public orderId: string;

  public newUser = {
    email: '',
    password: '',
    f_name: '',
    l_name: '',
    dob: '',
    phone_no: '',
    inst_name: '',
    qual: '',
    usn: '',
    yop: '',
    subscribed: true,
    semester: 0,
    college: '',
    branch: '',
    state: '',
    city: ''
  };

  public allStates = [
    'Andhra Pradesh',
    'Arunachal Pradesh',
    'Assam',
    'Bihar',
    'Chhattisgarh',
    'Goa',
    'Gujarat',
    'Haryana',
    'Himachal Pradesh',
    'Jharkhand',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'Maharashtra',
    'Manipur',
    'Meghalaya',
    'Mizoram',
    'Nagaland',
    'Odisha',
    'Punjab',
    'Rajasthan',
    'Sikkim',
    'Tamil Nadu',
    'Telangana',
    'Tripura',
    'Uttar Pradesh',
    'Uttarakhand',
    'West Bengal',
    'Andaman and Nicobar Islands',
    'Chandigarh',
    'Dadra & Nagar Haveli and Daman & Diu',
    'Delhi',
    'Jammu and Kashmir',
    'Lakshadweep',
    'Puducherry',
    'Ladakh'
  ];

  public cities = [];

  public faqs = [
    {
      question: 'Who is eligible to join this course?'
    }, 
    {
      question: 'I am from a non-technical background and have no knowledge of coding. Will this course be helpful for me?'
    }
  ];
  public isCollapsed = [true, true, true, true, true, true, true, true, true, true];

  constructor(
    public router: Router,
    public registerService: RegisterService,
    public paymentService: PaymentService,
    private winRef: WindowRef,
    public modalService: BsModalService) {

      this.router.events.subscribe((y: NavigationEnd) => {
        if (y instanceof NavigationEnd) {
          gtag('config', 'UA-163800914-1', { 'page_path': y.url });

          fbq('track', 'PageView', {
            value: 0.1,
            currency: 'INR',
          });
        }
      });
  }

  ngOnInit() {
    if (window.outerWidth < 440) {
      this.itemsPerSlide = 1;
    }

    this.selectedModules[0] = true;

    // Promise.resolve(null).then(() => this.openModal(this.regTemplate));
    this.blurOnEntry = false;
    // this.openModal(this.sucTemplate);

    this.paymentService.getCampaignContact().subscribe(contactResponse => {
      const resText = JSON.parse(JSON.stringify(contactResponse));

      this.selContactNum = resText.phone;
      this.selContactInfo = resText.name + ', ' + resText.desig;
      this.selWhatsappInfo = resText.whatsapp;

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;

      this.selContactNum = '+91 ************';
      this.selContactInfo = 'Himanshu Sharma, Founder, Managing Director, Quant Masters';
      this.selWhatsappInfo = '919900001452';
    });

    $(window).scroll(() => {
      if ($(window).width() < 450) {
        const whatsappButtonTop = $('.qm-contact--box').offset().top;
        const footerTop = $('.ftr').offset().top;

        const disappStyles = {
          transition: 'all 0.2s ease-out',
          opacity: 0
        };

        const reappStyles = {
          transition: 'all 0.2s ease-in',
          opacity: 1
        };

        const whatsappButton = $('.qm-contact--box');

        if (whatsappButtonTop > footerTop) {
          whatsappButton.css(disappStyles);
        } else if (whatsappButtonTop < footerTop) {
          whatsappButton.css(reappStyles);
        }
      }
    });
  }

  openRegForm(num: number) {

    if (num === 1) {
      this.selectedPlan   = 1;
      this.selectedDesc   = '500+ Hours Recorded Placement Training Program';
      this.selectedAmount = 2599;
    } else {
      this.selectedPlan   = 4;
      this.selectedDesc   = '500+ Hours Recorded Placement Training Program';
      this.selectedAmount = 2499;
    }

    this.openModal(this.regTemplate);
    // this.openModal(this.buyTemplate);
    // this.openModal(this.sucTemplate);
  }

  openModuleDetails(idx: number) {

    this.selectedModules.fill(false);
    this.selectedModules[idx] = true;
    this.selectedContentIdx = idx;
  }

  scrollToView() {
    $([document.documentElement, document.body]).animate({
      scrollTop: $('#stats-area').offset().top - 20
    }, 700);
  }

  registerQuick(isValid: boolean) {
    if (!isValid) {
      return;
    }

    const newUser = new NewUser();
    newUser.f_name = this.newUser.f_name;
    newUser.l_name = this.newUser.l_name;
    newUser.email = this.newUser.email;
    newUser.password = this.newUser.password;
    newUser.inst_name = this.newUser.college;

    const newRegUser = {
      workshop_id: 'aa8afa43-6106-3928-957b-8cd54c530709',
      name: this.selectedDesc,
      email: this.newUser.email,
      f_name: this.newUser.f_name,
      l_name: this.newUser.l_name,
      phone_no: this.newUser.phone_no.toString(),
      semester: this.newUser.semester,
      branch: this.newUser.branch,
      college: this.newUser.college,
      state: this.newUser.state,
      city: this.newUser.city
    };

    this.showIndicator = true;
    this.modalRef.hide();
    this.registerService.sendWorkshpRegistration(newRegUser).subscribe(resp => {
      const respT = JSON.parse(JSON.stringify(resp));

      if (respT.text === 'rec added') {
        this.openModal(this.buyTemplate);
        this.showIndicator = false;
        this.blurOnEntry = false;
      } else {
        this.openModal(this.failTemplate);
      }
    }, error => {
      this.openModal(this.failTemplate);
      this.showIndicator = false;
    });
  }

  selectCities() {
    this.showIndicator = true;

    const cities = [];
    for (const city of CitiesJson) {
      if (city.state === this.newUser.state) {
        cities.push(city);
      }
    }

    this.cities = cities;
  }

  onCheckout() {

    const that = this;
    this.showIndicator = true;
    this.paymentService.getOrderId(1999 * 100).subscribe(orderResponse => {
      this.orderId = orderResponse;

      fbq('track', 'InitiateCheckout', {
        value: 0.2,
        currency: 'INR',
      });

      const options: any = {
        'key': environment.razorpayKey, // Enter the Key ID generated from the Dashboard
        'amount':  this.selectedAmount * 100, // '250000',
        'currency': 'INR',
        'name': 'Quant Masters',
        'description': this.selectedDesc,
        'image': 'https://quantmasters.in/assets/QM_Logo_No_Text.svg',
        'order_id': that.orderId,
        'modal': {
          'escape': false
        },
        'prefill': {
          'name': that.newUser.f_name + ' ' + that.newUser.l_name,
          'email': that.newUser.email,
          'contact': that.newUser.phone_no
        }
      };

      options.handler = ((paymentResponse) => {
        options['payment_response_id'] = paymentResponse.razorpay_payment_id;

        that.paymentService.confirmPayment(paymentResponse.razorpay_payment_id, options.amount).subscribe(ConfirmResponse => {
          const resp = JSON.parse(JSON.stringify(ConfirmResponse));

          if (resp.msg === 'Captured' || resp.msg === 'Already Captured') {
            that.modalRef.hide();

            fbq('track', 'Purchase', {
              value: 2950,
              currency: 'INR',
            });

            that.paymentService.sendMailPostPaymentInRecorded(
              that.newUser.email,
              that.newUser.f_name,
              that.newUser.l_name,
              that.selectedPlan)
            .subscribe(response => {

              that.openModal(that.sucTemplate);
              that.showIndicator = false;
            }, error => {
              that.showIndicator = false;
            });
          }
        }, error => {
          const resp = JSON.parse(JSON.stringify(error));
          that.showIndicator = false;
          console.log(resp.msg);
        });
      });

      const rzp = new that.winRef.nativeWindow.Razorpay(options);
      rzp.open();
    }, error => {
      that.showIndicator = false;
    });
  }

  sendToWhatsapp() {
    const whatsAppUrl = 'https://wa.me/' + this.selWhatsappInfo;

    window.open(whatsAppUrl, '_blank');
  }

  switchTheme() {

    this.dark_theme = !this.dark_theme;

    const toggleCssDark = {
      'transform': 'translateX(100%)',
      'background-color': '#34323D'
    };

    const toggleCssNorm = {
      'transform': 'translateX(0)',
      'color': '#fff',
      'background-color': '#F49820'
    };

    if (this.dark_theme) {
      $('.toggle').css(toggleCssDark);
      $('.landing-2-wrap').addClass('dark-theme');
      $('body').css({ 'background-color': '#26242E' });

      this.modalConfig.class = 'reg-modal dark-theme';
    } else {
      $('.toggle').css(toggleCssNorm);
      $('.landing-2-wrap').removeClass('dark-theme');
      $('body').css({ 'background-color': '#F8F9F9' });

      this.modalConfig.class = 'reg-modal';
    }
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }
}
