export interface ChapterPaper {
  paper_id: string
  paper_name: string
  group_id: string
  sub_group_id: string
  level: string
  no_of_ques: string
  status: string
  show_ans: string
  public: string
  once_ans: string
  created_at: string
  time_lim: number
  type: number
  neg_marks: string
  rand_ques: string
}

export interface TMCQSubGroup {
  group_id: string
  sub_group_id: string
  sub_group_name: string
}

export interface TMCQGroup {
  group_id: string
  group_name: string
}

export interface Question {
  paper_id: string
  ques_no: string
  question: string
  opt_1: string
  opt_2: string
  opt_3: string
  opt_4: string
  opt_5: string
  correct_opt: string
  bAnswered: boolean
  selected_ans: number
  options: string[]
  explanation: string
  img_expln: string
  question_no: string
  original_qno: string
}

export interface PaperDetails {
  paper_id: string
  paper_name: string
  paper_desc: string
  status: number
  time_lim: number
  created_at: string
  show_ans: string
  once_ans: string
  public: string
  no_of_questions: string
  neg_marks: string
  rand_ques: string
}
