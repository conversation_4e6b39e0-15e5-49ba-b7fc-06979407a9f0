'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { AuthService } from '@/lib/auth'
import { LoginRefresh } from '@/lib/cookies'
import { User, AuthUserResponse } from '@/types/user'

export const useAuth = () => {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState<boolean>(true)

  // Check if the user is authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      const isLoggedIn = LoginRefresh.fnCheckUserLoginStatus()
      setIsAuthenticated(isLoggedIn)

      if (isLoggedIn && typeof window !== 'undefined') {
        setUser({
          email: localStorage.getItem('QMA_USR') || '',
          name: localStorage.getItem('QMA_NAME') || '',
          complete: localStorage.getItem('QMA_COMPLETE') || '',
          subscribed: localStorage.getItem('QMA_SUBSCRIBED') || ''
        })
      }

      setLoading(false)
    }

    checkAuth()
  }, [])

  // Login function
  const login = async (email: string, password: string): Promise<boolean> => {
    setLoading(true)

    try {
      const response = await AuthService.loginUser({ email, passwd: password })

      if (response) {
        LoginRefresh.fnSetCookiesAndSession(
          response as AuthUserResponse,
          email,
          response.user || '',
          response.complete || '1',
          response.key || Math.random().toString(36).substring(7)
        )

        setIsAuthenticated(true)
        setUser({
          email,
          name: response.user || '',
          complete: response.complete || '',
          subscribed: response.subscribed || ''
        })

        return true
      }

      return false
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Logout function
  const logout = async (): Promise<boolean> => {
    setLoading(true)

    try {
      const email = localStorage.getItem('QMA_USR') || ''

      if (email) {
        await AuthService.logoutUser(email)
      }

      LoginRefresh.fnClearCookiesAndSession()
      setIsAuthenticated(false)
      setUser(null)

      router.push('/user/login')
      return true
    } catch (error) {
      console.error('Logout error:', error)
      return false
    } finally {
      setLoading(false)
    }
  }

  // Refresh token function
  const refreshToken = async (): Promise<boolean> => {
    if (!isAuthenticated) return false

    try {
      const email = localStorage.getItem('QMA_USR') || ''

      if (email) {
        const response = await AuthService.refreshLogin(email)

        if (response) {
          LoginRefresh.fnSetCookiesAndSession(
            response as AuthUserResponse,
            email,
            response.user || localStorage.getItem('QMA_NAME') || '',
            response.complete || localStorage.getItem('QMA_COMPLETE') || '1',
            response.key || localStorage.getItem('QMA_KEY') || ''
          )

          return true
        }
      }

      return false
    } catch (error) {
      console.error('Token refresh error:', error)
      return false
    }
  }

  return {
    isAuthenticated,
    user,
    loading,
    login,
    logout,
    refreshToken
  }
}
