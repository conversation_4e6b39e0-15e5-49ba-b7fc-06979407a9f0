import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { NotesHomeComponent } from './notes-home/notes-home.component';
import { NotesDetailComponent } from './notes-detail/notes-detail.component';

const routes: Routes = [
  {
    path: '',
    component: NotesHomeComponent
  },
  {
    path: 'read/:noteName',
    component: NotesDetailComponent
  },
  {
    path: 'read/:noteName/:noteId',
    component: NotesDetailComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class NotesAppRoutingModule { }
