// app/notes/[...slug]/notes-detail-wrapper.tsx
'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { NotesData } from '@/types/notes-types'
import NotesDetailClient from '@/components/notes/notes-detail-client'
import Link from 'next/link'

export default function NotesDetailClientWrapper() {
  const router = useRouter()
  const [noteId, setNoteId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Get noteId from localStorage (set by notes home page)
    const storedNotesData = localStorage.getItem('QNotesData')

    if (storedNotesData) {
      try {
        const notesData: NotesData = JSON.parse(storedNotesData)
        setNoteId(notesData.thisNoteId)
      } catch (error) {
        console.error('Error parsing notes data:', error)
        router.push('/notes')
        return
      }
    } else {
      // If no stored data, redirect to notes home
      router.push('/notes')
      return
    }

    setIsLoading(false)
  }, [router])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="text-center">Loading note...</div>
      </div>
    )
  }

  if (!noteId) {
    return (
      <div className="container mx-auto py-8 px-4 text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Note Not Found
        </h1>
        <p className="text-gray-600 mb-4">
          Unable to load the requested note. Please select a note from the notes
          list.
        </p>
        <Link
          href="/notes"
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 inline-block"
        >
          Back to Notes
        </Link>
      </div>
    )
  }

  return <NotesDetailClient initialNote={null} noteId={noteId} />
}
