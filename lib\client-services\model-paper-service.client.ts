// lib/client-services/tests-service.client.ts

import axios from 'axios'
import { Paper, AnswerSubmission } from '@/types/model-paper-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  papersUrl: `${API_BASE_URL}/test/question/ret/papers`,
  questionsUrl: `${API_BASE_URL}/test/question/ret`,
  submitUrl: `${API_BASE_URL}/test/answer/submit`,
  indResultsAllUrl: `${API_BASE_URL}/view/results/ind/all`,
  indResultsUrl: `${API_BASE_URL}/view/results/ind/bypaper`,
  allResultsUrl: `${API_BASE_URL}/view/results/all`,
  uploadExplUrl: `${API_BASE_URL}/test/model/paper/`,
  openResultsUrl: `${API_BASE_URL}/v2/test/open`,
  createPaperUrl: `${API_BASE_URL}/admin/paper/upload/test`,
  createQuestionUrl: `${API_BASE_URL}/admin/paper/upload/question`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class TestsService {
  /**
   * Get all papers
   */
  static async getPapers(): Promise<Paper[]> {
    try {
      const response = await axios.get(ENDPOINTS.papersUrl, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching papers:', error)
      return []
    }
  }

  /**
   * Get questions for a specific paper
   */
  static async getQuestions(paperId: string): Promise<any> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.questionsUrl}/${paperId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching questions for paper ${paperId}:`, error)
      throw error
    }
  }

  /**
   * Submit answers
   */
  static async submitAnswers(submission: AnswerSubmission): Promise<any> {
    try {
      const response = await axios.post(
        ENDPOINTS.submitUrl,
        submission,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error submitting answers:', error)
      throw error
    }
  }

  /**
   * Get individual results for a user
   */
  static async getIndividualResults(email: string): Promise<any> {
    const data = { email }

    try {
      const response = await axios.post(
        ENDPOINTS.indResultsAllUrl,
        data,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching individual results:', error)
      throw error
    }
  }

  /**
   * Get individual result for a specific paper
   */
  static async getIndividualResult(
    email: string,
    paperId: string
  ): Promise<any> {
    const data = { email, paper_id: paperId }

    try {
      const response = await axios.post(
        ENDPOINTS.indResultsUrl,
        data,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching individual result:', error)
      throw error
    }
  }

  /**
   * Get all results
   */
  static async getAllResults(): Promise<any> {
    try {
      const response = await axios.get(
        ENDPOINTS.allResultsUrl,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching all results:', error)
      throw error
    }
  }

  /**
   * Get all open results for a user
   */
  static async getAllOpenResults(email: string): Promise<any> {
    const url = `${ENDPOINTS.openResultsUrl}/papers/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching open results:', error)
      throw error
    }
  }

  /**
   * Get open paper section-wise marks
   */
  static async getOpenPaperSectionWiseMarks(
    email: string,
    answerId: string
  ): Promise<any> {
    const url = `${ENDPOINTS.openResultsUrl}/papers/${email}/section/${answerId}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching section-wise marks:', error)
      throw error
    }
  }

  /**
   * Get all open company results
   */
  static async getAllOpenCompanyResults(email: string): Promise<any> {
    const url = `${ENDPOINTS.openResultsUrl}/company/papers/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching company results:', error)
      throw error
    }
  }

  /**
   * Get all weekly competitive results
   */
  static async getAllWeeklyCompetitiveResults(email: string): Promise<any> {
    const url = `${ENDPOINTS.openResultsUrl}/competitive/${email}/marks`

    try {
      const response = await axios.get(url, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching competitive results:', error)
      throw error
    }
  }

  /**
   * Add explanation to a question
   */
  static async addExplanation(
    paperId: string,
    quesNo: string,
    explanation: string
  ): Promise<any> {
    const url = `${ENDPOINTS.uploadExplUrl}${paperId}/explanation`
    const reqBody = {
      question_no: quesNo,
      explanation
    }

    try {
      const response = await axios.post(url, reqBody, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error adding explanation:', error)
      throw error
    }
  }

  /**
   * Update paper details
   */
  static async updatePaperDetails(paper: Paper): Promise<any> {
    try {
      const response = await axios.put(
        ENDPOINTS.createPaperUrl,
        paper,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating paper details:', error)
      throw error
    }
  }

  /**
   * Update question details
   */
  static async updateQuestionDetails(question: object): Promise<any> {
    try {
      const response = await axios.put(
        ENDPOINTS.createQuestionUrl,
        question,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating question details:', error)
      throw error
    }
  }
}
