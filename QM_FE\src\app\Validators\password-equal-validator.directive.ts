import { Directive, Input } from '@angular/core';
import { Validator, NG_VALIDATORS, AbstractControl, FormGroup, ValidatorFn, ValidationErrors } from '@angular/forms';

export const passwordEqualValidator: ValidatorFn = (control: FormGroup): ValidationErrors | null => {
  const pass1 = control.get('password');
  const pass2 = control.get('conf_password');

  return pass1 && pass2 && pass1.value !== pass2.value ? {'notEqual' : true} : null;
};

@Directive({
  selector: '[appPasswordEqualValidator]',
  providers: [{ provide: NG_VALIDATORS, useExisting: PasswordEqualValidatorDirective, multi: true }]
})
export class PasswordEqualValidatorDirective implements Validator {

  validate(control: AbstractControl): ValidationErrors {
    return passwordEqualValidator(control);
  }
}
