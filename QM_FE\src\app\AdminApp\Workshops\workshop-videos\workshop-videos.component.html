<div class="workshop-videos--wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="container-fluid">
    <div class="row">
      <h5>Manage Workshop Videos</h5>
    </div>
    <div class="row">
      <nav aria-label="breadcrumb" *ngIf="selectedGroup != ''">
        <ol class="breadcrumb">
          <li class="breadcrumb-item active" aria-current="page">Group: {{ selectedGroup }}</li>
        </ol>
      </nav>
      <nav aria-label="breadcrumb" *ngIf="selectedSubGroup != ''">
        <ol class="breadcrumb">
          <li class="breadcrumb-item active" aria-current="page">Sub-Group: {{ selectedSubGroup }}</li>
        </ol>
      </nav>
    </div>
    <div class="row">
      <div class="col">
        <div class="dropdown" dropdown>
          <button class="btn btn-secondary dropdown-toggle" type="button" dropdownToggle aria-controls="dropdown-basic">
            Select Group
          </button>
          <ul id="dropdown-basic" *dropdownMenu class="dropdown-menu" role="menu" aria-labelledby="button-basic">
            <li role="menuitem" *ngFor="let group of groups" (click)="selectGroup(group.group_id, group.group_name)"><a class="dropdown-item">{{ group.group_name }}</a></li>
          </ul>
        </div>
      </div>
      <div class="col" *ngIf="subGroups">
        <div class="row">
          <div class="dropdown" dropdown>
            <button class="btn btn-secondary dropdown-toggle" type="button" dropdownToggle aria-controls="dropdown-basic">
              Select Sub Group
            </button>
            <ul id="dropdown-basic" *dropdownMenu class="dropdown-menu" role="menu" aria-labelledby="button-basic">
              <li role="menuitem" *ngFor="let subGroup of subGroups" (click)="selectSubGroup(subGroup.sub_group_id, subGroup.sub_group_name)"><a class="dropdown-item">{{ subGroup.sub_group_name }}</a></li>
            </ul>
          </div>
          <div class="ml-3">
            <div class="btn-group" role="group" aria-label="Basic example">
              <button type="button" class="btn btn-success">Create</button>
              <button type="button" class="btn btn-info">Edit</button>
              <button type="button" class="btn btn-danger">Delete</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>