<div class="test-container">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="header">
    <div class="title">
      <p>{{ paperName }}</p>
      <div class="time-box" *ngIf="testType != 6">
        <p>Time Remaining</p>
        <h5 class="timeRem"></h5>
      </div>
    </div>
  </div>
  <div class="test-content" [class.blur-bg]="blurQuestions">
    <div class="test-area" *ngIf="testType === 1">
      <div *ngFor="let qn of questions; let i = index" class="question">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.ques_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{ qn.ques_no }})** {{ qn.question }}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt, 'ans-correct': qn.correct_opt == idx+1}">
            <input type="radio" name="{{qn.ques_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx+1;"><markdown>{{ opt }}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt">Selected Option is wrong</small>
              <br />
          </div>
        </div>
        <button *ngIf="bPaperSubmitted || showAllExpl" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <div *ngIf="qn.img_expln == '0'" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 2">
      <div *ngFor="let qn of competitiveQuestions; let i = index" class="question">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}} **({{ qn.question_origin }})**</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div
            [ngClass]="{'option':bPaperSubmitted,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt, 'ans-correct': qn.correct_opt == idx+1}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx+1;"><label>{{opt}}</label>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="bPaperSubmitted" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <markdown>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</markdown>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 3">
      <div *ngFor="let qn of companyQuestions; let i = index" class="question">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <p>**{{qn.question_no}})** {{qn.question}}</p>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div
            [ngClass]="{'option':bPaperSubmitted,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt, 'ans-correct': qn.correct_opt == idx+1}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx+1;"><label>{{opt}}</label>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="bPaperSubmitted" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <markdown>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</markdown>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 4">
      <div *ngFor="let qn of modelQuestions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.type == 0 && data.question_no == qn.question_no" [data]="data.value"></markdown>
          <markdown *ngIf="data.type == 1 && data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div [attr.data-index]="idx+1"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}"
              (click)="qn.bAnswered = true; qn.selected_ans = idx+1;"><label>{{opt}}</label>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <markdown [data]="qn.explanation || 'No explanation available'"></markdown>
          <!--<div mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>-->
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 5 || testType === 7">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <div *ngIf="qn.img_expln == '0' && qn.explanation.indexOf('$$') != 0">
            <markdown [data]="qn.explanation || 'No Explanation Available'"></markdown>
          </div>
          <div *ngIf="qn.img_expln == '0' && qn.explanation.indexOf('$$') == 0" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 6">
      <div *ngFor="let qn of questions; let i = index" class="question">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.type == 0 && data.question_no == qn.ques_no" [data]="data.value"></markdown>
          <markdown *ngIf="data.type == 1 && data.question_no == qn.ques_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.ques_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':!isCollapsed[i], 'ans-correct': qn.correct_opt == idx+1}">
            <b>{{ questionTexts[idx] }}</b><markdown>{{opt}}</markdown>
            <small *ngIf="!isCollapsed[i] && qn.correct_opt == idx + 1">Correct Option</small>
            <br />
          </div>
        </div>
        <button (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Answer</button>
        <div class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <div *ngIf="qn.img_expln == '0'" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 8">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="(randQues && (data.question_no == qn.original_qno))
                            || (!randQues && (data.question_no == qn.question_no))" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <div *ngIf="qn.img_expln == '0'" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 9">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
					<markdown [data]="qn.explanation || 'No Explanation Available'"></markdown>
          <!-- <div *ngIf="qn.img_expln == '0'" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div> -->
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 10">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div *ngIf="bPaperSubmitted || showAllExpl" class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <div *ngIf="qn.img_expln == '0'" mathjax>{{ qn.explanation !== "" ? qn.explanation : "Explanation Not Available" }}</div>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 11">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <markdown [data]="qn.explanation || 'No explanation available'"></markdown>
        </div>
      </div>
    </div>
    <div class="test-area" *ngIf="testType === 13 || testType === 14">
      <div *ngFor="let qn of questions; let i = index" class="question" id="{{ 'qm-q--' + (i + 1) }}">
        <div class="instr-text" *ngFor="let data of additionData">
          <markdown *ngIf="data.question_no == qn.question_no" [data]="data.value"></markdown>
        </div>
        <markdown>**{{qn.question_no}})** {{qn.question}}</markdown>
        <div *ngFor="let opt of qn.options; let idx = index" class="options-wrap">
          <div class="option-wrap--2" *ngIf="opt"
            [ngClass]="{'option':bPaperSubmitted && showAns,'ans-wrong': qn.selected_ans == idx +1 && qn.selected_ans !== qn.correct_opt && showAns, 'ans-correct': qn.correct_opt == idx+1 && showAns}">
            <input type="radio" name="{{qn.question_no}}" value="{{opt}}" [disabled]="bPaperSubmitted"
              (click)="qn.bAnswered = true; qn.selected_ans = idx + 1;"><markdown>{{opt}}</markdown>
              <small *ngIf="bPaperSubmitted && qn.correct_opt == idx + 1 && showAns">Correct Option</small>
              <small *ngIf="bPaperSubmitted && qn.selected_ans == idx + 1 && qn.selected_ans != qn.correct_opt && showAns">Selected Option</small>
              <br />
          </div>
        </div>
        <button *ngIf="(bPaperSubmitted || showAllExpl) && showAns" (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]" [attr.aria-expanded]="!isCollapsed"
              aria-controls="collapseAnswer">Show Explanation</button>
        <div class="qn-explanation" id="collapseAnswer" [collapse]="isCollapsed[i]" [isAnimated]="true">
          <div *ngIf="qn.img_expln == '1'">
            <img src="{{ qn.explanation }}" alt="Image Explanation" />
          </div>
          <markdown [data]="qn.explanation || 'No explanation available'"></markdown>
        </div>
      </div>
    </div>
  </div>
  <div class="tracking-wrap" *ngIf="testType != 6">
    <button *ngIf="isSuperUser" (click)="showAllExpl = !showAllExpl">Show All Explanations</button>
    <div class="time-box-2" *ngIf="showPopoutTimer">
      <p>Time Remaining</p>
      <h5 class="timeRem"></h5>
    </div>
    <div class="tracking" *ngIf="testType === 1">
      <div *ngFor="let qn of questions" class="item">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.ques_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 2">
      <div *ngFor="let qn of competitiveQuestions" class="item">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 3">
      <div *ngFor="let qn of companyQuestions" class="item">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 4">
      <div *ngFor="let qn of modelQuestions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 5 || testType === 7">
      <div *ngFor="let qn of questions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 8">
      <div *ngFor="let qn of questions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 9">
      <div *ngFor="let qn of questions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 10">
      <div *ngFor="let qn of questions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking" *ngIf="testType === 11">
      <div *ngFor="let qn of questions" class="item" (click)="scrollToQuestion(qn.question_no)">
        <p class="item-inner" [ngClass]="{'answered': qn.bAnswered}">{{qn.question_no}}</p>
      </div>
    </div>
    <div class="tracking-legend">
      <div class="legend">
        <div></div>
        <p>Unanswered</p>
      </div>
      <div class="legend">
        <div></div>
        <p>Answered</p>
      </div>
      <p *ngIf="bPaperSubmitted">Your marks: <b>{{ marks }} / {{ negMarks ? noQues * 2 : noQues }}</b></p>
      <div class="section-marks--wrap" *ngIf="bPaperSubmitted && sectionTrack && sectionTrack.length > 0">
        <p>Section Wise Breakdown</p>
        <p *ngFor="let sectionData of sectionTrack">{{ sectionData.section_name }}: <b>{{ sectionData.marks }}</b></p>
      </div>
    </div>
  </div>
  <div class="footer">
    <button *ngIf="testType != 6" (click)="evaluateAnswers(false)" [disabled]="bPaperSubmitted">Submit</button>
  </div>
  <ng-template #confTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left">Before we begin...</h4>
    </div>
    <div class="modal-body text-center">
      <p>You have {{ paperLim }} mins to finish this test.</p>
      <p *ngIf="!showAns">Answers are not displayed for this paper.</p>
      <p *ngIf="negMarks"><b>Marking Scheme:</b></p>
      <p *ngIf="negMarks">+2 for every Correct Answer</p>
      <p *ngIf="negMarks">-1 for every Wrong Answer</p>
      <p>Press begin to start</p>
      <button type="button" class="btn btn-primary" (click)="startTest()">Begin</button>
      <button type="button" class="btn btn-danger" (click)="goBack()" style="margin-left: 15px">No, not yet</button>
    </div>
  </ng-template>
  <ng-template #warnTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Wait a minute!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body text-center">
      <p>Looks like you've not answered all the questions.</p>
      <p>Do you want to submit anyway?</p>
      <button type="button" class="btn btn-info" (click)="confirm()">Yes</button>
      <button type="button" class="btn btn-primary" (click)="modalRef.hide()" style="margin-left: 15px">No</button>
    </div>
  </ng-template>
  <ng-template #marksTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Answers Submitted!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>You've scored <b>{{ marks }}</b> out of <b>{{ negMarks ? noQues * 2 : noQues }}</b> in this test.</p>
      <p *ngIf="sectionTrack && sectionTrack.length > 0" class="table-heading">Section Wise Breakdown</p>
      <div class="breakdown-table" *ngIf="sectionTrack && sectionTrack.length > 0">
        <div class="headers">
          <p>Section</p>
          <p>Marks</p>
        </div>
        <div *ngFor="let sectionData of sectionTrack">
          <p>{{ sectionData.section_name }}</p>
          <b>{{ sectionData.marks }}</b>
        </div>
      </div>
    </div>
  </ng-template>
</div>
<div class="copy-content">
  <p>&copy; 2022 Quant Masters. All Rights Reserved.</p>
</div>
