// types/model-paper-types.ts

export interface Paper {
  paper_id: string
  paper_name: string
  paper_desc: string
  status: number
  time_lim: number
  created_at: string
  show_ans: string
  once_ans: string
  public: string
  no_of_ques: number
}

export interface AnswerSubmission {
  email: string
  paper_id: string
  marks: number
  answered_on: string
}

export interface Question {
  question_id: string
  question_text: string
  options: string[]
  correct_answer: number
  explanation?: string
}
