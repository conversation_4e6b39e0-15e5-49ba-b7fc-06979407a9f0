'use client'

import Link from 'next/link'
import { useState } from 'react'
import { usePathname } from 'next/navigation'
import Image from 'next/image'
import { useAuth } from '@/lib/hooks/useAuth'
import { UserAccountNav } from './user-account-nav'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { ChevronDownIcon, Bell, Search, Menu } from 'lucide-react'
import { buttonVariants } from '../ui/button'

export default function Navigation() {
  const { isAuthenticated } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const pathname = usePathname()

  // Check if current route is in an admin or dashboard area
  const isSpecialRoute =
    pathname.includes('/admin') ||
    pathname.includes('/dashboard') ||
    pathname.includes('/technical')

  // Handle menu toggle for mobile
  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  // Don't show navigation for special routes that have their own navigation
  if (isSpecialRoute) return null

  // Define navigation links for easier modification
  const navLinks = [
    { href: '/', label: 'Home' },
    { href: '/achievers', label: 'Our Achievers' }
  ]

  // Define dropdown menus
  const dropdownMenus = [
    {
      label: 'Resources',
      items: [
        { href: '/technical/apps', label: 'Technical' },
        // { href: '/notes', label: 'Notes' },
        {
          href: 'https://placements.quantmasters.in',
          label: 'Placements',
          external: true
        },
        { href: '/certification/view', label: 'Certification' }
      ]
    },
    {
      label: 'Products',
      items: [
        { href: '/dashboard', label: 'Dashboard' },
        {
          href: 'https://firebasestorage.googleapis.com/v0/b/portal-quant-masters.appspot.com/o/quant-masters-desktop.exe?alt=media&token=b2fac42c-0f23-49a3-abed-78a5015138e6',
          label: 'Download Desktop App',
          external: true
        },
        {
          href: 'https://vamsikrishna-vk.medium.com/how-to-install-quant-masters-desktop-application-on-windows-e2affb43c09d',
          label: 'Install Guide',
          external: true
        }
      ]
    }
  ]

  // Render nav link
  const renderNavLink = (link: { href: string; label: string }) => (
    <Link
      key={link.label}
      href={link.href}
      className={`px-3 py-2 rounded-lg text-md font-medium transition-all duration-200 ${
        pathname === link.href
          ? 'text-sky-700 bg-gradient-to-r from-sky-50 to-blue-50'
          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      {link.label}
    </Link>
  )

  // Render dropdown menu
  const renderDropdownMenu = (
    menu: {
      label: string
      items: { href: string; label: string; external?: boolean }[]
    },
    index: number
  ) => (
    <DropdownMenu key={menu.label + index}>
      <DropdownMenuTrigger className="px-3 py-2 rounded-lg text-md font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none transition-all duration-200">
        <p className="inline-flex items-center">
          {menu.label} <ChevronDownIcon className="ml-1 h-4 w-4" />
        </p>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="start"
        className="bg-white rounded-xl shadow-lg  border-0 p-1"
      >
        {menu.items.map((item) => (
          <DropdownMenuItem
            key={item.label}
            className="rounded-lg hover:bg-gray-100 px-3 py-2"
          >
            {item.external ? (
              <a
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className="block w-full text-md text-gray-700 hover:text-gray-900"
              >
                {item.label}
              </a>
            ) : (
              <Link
                href={item.href}
                className="block w-full text-md text-gray-700 hover:text-gray-900"
              >
                {item.label}
              </Link>
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )

  // Render mobile nav item
  const renderMobileNavItem = (link: { href: string; label: string }) => (
    <Link
      key={link.label}
      href={link.href}
      className={`block px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${
        pathname === link.href
          ? 'text-sky-700 bg-gradient-to-r from-sky-50 to-blue-50'
          : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
      }`}
    >
      {link.label}
    </Link>
  )

  return (
    <nav className="fixed z-30 w-full bg-gray-50">
      <div className="py-3 px-3 lg:px-5 lg:pl-3">
        <div className="flex justify-between items-center">
          <div className="flex justify-start items-center">
            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="p-2 mr-2 text-gray-600 rounded cursor-pointer lg:hidden hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 focus:ring-2 focus:ring-gray-100"
            >
              <Menu className="w-6 h-6" />
            </button>

            <Link
              href="/"
              className="text-md font-semibold flex items-center lg:mr-1.5"
            >
              <Image
                src="/img/logo.png"
                alt="QuantMasters"
                width={150}
                height={40}
                className="mr-2 h-8 w-auto"
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-2">
            {/* Regular nav links */}
            {navLinks.map(renderNavLink)}

            {/* Dropdown menus */}
            {dropdownMenus.map(renderDropdownMenu)}
          </div>

          <div className="flex items-center">
            {/* Notifications */}
            {isAuthenticated && (
              <button
                type="button"
                className="p-2 text-gray-500 rounded-2xl hover:text-gray-900 hover:bg-gray-100"
              >
                <Bell className="w-5 h-5" />
              </button>
            )}

            {/* Login or User Account */}
            {!isAuthenticated ? (
              <Link
                href="/user/login"
                className={buttonVariants({ variant: 'outline' })}
              >
                Login
              </Link>
            ) : (
              <div className="ml-3">
                <UserAccountNav />
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {/* Regular nav links */}
            {navLinks.map(renderMobileNavItem)}

            {/* Dropdown menus */}
            {dropdownMenus.map((menu, index) => (
              <MobileDropdown key={menu.label} menu={menu} index={index} />
            ))}
          </div>
        </div>
      )}
    </nav>
  )
}

function MobileDropdown({
  menu,
  index
}: {
  menu: {
    label: string
    items: { href: string; label: string; external?: boolean }[]
  }
  index: number
}) {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()

  return (
    <div>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 rounded-lg text-base font-medium text-gray-700 hover:bg-gray-100"
      >
        {menu.label}
        <ChevronDownIcon
          className={`ml-1 h-4 w-4 transition-transform ${index} ${isOpen ? 'rotate-180' : ''}`}
        />
      </button>
      {isOpen && (
        <div className="pl-4 mt-1 space-y-1">
          {menu.items.map((item) =>
            item.external ? (
              <a
                key={item.label}
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className="block px-3 py-2 rounded-lg text-md font-medium text-gray-700 hover:bg-gray-100"
              >
                {item.label}
              </a>
            ) : (
              <Link
                key={item.label}
                href={item.href}
                className={`block px-3 py-2 rounded-lg text-md font-medium ${
                  pathname === item.href
                    ? 'text-sky-700 bg-gradient-to-r from-sky-50 to-blue-50'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                {item.label}
              </Link>
            )
          )}
        </div>
      )}
    </div>
  )
}
