import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { toast } from 'sonner'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'
import LoadingIndicator from '@/components/shared/indicator'

interface CreateGroupModalProps {
  isOpen: boolean
  modalType: 'group' | 'subgroup'
  paperType: number
  groups?: any[] // For subgroup creation
  onClose: () => void
  onSuccess: (newItem: any) => void
}

export default function CreateGroupModal({
  isOpen,
  modalType,
  paperType,
  groups = [],
  onClose,
  onSuccess
}: CreateGroupModalProps) {
  const [name, setName] = useState('')
  const [selectedGroupId, setSelectedGroupId] = useState('')
  const [loading, setLoading] = useState(false)

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setName('')
      setSelectedGroupId('')
    }
  }, [isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!name.trim()) {
      toast.error(`Please enter a ${modalType} name`)
      return
    }

    if (modalType === 'subgroup' && !selectedGroupId) {
      toast.error('Please select a group')
      return
    }

    try {
      setLoading(true)

      let result
      if (modalType === 'group') {
        result = await AdminPapersClientService.createGroup(name, paperType)
      } else {
        result = await AdminPapersClientService.createSubGroup(
          selectedGroupId,
          name
        )
      }

      onSuccess(result)
      toast.success(
        `${modalType === 'group' ? 'Group' : 'SubGroup'} created successfully`
      )
      onClose()
    } catch (error) {
      console.error(`Error creating ${modalType}:`, error)
      toast.error(`Failed to create ${modalType}`)
    } finally {
      setLoading(false)
    }
  }

  const getTitle = () => {
    if (modalType === 'group') {
      return paperType === 11 ? 'Create New Topic' : 'Create New Group'
    }
    return 'Create New SubTopic'
  }

  const getDescription = () => {
    if (modalType === 'group') {
      return paperType === 11
        ? 'Create a new topic for Technical MCQ papers'
        : 'Create a new group for practice papers'
    }
    return 'Create a new subtopic under the selected topic'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {modalType === 'subgroup' && (
            <div>
              <Label htmlFor="group">Select Group/Topic *</Label>
              <Select
                value={selectedGroupId}
                onValueChange={setSelectedGroupId}
              >
                <SelectTrigger id="group">
                  <SelectValue placeholder="Select a group" />
                </SelectTrigger>
                <SelectContent>
                  {groups.map((group) => (
                    <SelectItem key={group.group_id} value={group.group_id}>
                      {group.group_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label htmlFor="name">
              {modalType === 'group'
                ? paperType === 11
                  ? 'Topic Name'
                  : 'Group Name'
                : 'SubTopic Name'}{' '}
              *
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder={`Enter ${modalType} name`}
              disabled={loading}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading && <LoadingIndicator isLoading={loading} />}
              Create
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
