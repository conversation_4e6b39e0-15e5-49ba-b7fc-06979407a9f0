import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { User } from '../Models/User';

@Injectable({
  providedIn: 'root'
})
export class LoginService {
  private loginUrl   =  'https://api.quantmasters.in/user/login';
  private lougoutUrl = 'https://api.quantmasters.in/user/logout';
  private refreshUrl = 'https://api.quantmasters.in/login/';
  private checkUrl   = 'https://api.quantmasters.in/user/manage/';
  private checAdmUrl = 'https://api.quantmasters.in/admin/manage/';
  private baseUrl    = 'https://api.quantmasters.in/'

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  loginUser(user: User): Observable<string> {
    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const data: User = user;

    return this.http.post<string>(this.loginUrl, data, httpOpts);
  }

  refreshUserLogin(email: string): Observable<string> {

    const url = this.refreshUrl + email + '/refresh';

    const httpOpts = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  checkUserLogin(email: string): Observable<string> {
    this.setSecurityToken();

    const url = this.checkUrl + email + '/perm/check';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  checkAdminLogin(email: string): Observable<string> {
    this.setSecurityToken();

    const url = this.checAdmUrl + email + '/perm/check';

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(url, httpOpts);
  }

  logoutUser(email: string): Observable<string> {
    const httpOps = {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
      })
    };

    const data = '{"email": "' + email + '"}';

    return this.http.post<string>(this.lougoutUrl, data, httpOps);
  }

  refreshLogin(email: string): Observable<any> {
    this.setSecurityToken();

    // const url = this.baseUrl + email + '/perm/check'; /user/{email}/refresh
    const url = `${this.baseUrl}user/${email}/refresh`;

    const httpOpts = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<any>(url, httpOpts);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
