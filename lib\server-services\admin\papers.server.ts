// lib/server-services/admin/papers.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { Paper } from '@/types/admin-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value
  return !!(token && email)
}

export class AdminPapersServerService {
  /**
   * Get papers by type and optional topic/subtopic
   */
  static async getPapersByType(
    type: number,
    topicId?: string,
    subtopicId?: string
  ): Promise<Paper[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      let url = ''

      switch (type) {
        case 1: // Chapter-Wise Papers
          if (!subtopicId) return []
          // Use the progression API to match Angular implementation
          url = `${API_BASE_URL}/test/progression/group/${subtopicId}`
          break
        case 4: // Mock Papers
          url = `${API_BASE_URL}/test/question/ret/papers`
          break
        case 5: // Trial Papers
          url = `${API_BASE_URL}/test/open/papers`
          break
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/company/papers`
          break
        case 6: // Chapter-Wise Practice
          if (!topicId) return []
          url = `${API_BASE_URL}/test/progression/practice/group/${topicId}`
          break
        case 8: // Weekly Competitive
          url = `${V2_BASE_URL}/admin/test/open/competitive/papers`
          break
        case 9: // Section-Wise Papers
          url = `${V2_BASE_URL}/test/sectionWise/papers`
          break
        case 11: // Technical MCQs
          if (!subtopicId) return []
          // Use v3 API to match Angular implementation
          url = `${API_BASE_URL}/v3/admin/tmcq/papers/${subtopicId}`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          if (!topicId) return []
          // Use competitive group API to match Angular implementation
          url = `${API_BASE_URL}/test/competitive/group/${topicId}`
          break
        default:
          return []
      }

      const response = await axios.get(url, createAuthHeaders(token))
      return response.data || []
    } catch (error) {
      console.error(`Error fetching papers for type ${type}:`, error)
      return []
    }
  }

  /**
   * Update paper status
   */
  static async updatePaperStatus(
    paperId: string,
    status: string,
    type: number
  ): Promise<void> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      let url = ''
      const data = { status }

      switch (type) {
        case 5: // Trial Papers
          url = `${V2_BASE_URL}/admin/test/open/paper/${paperId}`
          break
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/company/paper/${paperId}`
          break
        case 8: // Weekly Competitive
          url = `${V2_BASE_URL}/admin/test/competitive/weekly/${paperId}`
          break
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper`
          Object.assign(data, { paper_id: paperId })
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          url = `${V2_BASE_URL}/admin/test/competitive/practice/paper`
          Object.assign(data, { paper_id: paperId })
          break
        default:
          throw new Error(`Status update not supported for paper type ${type}`)
      }

      await axios.put(url, data, createAuthHeaders(token))
    } catch (error) {
      console.error(`Error updating paper status for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Delete paper
   */
  static async deletePaper(paperId: string, type: number): Promise<void> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      let url = ''

      switch (type) {
        case 5: // Trial Papers
        case 7: // Company Papers
          url = `${API_BASE_URL}/admin/paper/open/update/${paperId}`
          break
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper/${paperId}`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          url = `${V2_BASE_URL}/admin/test/competitive/practice/paper/${paperId}`
          break
        default:
          throw new Error(`Delete not supported for paper type ${type}`)
      }

      await axios.delete(url, createAuthHeaders(token))
    } catch (error) {
      console.error(`Error deleting paper for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Get paper details for editing
   */
  static async getPaperDetails(paperId: string): Promise<Paper | null> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    try {
      // This would need to be implemented based on the actual API
      // For now, return null as the client will build from search params
      console.log(`Fetching details for paper ID: ${paperId}`)
      return null
    } catch (error) {
      console.error('Error fetching paper details:', error)
      return null
    }
  }

  /**
   * Get paper questions for editing
   */
  static async getPaperQuestions(
    paperId: string,
    paperType?: number
  ): Promise<any[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    try {
      const headers = await createAuthHeaders(await getServerSideToken())
      let url = ''

      // Use different endpoints based on paper type matching Angular patterns
      if (paperType) {
        switch (paperType) {
          case 1: // Chapter-wise
          case 5: // Trial Papers
          case 7: // Company Papers
          case 8: // Weekly Competitive
            url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
            break
          case 11: // Technical MCQs
            url = `${API_BASE_URL}/v3/admin/tmcq/paper/${paperId}`
            break
          case 13: // Verbal Practice
          case 14: // Technical Practice
            url = `${V2_BASE_URL}/test/competitive/practice/paper/${paperId}`
            break
          default:
            url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
        }
      } else {
        url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
      }

      const response = await fetch(url, headers)
      if (!response.ok) {
        throw new Error(`Failed to fetch questions: ${response.statusText}`)
      }

      const data = await response.json()
      return data?.questions || []
    } catch (error) {
      console.error('Error fetching paper questions:', error)
      return []
    }
  }
}
