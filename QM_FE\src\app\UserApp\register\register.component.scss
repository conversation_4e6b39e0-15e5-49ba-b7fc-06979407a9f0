.register-wrap {
  width: 100%;
  padding-top: 1em;
  font-family: "Montserrat", sans-serif;

  .reg-card {
    display: flex;
    align-items: center;
    width: 85%;
    margin: 2em auto;
    background-color: rgba(11, 111, 177, 0.1);
    border-radius: 15px;
    box-shadow: 0px 3px 20px rgba(17, 153, 158, 0.16);

    .left-sect,
    .right-sect {
      height: 100%;
      width: 50%;
    }

    .left-sect {
      background-color: #fff;

      .reg-heading {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        margin-top: 2em;
        padding: 0 3em;
        border: none;
        border-radius: 5px;

        .card-title-cust {
          font: normal 35px "Montserrat", sans-serif;
          margin: 0.3em 0;
        }
      }

      form {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        width: 100%;

        .elem-custom-cta {
          display: flex;
          align-items: center;
        }

        .form-row--1 {
          display: flex;
          justify-content: space-between;
          width: 90%;
          margin-top: 1em;

          .form-elem {
            position: relative;
            width: 50%;
          }

          svg {
            position: absolute;
            top: 20%;
            left: 80%;
            width: 30px;
            height: 30px;
          }
        }

        .form-row--2 {
          display: flex;
          width: 90%;
          margin-top: 1em;

          .form-elem {
            width: 100%;

            input {
              width: 97.5%;
            }
          }

          i {
            font-style: normal;
            font-weight: bold;
            color: #0b6fb1;
          }
        }

        .form-row--3 {
          display: flex;
          justify-content: space-between;
          width: 89%;
          margin-top: 1em;

          & .form-elem {
            width: 100%;
          }
        }

        input,
        select {
          display: block;
          height: 50px;
          width: 95%;
          padding: 3px 5px;
          border: solid 1.5px #707070;
          border-radius: 5px;
          transition: all 0.3s ease;
          font-size: 0.8em;

          &:focus {
            border: solid 1.5px #0b6fb1;
            transition: all 0.3s ease;
          }

          &:focus + .placeholder-text {
            top: -75px;
            font-size: 13px;
            transition: all 0.3s ease;
          }
        }

        img {
          height: 30px;
          position: relative;
          top: -50%;
          left: 80%;
        }

        input::placeholder 

        .placeholder-text {
          position: relative;
          top: -56px;
          left: 10px;
          padding: 3px;
          font-size: 17px;
          background-color: #fff;
          transition: all 0.4s ease;
        }

        .custom-btn {
          width: 100%;
          height: 40px;
          background-color: #e88224;
          border: none;
          border-radius: 4px;
          color: #fff;
          margin-bottom: 3em;
          padding: 8px;
          box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
        }

        .custom-btn:active:after {
          transition: 0s;
          opacity: 0.7;
          clip-path: circle(0% at 0% 0%);
        }

        .custom-btn::after {
          content: "";
          display: block;
          position: relative;
          top: -32px;
          left: -8px;
          height: 40px;
          width: 150px;
          background-color: #e88224;
          opacity: 0;
          clip-path: circle(150% at 0% 0%);
          transition: all 0.4s ease-in;
        }

        .clear-btn {
          background-color: #f25252;
        }

        #passwordHelpBlock,
        #emailHelpBlock {
          margin-top: 10px;
          font-size: 80%;
          color: #6c757d;
          width: 90%;
        }

        .form-error--text {
          font-size: 80%;
          color: #dc3545;
        }

        .is-inv {
          background-color: #dc3545;
        }

        .hidden-xs-up {
          display: none;
        }

        .cbx {
          position: relative;
          top: 1px;
          width: 20px;
          height: 20px;
          border: 1px solid #c8ccd4;
          border-radius: 3px;
          vertical-align: middle;
          transition: background 0.1s ease;
          cursor: pointer;
        }

        .cbx:after {
          content: "";
          position: absolute;
          top: 1px;
          left: 5px;
          width: 8px;
          height: 14px;
          opacity: 0;
          transform: rotate(45deg) scale(0);
          border-right: 2px solid #fff;
          border-bottom: 2px solid #fff;
          transition: all 0.3s ease;
          transition-delay: 0.15s;
        }

        .lbl {
          margin-left: 10px;
          vertical-align: middle;
          font-size: 16px;
          width: 95%;
          cursor: pointer;
        }

        #cbx:checked ~ .cbx {
          border-color: transparent;
          background: #e88224;
          animation: jelly 0.6s ease;
        }

        #cbx:checked ~ .cbx:after {
          opacity: 1;
          transform: rotate(45deg) scale(1);
        }

        .cntr {
          position: absolute;
          top: 50%;
          left: 0;
          width: 100%;
          text-align: center;
        }

        @-moz-keyframes jelly {
          from {
            transform: scale(1, 1);
          }
          30% {
            transform: scale(1.25, 0.75);
          }
          40% {
            transform: scale(0.75, 1.25);
          }
          50% {
            transform: scale(1.15, 0.85);
          }
          65% {
            transform: scale(0.95, 1.05);
          }
          75% {
            transform: scale(1.05, 0.95);
          }
          to {
            transform: scale(1, 1);
          }
        }

        @-webkit-keyframes jelly {
          from {
            transform: scale(1, 1);
          }
          30% {
            transform: scale(1.25, 0.75);
          }
          40% {
            transform: scale(0.75, 1.25);
          }
          50% {
            transform: scale(1.15, 0.85);
          }
          65% {
            transform: scale(0.95, 1.05);
          }
          75% {
            transform: scale(1.05, 0.95);
          }
          to {
            transform: scale(1, 1);
          }
        }

        @-o-keyframes jelly {
          from {
            transform: scale(1, 1);
          }
          30% {
            transform: scale(1.25, 0.75);
          }
          40% {
            transform: scale(0.75, 1.25);
          }
          50% {
            transform: scale(1.15, 0.85);
          }
          65% {
            transform: scale(0.95, 1.05);
          }
          75% {
            transform: scale(1.05, 0.95);
          }
          to {
            transform: scale(1, 1);
          }
        }

        @keyframes jelly {
          from {
            transform: scale(1, 1);
          }
          30% {
            transform: scale(1.25, 0.75);
          }
          40% {
            transform: scale(0.75, 1.25);
          }
          50% {
            transform: scale(1.15, 0.85);
          }
          65% {
            transform: scale(0.95, 1.05);
          }
          75% {
            transform: scale(1.05, 0.95);
          }
          to {
            transform: scale(1, 1);
          }
        }
      }
    }

    .right-sect {
      svg {
        display: block;
        width: 90%;
        height: 80%;
        margin: auto;
      }
    }
  }
}

@media (max-width: 440px) {
  .register-wrap {
    .reg-card {
      flex-direction: column;
      margin-top: 1em;
      width: 95%;
      .reg-heading {
        padding: 0 1em !important;
        margin-top: 0.5em;
        .card-title-cust {
          font: normal 20px "Montserrat", sans-serif !important;
        }
      }
      .left-sect,
      .right-sect {
        width: 100%;
      }

      .left-sect {
        form {
          width: 100%;

          .lbl {
            width: 91%;
          }

          .form-row--1 {
            display: flex;
            flex-direction: column;
            .form-elem {
              padding-top: 5px;
              width: 100%;
              input {
                width: 100%;
              }

              img {
                height: 40px;
                top: -50.5%;
              }
            }
            svg {
              left: 88%;
              top: 15px;
            }
          }
          .form-row--2 {
            .form-elem {
              width: 100%;
              input {
                width: 100%;
              }
            }
          }
          .form-row--3 {
            display: flex;
            flex-direction: column;
            .form-elem {
              padding-top: 5px;
              width: 100%;
              input {
                width: 100%;
              }
              select {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
