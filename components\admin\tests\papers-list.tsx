// components/admin/tests/papers-list.tsx
import { useState, memo, useMemo } from 'react'
import {
  Edit,
  Trash,
  Eye,
  EyeOff,
  FileText,
  RefreshCw,
  Copy,
  Search
} from 'lucide-react'
import { Paper, getPaperTypeConfig } from '@/types/admin-types'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'

interface PapersListProps {
  papers: Paper[]
  paperType: number | null
  onEdit: (paper: Paper) => void
  onDelete: (paperId: string) => void
  onToggleStatus: (paperId: string, newStatus: string) => void
  onRefreshCache?: () => void
  onCopyUrl?: () => void
}

const PapersList = memo(
  function PapersList({
    papers,
    paperType,
    onEdit,
    onDelete,
    onToggleStatus,
    onRefreshCache,
    onCopyUrl
  }: PapersListProps) {
    const [currentPage, setCurrentPage] = useState(1)
    const [searchQuery, setSearchQuery] = useState('')
    const itemsPerPage = 10

    // Filter papers based on search query
    const filteredPapers = useMemo(() => {
      if (!searchQuery.trim()) return papers

      return papers.filter((paper) =>
        paper.paper_name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }, [papers, searchQuery])

    // Reset to first page when search changes
    useMemo(() => {
      setCurrentPage(1)
    }, [searchQuery])

    if (!paperType) return null

    const config = getPaperTypeConfig(paperType)

    const paginatedPapers = filteredPapers.slice(
      (currentPage - 1) * itemsPerPage,
      currentPage * itemsPerPage
    )

    const totalPages = Math.ceil(filteredPapers.length / itemsPerPage)

    const formatTime = (timeInMs: string) => {
      const minutes = Math.round(parseInt(timeInMs) / 60000)
      return minutes > 0 ? `${minutes} mins` : 'No limit'
    }

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value)
    }

    const clearSearch = () => {
      setSearchQuery('')
    }

    return (
      <div className="space-y-6">
        {/* Search Bar */}
        <div className="flex items-center justify-between space-x-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search papers by name..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10 pr-10"
            />

            <Button
              variant="ghost"
              size="sm"
              onClick={clearSearch}
              disabled={!searchQuery}
              className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 p-0 hover:bg-gray-100"
            >
              ×
            </Button>
          </div>

          {/* Special action buttons */}
          <div className="flex justify-end space-x-2">
            {paperType === 8 && onRefreshCache && (
              <Button
                onClick={onRefreshCache}
                variant="outline"
                className="bg-sky-600 text-white hover:bg-sky-700"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Cache
              </Button>
            )}

            {(paperType === 13 || paperType === 14) && onCopyUrl && (
              <Button
                onClick={onCopyUrl}
                variant="outline"
                className="bg-green-600 text-white hover:bg-green-700"
              >
                <Copy className="h-4 w-4 mr-2" />
                Copy List URL
              </Button>
            )}
          </div>
        </div>

        {/* Papers Count */}
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600">
            Showing {paginatedPapers.length} of {filteredPapers.length} papers
            {searchQuery && (
              <span className="ml-1 text-blue-600">
                (filtered from {papers.length} total)
              </span>
            )}
          </p>
          {totalPages > 1 && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          )}
        </div>

        {/* Papers Grid */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Paper Name</TableHead>
                <TableHead>Questions</TableHead>
                <TableHead>Time Limit</TableHead>
                {config.hasStatusToggle && <TableHead>Status</TableHead>}
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedPapers.map((paper) => (
                <TableRow key={paper.paper_id}>
                  <TableCell className="font-medium">
                    {paper.paper_name}
                  </TableCell>
                  <TableCell>{paper.no_of_ques}</TableCell>
                  <TableCell>{formatTime(paper.time_lim)}</TableCell>
                  {config.hasStatusToggle && (
                    <TableCell>
                      <Badge
                        variant={paper.status === '1' ? 'default' : 'secondary'}
                        className="cursor-pointer"
                        onClick={() =>
                          onToggleStatus(paper.paper_id, paper.status)
                        }
                      >
                        {paper.status === '1' ? (
                          <>
                            <Eye className="h-3 w-3 mr-1" />
                            Shown
                          </>
                        ) : (
                          <>
                            <EyeOff className="h-3 w-3 mr-1" />
                            Hidden
                          </>
                        )}
                      </Badge>
                    </TableCell>
                  )}
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onEdit(paper)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      {config.canDelete && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => onDelete(paper.paper_id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-2 pt-6">
            <Button
              variant="outline"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page =
                Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
              return (
                <Button
                  key={page}
                  variant={currentPage === page ? 'default' : 'outline'}
                  onClick={() => setCurrentPage(page)}
                  className="w-10"
                >
                  {page}
                </Button>
              )
            })}

            <Button
              variant="outline"
              onClick={() =>
                setCurrentPage(Math.min(totalPages, currentPage + 1))
              }
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        )}

        {/* No papers message */}
        {filteredPapers.length === 0 && (
          <div className="text-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No papers found' : 'No papers found'}
            </h3>
            <p className="text-gray-600">
              {searchQuery
                ? `No papers match "${searchQuery}". Try a different search term.`
                : config.canCreate
                  ? 'Create your first paper to get started.'
                  : 'No papers are available for this configuration.'}
            </p>
            {searchQuery && (
              <Button variant="outline" onClick={clearSearch} className="mt-4">
                Clear search
              </Button>
            )}
          </div>
        )}
      </div>
    )
  },
  (prevProps, nextProps) => {
    // Custom comparison for memo - re-render only if necessary
    return (
      prevProps.paperType === nextProps.paperType &&
      prevProps.papers.length === nextProps.papers.length &&
      // Check if first and last paper are the same (quick check for data changes)
      JSON.stringify(prevProps.papers[0]) ===
        JSON.stringify(nextProps.papers[0]) &&
      JSON.stringify(prevProps.papers[prevProps.papers.length - 1]) ===
        JSON.stringify(nextProps.papers[nextProps.papers.length - 1])
    )
  }
)

export default PapersList
