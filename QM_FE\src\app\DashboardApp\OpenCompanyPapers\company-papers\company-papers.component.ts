import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { OpenTestsService } from 'src/app/Services/open-tests.service';

import { ChapterPaper } from '../../../Models/Dashboard/Chapters/ChapterPaper';

declare let gtag: Function;

@Component({
  selector: 'app-company-papers',
  templateUrl: './company-papers.component.html',
  styleUrls: ['./company-papers.component.scss']
})
export class CompanyPapersComponent implements OnInit {

  public papers: ChapterPaper[];
  public companyType: number;
  public pageHeading: string;

  public showIndicator = false;
  public tcsDig2022Collapsed = true;
  public tcsNqt22FoundationCollapsed = true;

  constructor(private router: Router,
              private route: ActivatedRoute,
              private openTestsService: OpenTestsService) {
      router.events.subscribe((y: NavigationEnd) => {
        if (y instanceof NavigationEnd) {
          gtag('config', 'UA-*********-1', { 'page_path': y.url });
        }
      });
    }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.route.paramMap.subscribe(params => {
      this.companyType = parseInt(params.get('paper_type'), 10);
    });

    this.openTestsService.getFreeCompanyPapers().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      that.papers = respText;

      // Company Type 3 is up for grabs!
      let regexp = new RegExp(/ /);
      if (that.companyType === 1) {
        regexp = /^INFOSYS*/;
        that.pageHeading = 'Infosys Papers';
      } else if (that.companyType === 2) {
        regexp = /^ACCENTURE*/;
        that.pageHeading = 'Accenture Papers';
      } else if (that.companyType === 4) {
        regexp = /^TCS (NQT||DIGITAL)*/;
        that.pageHeading = 'TCS-NQT Papers (Previous Papers)';
      } else if (that.companyType === 5) {
        regexp = /^WIPRO MOCK*/;
        that.pageHeading = 'Wipro Papers';
      } else if (that.companyType === 6) {
        regexp = /^Cognizant*/;
        that.pageHeading = 'Cognizant Papers';
      } else if (that.companyType === 7) {
        regexp = /^Capgemini*/;
        that.pageHeading = 'Capgemini Papers';
      } else if (that.companyType === 8) {
        regexp = /^Tech Mahindra*/;
        that.pageHeading = 'Tech Mahindra Papers';
      } else if (that.companyType === 9) {
        regexp = /^Zoho*/;
        that.pageHeading = 'Wipro Papers';
      } else if (that.companyType === 10) {
        regexp = /^HCL*/;
        that.pageHeading = 'HCL Papers';
      } else if (that.companyType === 11) {
        regexp = /^NTT*/;
        that.pageHeading = 'NTT Papers';
      } else if (that.companyType === 12) {
        regexp = /^ORACLE*/;
        that.pageHeading = 'Oracle Papers';
      } else if (that.companyType === 13) {
        regexp = /^DXC*/;
        that.pageHeading = 'DXC Technology Papers';
      } else if (that.companyType === 14) {
        regexp = /^DELOITTE*/;
        that.pageHeading = 'Deloitte Papers';
      } else if (that.companyType === 15) {
        regexp = /^L&T*/;
        that.pageHeading = 'L&T Papers';
      } else if (that.companyType === 16) {
        regexp = /^MINDTREE*/;
        that.pageHeading = 'Mindtree Papers';
      } else if (that.companyType === 17) {
        regexp = /^HEXAWARE*/;
        that.pageHeading = 'Hexaware Papers';
      } else if (that.companyType === 18) {
        regexp = /^MPHASIS*/;
        that.pageHeading = 'Mphasis Papers';
      } else if (that.companyType === 19) {
        regexp = /^SLK SOFTWARE*/;
        that.pageHeading = 'SLK Software Papers';
      } else if (that.companyType === 20) {
        regexp = /^HSBC*/;
        that.pageHeading = 'HSBC Papers';
      } else if (that.companyType === 21) {
        regexp = /^VM WARE*/;
        that.pageHeading = 'VM Ware Papers';
      } else if (that.companyType === 22) {
        regexp = /^WIPRO WILP*/;
        that.pageHeading = 'Wipro WILP Papers';
      } else if (that.companyType === 23) {
        regexp = /^WIPRO NLTH*/;
        that.pageHeading = 'WIPRO NLTH Papers';
      } else if (that.companyType === 24) {
        regexp = /^AMAZON*/;
        that.pageHeading = 'Amazon Papers';
      } else if (that.companyType === 25) {
        regexp = /^GOLDMAN SACHS*/;
        that.pageHeading = 'Goldman Sachs Papers';
      } else if (that.companyType === 26) {
        regexp = /^QUALCOMM*/;
        that.pageHeading = 'Qualcomm Papers';
      } else if (that.companyType === 27) {
        regexp = /^DELL */;
        that.pageHeading = 'DELL Papers';
      } else if (that.companyType === 28) {
        regexp = /^WELLS FARGO*/;
        that.pageHeading = 'Wells Fargo Papers';
      } else if (that.companyType === 29) {
        regexp = /^ARICENT*/;
        that.pageHeading = 'Aricent Papers';
      } else if (that.companyType === 30) {
        regexp = /^AMDOCS*/;
        that.pageHeading = 'Amdocs Papers';
      } else if (that.companyType === 31) {
        regexp = /^CISCO*/;
        that.pageHeading = 'Cisco Papers';
      } else if (that.companyType === 32) {
        regexp = /^CGI*/;
        that.pageHeading = 'CGI Papers';
      } else if (that.companyType === 33) {
        regexp = /^SAPIENT*/;
        that.pageHeading = 'Sapient Papers';
      } else if (that.companyType === 34) {
        regexp = /^ATOS*/;
        that.pageHeading = 'Atos Papers';
      } else if (that.companyType === 35) {
        regexp = /^PWC*/;
        that.pageHeading = 'PWC Papers';
      } else if (that.companyType === 36) {
        regexp = /^ADOBE*/;
        that.pageHeading = 'Adobe Papers';
      } else if (that.companyType === 37) {
        regexp = /^TCS-NQT 2022.*/;
        that.pageHeading = 'TCS-NQT 2022 (Latest Pattern)';
      } else if (that.companyType === 38) {
        regexp = /^KPMG*/;
        that.pageHeading = 'KPMG Papers';
      } else if (that.companyType === 39) {
        regexp = /^SYNTEL*/;
        that.pageHeading = 'Syntel Papers';
      } else if (that.companyType === 40) {
        regexp = /^ERICSSON*/;
        that.pageHeading = 'Ericsson Papers';
      } else if (that.companyType === 41) {
        regexp = /^COMVIVA*/;
        that.pageHeading = 'Comviva Papers';
      } else if (that.companyType === 42) {
        regexp = /^EY*/;
        that.pageHeading = 'EY Papers';
      } else if (that.companyType === 43) {
        regexp = /^GENPACT*/;
        that.pageHeading = 'Genpact Papers';
      } else if (that.companyType === 44) {
        regexp = /^VERIZON*/;
        that.pageHeading = 'Verizon Papers';
      } else if (that.companyType === 45) {
        regexp = /^HP*/;
        that.pageHeading = 'HP Papers';
      } else if (that.companyType === 46) {
        regexp = /^KPIT*/;
        that.pageHeading = 'KPIT Papers';
      } else if (that.companyType === 47) {
        regexp = /^MUSIGMA*/;
        that.pageHeading = 'Musigma Papers';
      } else if (that.companyType === 48) {
        regexp = /^HONEYWELL*/;
        that.pageHeading = 'Honeywell Papers';
      } else if (that.companyType === 49) {
        regexp = /^INTEL*/;
        that.pageHeading = 'Intel Papers';
      } else if (that.companyType === 50) {
        regexp = /^TORRRI HARRIS*/;
        that.pageHeading = 'Torri Harris Papers';
      } else if (that.companyType === 51) {
        regexp = /^SIEMENS*/;
        that.pageHeading = 'Siemens Papers';
      } else if (that.companyType === 52) {
        regexp = /^TATA ELIXI*/;
        that.pageHeading = 'TATA Elixi Papers';
      } else if (that.companyType === 53) {
        regexp = /^PERSISTENT*/;
        that.pageHeading = 'Persistent Systems Papers';
      } else if (that.companyType === 50) {
        regexp = /^TORRY HARRIS*/;
        that.pageHeading = 'Torry Harris';
      } else if (that.companyType === 51) {
        regexp = /^TATA ELXSI*/;
        that.pageHeading = 'Tata Elxsi';
      } else if (that.companyType === 52) {
        regexp = /^SIEMENS*/;
        that.pageHeading = 'Siemens';
      } else if (that.companyType === 53) {
        regexp = /^PERSISTENT SYSTEMS*/;
        that.pageHeading = 'Persistent Systems';
      } else if (that.companyType === 54) {
        regexp = /^HDFC*/;
        that.pageHeading = 'HDFC';
      } else if (that.companyType === 55) {
        regexp = /^SONATA SOFTWARE*/;
        that.pageHeading = 'Sonata Software';
      } else if (that.companyType === 56) {
        regexp = /^HARMAN*/;
        that.pageHeading = 'Harman';
      } else if (that.companyType === 57) {
        regexp = /^RELIANCE JIO*/;
        that.pageHeading = 'Reliance Jio';
      } else if (that.companyType === 58) {
        regexp = /^SAMSUNG*/;
        that.pageHeading = 'Samsung';
      } else if (that.companyType === 59) {
        regexp = /^BIRLASOFT*/;
        that.pageHeading = 'Birlasoft';
      } else if (that.companyType === 60) {
        regexp = /^ITC INFOTECH*/;
        that.pageHeading = 'ITC infotech';
      } else if (that.companyType === 61) {
        regexp = /^ADP*/;
        that.pageHeading = 'ADP';
      } else if (that.companyType === 62) {
        regexp = /^CYIENT*/;
        that.pageHeading = 'Cyient';
      } else if (that.companyType === 63) {
        regexp = /^ROBERT BOSCH*/;
        that.pageHeading = 'Robert Bosch';
      } else if (that.companyType === 64) {
        regexp = /^JP MORGAN*/;
        that.pageHeading = 'JP Morgan';
      } else if (that.companyType === 65) {
        regexp = /^MARUTHI SUZUKI*/;
        that.pageHeading = 'Maruthi Suzuki';
      } else if (that.companyType === 66) {
        regexp = /^TATA POWER*/;
        that.pageHeading = 'Tata Power';
      } else if (that.companyType === 67) {
        regexp = /^HAPPIEST MINDS*/;
        that.pageHeading = 'Happiest minds';
      } else if (that.companyType === 68) {
        regexp = /^SASKEN*/;
        that.pageHeading = 'Sasken';
      } else if (that.companyType === 69) {
        regexp = /^LTI*/;
        that.pageHeading = 'LTI';
      } else if (that.companyType === 70) {
        regexp = /^LG*/;
        that.pageHeading = 'LG';
      } else if (that.companyType === 71) {
        regexp = /^VENDATA*/;
        that.pageHeading = 'Vedanta';
      } else if (that.companyType === 72) {
        regexp = /^NEWGEN TECHNOLOGIES*/;
        that.pageHeading = 'Newgen Technologies';
      } else if (that.companyType === 73) {
        regexp = /^ZENSAR*/;
        that.pageHeading = 'Zensar';
      } else if (that.companyType === 74) {
        regexp = /^TAVANT TECHNOLOGIES*/;
        that.pageHeading = 'Tavant Technologies';
      } else if (that.companyType === 75) {
        regexp = /^TVS MOTORS*/;
        that.pageHeading = 'TVS Motors';
      } else if (that.companyType === 76) {
        regexp = /^PHILIPS*/;
        that.pageHeading = 'Philips';
      } else if (that.companyType === 77) {
        regexp = /^NESS TECHNOLOGIES*/;
        that.pageHeading = 'Ness Technologies';
      } else if (that.companyType === 78) {
        regexp = /^TATA MOTORS*/;
        that.pageHeading = 'Tata Motors';
      } else if (that.companyType === 79) {
        regexp = /^TATA STEEL*/;
        that.pageHeading = 'Tata Steel';
      } else if (that.companyType === 80) {
        regexp = /^SONY*/;
        that.pageHeading = 'Sony';
      } else if (that.companyType === 81) {
        regexp = /^RELIANCE INDUSTRIES LIMITED*/;
        that.pageHeading = 'Reliance Industries Limited';
      } else if (that.companyType === 82) {
        regexp = /^SUTHERLAND GLOBAL SERVICES*/;
        that.pageHeading = 'Sutherland Global Services';
      } else if (that.companyType === 83) {
        regexp = /^AXTRIA*/;
        that.pageHeading = 'Axtria';
      } else if (that.companyType === 84) {
        regexp = /^EXPEDIA*/;
        that.pageHeading = 'Expedia';
      } else if (that.companyType === 85) {
        regexp = /^GENERAL MOTORS*/;
        that.pageHeading = 'General Motors';
      } else if (that.companyType === 86) {
        regexp = /^FLEXTRONICS*/;
        that.pageHeading = 'Flextronics';
      } else if (that.companyType === 87) {
        regexp = /^JINDAL STEEL*/;
        that.pageHeading = 'Jindal Steel';
      } else if (that.companyType === 88) {
        regexp = /^NUCLEUS SOFTWARE*/;
        that.pageHeading = 'Nucleus Software';
      } else if (that.companyType === 89) {
        regexp = /^ESSAR*/;
        that.pageHeading = 'Essar';
      } else if (that.companyType === 90) {
        regexp = /^GODREJ*/;
        that.pageHeading = 'Godrej';
      } else if (that.companyType === 91) {
        regexp = /^INFOGAIN*/;
        that.pageHeading = 'Infogain';
      } else if (that.companyType === 92) {
        regexp = /^MAZARS*/;
        that.pageHeading = 'Mazars';
      }

      that.papers = that.papers.filter(x => x.paper_name.match(regexp) != null);

      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['/dashboard/test/', '7', paperId, paperName, paperLim]);
  }
}
