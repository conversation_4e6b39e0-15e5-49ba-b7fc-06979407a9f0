import { Component, OnInit } from '@angular/core';
import { state, style, trigger, transition, animate } from '@angular/animations';

import * as $ from 'jquery';

@Component({
  selector: 'app-left-nav',
  templateUrl: './left-nav.component.html',
  styleUrls: ['./left-nav.component.scss'],
  animations: [
    trigger('openClose', [
      state('open', style({
        transform: 'translateX(0)'
      })),
      state('closed', style({
        transform: 'translateX(-235px)'
      })),
      transition('open => closed', [
        animate('0.5s ease-in-out')
      ]),
      transition('closed => open', [
        animate('0.4s ease-in-out')
      ])
    ])
  ]
})
export class LeftNavComponent implements OnInit {

  public isNavOpen = false;

  constructor() { }

  ngOnInit() {

    this.isNavOpen = true;
  }

  toggleNav() {
    if ($(window).width() <= 440) {
      this.isNavOpen = !this.isNavOpen;
    }
  }

}
