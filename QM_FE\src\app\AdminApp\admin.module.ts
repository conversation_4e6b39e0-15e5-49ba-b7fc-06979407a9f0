import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { MathJaxModule } from 'ngx-mathjax';

import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { ModalModule } from 'ngx-bootstrap/modal';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { ButtonsModule } from 'ngx-bootstrap/buttons';
import { AlertModule } from 'ngx-bootstrap/alert';

import { VgCoreModule } from 'videogular2/compiled/core';
import { VgControlsModule } from 'videogular2/compiled/controls';
import { VgOverlayPlayModule } from 'videogular2/compiled/overlay-play';
import { VgStreamingModule } from 'videogular2/compiled/streaming';
import { VgBufferingModule } from 'videogular2/compiled/buffering';

import { MarkdownModule } from 'ngx-markdown';

import { AdminRoutingModule } from './admin-routing.module';
import { UploadConsoleComponent } from './upload-console/upload-console.component';
import { AdminNavComponent } from './Components/admin-nav/admin-nav.component';
import { LeftNavComponent } from './Components/left-nav/left-nav.component';
import { UsersComponent } from './users/users.component';
import { TestsComponent } from './tests/tests.component';
import { TestimonialsComponent } from './testimonials/testimonials.component';
import { ResultsComponent } from './results/results.component';
import { UploadExplanationComponent } from './Upload/upload-explanation/upload-explanation.component';
import { AnalyticsComponent } from './Analytics/analytics.component';
import { VideoUsageComponent } from './Analytics/video-usage/video-usage.component';
import { WorkshopsComponent } from './Workshops/workshops/workshops.component';
import { WorkshopVideosComponent } from './Workshops/workshop-videos/workshop-videos.component';
import { MetricsComponent } from './metrics/metrics.component';
import { UpldPlacementPostComponent } from './Upload/upld-placement-post/upld-placement-post.component';
import { UploadTestsComponent } from './Upload/upload-tests/upload-tests.component';

import { MathjaxBindDirective } from './Directives/mathjax-bind.directive';
import { BlogsComponent } from './blogs/blogs.component';
import { NotesComponent } from './notes/notes.component';
import { AngularMarkdownEditorModule } from 'angular-markdown-editor';

import { PaperDetailComponent } from './Upload/paper-detail/paper-detail.component';
import { CollapseModule } from 'ngx-bootstrap/collapse';

@NgModule({
  declarations: [
    UploadConsoleComponent,
    AdminNavComponent,
    LeftNavComponent,
    UsersComponent,
    TestsComponent,
    TestimonialsComponent,
    ResultsComponent,
    UploadExplanationComponent,
    AnalyticsComponent,
    VideoUsageComponent,
    WorkshopsComponent,
    WorkshopVideosComponent,
    MetricsComponent,
    MathjaxBindDirective,
    UpldPlacementPostComponent,y
    
    UploadTestsComponent,
    BlogsComponent,
    NotesComponent,
    PaperDetailComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    BsDatepickerModule.forRoot(),
    BsDropdownModule.forRoot(),
    ModalModule.forRoot(),
    PaginationModule.forRoot(),
    MarkdownModule.forRoot(),
    AdminRoutingModule,
    SharedIndicatorModule,
    MathJaxModule.forRoot({
      version: '2.7.5',
      config: 'TeX-AMS_HTML',
      hostname: 'cdnjs.cloudflare.com'
    }),
    VgCoreModule,
    VgControlsModule,
    VgOverlayPlayModule,
    VgStreamingModule,
    VgBufferingModule,
    AngularMarkdownEditorModule.forRoot({ iconlibrary: 'fa' }),
    CollapseModule.forRoot(),
    ButtonsModule.forRoot(),
    //AgmCoreModule.forRoot({
      //apiKey: 'AIzaSyAuvwpcrhORrE_LiFfG1DMX8OD3-D9omGo'
    //}),
    AlertModule.forRoot()
  ]
})
export class AdminModule { }
