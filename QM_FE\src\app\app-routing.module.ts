import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { HomeComponent } from './WebApp/home/<USER>';
// import { PlansComponent } from './WebApp/plans/plans.component';
import { CheckoutComponent } from './WebApp/checkout/checkout.component';
import { AchieversComponent } from './WebApp/achievers/achievers.component';
import { LinksComponent } from './WebApp/links/links.component';
import { PrivacyPolicyComponent } from './WebApp/privacy-policy/privacy-policy.component';


const routes: Routes = [
  { path: 'home', component: HomeComponent, data: { animation: 'LeftPage' } },
  // { path: 'plans', component: PlansComponent },
  { path: 'checkout', component: CheckoutComponent },
  { path: 'achievers', component: AchieversComponent },
  { path: 'links', component: LinksComponent },
  { path: 'privacy-policy', component: PrivacyPolicyComponent },
  {
    path: 'user',
    loadChildren: './UserApp/user-app.module#UserAppModule'
  },
  {
    path: 'dashboard',
    loadChildren: './DashboardApp/student-dashboard.module#StudentDashboardModule'
  },
  {
    path: 'technical',
    loadChildren: './TechnicalApp/technical-app.module#TechnicalAppModule'
  },
  {
    path: 'admin/console',
    loadChildren: './AdminApp/admin.module#AdminModule'
  },
  {
    path: 'blog',
    loadChildren: './BlogApp/blog-app.module#BlogAppModule'
  },
  {
    path: 'notes',
    loadChildren: './NotesApp/notes-app.module#NotesAppModule'
  },
  {
    path: 'placement',
    loadChildren: './CampaignsApp/campaigns-app.module#CampaignsAppModule'
  },
  {
    path: 'certification',
    loadChildren: './CertApp/cert-app.module#CertAppModule'
  },
  {
    path: 'faqs',
    loadChildren: './FAQApp/faq-app.module#FAQAppModule'
  },
  {
    path: '',
    redirectTo: '/home',
    pathMatch: 'full',
  },
  { path: '**',
    redirectTo: '/home',
    pathMatch: 'full'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, {
    scrollPositionRestoration: 'enabled'
  })],
  exports: [RouterModule]
})
export class AppRoutingModule { }
