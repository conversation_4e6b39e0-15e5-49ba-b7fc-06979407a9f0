import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { ChapterPracticeService } from '../../Services/Dashboard/chapter-practice.service';
import { SuperGroup } from 'src/app/Models/Dashboard/Chapters/SuperGroup';
import { Group } from 'src/app/Models/Dashboard/Chapters/Group';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';

declare let gtag: Function;

@Component({
  selector: 'app-chapter-practice-papers',
  templateUrl: './chapter-practice-papers.component.html',
  styleUrls: ['./chapter-practice-papers.component.scss']
})
export class ChapterPracticePapersComponent implements OnInit {

  public superGroups: SuperGroup[];
  public paperGroups: Group[];
  public ChapterPaper: ChapterPaper[];
  public isCollapsed = [];

  public selectedGroup = [];

  public showIndicator = false;

  private safeGroupId = '';
  private safeIdx = 0;

  constructor(private router: Router,
              private activatedRoute: ActivatedRoute,
              private chapterPracticeService: ChapterPracticeService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const that = this;

    if (localStorage.getItem('qmChapterGroupIdx') != null) {
      this.safeIdx = parseInt(localStorage.getItem('qmChapterGroupIdx'));
    }

    if (localStorage.getItem('qmChapterGroupId') != null) {
      this.safeGroupId = localStorage.getItem('qmChapterGroupId');
    }

    this.showIndicator = true;
    this.chapterPracticeService.getSuperGroups().subscribe(response => {
      that.superGroups = response;
      this.superGroups.splice(this.superGroups.findIndex(x => x.super_group_id === '8e3208cb-fae1-333f-9ff7-3e63044f6126'), 1);

      for (let i = 0; i < that.superGroups.length; i++) {
        that.selectedGroup.push(false);
      }

      that.selectedGroup[0] = true;

      that.selectedGroup[that.safeIdx] = true;
      that.selectGroup(that.superGroups[that.safeIdx].super_group_id, that.safeIdx);
      that.showIndicator = false;
    }, error => {

    });
  }

  selectGroup(superGrp: string, groupIdx: number) {
    const that = this;

    this.showIndicator = true;
    localStorage.setItem('qmChapterGroupIdx', groupIdx.toString());
    localStorage.setItem('qmChapterGroupId', superGrp);
    that.chapterPracticeService.getGroups(superGrp).subscribe(response => {
      that.paperGroups = response;

      that.selectedGroup.fill(false);
      that.selectedGroup[groupIdx] = true;

      for (const topic of this.paperGroups) {
          that.isCollapsed.push(true);

          that.ChapterPaper = [];
          that.chapterPracticeService.getPapersOfAGroup(topic.group_id).subscribe(response2 => {

            if (response2.length > 0) {
              for (const paper of response2) {
                that.ChapterPaper.push(paper);
              }
            } else {
              this.paperGroups.splice(this.paperGroups.indexOf(topic), 1);
            }

            that.showIndicator = false;
          }, error => {
            that.showIndicator = false;
          });
      }
    }, error => {
      that.showIndicator = false;
    });
  }

  selectPaperGroup(paperGroupId: string) {
    const that = this;
    that.chapterPracticeService.getPapersOfAGroup(paperGroupId).subscribe(response => {
      if (response.length > 0) {
        that.ChapterPaper = response;
      }
    }, error => {

    });
  }

  filterChapterPapers(groupId: string) {
    return this.ChapterPaper.filter(x => x.group_id === groupId);
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '6', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans() {
    this.router.navigate(['/plans']);
  }
}
