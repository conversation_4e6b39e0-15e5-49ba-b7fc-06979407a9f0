.results-wrap {
  max-width: 1330px;
  width: 90%;
  margin: 1em auto;
  font-family: 'Montserrat', 'serif';
  border-radius: 5px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  padding: 15px;
  background-color: #fff;
  position: relative;
  z-index: 10;

  .options-box {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1em;

    .custom-btn {
      min-width: 130px;
      height: 40px;
      background-color: #E88224;
      border: none;
      border-radius: 4px;
      color: #fff;
      margin-top: 2em;
      padding: 8px;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);

      &:first-of-type {
        margin-left: 0;
      }
    }

    .custom-btn:active:after {
      transition: 0s;
      opacity: 0.7;
      clip-path: circle(0% at 0% 0%);
    }

    .custom-btn::after {
      content: "";
      display: block;
      position: relative;
      top: -32px;
      left: -8px;
      height: 40px;
      width: 130px;
      background-color: #E88224;
      opacity: 0;
      clip-path: circle(150% at 0% 0%);
      transition: all 0.4s ease-in;
    }
  }

  .data-table {
    margin-top: 1em;

    .headers,
    .table-data {
      display: grid;
      grid-template-columns: 2fr 1.5fr 1fr 1fr 0.5fr;
      padding: 5px;

      h6,
      p {
        padding: 10px;
        margin: 0;
      }

      button {
        font-size: 0.8em;
        background-color: #E74C3C;
        color: #FADBD8;
        border: none;
        border-radius: 0.3em;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
      }
    }

    .headers {
      background-color: #1ABC9C;
      color: #fff;
    }
    
    .table-data:last-of-type {
      margin-bottom: 1em;
    }

    .table-data:nth-of-type(2n) {
      background-color: #D5F5E3;
    }
  }
}

.section-modal {
  .modal-body {
    h6 {
      display: block;
      text-align: right;
    }

    .marks-table {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      p {
        margin: 0;
        text-align: center;
      }

      .headers, .content-row {
        width: 100%;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        place-items: center;
        align-items: center;
        padding: 0.7em;
      }

      .headers {
        font-weight: 900;
        margin-top: 1em;
        color: #fff;
        background-color: #566573;
      }

      .content-row {
        background-color: #EBEDEF;
      }

      .content-row:nth-of-type(2n+1) {
        background-color: #D5D8DC;
      }
    }
  }
}

@media (max-width: 440px) {
  .results-wrap {
    width: 98%;

    .data-table {
      .headers,
      .table-data {
        font-size: 12px;
        grid-template-columns: 1fr 1fr 0.5fr 1fr;
      }
    }
  }
}