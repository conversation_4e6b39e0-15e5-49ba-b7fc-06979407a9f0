// app/dashboard/model-papers/page.tsx
import { Metadata } from 'next'
import { ModelPapersServerTestsService } from '@/lib/server-services/model-paper-service.server'
import ModelPapersClient from '@/components/model-papers/model-papers-client'

export const metadata: Metadata = {
  title: 'Model Papers | Quant Masters',
  description: 'Practice with our comprehensive model papers'
}

export default async function ModelPapersPage() {
  // Fetch papers on the server
  const papers = await ModelPapersServerTestsService.getPapers()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-2xl font-bold text-center">
            Model Practice Papers
          </h1>
          <p className="text-center text-gray-600 mt-2">
            Custom compiled aptitude tests covering a wide variety of concepts
          </p>
        </div>
      </div>

      {/* Pass the fetched papers to the client component */}
      <ModelPapersClient initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
