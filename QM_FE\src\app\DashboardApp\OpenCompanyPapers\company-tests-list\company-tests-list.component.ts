import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';

import { LoginService } from '../../../Services/login.service';

declare let gtag: Function;

@Component({
  selector: 'app-company-tests-list',
  templateUrl: './company-tests-list.component.html',
  styleUrls: ['./company-tests-list.component.scss']
})
export class CompanyTestsListComponent implements OnInit {

  public showIndicator = false;
  public lockedResource = false;

  constructor(private router: Router,
              private activatedRoute: ActivatedRoute,
              private loginService: LoginService) {
    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-*********-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    this.showIndicator = true;
    if (sessionStorage.getItem('QMail')) {

      this.loginService.checkUserLogin(sessionStorage.getItem('QMail')).subscribe(response => {
        this.showIndicator = false;

        const respText = JSON.parse(JSON.stringify(response));

        if (respText.msg === '821f069c-12dd-3d3d-9462-d6e84564b658') {
          this.lockedResource = false;
        } else {
          this.lockedResource = true;
        }
      }, error => {
        this.showIndicator = false;
        this.lockedResource = true;
      });
    } else {
      this.lockedResource = true;
    }
  }

  takeToLanding() {
    this.router.navigate(['/placement/training/live']);
  }

  takeToPapersList(num: number) {

    if (this.lockedResource) {
      return;
    }

    this.router.navigate(['list/', num], { relativeTo: this.activatedRoute });
  }
}
