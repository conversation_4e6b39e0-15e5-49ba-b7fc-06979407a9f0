<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="426.107" height="350" viewBox="0 0 426.107 350">
  <defs>
    <linearGradient id="linear-gradient" x1="0.516" y1="0.975" x2="0.516" y2="-0.026" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="gray" stop-opacity="0.251"/>
      <stop offset="0.54" stop-color="gray" stop-opacity="0.122"/>
      <stop offset="1" stop-color="gray" stop-opacity="0.102"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="0.514" y1="0.995" x2="0.514" y2="-0.003" xlink:href="#linear-gradient"/>
  </defs>
  <g id="undraw_be_the_hero_ssr2" transform="translate(0.107)">
    <path id="Path_94" data-name="Path 94" d="M126.452,191.63c1.2,13.488,8.843,25.7,13.509,38.542a91.383,91.383,0,0,1,1.777,56.732c-1.11,3.827-2.48,7.667-2.48,11.586,0,13.512,15.39,22.481,29.374,28.446A535.381,535.381,0,0,0,279.2,360.352c12.41,2.345,27.912,3.356,35.765-5.461,3.771-4.235,4.681-9.764,7.449-14.515,7.058-12.117,24.233-16.824,39.651-19.521a484.2,484.2,0,0,1,56.188-6.468c18.965-1.067,40.5-2,53.068-14.451,7.989-7.919,10.313-18.845,10.633-29.165,1.042-33.855-15.13-70.491,5.628-99.812,8.7-12.3,22.915-21.278,31.555-33.612,12.506-17.871,11.428-40.567,2.96-59.612S498.631,42.5,483.848,26.628c-4.889-5.246-10.105-10.7-17.447-13.136-7.641-2.533-16.3-1.37-24.5-.172-37.267,5.441-75.947,11.37-107.2,30.06C319.18,52.661,306.215,64.7,292.362,75.769c-29.913,23.919-64.567,46.013-104.067,55.457C156.206,138.9,123.568,159.6,126.452,191.63Z" transform="translate(-102.499 -11.983)" fill="#11999e" opacity="0.1"/>
    <path id="Path_95" data-name="Path 95" d="M591.471,389.459c.643-8.877.447-15.685-.4-16.38a6.045,6.045,0,0,1-.955-2.517,2.322,2.322,0,0,0,1.174-2.4c-1.062-4.435-17.87-37.084-20.575-37.679s-2.145-48.977-2.145-48.977l3.946-36.908h-.056a26.909,26.909,0,0,1,1.626-5.833c.152-.372.312-.731.471-1.087,3.267.116,6.391-1.334,9.091-5.15,2.94-4.155,5.672-7.851,8.148-11.091,5.848-4.555,13.045-11.586,15.134-19.74,3.695-14.415-13.181-29.165-13.181-29.165L588.5,169c-.068-.2-.128-.4-.184-.6s-.1-.4-.148-.583a16.662,16.662,0,0,0,3.559-.823h0a3.654,3.654,0,0,0,3.595.308l.072-.04a3.4,3.4,0,0,0,.571-.2,10.133,10.133,0,0,0,1.622-1.1c2.053-1.51,4.506-2.345,6.746-3.552,3.815-2.05,7.146-5.425,8.116-9.656a22.979,22.979,0,0,0,.435-4.163,8.093,8.093,0,0,0-.248-3c-.264-.767-.755-1.434-1.062-2.185-.667-1.63-.4-3.468-.435-5.23a13.231,13.231,0,0,0-3.523-9.053,10.025,10.025,0,0,0-9.039-2.96,17.369,17.369,0,0,0-2.54.8c-.372.136-.739.28-1.106.4a7.628,7.628,0,0,1-4.394.587c-1.793-.42-3.355-1.926-5.193-1.71a3.561,3.561,0,0,0-1.1.336,5.992,5.992,0,0,0-1.825.959,20.959,20.959,0,0,0-2.353,1.95,5.607,5.607,0,0,0-1.961,3.268,4.056,4.056,0,0,0,1.023,2.944,16.9,16.9,0,0,0-7.449,23.863q-.048.276-.1.559a41.312,41.312,0,0,1-1,4.187,3.615,3.615,0,0,1,.116-.759c-.268-.06-.543-.112-.827-.16h-.128q-.375-.064-.759-.112h-.064c-3.838-.483-8.524-.056-11.184,1.466a22.839,22.839,0,0,0-9.7,11.055,27.378,27.378,0,0,0-6.307,9.988,13.661,13.661,0,0,0-6.295-1.3c-3.235-6.392-9.355-14.359-17.375-5.845-8.44,8.961,1.925,11.586,9.031,12.357a7.877,7.877,0,0,0,1.087,2.3,21.529,21.529,0,0,0,1.689,2.229,1.389,1.389,0,0,1-.7.036c-1.634-.455-1.514,16.237,5.792,21.107a40.979,40.979,0,0,1-4.689,8.6,48.247,48.247,0,0,0-6.283,12.5,16.7,16.7,0,0,0-1.142,5.793h0a2.24,2.24,0,0,0,.088.427h0a1.4,1.4,0,0,0,.068.172h0a1.045,1.045,0,0,0,.088.148h0a.828.828,0,0,0,.1.116h0a.729.729,0,0,0,.116.088h0a.735.735,0,0,0,.132.06h0a.863.863,0,0,0,.144.028h.024a1.135,1.135,0,0,0,.16,0h.032a1.557,1.557,0,0,0,.176-.024H524l.18-.048h.06l.184-.072.08-.032.2-.1.088-.044.228-.132.084-.048.284-.18.056-.036.363-.26c-.352.687-.759,1.434-1.226,2.205-2.309,3.772-6.1,8.142-11.7,9.373-10.385,2.281-37.111,36.448-37.111,36.448s-8.24-.571-4.9,25.7,3.97,40.531,3.97,40.531l.148.092c-3.131,3.564-9.518,9.964-15.07,10.04-7.7.1-12.238,5.17-8.58,8.338s43.538-1.822,43.538-1.822.264-10.423-1.634-15.214a9.224,9.224,0,0,0,.895-.907l-1.969-52.693s24.9-21.346,37.035-23.416c-.08-.044-.16-.084-.236-.124a8.543,8.543,0,0,1,2.321-.044c10.609,1.442,18.661,49.816,18.661,49.816a33.053,33.053,0,0,0,7.785,13.484c5.832,5.869,18.773,28.542,18.773,28.542l.539-.06.072.124h0c0,3.436-.523,8.594-3.2,12.617-4.394,6.624,7.937,9.189,7.937,9.189S590.832,398.336,591.471,389.459Z" transform="translate(-296.953 -80.431)" fill="url(#linear-gradient)"/>
    <path id="Path_96" data-name="Path 96" d="M481.525,701.1s-9.279,12.2-16.956,12.305-12.2,5.138-8.552,8.286,43.434-1.8,43.434-1.8.375-14.99-3.068-17.179S481.525,701.1,481.525,701.1Z" transform="translate(-299.762 -425.741)" fill="#444176"/>
    <path id="Path_97" data-name="Path 97" d="M765.383,733.424s1.422,9.66-2.964,16.249,7.917,9.129,7.917,9.129,8.935,2.489,9.586-6.336.439-15.581-.4-16.3-1.538-5.685-1.538-5.685Z" transform="translate(-483.982 -443.426)" fill="#444176"/>
    <path id="Path_98" data-name="Path 98" d="M610.761,546.392s-12.925-22.549-18.741-28.366a32.767,32.767,0,0,1-7.769-13.408s-8.044-48.1-18.625-49.541-39.607,23.372-39.607,23.372l2.069,52.369c-6.3,7.3-18.657-.464-18.657-.464s-.635-14.207-3.966-40.332,4.877-25.569,4.877-25.569,26.63-33.959,36.991-36.229c5.592-1.219,9.355-5.565,11.659-9.313a30.637,30.637,0,0,0,2.86-5.993l44.269,4.734-3.922,36.7s-.543,48.1,2.157,48.7,19.468,33.064,20.531,37.471S610.761,546.392,610.761,546.392Z" transform="translate(-329.752 -252.737)" fill="#565387"/>
    <g id="Group_4" data-name="Group 4" transform="translate(187.915 164.533)" opacity="0.1">
      <path id="Path_99" data-name="Path 99" d="M583.1,517.855a4.361,4.361,0,0,0-1.562-.555c-10.581-1.438-39.607,23.372-39.607,23.372L544,593.053a9.08,9.08,0,0,1-6.79,3.12c3.743.871,8.076.8,11.052-2.665l-2.089-52.393S570.994,519.912,583.1,517.855Z" transform="translate(-537.21 -479.912)"/>
      <path id="Path_100" data-name="Path 100" d="M766.586,547.13c-1.062-4.395-17.83-36.876-20.531-37.471s-2.157-48.7-2.157-48.7l3.922-36.7-4.246-.455-3.922,36.7s-.543,48.1,2.157,48.7,19.468,33.064,20.531,37.471c.719,2.984-5.991,4.61-10.429,5.35l.555.959S767.649,551.537,766.586,547.13Z" transform="translate(-658.765 -423.81)"/>
    </g>
    <path id="Path_101" data-name="Path 101" d="M771.526,228.816s-41.864-1.41-34.211-7.032c4.721-3.472,6.762-10.26,7.637-14.954a39.8,39.8,0,0,0,.631-5.022s22.316-2.213,16.828,6.093a8.334,8.334,0,0,0-.8,7.115C763.581,221.952,771.526,228.816,771.526,228.816Z" transform="translate(-468.911 -125.808)" fill="#ffbec7"/>
    <path id="Path_102" data-name="Path 102" d="M673.967,244.57a26.937,26.937,0,0,0-12.127,18.642c-2,12.976-3.639-.4-3.639-.4s-5.8,5.513-7.589,5.018-1.47,19.473,8.072,22.03,17.08-39.277,17.08-39.277Z" transform="translate(-416.891 -151.647)" fill="#dfe5ee"/>
    <path id="Path_103" data-name="Path 103" d="M673.967,244.57a26.937,26.937,0,0,0-12.127,18.642c-2,12.976-3.639-.4-3.639-.4s-5.8,5.513-7.589,5.018-1.47,19.473,8.072,22.03,17.08-39.277,17.08-39.277Z" transform="translate(-416.891 -151.647)" opacity="0.05"/>
    <path id="Path_104" data-name="Path 104" d="M639.371,267.091s-7.781-24.77-19.884-11.942,14.6,12.6,14.6,12.6Z" transform="translate(-396.818 -155.786)" fill="#ffbec7"/>
    <path id="Path_105" data-name="Path 105" d="M775.3,207.9a8.335,8.335,0,0,0-.8,7.115,16.775,16.775,0,0,1-16.664-8.19,39.8,39.8,0,0,0,.631-5.022S780.791,199.595,775.3,207.9Z" transform="translate(-481.8 -125.809)" opacity="0.1"/>
    <path id="Path_152" data-name="Path 152" d="M16.395-.329A16.772,16.772,0,1,1-.382,16.444,16.773,16.773,0,0,1,16.395-.329Z" transform="translate(271.956 85.825) rotate(-81.78)" fill="#ffbec7"/>
    <path id="Path_106" data-name="Path 106" d="M687.8,417.692l-.575,5.366c-4.885-1.306-38.4-9.988-45.491-4.77a8.169,8.169,0,0,1-1.062.675,30.637,30.637,0,0,0,2.86-5.993Z" transform="translate(-411.43 -252.767)" opacity="0.1"/>
    <path id="Path_107" data-name="Path 107" d="M719.967,260.479c-2.233,8.7-10.313,16.089-16.321,20.559a67.768,67.768,0,0,1-6.914,4.563,25.349,25.349,0,0,0-9.51,11.722,27.481,27.481,0,0,0-2,9.748s-38.433-10.551-46.086-4.93c-4.022,2.956-3.387-1.223-1.877-5.849a47.876,47.876,0,0,1,6.259-12.425c2.113-3.04,4.765-7.495,5.564-11.646,1.058-5.513,8.244-26.484,11.583-36.073A22.836,22.836,0,0,1,670.9,223.719c3.1-1.754,8.9-2.046,12.942-1.155,0,0-1.3,4.335,4.681,5.11.647.084,1.29.2,1.925.348,2.4.563,8.3,1.686,10.924-.208l5.436,3.648S723.646,246.148,719.967,260.479Z" transform="translate(-408.685 -138.139)" fill="#dfe5ee"/>
    <path id="Path_108" data-name="Path 108" d="M785.142,147.48a2.03,2.03,0,0,0-.439-2.229,5.959,5.959,0,0,0-2.033-1.282l-3.647-1.654a12.693,12.693,0,0,1-3.391-2,4.3,4.3,0,0,1-1.546-3.476,5.482,5.482,0,0,1,1.957-3.248,20.861,20.861,0,0,1,2.345-1.938,5.1,5.1,0,0,1,2.281-1.047c1.825-.216,3.383,1.282,5.173,1.7,2.7.631,5.288-1.235,7.989-1.81a10.009,10.009,0,0,1,9.015,2.944,13.214,13.214,0,0,1,3.515,9c.028,1.754-.228,3.6.435,5.194.3.747.8,1.41,1.058,2.173a8.023,8.023,0,0,1,.248,2.988,22.784,22.784,0,0,1-.419,4.155c-.967,4.207-4.29,7.559-8.092,9.588-2.237,1.2-4.681,2.03-6.726,3.528a10.1,10.1,0,0,1-1.6,1.091,3.654,3.654,0,0,1-3.571-.308,2.6,2.6,0,0,1-1.2-2.157,5.219,5.219,0,0,1,1.059-2.309c1.158-1.906,1.43-4.606-.112-6.225-.675-.707-1.6-1.123-2.265-1.846-1.07-1.2-1.2-2.924-1.234-4.523-.1-2.984-.18-6.093,1.066-8.789" transform="translate(-491.547 -83.039)" opacity="0.1"/>
    <path id="Path_109" data-name="Path 109" d="M786.313,144.651a5.96,5.96,0,0,0-2.033-1.282l-3.647-1.654a12.691,12.691,0,0,1-3.391-2,4.3,4.3,0,0,1-1.546-3.476,5.482,5.482,0,0,1,1.957-3.248A20.855,20.855,0,0,1,780,131.055a5.1,5.1,0,0,1,2.281-1.047c1.825-.216,3.383,1.282,5.173,1.7,2.7.631,5.288-1.235,7.989-1.81a10.009,10.009,0,0,1,9.015,2.944,13.214,13.214,0,0,1,3.515,9c.028,1.754-.228,3.6.435,5.194.3.747.8,1.41,1.058,2.173a8.026,8.026,0,0,1,.248,2.988,22.775,22.775,0,0,1-.431,4.139c-.967,4.207-4.29,7.559-8.092,9.589-2.237,1.2-4.681,2.03-6.726,3.528a10.1,10.1,0,0,1-1.6,1.091,3.654,3.654,0,0,1-3.571-.308,2.6,2.6,0,0,1-1.2-2.157,5.218,5.218,0,0,1,1.058-2.309c1.158-1.906,1.43-4.607-.112-6.225-.675-.707-1.6-1.123-2.265-1.846-1.07-1.2-1.2-2.924-1.234-4.523-.1-2.984-.611-5.8.635-8.51" transform="translate(-492.514 -82.679)" fill="#88455e"/>
    <path id="Path_110" data-name="Path 110" d="M747.364,309.514c-2.457,2.841-5.991,7.076-10.289,12.7a67.757,67.757,0,0,1-6.914,4.563,25.349,25.349,0,0,0-9.51,11.722c-13.089.687-28.911-25.338-28.911-25.338L702.1,302.311l17.663,12.625,5.752-12.325a20.771,20.771,0,0,1,16.8-11.9l.276-.024c3.679-.336,5.8,1.862,7.026,4.642A13.475,13.475,0,0,1,747.364,309.514Z" transform="translate(-442.102 -179.32)" opacity="0.1"/>
    <g id="Group_5" data-name="Group 5" transform="translate(227.45 83.97)" opacity="0.05">
      <path id="Path_111" data-name="Path 111" d="M747.527,222.324s-1.3,4.335,4.681,5.11c.647.084,1.29.2,1.925.348a32.166,32.166,0,0,0,5.424.819c-1.074-.168-2.025-.368-2.7-.527a18.129,18.129,0,0,0-1.925-.348c-5.991-.775-4.681-5.11-4.681-5.11a21.879,21.879,0,0,0-3.575-.455C746.972,222.212,747.255,222.264,747.527,222.324Z" transform="translate(-702.547 -222.16)"/>
      <path id="Path_112" data-name="Path 112" d="M776.625,240.1l-5.436-3.648a4.689,4.689,0,0,1-1.334.643l4.042,2.713s16.832,14.682,13.153,29.013c-2.233,8.7-10.313,16.089-16.321,20.559a67.756,67.756,0,0,1-6.914,4.562,25.349,25.349,0,0,0-9.51,11.722,27.414,27.414,0,0,0-1.965,9.313c1.706.436,2.708.711,2.708.711a27.482,27.482,0,0,1,2-9.748,25.349,25.349,0,0,1,9.51-11.722,67.752,67.752,0,0,0,6.914-4.563c5.991-4.467,14.088-11.862,16.321-20.559C793.457,254.78,776.625,240.1,776.625,240.1Z" transform="translate(-705.946 -230.741)"/>
      <path id="Path_113" data-name="Path 113" d="M636.478,419.271l-.288.2c.152,1.263,1,1.574,3.016.092,2.4-1.75,7.745-1.934,13.98-1.354C645.812,417.318,639.2,417.274,636.478,419.271Z" transform="translate(-636.19 -339.531)"/>
    </g>
    <path id="Path_114" data-name="Path 114" d="M663.83,361.35s3.088,7.99,7.373,10.887,9.9,7.807,9.9,7.807" transform="translate(-425.34 -221.771)" opacity="0.05"/>
    <path id="Path_115" data-name="Path 115" d="M572.954,425.678l-6.439-27.815c-2.748-4.567-5.572-7.02-7.941-8.326a10.529,10.529,0,0,0-6.247-1.8l-.863-.911c-3.283-4.638-5.025-16.78-5.025-16.78-.172-5.693-3.779-9.549-8.42-12.153-7.821-4.686-19.037-5.593-21.421-5.733l-1.9-3.783-.511-.124-.12-.24c-.4-.108-.851-.2-1.286-.288l3.363-69.033.032-.667,13.281.647a4.023,4.023,0,0,0,.4-8.03l-15.55-.759v-.336l1.454-29.828v-.336a6.093,6.093,0,0,0,2.333-4.531,5.839,5.839,0,1,0-11.664-.567,6.093,6.093,0,0,0,1.882,4.734v.336l-1.462,29.832v.336l-15.566-.735a4.023,4.023,0,0,0-.4,8.03l12.634.615-.032.667-3.407,69.916a92.405,92.405,0,0,0-14.435,3l-5.5,19.517-16.053,2.909-8.364,18.378-7.8,13.676c-.827.3-1.63.627-2.4.959h0c-29.058,12.265-30.237,42.725-30.237,42.725,8.1,13.807,48.982,7.755,48.982,7.755C487.616,477.859,534,465.7,534,465.7a135.858,135.858,0,0,0,27.017,4.219h0c19.468.743,24.365-5.741,24.365-5.741C592.386,446.409,572.954,425.678,572.954,425.678Z" transform="translate(-276.079 -142.135)" fill="url(#linear-gradient-2)"/>
    <path id="Path_153" data-name="Path 153" d="M0,0H35.15V7.484H0Z" transform="matrix(0.106, -0.994, 0.994, 0.106, 228.721, 131.666)" fill="#11999e"/>
    <path id="Path_154" data-name="Path 154" d="M0,0,78.761,0v12.69L0,12.69Z" transform="matrix(0.106, -0.994, 0.994, 0.106, 217.232, 211.612)" fill="#f0f5fa"/>
    <path id="Path_155" data-name="Path 155" d="M1.191.129H7.374l0,78.761H1.193Z" transform="matrix(0.994, 0.106, -0.106, 0.994, 230.92, 133.732)" opacity="0.05"/>
    <path id="Path_156" data-name="Path 156" d="M0,0H3.256V12.69H0Z" transform="matrix(0.106, -0.994, 0.994, 0.106, 225.256, 136.537)" opacity="0.1"/>
    <path id="Path_157" data-name="Path 157" d="M0,0H3.58V7.485H0Z" transform="matrix(0.106, -0.994, 0.994, 0.106, 228.721, 131.667)" opacity="0.1"/>
    <path id="Path_158" data-name="Path 158" d="M3.975.75H42.7a3.905,3.905,0,0,1,0,7.811H3.975a3.905,3.905,0,0,1,0-7.811Z" transform="matrix(0.994, 0.106, -0.106, 0.994, 209.681, 125.597)" fill="#11999e"/>
    <path id="Path_116" data-name="Path 116" d="M420.06,628.638s-.324-31.242,30.584-43.947l7.158-13.668,7.485-18.226,15.945-3.6,4.553-19.2s17.575-6.185,28.959-3.907L517.021,530s29.286-.324,30.584,15.621c0,0,2.928,14.646,7.158,17.251,0,0,7.158-1.626,14.315,8.789l7.809,26.688s20.5,19.2,14.315,36.776c0,0-8.46,12.042-51.41,3.907,0,0-45.878,13.983-70.3-5.194C469.521,633.844,428.847,641.654,420.06,628.638Z" transform="translate(-278.938 -320.393)" fill="#a8a8a8"/>
    <path id="Path_117" data-name="Path 117" d="M471.38,714.164s6.507-9.113,6.183-15.621S479.1,676.15,479.1,676.15" transform="translate(-309.76 -410.802)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_118" data-name="Path 118" d="M530.85,665.115s6.507-12.042,5.856-18.55c0,0,6.834-12.385,12.039-11.39" transform="translate(-345.476 -386.162)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_119" data-name="Path 119" d="M620.46,607.058s17.894-6.508,18.873-11.718" transform="translate(-399.293 -362.277)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_120" data-name="Path 120" d="M636.75,646s18.873-8.446,18.873-10.739h9.435s13.664,17.9,12.039,25.713" transform="translate(-409.076 -386.248)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_121" data-name="Path 121" d="M603.109,694.5s-18.222-1.3-24.729,3.907-18.222,7.811-18.222,7.811-6.834,14.646-9.435,17.251-6.834,11.718-6.834,11.718" transform="translate(-353.307 -421.735)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_122" data-name="Path 122" d="M692.15,737.9s1.937,6.832,4.553,10.739,6.507,17.9,6.507,17.9" transform="translate(-442.348 -447.881)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_123" data-name="Path 123" d="M768.72,787.59s4.13,11.494,7.761,11.586" transform="translate(-488.334 -477.719)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_124" data-name="Path 124" d="M701,664.1s10.729,2.9,15.214,9.928,18.2,13.4,18.2,13.4" transform="translate(-447.663 -403.566)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_125" data-name="Path 125" d="M712.51,577.42s0,14.97,4.23,14.97,13.668,1.642,13.668,1.642" transform="translate(-454.576 -351.516)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <path id="Path_126" data-name="Path 126" d="M650.7,532.95s-1.082,25.861-6.611,32.553" transform="translate(-413.484 -324.813)" fill="none" stroke="#000" stroke-miterlimit="10" stroke-width="1" opacity="0.1"/>
    <g id="Group_6" data-name="Group 6" transform="translate(161.924 204.905)" opacity="0.05">
      <path id="Path_127" data-name="Path 127" d="M703.433,597.6l-7.809-26.688c-7.158-10.416-14.316-8.789-14.316-8.789-4.23-2.6-7.158-17.235-7.158-17.235-1.3-15.949-30.584-15.621-30.584-15.621l-2.277-3.907a34.753,34.753,0,0,0-9.45-.34c.8.08,1.554.192,2.293.34l2.277,3.907s29.286-.324,30.584,15.621c0,0,2.928,14.646,7.158,17.251,0,0,7.158-1.626,14.316,8.789l7.809,26.688s20.5,19.185,14.315,36.764c0,0-4.174,5.941-21.541,6.656,23.41.859,28.7-6.656,28.7-6.656C723.932,616.806,703.433,597.6,703.433,597.6Z" transform="translate(-568.051 -524.86)"/>
      <path id="Path_128" data-name="Path 128" d="M492.986,795.74A152.037,152.037,0,0,1,472.14,798a132.431,132.431,0,0,0,22.092-1.322Q493.605,796.22,492.986,795.74Z" transform="translate(-472.14 -687.518)"/>
      <path id="Path_129" data-name="Path 129" d="M654.088,808.77a133.674,133.674,0,0,1-30.728,4.858c15,.543,29.206-2.581,35.066-4.083C657.02,809.309,655.57,809.054,654.088,808.77Z" transform="translate(-562.959 -695.342)"/>
    </g>
    <path id="Path_159" data-name="Path 159" d="M.483.077H4.389V31.971H.483Z" transform="matrix(0.994, 0.106, -0.106, 0.994, 235.42, 96.788)" opacity="0.05"/>
    <path id="Path_130" data-name="Path 130" d="M655.793,254.889l-.272,2.541a5.859,5.859,0,0,1-7.441-.8l.272-2.541Z" transform="translate(-415.881 -157.363)" opacity="0.1"/>
    <path id="Path_160" data-name="Path 160" d="M5.757-.086A5.856,5.856,0,1,1-.1,5.769,5.856,5.856,0,0,1,5.757-.086Z" transform="matrix(0.106, -0.994, 0.994, 0.106, 230.071, 99.962)" fill="#11999e"/>
    <path id="Path_131" data-name="Path 131" d="M663.567,241.3a5.856,5.856,0,0,1-6.447,5.194l1.246-11.65a5.857,5.857,0,0,1,5.2,6.456Z" transform="translate(-421.31 -145.804)" opacity="0.05"/>
    <path id="Path_132" data-name="Path 132" d="M677.164,294.243l-4.082,6.62-3.906-3.392-5.352-4.65a18.82,18.82,0,0,1-16.233-8.789c-2.716-4.1-.6-6.452,2.692-7.791a13.674,13.674,0,0,1,15.741,4.195Z" transform="translate(-414.84 -170.085)" fill="#ffbec7"/>
    <path id="Path_133" data-name="Path 133" d="M644.855,306.585l-4.078,6.62-3.906-3.4,5.74-6.009Z" transform="translate(-382.53 -182.427)" opacity="0.1"/>
    <path id="Path_134" data-name="Path 134" d="M728.876,299.9l-5.752,12.321-17.679-12.625L695.1,310.447s23.734,39.041,37.107,20.224c8.112-11.43,14.647-19.389,18.525-23.875A13.466,13.466,0,0,0,753,292.612c-1.222-2.8-3.347-4.974-7.026-4.642L745.7,288A20.771,20.771,0,0,0,728.876,299.9Z" transform="translate(-444.12 -177.687)" fill="#dfe5ee"/>
    <path id="Path_135" data-name="Path 135" d="M982.7,586.079s-5.3-17.978.995-31.219a28.408,28.408,0,0,0,2.3-17.819,47.773,47.773,0,0,0-2.52-8.362" transform="translate(-615.589 -322.249)" fill="none" stroke="#535461" stroke-miterlimit="10" stroke-width="2"/>
    <path id="Path_136" data-name="Path 136" d="M985.529,501.735c0,2.205-3.994,8.594-3.994,8.594s-3.994-6.392-3.994-8.594a3.994,3.994,0,1,1,7.989,0Z" transform="translate(-613.745 -303.67)" fill="#11999e"/>
    <path id="Path_137" data-name="Path 137" d="M1002,529.253c-1.2,1.85-8.021,5.046-8.021,5.046s.124-7.535,1.322-9.385a3.995,3.995,0,0,1,6.7,4.339Z" transform="translate(-623.619 -318.983)" fill="#11999e"/>
    <path id="Path_138" data-name="Path 138" d="M999.09,579.039c-1.969.991-9.47.308-9.47.308s3.91-6.44,5.876-7.431a4,4,0,1,1,3.595,7.128Z" transform="translate(-621 -347.906)" fill="#11999e"/>
    <path id="Path_139" data-name="Path 139" d="M989.933,618.9c-1.773,1.31-9.283,1.9-9.283,1.9s2.764-7.008,4.537-8.318a3.995,3.995,0,1,1,4.745,6.416Z" transform="translate(-615.613 -372.029)" fill="#11999e"/>
    <path id="Path_140" data-name="Path 140" d="M970.941,541.469c1.6,1.534,8.943,3.128,8.943,3.128s-1.8-7.315-3.383-8.849a3.995,3.995,0,1,0-5.56,5.721Z" transform="translate(-608.96 -325.721)" fill="#11999e"/>
    <path id="Path_141" data-name="Path 141" d="M959.017,586.719c1.969.991,9.47.308,9.47.308s-3.91-6.44-5.876-7.431a3.995,3.995,0,1,0-3.595,7.127Z" transform="translate(-601.21 -352.517)" fill="#11999e"/>
    <path id="Path_142" data-name="Path 142" d="M956.336,633.836c1.773,1.31,9.283,1.9,9.283,1.9s-2.764-7.008-4.538-8.318a3.995,3.995,0,0,0-4.745,6.416Z" transform="translate(-600.13 -381.137)" fill="#11999e"/>
    <path id="Path_143" data-name="Path 143" d="M114.835,517.87a39.918,39.918,0,0,1,7.278,41.518c-9.434,24.8,16.185,66.049,16.185,66.049s-.276-.04-.8-.128c-34.323-6.061-51.374-45.322-32.29-74.491C112.306,539.971,118.13,527.251,114.835,517.87Z" transform="translate(-85.073 -315.758)" fill="#11999e"/>
    <path id="Path_144" data-name="Path 144" d="M114.841,518.56a39.918,39.918,0,0,1,7.278,41.518c-9.434,24.8,16.185,66.049,16.185,66.049s-.276-.04-.8-.128c-34.319-6.061-51.37-45.338-32.286-74.5C112.312,540.665,118.136,527.925,114.841,518.56Z" transform="translate(-85.079 -316.172)" opacity="0.1"/>
    <path id="Path_145" data-name="Path 145" d="M135.447,517.87s9.434,20.487,0,37.2-1.6,63.624,23.45,70.364" transform="translate(-105.685 -315.758)" fill="none" stroke="#535461" stroke-miterlimit="10" stroke-width="1"/>
    <path id="Path_146" data-name="Path 146" d="M66.75,686.766s19.069-3.036,20.806,14.055,36.667,19.473,36.667,19.473l-.655.428c-28.839,18.937-56.411,12.062-52.253-13.084C72.841,698.292,72.733,688.9,66.75,686.766Z" transform="translate(-66.75 -417.051)" fill="#11999e"/>
    <path id="Path_147" data-name="Path 147" d="M66.75,687.08s14.8,3.248,13.581,16.712,21.984,29.6,43.893,16.816" transform="translate(-66.75 -417.365)" fill="none" stroke="#535461" stroke-miterlimit="10" stroke-width="1"/>
    <path id="Path_148" data-name="Path 148" d="M945.12,655.529s8.951-.276,11.647-2.2,13.768-4.215,14.435-1.135,13.449,15.321,3.347,15.405-23.478-1.574-26.17-3.2S945.12,655.529,945.12,655.529Z" transform="translate(-594.275 -395.514)" fill="#a8a8a8"/>
    <path id="Path_149" data-name="Path 149" d="M974.73,673.815c-10.106.08-23.478-1.574-26.171-3.2-2.049-1.25-2.868-5.729-3.14-7.8h-.3s.567,7.219,3.259,8.861,16.065,3.3,26.17,3.2c2.916-.024,3.922-1.063,3.87-2.6C978.013,673.216,976.9,673.795,974.73,673.815Z" transform="translate(-594.275 -402.797)" opacity="0.2"/>
    <path id="Path_150" data-name="Path 150" d="M159,752.379s-15.022-3.012-19.037-7.008-22.028-11.015-24.03-5.993-27.025,22.014-10,25.018,40.051,4,45.055,2S159,752.379,159,752.379Z" transform="translate(-86.735 -447.721)" fill="#a8a8a8"/>
    <path id="Path_151" data-name="Path 151" d="M105.939,784.568c17.024,3,40.051,4,45.055,2,3.815-1.526,6.463-8.853,7.509-12.265l.5.1s-3,12.018-8.009,14.019-28.036,1-45.055-2c-4.913-.867-6.319-2.9-5.788-5.481C100.567,782.614,102.28,783.908,105.939,784.568Z" transform="translate(-86.747 -469.739)" opacity="0.2"/>
  </g>
</svg>
