// types/afcat-paper-types.ts

export interface ChapterPaper {
  paper_id: string
  paper_name: string
  group_id: string
  sub_group_id: string
  level: string
  no_of_ques: string
  status: string
  show_ans: string
  public: string
  once_ans: string
  created_at: string
  time_lim: number
  type: number
  neg_marks: string
  rand_ques: string
}

export interface AnswerSubmission {
  email: string
  paper_id: string
  marks: number
}

export interface SectionAnswerSubmission {
  email: string
  paper_id: string
  answer_id: string
  data: any
}

export interface QuestionExplanation {
  question_no: string
  explanation: string
}
