<div class="forgot-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="forgot-card">
    <div class="instr">
      <p>Forgot Password?</p>
      <p>Enter your email below and we'll mail the details to reset your password.</p>
    </div>
    <form #pwForgotForm="ngForm" (ngSubmit)="handleForgotPW(pwForgotForm.valid)">
        <label for="email">Email:</label>
      <input type="text" name="email" placeholder="<EMAIL>" [(ngModel)]="e_mail" #email="ngModel" [class.is-inv]="email.touched && email.invalid || (email.pristine && pwForgotForm.submitted)" required email>
      <small class="form-text text-danger" *ngIf="email.touched && email.errors?.required || (email.pristine && pwForgotForm.submitted)">Enter your email please!</small>
      <small class="form-text text-danger" *ngIf="email.touched && email.errors?.email && pwForgotForm.submitted">That doesn't look like an email address.</small>
      <button type="submit" class="custom-btn">Reset Password</button>
    </form>
  </div>
  <ng-template #successTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-success">Forgot Password</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      We have sent a mail to your email with the instructions to reset your password.
    </div>
  </ng-template>
  <ng-template #genErrorTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-danger">OOPS!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p *ngIf="!emailErr">{{ resetErr }}</p>
      <p *ngIf="emailErr">Email not found, please create your account <a href="https://quantmasters.in/user/register">here</a>.</p>
    </div>
  </ng-template>
</div>
