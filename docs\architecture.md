# Quant Masters Application - Architectural Guidelines

## Architecture Overview

This is a **Next.js 15 SSR-based application** with TypeScript, migrated from Angular. It follows a modular, component-based architecture with clear separation of concerns.

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/UI components
- **Icons**: Lucide React
- **Charts**: Chart.js with react-chartjs-2
- **State Management**: React hooks (useState, useEffect)
- **Authentication**: Custom middleware with cookie-based auth

## Directory Structure & Organization

### 1. App Router Structure (`app/`)

The application uses Next.js App Router with route groups for organization:

```
app/
├── (marketing)/         # Public marketing pages
├── (auth)/              # Authentication pages  
├── (dashboard)/         # Protected dashboard pages
├── (technical)/         # Technical support pages
├── (admin)/             # Admin console
├── blog/                # Blog pages
├── notes/               # Notes pages
├── placement/           # Placement related pages
├── certification/       # Certification pages
├── faqs/                # FAQ pages
├── layout.tsx           # Root layout
├── loading.tsx          # Global loading component
└── not-found.tsx        # 404 page
```

**Route Groups Purpose**:
- `(marketing)`: Landing page, achievers, checkout, links, privacy policy
- `(auth)`: User authentication (login, register, forgot password)
- `(dashboard)`: All protected user functionality
- `(technical)`: Technical support and documentation
- `(admin)`: Admin console for content management

### 2. Component Architecture (`components/`)

Components are organized by domain and reusability:

```
components/
├── ui/                  # Reusable UI primitives (shadcn/ui)
├── layout/              # Layout components (nav, footer, sidebar)
├── shared/              # Shared utilities (indicators, etc.)
└── [domain]/            # Feature-specific components
```

**Domain-Specific Components**:
- `chapter-papers/` - Chapter practice functionality
- `competitive-papers/` - Competitive exam papers
- `notes/` - Notes and reading functionality
- `test/` - Test taking interface
- `student-exp/` - Student reviews/testimonials

### 3. Service Layer (`lib/`)

Services are split between server and client:

```
lib/
├── server-services/     # Server-side data fetching
├── client-services/     # Client-side API calls
├── hooks/               # Custom React hooks
├── auth.ts              # Authentication utilities
├── cookies.ts           # Cookie handling
└── utils.ts             # General utilities
```

## Component Patterns

### 1. Server-First Approach

Pages fetch data on the server and pass to client components:

```tsx
// Server Component (page.tsx)
export default async function ChapterPapersPage() {
  const superGroups = await ServerChaptersService.getSuperGroups()
  return <ChapterPapersClient initialSuperGroups={superGroups} />
}
```

### 2. Client Component Pattern

Client components handle interactivity and state:

```tsx
// Client Component
'use client'
export default function ChapterPapersClient({ initialSuperGroups }) {
  const [groups, setGroups] = useState(initialSuperGroups)
  // Interactive logic here
}
```

### 3. UI Component Structure

Consistent component structure using shadcn/ui:

```tsx
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
```

## Data Flow Architecture

### 1. Server-Side Data Fetching

- Server services handle initial data loading
- Located in `lib/server-services`
- Used in page components for SSR

### 2. Client-Side Interactions

- Client services handle user interactions
- Located in `lib/client-services`
- Used in client components for dynamic updates

### 3. State Management

- React hooks for component state
- No external state management library
- Props drilling for data sharing

## Key Architectural Decisions

### 1. Component Naming Convention

- **Pages**: `page.tsx` (Next.js convention)
- **Client Components**: `*-client.tsx` 
- **Server Services**: `*.server.ts`
- **Client Services**: `*.client.ts`

### 2. Route Structure

The application uses dynamic routing with catch-all segments for flexibility:

- `/user/[...slug]` - Authentication pages
- `/dashboard/[...slug]` - Dashboard pages
- `/technical/[...slug]` - Technical pages
- `/admin/[...slug]` - Admin pages
- `/blog/[...slug]` - Blog posts
- `/notes/[...slug]` - Notes pages
- `/placement/[...slug]` - Placement content
- `/certification/[...slug]` - Certification content
- `/faqs/[...slug]` - FAQ pages

### 3. Authentication Flow

- Middleware-based route protection (`middleware.ts`)
- Cookie-based session management
- Protected routes: `/dashboard`, `/admin`, `/technical`

### 4. Styling Approach

- Tailwind CSS for utility-first styling
- Shadcn/ui for consistent component library
- Lucide React for icons (no hardcoded SVGs)

### 5. Type Safety

- TypeScript throughout
- Type definitions in `types` directory
- Interface definitions for props and data structures

## Navigation Structure

### 1. Main Navigation (`components/layout/nav.tsx`)

- Conditional rendering based on route
- Hidden on `/dashboard`, `/admin`, `/technical` routes
- Dropdown menus for organized navigation

### 2. Dashboard Sidebar (`components/layout/sidebar.tsx`)

- Grouped navigation sections:
  - Interviews & Testimonials
  - Notes & Resources  
  - Practice & Assessments

### 3. Responsive Design

- Mobile-first approach with Tailwind breakpoints
- Collapsible navigation for mobile
- Grid layouts that adapt to screen size

## Test Interface Architecture

The test-taking functionality (`components/test/test-container.tsx`) follows a complex pattern:

1. **Test Types**: Different test modes (1-11) for various paper types
2. **Question Management**: State for different question types
3. **Timer Integration**: Countdown timer with popout functionality
4. **Result Calculation**: Client-side answer evaluation

## Performance Considerations

1. **Server-Side Rendering**: Initial data loaded server-side
2. **Dynamic Imports**: Chart components loaded lazily
3. **Image Optimization**: Next.js Image component usage
4. **Loading States**: Skeleton components for better UX
5. **Global Loading**: Centralized loading component at app level

## Development Guidelines

### 1. File Organization

- Keep server logic in page components
- Extract client interactions to separate components
- Use meaningful file naming conventions
- Group related functionality in folders

### 2. Component Design

- Single responsibility principle
- Props interface definitions
- Consistent error handling patterns
- Reusable UI components in `components/ui/`

### 3. Data Fetching

- Server-side for initial loads
- Client-side for user interactions
- Proper loading and error states
- Use Next.js built-in loading.tsx for route-level loading

### 4. Route Management

- Use route groups for logical organization
- Implement catch-all routes for flexibility
- Maintain consistent URL patterns
- Handle 404 cases with not-found.tsx

### 5. Layout Strategy

- Root layout for global styles and providers
- Conditional navigation based on route groups
- Consistent footer across all pages
- Responsive design patterns

## Security Considerations

1. **Authentication**: Cookie-based sessions with secure headers
2. **Route Protection**: Middleware for protected routes
3. **Data Validation**: Type checking with TypeScript
4. **CORS**: Proper API endpoint security

## Future Scalability

1. **Modular Architecture**: Domain-driven component organization
2. **Service Layer**: Clear separation of server and client logic
3. **Type Safety**: Full TypeScript coverage
4. **Performance**: SSR + selective client hydration
5. **Extensibility**: Flexible routing with catch-all segments

This architecture provides a scalable foundation for the educational platform while maintaining clear separation between server and client responsibilities, with room for future enhancements and feature additions.