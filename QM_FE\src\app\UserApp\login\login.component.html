<app-nav></app-nav>
<div class="login-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="login-content">
    <div class="login">
      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="60" height="60"
        viewBox="0 0 70 70">
        <defs>
          <linearGradient id="linear-gradient" x1="0.5" x2="0.5" y2="1" gradientUnits="objectBoundingBox">
            <stop offset="0" stop-color="#38a3e9" />
            <stop offset="1" stop-color="#0b6fb1" stop-opacity="0.98" />
          </linearGradient>
        </defs>
        <g id="Group_18" data-name="Group 18" transform="translate(-315 -256)">
          <rect id="Rectangle_85" data-name="Rectangle 85" width="70" height="70" transform="translate(315 256)"
            fill="url(#linear-gradient)" />
          <path id="ic_perm_identity_24px"
            d="M24,8.75A5.25,5.25,0,1,1,18.75,14,5.249,5.249,0,0,1,24,8.75m0,22.5c7.425,0,15.25,3.65,15.25,5.25v2.75H8.75V36.5c0-1.6,7.825-5.25,15.25-5.25M24,4A10,10,0,1,0,34,14,10,10,0,0,0,24,4Zm0,22.5c-6.675,0-20,3.35-20,10V44H44V36.5C44,29.85,30.675,26.5,24,26.5Z"
            transform="translate(326 267)" fill="#fff" />
        </g>
      </svg>
      <p class="sect-text--1">Login on QuantMasters</p>
      <p class="sect-text--2">Login to get access to your resources</p>
      <form #loginForm="ngForm" (ngSubmit)="login(loginForm.valid)">
        <div class="form-group--1">
          <div class="form-elem">
            <input name="email" type="email" placeholder="Email" [(ngModel)]="user.email" #email="ngModel"
              [class.is-inv--input]="email.touched && email.invalid || (email.pristine && loginForm.submitted)" required
              email autocomplete="on" />
            <small class="form-error--text"
              *ngIf="email.touched && email.errors?.required || (email.pristine && loginForm.submitted)">Enter your
              email
              please!</small>
            <small class="form-error--text" *ngIf="email.touched && email.errors?.email">That doesn't look like an
              email address.</small>
          </div>
          <div class="form-elem">
            <input name="password" type="password" placeholder="Password" aria-describedby="passwordHelpBlock"
              [(ngModel)]="user.passwd" #password="ngModel"
              [class.is-inv--input]="password.touched && password.errors?.required || (password.pristine && loginForm.submitted)"
              required autocomplete="on" />
            <small class="form-error--text"
              *ngIf="password.touched && password.errors?.required || (email.pristine && loginForm.submitted)">Please
              Enter your
              password!</small>
          </div>
        </div>
        <div class="form-group--1">
          <!-- <form>
            <mat-checkbox>Remember me!</mat-checkbox>
          </form> -->
          <button class="custom-btn" type="submit">Log In</button>
          <p>
            <a routerLink="/user/password/forgot">Forgot Your Password?</a>
          </p>
          <p>
            Don't have an account? <a routerLink="/user/register">Sign Up Here.</a>
          </p>
        </div>
      </form>
    </div>
    <div class="login-guy">
      <svg xmlns="http://www.w3.org/2000/svg" width="465.715" height="435.793" viewBox="0 0 582.715 575.793">
        <g id="Group_84" data-name="Group 84" transform="translate(-3635.285 -1572.655)">
          <g id="Group_43" data-name="Group 43" transform="translate(-214.366 300.655)">
            <g id="Group_41" data-name="Group 41" transform="translate(3850.995 1272)">
              <g id="Group_35" data-name="Group 35" transform="translate(0)">
                <g id="undraw_next_option_2ajo" transform="translate(0)">
                  <path id="Path_2067" data-name="Path 2067" d="M53.26,6.447,105.474-.274,50.813,535.65-1.4,542.372Z"
                    transform="matrix(0.926, -0.379, 0.379, 0.926, 77.282, 59.171)" fill="#d0cde1" />
                  <path id="Path_2068" data-name="Path 2068" d="M51.007,6.453,103.22-.268,50.871,512.989-1.342,519.71Z"
                    transform="matrix(0.926, -0.379, 0.379, 0.926, 0, 72.463)" fill="#d0cde1" />
                  <path id="Path_2069" data-name="Path 2069" d="M53.26,6.447,105.474-.274,50.813,535.65-1.4,542.372Z"
                    transform="matrix(0.926, -0.379, 0.379, 0.926, 166.116, 43.061)" fill="#d0cde1" />
                  <ellipse id="Ellipse_57" data-name="Ellipse 57" cx="146.374" cy="33.973" rx="146.374" ry="33.973"
                    transform="translate(149.314 507.846)" fill="#3f3d56" />
                  <path id="Path_2049" data-name="Path 2049"
                    d="M633.508,505.164l-1.1-.467L220.917,330.509l1.132-.784L504.527,133.961l.222.64ZM223.632,330.194l407.963,172.7L504.062,135.85Z"
                    transform="translate(-97.025 -67.309)" fill="#3f3d56" />
                  <rect id="Rectangle_614" data-name="Rectangle 614" width="102.462" height="135.893"
                    transform="translate(478.909 360.746)" fill="#0b6fb1" />
                  <rect id="Rectangle_615" data-name="Rectangle 615" width="102.462" height="135.893"
                    transform="translate(369.38)" fill="#0b6fb1" />
                  <rect id="Rectangle_616" data-name="Rectangle 616" width="102.462" height="135.893"
                    transform="translate(80.165 194.733)" fill="#0b6fb1" />
                  <path id="Path_2063" data-name="Path 2063" d="M716.165,74.307l26.247,16.111-26.247,15.41Z"
                    transform="translate(-305.035 -22.257)" fill="#3f3d56" />
                  <path id="Path_2064" data-name="Path 2064"
                    d="M236.08,445.2H158.35V337.328h77.73Zm-76.65-1.5H235V338.826H159.43Z"
                    transform="translate(-66.038 -128.222)" fill="#d0cde1" />
                  <rect id="Rectangle_617" data-name="Rectangle 617" width="47.502" height="6.013"
                    transform="translate(107.426 243.225)" fill="#3f3d56" />
                  <rect id="Rectangle_618" data-name="Rectangle 618" width="47.502" height="6.013"
                    transform="translate(107.426 260.036)" fill="#3f3d56" />
                  <rect id="Rectangle_619" data-name="Rectangle 619" width="47.502" height="6.013"
                    transform="translate(107.426 276.848)" fill="#3f3d56" />
                  <path id="Path_2065" data-name="Path 2065"
                    d="M1026.171,665.9H950.46V614.766h75.711Zm-74.7-1.4h73.692V616.167H951.469Z"
                    transform="translate(-458.339 -211.321)" fill="#d0cde1" />
                  <path id="Path_2066" data-name="Path 2066"
                    d="M809.495,150.444H733.784V99.309h75.711Zm-74.7-1.4h73.692V100.71H734.794Z"
                    transform="translate(-351.028 -56.93)" fill="#d0cde1" />
                </g>
              </g>
            </g>
            <path id="Path_2189" data-name="Path 2189"
              d="M625.985,588.09l6.24,4.858-13.288,10.38-10.9-8.5L587.87,610.6l3.843,3,16.346-12.748,10.9,8.5,17.153-13.368,6.24,4.867V588.09Z"
              transform="translate(3768.566 1097.868)" fill="#3f3d56" />
            <path id="Path_2206" data-name="Path 2206" d="M179.649,94.816V66.772h-4.6V98.435h59.322V94.816Z"
              transform="translate(4176.547 1619.186)" fill="#3f3d56" />
          </g>
          <g id="Group_83" data-name="Group 83" transform="translate(-681 52.316)">
            <path id="Path_2342" data-name="Path 2342" d="M0,0,14.615.068,14.7,21.106.087,21.038Z"
              transform="translate(4607.702 1686.401) rotate(-177.779)" fill="#0b6fb1" />
            <path id="Path_2324" data-name="Path 2324"
              d="M361.157,312.271s20.221-10.82,19.9-1.476-20.383,15.492-20.383,15.492Z"
              transform="translate(4298.54 1426.106)" fill="#ffb9b9" />
            <path id="Path_2325" data-name="Path 2325"
              d="M288.046,425.734s9.54,26.151,13.065,45.021,8.893,44.84,1.988,63.257S276.562,606.559,276.36,612.4s1.682,11.771-2.49,11.59S247,615.8,246.074,612.254s7.746-12.532,7.746-12.532l21.4-75.109-22.33-48.933-16.107,73-3.155,91.109s-15.483-5.352-17.731-.77c0,0-6.937-10.83-7.818-15.547s3.276-94.613,3.276-94.613-14.212-102.392-2.739-101.894S265.028,396.659,288.046,425.734Z"
              transform="translate(4360.556 1391.709)" fill="#2f2e41" />
            <path id="Path_2326" data-name="Path 2326"
              d="M298.682,714.138s10.583,26.2,11.585,27.409,7.9,13.211,2.684,12.985-17.528-6.61-26.592-16.363-21.013-26.649-19.929-27.772,8.506-4.31,8.506-4.31Z"
              transform="translate(4335.905 1296.888)" fill="#2f2e41" />
            <path id="Path_2327" data-name="Path 2327"
              d="M236.769,738.483s.639,11.726,2.563,16.489,1.682,11.771-3.533,11.545-18.693-3.151-18.693-3.151-.8-7.054.283-8.177,5.7-13.79,3.736-17.385S236.769,738.483,236.769,738.483Z"
              transform="translate(4355.534 1287.085)" fill="#2f2e41" />
            <path id="Path_2328" data-name="Path 2328"
              d="M226.331,176.768s-11.117,19.4-15.451,23.9,7.858,14.379,7.858,14.379l21.822,3.287s-.274-22.239.849-24.529S226.331,176.768,226.331,176.768Z"
              transform="translate(4358.256 1468.27)" fill="#ffb9b9" />
            <path id="Path_2329" data-name="Path 2329"
              d="M240.615,211.756s-4.5,9.163-8.667,8.982-25.75-10.477-26.672-14.026-7.148,25.426-7.148,25.426l52.8,104.069,12.88-9.969-6.241-61.1-5.125-32.978Z"
              transform="translate(4362.94 1458.672)" fill="#0b6fb1" />
            <path id="Path_2330" data-name="Path 2330"
              d="M192.505,301.526a35.967,35.967,0,0,1,3.239,15.348c-.008.234-.016.467-.035.7-.5,10.811-4.056,23.314-5.332,32.407-.926,6.523-.662,11.283,2.746,12.4,8.263,2.7-1.2,4.627,30.763,16.544s37.547,1.631,37.628-.706-4.527-20.084-3.444-21.206,15.159,14.7,19.412,12.541.485-14.017.485-14.017-9.581-24.982-9.378-30.823-14.512-33.385-14.512-33.385L244.07,218.369s-3.848-9.526-9.023-10.92-9.468,1.928-9.468,1.928l8.659,21.433,9.135,37.831-1.528,13.971s-10.623-25.028-17.6-34.689-21.853-32.534-21.853-32.534-3.473-13.124-.851-17.046-10.936-4.523-16.718,11.6c-3.064,8.547-8.039,21.293-11.155,33.16-2.777,10.513-4.089,20.341-1.338,25.959C176.535,277.647,187.113,288.928,192.505,301.526Z"
              transform="translate(4373.762 1462.038)" fill="#575a89" />
            <path id="Path_2331" data-name="Path 2331"
              d="M172.33,290.223c4.2,8.582,14.782,19.862,20.174,32.462,1.408-5.683,3.127-11.562,3.127-11.562s6.59-39.488-12.9-49.693a12.055,12.055,0,0,0-9.06,2.835C170.891,274.777,169.579,284.605,172.33,290.223Z"
              transform="translate(4373.762 1440.88)" opacity="0.1" />
            <path id="Path_2332" data-name="Path 2332"
              d="M300.426,300.362l16.526,5.4,22.945,1,2.36,22.329-37.709,3.042S295.131,302.472,300.426,300.362Z"
              transform="translate(4323.049 1428.253)" fill="#575a89" />
            <path id="Path_2333" data-name="Path 2333"
              d="M251.889,411.388S274,415.5,268.952,422.947s-24.631-.382-24.631-.382Z"
              transform="translate(4344.638 1392.305)" fill="#ffb9b9" />
            <path id="Path_2334" data-name="Path 2334"
              d="M208.549,375.78c-.5,10.811-4.056,23.314-5.332,32.407,7.043,7.465,13.74,14.05,16.264,14.159,5.215.226,13.6-.579,16.688.725s4.9-20.844,4.9-20.844A18.518,18.518,0,0,0,231,391.261C225.453,388.645,214.814,381.55,208.549,375.78Z"
              transform="translate(4360.923 1403.834)" opacity="0.1" />
            <path id="Path_2335" data-name="Path 2335"
              d="M179.97,251.059s-17.65-3.106-16.7,29.69-1.86,53.731,5.238,59.889,30.157,34.065,35.372,34.291,13.6-.579,16.688.725,4.9-20.844,4.9-20.844a18.518,18.518,0,0,0-10.066-10.966c-8.222-3.867-27.594-17.576-27.432-22.248s4.9-20.844,4.9-20.844S199.463,261.265,179.97,251.059Z"
              transform="translate(4376.764 1444.241)" fill="#575a89" />
            <ellipse id="Ellipse_97" data-name="Ellipse 97" cx="22.963" cy="25.717" rx="22.963" ry="25.717"
              transform="translate(4577.188 1622.337)" fill="#ffb9b9" />
            <path id="Path_2336" data-name="Path 2336"
              d="M261.109,124.232a4.275,4.275,0,0,0,1.931,1.085,1.4,1.4,0,0,0,1.6-1.239,6.355,6.355,0,0,0,2.191,3.147,1.922,1.922,0,0,0,2.886-1.242,5.984,5.984,0,0,0,1.165,2.755,1.616,1.616,0,0,0,2.453.092c-.47,2.875-.18,5.832-.327,8.75s-.842,6.012-2.811,7.93c-2.871,2.8-7.188,2.108-10.964,1.578a2.988,2.988,0,0,0-1.718.105c-1.414.675-1.35,2.879-1.369,4.6-.067,6.028-4.635,11.576-9.994,12.138a5.263,5.263,0,0,1-4.252-1.162c-1.072-1.018-1.62-2.677-2.849-3.441-2-1.244-4.422.57-5.884,2.54s-2.9,4.392-5.165,4.753c-2.994.477-5.274-2.94-6.393-6.086a39.413,39.413,0,0,1,.63-27.569C228.486,117.7,249.84,115.29,261.109,124.232Z"
              transform="translate(4354.503 1486.961)" fill="#2f2e41" />
          </g>
        </g>
      </svg>
    </div>
  </div>
</div>
<svg class="neg-space" xmlns="http://www.w3.org/2000/svg" width="184.715" height="186.713"
  viewBox="0 0 184.715 186.713">
  <ellipse id="Ellipse_1" data-name="Ellipse 1" cx="59" cy="72.5" rx="59" ry="72.5"
    transform="translate(97.024) rotate(42)" fill="#e88224" />
</svg>
<!-- <div class="login-wrap"> -->
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>

  <ng-template #failureTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-danger">Bad Login</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p *ngIf="!askReg">{{ loginErr }}</p>
      <p *ngIf="askReg">Email not found, please create your account <a href="https://quantmasters.in/user/register">here</a>.</p>
    </div>
  </ng-template>
<!-- </div> -->