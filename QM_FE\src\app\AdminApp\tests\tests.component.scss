.tests-upload-container {
  height: 100%;
  max-width: 1000px;
  margin: auto;
  padding: 10px;
  font: 17px 'Montserrat', 'sans-serif';
  
  .sect-tests--heading {
    font-size: 21px;
    font-weight: bold;
    margin-bottom: 2em;
  }

  .sect-tests--operations {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    width: 100%;

    .ops--card {
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2em 1em;
      background-color: #fff;
      box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.16);
      transform: scale(1);
      transition: all 0.3s ease-out;

      &:hover {
        transform: scale(1.05);
        box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.16);
        transition: all 0.2s ease-in;
      }

      img {
        height: 70px;
        width: 70px;
      }

      p {
        margin: 1em;
        text-align: center;
      }
    }
  }
}

@media (max-width: 440px) {
  .tests-upload-container {
    .sect-tests--operations {
      text-align: center;
    }

    .sect-tests--operations {
      grid-template-columns: 1fr 1fr;
    }
  }
}