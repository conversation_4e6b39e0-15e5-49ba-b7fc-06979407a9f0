.model-paper-list-container {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;

  .page-header {
    display: flex;
    height: 2.5rem;
    margin-bottom: 2em;

    .title {
      display: flex;
      justify-content: space-between;
      width: 100%;
      margin: 0 1rem;
      padding: 0.5rem;
      border-bottom: 1px solid;
    }
  }

  .content {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1rem;

    .paper-wrap {
      width: 90%;
      padding: 10px;
  
      .paper {
        position: relative;
        display: grid;
        grid-template-columns: 30% 30% 20% 20%;
        align-items: center;
        padding: 0.5em;
        color: #fff;
        background-color: #38A3E9;
        margin-bottom: 1em;
  
        .locked-resource {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
          width: 100%;
          height: 57px;
          background-color: rgba(146, 135, 135, 0.85);
  
          img {
            display: block;
            height: 100%;
            margin: auto;
          }
        }
  
        h6, p {
          margin: 0;
        }
  
        button {
          justify-self: end;
          padding: 0.5em 1em;
          color: #fff;
          cursor: pointer;
          background-color: #E88224;
          border: none;
          border-radius: 5px;
          transition: all 0.4s ease-out;
  
          &:hover {
            background-color: #0B6FB1;
            transition: all 0.2s ease-in;
          }
        }
      }
    }
  }

  // .content {
  //   // position: relative;
  //   // width: 100%;
  //   display: flex;
  //   justify-content: center;
  //   flex-direction: column;
  //   padding: 3rem 2rem;
  //   background-color: #ffffff;
  //   margin: 3rem 1rem;
  //   border-radius: 3px;
  //   table {
  //     border-bottom: 2px solid;
  //     text-align: center;

  //     th {
  //       width: 10rem;
  //       height: 2rem;
  //       background-color: #0b6fb1;
  //       color: white;
  //     }

  //     td {
  //       height: 2rem;
  //       text-align: left;
  //       padding: 1em;
  //     }

  //     .disabled {
  //       background-color: rgba(146, 135, 135, 0.85);
  //     }

  //     .active {
  //       background-color: #e27723;
  //     }
  //   }

  //   .footer {
  //     width: 100%;
  //     button {
  //       cursor: pointer;
  //       float: right;
  //       margin: 1rem;
  //       width: 10rem;
  //       padding: 0.3rem;
  //       background-color: #e88224;
  //       border: none;
  //       border-radius: 5px;
  //       color: white;
  //     }
  //   }
  // }
}

.copy-content {
  text-align: right;

  p {
    color: #707070;
    margin: 0;
  }
}

@media (max-width: 440px) {
  .model-paper-list-container {
    .page-header {
      height: 3.5rem;
    }

    .content {
      margin: 0;

      .paper-wrap {

        .paper {
          padding: 20px;
          grid-template-columns: 1fr;

          .locked-resource {
            height: 100%;

            img {
              height: 50%;
            }
          }
        }
      }
    }
  }
}
