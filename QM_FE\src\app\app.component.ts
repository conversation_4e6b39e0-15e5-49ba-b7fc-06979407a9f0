import { Component } from '@angular/core';
import { pageTransitionAnimation } from './Animations/page-transition';
import { Router, RouterOutlet } from '@angular/router';
import { LoginRefresh } from './Utility/LoginRefreshCookie';
import { LoginService } from '../app/Services/login.service';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
  providers: [LoginRefresh]
  // animations: [ pageTransitionAnimation ]
})
export class AppComponent {

  // private Cookie = document.cookie;
  // private Login_Email;
  // private Login_user;
  // private Login_session;
  // private Login_token;
  // private Login_exp;
  // private Login_Complete;
  // private Login_Key;
  public PresentDate;
  constructor(private router: Router) {
              // private LoginService: LoginService,
              // private LoginRefresh: LoginRefresh) {
  }

  title = 'QuantMasters';
  // ngOnInit() {
    // this.Login_user = this.LoginRefresh.getCookie("Login_User");
    // this.Login_session = this.LoginRefresh.getCookie("Login_SessionId");
    // this.Login_token = this.LoginRefresh.getCookie("Login_Token");
    // this.Login_Email = this.LoginRefresh.getCookie("Login_Email");
    // this.Login_Complete = this.LoginRefresh.getCookie("Login_Complete");
    // this.Login_Key = this.LoginRefresh.getCookie("Login_Key");
    // this.Login_exp = this.LoginRefresh.getCookie("Login_Expires");

    // if(this.Login_Email &&this.Login_session && this.Login_token && this.Login_exp) {
    //   this.PresentDate = new Date();
    //   this.Login_exp = new Date(this.Login_exp);
    //   if(this.Login_exp < this.PresentDate) {
    //     // No Action needed
    //     sessionStorage.clear();
    //     this.router.navigate(['/user/login']);
    //   }else {
    //     this.LoginService.refreshUserLogin(this.Login_Email).subscribe(response => {
    //       this.LoginRefresh.fnSetCookiesAndSession(response, this.Login_Email, this.Login_user, this.Login_Complete, this.Login_Key);
    //     }, error => {

    //     });
    //   }
    // }
  // }

  prepareRoute(outlet: RouterOutlet) {
    return outlet && outlet.activatedRouteData && outlet.activatedRouteData['animation'];
  }


}


