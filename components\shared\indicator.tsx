'use client'

interface IndicatorProps {
  isLoading: boolean
  fullScreen?: boolean
  text?: string
}

export default function LoadingIndicator({
  isLoading,
  fullScreen = false,
  text = 'Loading...'
}: IndicatorProps) {
  if (!isLoading) return null

  const containerClasses = fullScreen
    ? 'fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50'
    : 'flex items-center justify-center p-4'

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        {text && <p className="mt-2 text-gray-700">{text}</p>}
      </div>
    </div>
  )
}
