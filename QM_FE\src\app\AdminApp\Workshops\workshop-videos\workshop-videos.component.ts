import { Component, OnInit } from '@angular/core';

import { AdminViewService } from '../../../Services/admin-view.service';

@Component({
  selector: 'app-workshop-videos',
  templateUrl: './workshop-videos.component.html',
  styleUrls: ['./workshop-videos.component.scss']
})
export class WorkshopVideosComponent implements OnInit {

  public showIndicator = false;

  public groups: [{ group_name: string, group_id: string }];
  public subGroups: [{ sub_group_name: string, group_id: string, sub_group_id: string }];

  public selectedGroup = '';
  public selectedSubGroup = '';

  constructor(private adminViewService: AdminViewService) { }

  ngOnInit() {

    this.showIndicator = true;
    this.adminViewService.getWorkshopsViewGroups().subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.groups = resp;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  selectGroup(groupId: string, groupName: string) {

    this.showIndicator = true;

    this.selectedGroup = groupName;
    this.selectedSubGroup = '';
    this.adminViewService.getWorkshopsViewSubGroups(groupId).subscribe(response => {
      const resp = JSON.parse(JSON.stringify(response));

      this.subGroups = resp;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  selectSubGroup(subGroupId: string, subGroupName: string) {

    // this.showIndicator = true;

    this.selectedSubGroup = subGroupName;
    // this.adminViewService.getWorkshopsViewSubGroups(groupId).subscribe(response => {
    //   const resp = JSON.parse(JSON.stringify(response));

    //   this.subGroups = resp;
    //   this.showIndicator = false;
    // }, error => {
    //   this.showIndicator = false;
    // });
  }
}
