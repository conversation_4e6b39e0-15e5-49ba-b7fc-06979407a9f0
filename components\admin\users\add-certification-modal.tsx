'use client'

import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { AdminUsersClientService } from '@/lib/client-services/admin/users.client'

interface AddCertificationModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export default function AddCertificationModal({
  isOpen,
  onClose,
  onSuccess
}: AddCertificationModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    name: '',
    category: 0
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)

  const categoryOptions = [
    { value: 1, label: 'Excellence' },
    { value: 2, label: 'Achievement' },
    { value: 3, label: 'Completion' }
  ]

  const handleClose = () => {
    setFormData({
      email: '',
      name: '',
      category: 0
    })
    setError('')
    setSuccess(false)
    onClose()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.email.trim() || !formData.name.trim() || formData.category === 0) {
      setError('Please fill in all required fields')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await AdminUsersClientService.addCertificationUser(formData)
      if (response.success) {
        setSuccess(true)
        onSuccess()
        setTimeout(() => {
          handleClose()
        }, 2000)
      } else {
        setError(response.message || 'Failed to add certification user')
      }
    } catch (error) {
      setError('Error adding certification user')
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Add User to Certification</DialogTitle>
          <DialogDescription>
            Add a new user to the certification tracking system
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="name">Candidate Name *</Label>
            <Input
              id="name"
              type="text"
              placeholder="John Doe"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="category">Category *</Label>
            <Select
              value={formData.category.toString()}
              onValueChange={(value) => handleInputChange('category', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="-- Please choose category --" />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value.toString()}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-green-600 text-sm">
                Certification user added successfully!
              </p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add User'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
