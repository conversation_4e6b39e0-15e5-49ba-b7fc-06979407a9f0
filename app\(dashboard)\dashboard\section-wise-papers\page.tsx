import { Metadata } from 'next'
import { ServerSectionWisePapersService } from '@/lib/server-services/section-wise-papers.server'
import SectionWisePapersClient from '@/components/section-wise-papers/section-wise-papers-client'

export const metadata: Metadata = {
  title: 'Section-wise Practice Papers | Quant Masters',
  description:
    'Practice section-wise papers to improve your quantitative skills'
}

export default async function SectionWisePapersPage() {
  // Fetch papers on the server
  const papers = await ServerSectionWisePapersService.getPapers()

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center mb-2">
            Section-wise Practice Papers
          </h1>
          <p className="text-gray-600 text-center">
            Test your Strengths per section in Tests
          </p>
        </div>
      </div>

      {/* Pass the fetched papers to the client component */}
      <SectionWisePapersClient initialPapers={papers} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
