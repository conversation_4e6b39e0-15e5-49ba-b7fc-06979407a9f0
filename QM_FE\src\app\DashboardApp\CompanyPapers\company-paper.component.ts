import { Component, OnInit } from '@angular/core';

import { CompanyService } from '../../Services/Dashboard/company.service';
import { CompanyPapers } from 'src/app/Models/Dashboard/Company/CompanyPapers';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

declare let gtag: Function;

@Component({
  selector: 'app-company-papers',
  templateUrl: './company-paper.component.html',
  styleUrls: ['./company-paper.component.scss']
})
export class CompanyPaperComponent implements OnInit {

  public allPapers: CompanyPapers[];

  public showIndicator = false;

  public selectedCompany = '0';
  public selectedYear = '0';
  public companies = [
    'Accenture',
    'Capegemini',
    'Cognizant',
    'IBM',
    'Infosys',
    'KPIT',
    'TCS',
    'Wipro'
  ];
  public paperYears = [];
  public papersSelected: CompanyPapers[];
  public noPermission = true;

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute,
              public companyService: CompanyService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-*********-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    this.showIndicator = true;
    this.companyService.getAllPapers().subscribe(response => {
      this.allPapers = response;

      for (const paper of this.allPapers) {
        if (parseInt(paper.public, 10) === 1) {
          this.noPermission = false;
        }
      }
      this.showIndicator = false;
    }, error => {
      this.noPermission = true;
      this.showIndicator = false;
    });
  }

  onSelectCompany(event: any, company: string) {
    this.paperYears = [];
    this.allPapers.forEach(paper => {
      if (paper.company === company && this.paperYears.indexOf(paper.paper_year) === -1) {
        this.paperYears.push(paper.paper_year);
      }
    });
  }

  onSelectYear(event: any, year: string) {
    console.log('year :', year);
  }

  listPapers() {
    this.papersSelected = [];
    this.allPapers.forEach(paper => {
      if (paper.company === this.selectedCompany && paper.paper_year === parseInt(this.selectedYear, 10)) {
        this.papersSelected.push(paper);
      }
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['../test', '3', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

  takeToPlans() {
    this.router.navigate(['/plans']);
  }
}
