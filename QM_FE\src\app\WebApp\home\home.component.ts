import { Component, OnInit, AfterViewInit, ViewChild, TemplateRef, OnDestroy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Title } from '@angular/platform-browser';

import * as $ from 'jquery';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { StatsService } from '../../Services/stats.service';
import { UserService } from '../../Services/user.service';

import { UserLoginTrack } from '../../Models/UserLoginTrack';
import { CookieService } from 'ngx-cookie-service';

declare let gtag: Function;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('fillTemplate') public fillTemplate: TemplateRef<any>;

  public testimonials = [
    {
      'name': '<PERSON><PERSON>',
      'text': `Can you ever find a teacher who
              •Thinks in your perspective first?
              •Be a student along with you?
              •ease your preperation with his super tricks?
              •Work harder than you in smartest way?
              •moreover, a rare Gem of a person who brings out the hidden potential in you..
              •& yes finally be the catalyst for your dream to turn reality?
              •From Campus training to Any level of competitive exams
              •Does it sound impossible to  get such in real life?
              • There he exists..
              And yes I am most privileged to be one of his student.
              Such a Master
              Mr.Himanshu Sharma
              Fall short of words to express  my gratitude sir.
              Thank you for being such a favorite Master.`,
      'path': '../../assets/testimonials/devika.jpeg'
    },
    {
      'name': 'Shubham Soni',
      'text': `I am very fortunate that I have a teacher like Himanshu sir. Actually, I got the best teacher in
              every face of my student life till now but he is one of the best among all. I observed a lot while
              his teaching. The way of teaching,  his problem-solving method and technique are unique. The most
              important thing is his interaction with students. While teaching makes all the students very comfortable
              and calm. He knows how to teach and if you have such a teacher, you are truly lucky. He is master in soft
              skills and human psychology that\'s why I can share any problem with him and I know that he has the
              solution so I can say that he is one of the finest \'Guru\' in my life.`,
      'path': '../../assets/testimonials/shubham.jpeg'
    },
    {
      'name': 'Arpita',
      'text': `Himanshu sir ur training is great nd wonderful.. Before ur aptitude training I face many prblm in
              aptitude, but after ur training now I don\'t feel any difficulty.. Ur way of teaching is so easy.
              Everyone understand can understand. So last but not least I advice all to join Himanshu sir training
              institute.the best aptitude nd soft skill trainer in Banglore`,
      'path': '../../assets/testimonials/arpita.jpeg'
    },
    {
      'name': 'Alfiya',
      'text': `Your training was really good sir the way you thought the easy methods of solving the questions
              within a minute, the way you thought how to understand the question before solving it and the
              shortcuts which was really very helpful in order to solve the questions.`,
      'path': '../../assets/testimonials/alfiya.jpeg'
    },
    {
      'name': 'Akshay',
      'text': `Himanshu sir used to solve aptitude questions as fast as lightening.His tips &  tricks helped me
              to solve various questions in mind. I liked the way how he tackles and solves  things in simplest
              manner. Because of his guidance I scored 94 percentile in eLitmus. I will be always thankful to
              him and I feel I was lucky enough to get trained under such a enthusiastic and friendly person.`,
      'path': '../../assets/testimonials/akshay.jpeg'
    },
    {
      'name': 'Bhagyashree',
      'text': `We all used to  wait to attend your aptitude training session , we all used to easily catch up
              with your technique of solving difficult questions in a simpler way, all the short cut methods and
              tricks that you taught were so helpful to us  😊 at the same time you kept motivating us all the time
              when we felt like giving up 😊 your inspiration to most of the students and iam one of them 😇 be wt
              ur sir ...I wish you  good health and happiness... keep smiling alz `,
      'path': '../../assets/testimonials/bhagya.jpeg'
    },
    {
      'name': 'Aishwarya',
      'text': `It was great experience learning aptitude by Himashu Sir.He never acted as trainer, he was
              our mentor and friend guiding us in best way possible.I had the best time having trained by
              this amazing person.He is the best aptitude trainer I ever came across. More power to u Sir.`,
      'path': '../../assets/testimonials/aishwarya.jpeg'
    },
    {
      'name': 'Anup Agarwal',
      'text': `Himanshu sir, You hav always been a great trainer and an inspiration to us. You are the one
      who made our life less complicated by making us realise how easy aptitude actually was and is till
      date. Your skill in not making the class boring is the main thing that made us concentrate and do
      well in the subject. Your shortcuts do help in all the exams to score great. Thank you for training us sir..`,
      'path': '../../assets/testimonials/anup.jpeg'
    },
    {
      'name': 'Apoorva',
      'text': `Hi I am Apoorva and I am a proud student of Himanshu. I have undergone training since my first year of
              engineering under him. He has good techniques of solving and making us understand the concepts in a
              better way. He is a motivator. He helped me to get through hard times during placements. I am happy
              that I could get placed in a very good company because of the training that I got. I am thankful for
              all the blessings and inspiration. Thank you sir 😊`,
      'path': '../../assets/testimonials/apoorva.jpeg'
    },
    {
      'name': 'Yathin',
      'text': `It\'s a wonderful and very effective training by Himanshu sir. He\'s a very dedicated trainer and a best
              person And he\'s very frndly through out.. And was great support. The tricks n shortcuts were so effective
              n understandable. Thank you sir for being a good support`,
      'path': '../../assets/testimonials/yathin.jpeg'
    },
    {
      'name': 'Pooja',
      'text': `Himanshu sir has a very good hold on his mathematical skills and taught us various concepts and tricks
              to solve the aptitude questions. We were also given questions from different competitive exams and asked
              us to solve that gave us good practice. His sessions were very useful for me to clear the aptitude rounds
              of almost all the companies I attended.`,
      'path': '../../assets/testimonials/pooja.jpeg'
    },
    {
      'name': 'Afroz',
      'text': `I highly recommend Himanshu Sharma Sir for he is dedicated professional.  He is a resourceful trainer
              who has simple solutions to the Questions which we thought were hard. His way of solution of any Aptitude
              Question is very simple and easy to grasp. I was zero at aptitude and as his student I could easily able to
              understand any aptitude question, also using his method of solving I was able to crack few of the competitive
              exams. Not just a trainer but he is the best motivator which every student wants from a person training him/her.`,
      'path': '../../assets/testimonials/afroz.jpeg'
    },
    {
      'name': 'Deepshikha',
      'text': `Being taught by you was the best learning. Today wherever i am, its just because of you himanshu sir. The
              way you clear the concepts is commendable.`,
      'path': '../../assets/testimonials/deepshikha.jpeg'
    },
    {
      'name': 'Neha',
      'text': `Himanshu sir, his way of teaching is excellent. He listens to each and every students doubts and helps to solve
              in a easy methods. We learnt so many shortcut methods and it was very helpful. He covered all the topics in smooty
              way. His content was great and very relevant to me. He was enthusiastic and made the training interesting and
              enjoyable. Best trainer I have ever met. Finally as a person he is just amazing.`,
      'path': '../../assets/testimonials/neha.jpeg'
    },
    {
      'name': 'Pooja Mahadev',
      'text': `Himanshu sir one of my best teacher😊 I got to know about you in my aptitude cls.
              Earlier I was  very bad in apti, but when I attended ur clsss I felt I can do  👍🏻 your tricks in solving apti
              are very simple and we can remember easily, And Those tricks will take less time, if we practice more problems
              by using ur tricks anybody can crack aptitude exams for sure👍🏻
              And Most important in cls u were telling an awesome  thoughts# Inspirational words,
              those are really made my day😊 Thnks for that sir👍🏻`,
      'path': '../../assets/testimonials/poojam.jpeg'
    },
    {
      'name': 'Pranitha',
      'text': `Always thankful for Himanshu sir's guidance and support, even in the last minute the last minute words and guidance
              made a large impact on my performance. I would always wish to have many more training guidance and support from
              Himanshu sir.`,
      'path': '../../assets/testimonials/pranitha.jpeg'
    },
    {
      'name': 'Amogh',
      'text': `Himanshu Sharma is very dedicated, has thorough knowledge and experience in teaching aptitude. Good thing about
              his classes is that you never get bored even if it's of long session. He teaches all the approaches to solve a
              particular problem which  helps in understanding the best method to solve. After I got trained by him, I am able
              to crack every aptitude tests that I take. Tricks he teaches not only helps solving problems but also to solve
              them quickly.`,
      'path': '../../assets/testimonials/amogh.jpeg'
    },
    {
      'name': 'Sridhar',
      'text': `Himanshu's approach of starting from the basics as required, appeased my understanding and his equal intensity
              to support all the students at any time enhanced my desire to work harder, because of which I could successfully
              clear my life's first interview at HCL.`,
      'path': '../../assets/testimonials/sridhar.jpeg'
    },
    {
      'name': 'Daivik',
      'text': `Himanshu sir is an amazing educator and performer – his energy and great ideas are inspirational! I am always
              very happy to learn from him.`,
      'path': '../../assets/testimonials/daivik.jpeg'
    },
    {
      'name': 'Chandana',
      'text': `Someone has rightly said, "The average teacher explains complexity, while a gifted teacher reveals simplicity".
              I was lucky enough to get a gifted teacher like Himanshu sir in my life.He is the best mentor anyone could ever
              get.His aptitude skills are amazing and he teaches his students various shortcuts and tricks to solve any question
              within fraction of seconds,which helped me in clearing my aptitude round during my placements. I am really happy to
              be placed in few of the best companies.He really motivates all his students to strive hard and taste success.Himanshu
              sir is very friendly with everyone and always made sure no one got bored in d class and all of us would wait for his
              classes.I would recommend everyone to take his guidance and learn all the easiest and simplest way to arrive at any
              solution.`,
      'path': '../../assets/testimonials/chandana.jpeg'
    },
    {
      'name': 'Mahesh',
      'text': `Training under himanshu sir brought my best in Apti in which I was pretty average. The techniques he taught not
              only helped me but it forced me to generate my own techniques also. He's one of the best in this field and super
              friendly too.`,
      'path': '../../assets/testimonials/mahesh.jpeg'
    },
    {
      'name': 'Aditya',
      'text': `Quantmasters has really helped us to understand the concepts easily. They also informed us about the different
              streams which could be opted for our further endeavors. They let us know about our own capabilities and how to
              enhance our selves in an impeccable manner.`,
      'path': '../../assets/testimonials/aditya.jpeg'
    },
    {
      'name': 'Sneha',
      'text': `I went to quantmaster's in the last minute for my campus placements, but it was totally worth it. They helped me
              cover all the topics that was needed due to which I am performing really well in my placements. I owe it to them!
              It wasn't a training program like every other, they give a personal touch in whatever they do :)`,
      'path': '../../assets/testimonials/sneha.jpeg'
    }
  ];

  public placedList = [
    {
      'name': 'Pranitha',
      'company': 'Tata Consultancy Services',
      'batch': '2019',
      'path': '../../assets/testimonials/pranitha.jpeg'
    },
    {
      'name': 'Anirudh',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/anirudh.jpeg'
    },
    {
      'name': 'Amogh',
      'company': 'Intimatec',
      'batch': '2019',
      'path': '../../assets/testimonials/amogh.jpeg'
    },
    {
      'name': 'Vinith',
      'company': 'Tata Consultancy Services',
      'batch': '2019',
      'path': '../../assets/testimonials/vinith.jpeg'
    },
    {
      'name': 'Spoorthy',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/spoorthy.jpeg'
    },
    {
      'name': 'Tejaswini',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/tejaswini.jpeg'
    },
    {
      'name': 'Mahesh',
      'company': 'Accenture',
      'batch': '2019',
      'path': '../../assets/testimonials/mahesh.jpeg'
    },
    {
      'name': 'Nikita',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/nikita.jpeg'
    },
    {
      'name': 'Anvaya',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/anvaya.jpg'
    },
    {
      'name': 'Bhavuk',
      'company': 'Tata Consultancy Services',
      'batch': '2019',
      'path': '../../assets/testimonials/bhavuk.jpg'
    },
    {
      'name': 'Shashank',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/shashank.jpeg'
    },
    // {
    //   'name': 'Aditya',
    //   'company': '',
    //   'batch': '',
    //   'path': '../../assets/testimonials/aditya.jpeg'
    // },
    {
      'name': 'Sneha',
      'company': 'Tata Consultancy Services',
      'batch': '2019',
      'path': '../../assets/testimonials/sneha.jpeg'
    },
    {
      'name': 'Sumukha',
      'company': 'Accenture',
      'batch': '2019',
      'path': '../../assets/testimonials/sumukha.jpeg'
    },
    {
      'name': 'Sridhar',
      'company': 'HCL',
      'batch': '2019',
      'path': '../../assets/testimonials/sridhar.jpeg'
    },
    {
      'name': 'Daivik',
      'company': 'Infosys',
      'batch': '2019',
      'path': '../../assets/testimonials/daivik.jpeg'
    },
    {
      'name': 'Chandana',
      'company': 'NTT Data',
      'batch': '2019',
      'path': '../../assets/testimonials/chandana.jpeg'
    }
  ];

  public modalRef: BsModalRef;
  public config = {
    backdrop: true,
    ignoreBackdropClick: true,
    keyboard: false
  };

  public item_width: number;
  public left_val: number;
  public siteStats = {
    numRegistered: 0,
    numTests: 0,
    numAns: 0
  };

  public trackData: UserLoginTrack = new UserLoginTrack('', '', null, '', '', '', '');

  constructor(private title: Title,
              private router: Router,
              public modalService: BsModalService,
              private cookieService: CookieService,
              private statService: StatsService,
              private userService: UserService) {

    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-*********-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {

    const pcpml = sessionStorage.getItem('QUCmpl');

    if (pcpml && parseInt(pcpml, 10) !== 1) {
      this.openModal(this.fillTemplate);
    }

    if (sessionStorage.getItem('QMail') !== null) {

      this.trackData.email = sessionStorage.getItem('QMail');

      if (this.cookieService.check('_qfgp')) {
        this.trackData.last_cookie_val = this.cookieService.get('_qfgp');
      }

      this.trackData.platform = navigator.platform;
      this.trackData.uag_string = navigator.userAgent;

      navigator.geolocation.getCurrentPosition((data) => {

        this.trackData.latitude  = data.coords.latitude.toString();
        this.trackData.longitude = data.coords.longitude.toString();
        this.userService.postUserTrackData(this.trackData).subscribe(response => {
          const respText = JSON.parse(JSON.stringify(response));

          this.cookieService.set('_qfgp', respText.cookie, 2100);
        });
      }, (error) => {
        this.userService.postUserTrackData(this.trackData).subscribe(response => {
          const respText = JSON.parse(JSON.stringify(response));

          this.cookieService.set('_qfgp', respText.cookie, 2100);
        });
      });
    }

    // this.getStats();
  }

  ngAfterViewInit() {
    // Bootstrap our carousel
    this.item_width = $( '.t-item' ).width;
    $( '.t-item:first' ).before($( '.t-item:last' ));

    // this.startCarousel();
  }

  startCarousel(): void {
    const speed = 5000;
    let run = setInterval(this.moveRight, speed);

    $( '.t-wrap' ).hover(
      function() {
        clearInterval(run);
      },
      function() {
        run = setInterval(this.moveRight, speed);
      }
    );
  }

  getStats() {
    this.statService.retStatsRegistered().subscribe(response => {
      const resText = JSON.parse(JSON.stringify(response));

      this.siteStats.numRegistered = resText.text;
    }, error => {});

    this.statService.retStatsTests().subscribe(response => {
      const resText = JSON.parse(JSON.stringify(response));

      this.siteStats.numTests = resText.text;
    }, error => {});

    this.statService.retStatsAnswers().subscribe(response => {
      const resText = JSON.parse(JSON.stringify(response));

      this.siteStats.numAns = resText.text;
    }, error => {});
  }

  scrollToLoc(num: number): void {
    switch (num) {
      case 1:
        $('html, body').animate({scrollTop: $('.home-sect--001').offset().top}, 500);
        break;

      case 2:
        this.router.navigate(['tests']);
        break;

      case 3:
        this.router.navigate(['tests']);
        break;

      case 4:
        $('html, body').animate({scrollTop: 0}, 500);
    }
  }

  moveLeft() {
    const left_indent = parseInt($( '.t-wrap' ).css('left'), 10) + this.item_width;

    $( '.t-wrap' ).stop(true).delay(300).animate({'transition': 'translateX(' + left_indent + 'px)'}).fadeIn();
    $( '.t-item:first' ).before($( '.t-item:last' ));
  }

  moveRight() {
    const left_indent = parseInt($( '.t-wrap' ).css('left'), 10) - this.item_width;

    $( '.t-wrap' ).stop(true).delay(300).animate({'transition': 'translateX(' + left_indent + 'px)'}).fadeIn();
    $( '.t-item:last' ).after($( '.t-item:first' ));
  }

  takeToLandingPage() {
    this.router.navigate(['/placement/training/live']);
  }

  takeToLandingPageInternship() {
    this.router.navigate(['/placement/internship/ml-ai']);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  ngOnDestroy() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }
}
