.cert-header {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column ;
    justify-content: space-around;
    padding-top: 20px;
    margin: auto;
    background-color: #c3ece5;
    font: 17px 'Montserrat', 'sans-serif';
  
    .logo-box {
      z-index: 10;
      align-self: center;
  
      a {
        text-decoration: none;
        font-size: 28px;
        font-weight: bold;
        color: rgb(84, 141, 179);
  
        img {
          height: 45px;
          width: 45px;
          margin-bottom: 10px;
        }
      }
    }
  }

.Main-containt {
    width: 90%;
    max-width: 1440px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100%;
    background-color: #fff;
    border-radius: 6px;
    box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
    padding: 1em;
    margin: 5em auto;
    margin-top: 1em;
    position: relative;
    z-index: 10;
    font: 17px "Montserrat", "sans-serif";
    
    .edit-profile {
      width: 90%;
      margin-top: 1em;
      margin-left: 4em;
      font-size: larger;
      font-style: normal;
      font-family: "Montserrat" "sans-serif";
    }
  
    .left-sect {
      background-color: #fff;
      width: 100%;
      display: flex;
      justify-content: space-around;
      align-items: flex-start;
  
      .left-bar {
        width: 20%;
  
        .user-options {
          margin-top: 2em;
  
          .option-link {
            cursor: pointer;
            padding: 0.5rem;
            text-align: center;
            border-radius: 5px;
            color: #000;
            background-color: #eee;
            margin-bottom: 20px;
            transition: all 0.3s ease-out;
  
            &:hover {
              color: #fff;
              background-color: #48cc44;
              transition: all 0.2s ease-in;
            }
          }

          .verify-link:hover {
            background-color: #5DADE2;
          }
  
          .selected {
            background-color: #aeb6bf;
          }
        }
      }
      .detail-bar {
        width: 70%;
        min-height: 70vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        border-left: 2px solid #aeb6bf;
        margin-bottom: 1rem;

        .options {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 100%;
          margin-bottom: 2em;

          .share-link {
            cursor: pointer;
            margin-left: 1.5rem;
            
            img {
              display: inline-block;
              height: 2.3rem;
              border-radius: 50%;
              margin-left: 0.5rem;

              &:hover {
                background-color: #5DADE2;
              }
            }
          }

          .option-link {
            cursor: pointer;
            padding: 0.5rem;
            text-align: center;
            border-radius: 5px;
            color: #000;
            background-color: #eee;
            transition: all 0.2s ease-out;
            
            &:hover {
              color: #fff;
              background-color: #48cc44;
              transition: all 0.1s ease-in;
            }
          }
        }

        .pdf_viwer{
          width: 100%;
          align-self: center;
          margin-left: 20px;
        }
        p,
        small {
          width: 90%;
        }
  
        small {
          margin-bottom: 1rem;
        }
  
        .user-avatar--wrap {
          padding-top: 2em;
  
          img.profile-avatar {
            height: 150px;
            width: 150px;
            border-radius: 50%;
          }
  
          button {
            position: relative;
            bottom: 45px;
            left: 100px;
            height: 70px;
            width: 70px;
            cursor: pointer;
            border: none;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0);
  
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
  .Ng-temp-Main {
    align-items: center;
    margin-left: 1.2%;
    .Ng-temp-Button {
      display: flex;
      flex-direction: row;
      justify-content: center;
    }
  }
  .custom-btn {
    width: 150px;
    height: 40px;
    background-color: #e88224;
    border: none;
    border-radius: 4px;
    color: #fff;
    padding: 8px;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
  }

  .custom-btn:active:after {
    transition: 0s;
    opacity: 0.7;
    clip-path: circle(0% at 0% 0%);
  }

  .custom-btn::after {
    content: "";
    display: block;
    position: relative;
    top: -32px;
    left: -8px;
    height: 40px;
    width: 150px;
    background-color: #e88224;
    opacity: 0;
    clip-path: circle(150% at 0% 0%);
    transition: all 0.4s ease-in;
  }
  form {
    .form-row--1 {
      flex-direction: column;
      width: 100% !important;
      .form-elem {
        width: 100%;
        padding: 0.5em;

        input {
          width: 100%;
        }
      }
      .form-input {
        width: 100% !important;
      }
    }

    .custom-btn {
      margin-top: 0rem !important;
    }
  }

  @media (max-width: 440px) {
    .Main-containt {
      margin: 1em auto;
  
      .edit-profile {
        text-align: center;
        margin-left: 0;
      }
  
      .user-avatar--wrap {
        width: 50%;
      }
  
      .left-sect {
        width: 90%;
        display: flex;
        flex-direction: column;
  
        form {
          .form-row--1 {
            flex-direction: column;
            width: 100% !important;
            .form-elem {
              width: 100%;
              margin-top: 0.5em;
  
              input {
                width: 100%;
              }
            }
            .form-input {
              width: 100% !important;
            }
          }
        }
        .left-bar {
          border-bottom: 2px solid #aeb6bf;
          width: 100%;
          .user-options {
            margin-top: 0.5em;
          }
        }
        .detail-bar {
          border-left: 0px;
          width: 100%;
          .option-link{
            margin-top:  10px;
          }
          .pdf_viwer{
            margin-left: 0px;
          }
          p,
          small {
            text-align: center;
          }
        }
      }
    }
  }