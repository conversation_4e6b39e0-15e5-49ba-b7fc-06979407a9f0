# Missing Features Implementation Guide

This document provides code snippets for implementing the missing features in the Next.js admin papers functionality.

## 1. Missing Buttons for Specific Paper Types

### Add to `components/admin/tests/papers-list.tsx`

```typescript
interface PapersListProps {
  // ... existing props
  onRefreshCache?: () => void
  onCopyUrl?: () => void
}

// In the PapersList component, add these buttons based on paper type:
{paperType === 8 && (
  <button
    onClick={onRefreshCache}
    className="bg-sky-600 text-white px-4 py-2 rounded-md hover:bg-sky-700"
  >
    Refresh Cache
  </button>
)}

{(paperType === 13 || paperType === 14) && (
  <button
    onClick={onCopyUrl}
    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
  >
    Copy List URL
  </button>
)}
```

## 2. Missing Handlers Implementation

### Add to `components/admin/tests/papers-client.tsx`

```typescript
const handleRefreshCache = async () => {
  try {
    setLoading(true)
    // Call API to refresh cache for weekly competitive papers
    await AdminPapersClientService.refreshCompetitiveCache()
    toast.success('Cache refreshed successfully')
  } catch (error) {
    toast.error('Failed to refresh cache')
  } finally {
    setLoading(false)
  }
}

const handleCopyListUrl = () => {
  const baseUrl = selectedPaperType === 13 
    ? 'https://verbal-notes.quantmasters.in'
    : 'https://tech-notes.quantmasters.in'
  
  const listUrl = `${baseUrl}/papers/${selectedTopic}`
  navigator.clipboard.writeText(listUrl)
  toast.success('URL copied to clipboard!')
}
```

## 3. Missing API Methods

### Add to `lib/client-services/admin/papers.client.ts`

```typescript
static async refreshCompetitiveCache(): Promise<void> {
  const url = `${V2_BASE_URL}/admin/test/competitive/weekly/cache/refresh`
  await axios.post(url, {}, createAuthHeaders())
}

static async createGroup(groupName: string, paperType: number): Promise<any> {
  let url = ''
  const body = { group_name: groupName }
  
  switch (paperType) {
    case 11: // Technical MCQs
      url = `${V2_BASE_URL}/admin/test/tmcq/group`
      break
    case 13: // Verbal Practice
    case 14: // Technical Practice
      url = `${V2_BASE_URL}/admin/test/competitive/practice/group`
      break
  }
  
  const response = await axios.post(url, body, createAuthHeaders())
  return response.data
}

static async createSubGroup(groupId: string, subGroupName: string): Promise<any> {
  const url = `${V2_BASE_URL}/admin/test/tmcq/subgroup`
  const body = { group_id: groupId, sub_group_name: subGroupName }
  
  const response = await axios.post(url, body, createAuthHeaders())
  return response.data
}

// Question CRUD operations
static async addQuestion(
  paperId: string, 
  questionData: any, 
  paperType: number
): Promise<any> {
  let url = ''
  
  switch (paperType) {
    case 11: // Technical MCQs
      url = `${V2_BASE_URL}/admin/test/tmcq/paper/${paperId}/question`
      break
    case 5: // Trial/Company
      url = `${API_BASE_URL}/admin/paper/open/update/${paperId}/question`
      break
    // Add other paper types...
  }
  
  const response = await axios.post(url, questionData, createAuthHeaders())
  return response.data
}
```

## 4. Question Manager Component

### Create `components/admin/tests/question-manager.tsx`

```typescript
import { useState } from 'react'
import { toast } from 'sonner'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'

interface QuestionManagerProps {
  paperId: string
  paperType: number
  questions: any[]
  onQuestionsUpdate: (questions: any[]) => void
}

export function QuestionManager({ 
  paperId, 
  paperType, 
  questions, 
  onQuestionsUpdate 
}: QuestionManagerProps) {
  const [editingQuestion, setEditingQuestion] = useState<any>(null)
  const [showAddQuestion, setShowAddQuestion] = useState(false)
  
  const handleAddQuestion = async (questionData: any) => {
    try {
      const newQuestion = await AdminPapersClientService.addQuestion(
        paperId,
        questionData,
        paperType
      )
      onQuestionsUpdate([...questions, newQuestion])
      toast.success('Question added successfully')
    } catch (error) {
      toast.error('Failed to add question')
    }
  }
  
  const handleUpdateQuestion = async (questionNo: number, updates: any) => {
    try {
      await AdminPapersClientService.updateQuestion(
        paperId,
        questionNo,
        updates,
        paperType
      )
      const updated = questions.map(q => 
        q.question_no === questionNo ? { ...q, ...updates } : q
      )
      onQuestionsUpdate(updated)
      toast.success('Question updated successfully')
    } catch (error) {
      toast.error('Failed to update question')
    }
  }
  
  const handleDeleteQuestion = async (questionNo: number) => {
    if (!confirm('Are you sure you want to delete this question?')) return
    
    try {
      await AdminPapersClientService.deleteQuestion(
        paperId,
        questionNo,
        paperType
      )
      onQuestionsUpdate(questions.filter(q => q.question_no !== questionNo))
      toast.success('Question deleted successfully')
    } catch (error) {
      toast.error('Failed to delete question')
    }
  }
  
  return (
    <div className="space-y-4">
      {/* Add question button */}
      <button 
        onClick={() => setShowAddQuestion(true)}
        className="btn btn-primary"
      >
        Add Question
      </button>
      
      {/* Questions list */}
      {questions.map((question, index) => (
        <div key={question.question_no} className="border p-4 rounded">
          <h4>Question {question.question_no}</h4>
          <p>{question.question}</p>
          {/* Edit/Delete buttons */}
        </div>
      ))}
    </div>
  )
}
```

## 5. Create Group Modal

### Create `components/admin/tests/create-group-modal.tsx`

```typescript
import { useState } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { AdminPapersClientService } from '@/lib/client-services/admin/papers.client'

interface CreateGroupModalProps {
  isOpen: boolean
  paperType: number
  onClose: () => void
  onSuccess: (group: any) => void
}

export function CreateGroupModal({ 
  isOpen, 
  paperType, 
  onClose, 
  onSuccess 
}: CreateGroupModalProps) {
  const [groupName, setGroupName] = useState('')
  const [loading, setLoading] = useState(false)
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!groupName.trim()) {
      toast.error('Please enter a group name')
      return
    }
    
    try {
      setLoading(true)
      const newGroup = await AdminPapersClientService.createGroup(
        groupName,
        paperType
      )
      onSuccess(newGroup)
      setGroupName('')
      onClose()
      toast.success('Group created successfully')
    } catch (error) {
      toast.error('Failed to create group')
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Group</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="groupName">Group Name</Label>
            <Input
              id="groupName"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name"
              disabled={loading}
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Creating...' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

## API Endpoint Mapping

### Angular → Next.js API Endpoint Corrections

```typescript
// Update the API endpoints in AdminPapersClientService to match Angular patterns:

// For Technical MCQs (Type 11) - Currently uses v2, Angular uses v3
case 11:
  // Change from: /v2/test/tmcq/papers/{id}
  // To: /v3/admin/tmcq/papers/{id}
  url = `${API_BASE_URL}/v3/admin/tmcq/papers/${subtopicId}`
  break

// For getting questions - Angular uses different endpoints per type
switch (paperType) {
  case 1: // Chapter-wise
    url = `/test/sample/paper/new/${paperId}` // Generic endpoint
    break
  case 11: // Technical MCQs
    url = `/v3/admin/tmcq/paper/${paperId}`
    break
  case 13:
  case 14: // Practice papers
    url = `/v2/test/competitive/practice/paper/${paperId}`
    break
  // etc...
}
```

## Implementation Priority

1. **High Priority**
   - Complete question management (CRUD operations)
   - Fix API endpoint inconsistencies
   - Add refresh cache button for Weekly Competitive
   - Add copy URL button for Practice papers

2. **Medium Priority**
   - Create group/subgroup modals
   - Image upload for question explanations
   - Question validation logic

3. **Low Priority**
   - UI/UX improvements
   - Performance optimizations
   - Additional paper types 