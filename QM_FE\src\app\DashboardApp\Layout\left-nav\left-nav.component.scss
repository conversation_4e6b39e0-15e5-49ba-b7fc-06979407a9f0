.left-nav {
  width: 100%;
  min-width: 250px;
  height: 100%;
  min-height: 100vh;
  background-color: #0B6FB1;
  font: 14px 'Montserrat', 'serif';
  position: relative;
  z-index: 2;
  transform: translateX(0);

  .logo {
    width: 100%;
    text-align: center;
    padding: 1em 0;
    cursor: pointer;

    svg {
      height: 40px;
      width: 40px;
      margin-bottom: 0.5em;
    }

    h5 {
      display: inline;
      margin: 0;
      font-size: 28px;
      color: #fff;
    }
  }

  .mob-ham {
    display: none;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 11%;
    left: 100%;
    height: 70px;
    width: 55px;
    background-color: #0B6FB1;

    span,
    span::before {
      content: "";
      width: 80%;
      height: 10px;
      border-radius: 5px;
      background-color: #fff;
      transform: rotate(45deg);
    }

    span {
      position: relative;
      top: -11px;
    }

    span::before {
      position: relative;
      display: block;
      width: 100%;
      transform: rotate(-90deg);
      top: 18px;
      left: 17px;
    }
  }

  .user-profile {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    border: 0.5px solid rgb(123, 130, 254);
    padding: 1em 0;

    .profile-avatar {
      height: 50px;
      width: 50px;

      img {
        height: 100%;
        width: 100%;
        border-radius: 50%;
      }
    }

    p {
      color: #fff;
      margin: 0;
    }
  }

  .navs {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    width: 100%;
    padding: 1em 0 1em 1em;

    .nav-page {
      width: 90%;
      padding: 0.5em 0 0.5em 1em;
      margin-bottom: 0.3em;
      border-radius: 5px;
      transition: all 0.3s ease;
      text-decoration: none;
      color: #fff;
      position: relative;
      
      &:last-of-type {
        margin-bottom: 0;
      }

      &:hover {
        background-color: #E88224;
        transition: all 0.3s ease;
      }

      a {
        padding: 0.5em;
        margin-left: 0.3em;
        text-decoration: none;
        color: #fff;
      }

      span {
        --star-background: #ffcc00;

        position: absolute;
        bottom: 0.25em;
        right: 0.25em;
        font-size: 3rem;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: var(--star-background);
      }
    }
  }

  .nav-link--active {
    background-color: #E88224;
  }
}

@media (max-width: 440px) {
  .left-nav {
    .mob-ham {
      display: flex;
    }
  }
}