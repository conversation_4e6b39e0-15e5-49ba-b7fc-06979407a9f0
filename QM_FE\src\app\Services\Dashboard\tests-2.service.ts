import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ChapterWrapper } from '../../Models/Dashboard/Chapters/ChapterWrapper';
import { CompetitiveWrapper } from '../../Models/Dashboard/Competitive/CompetitiveWrapper';
import { CompanyWrapper } from '../../Models/Dashboard/Company/CompanyWrapper';
import { PaperAdditionData } from 'src/app/Models/PaperAddtionData';

@Injectable({
  providedIn: 'root'
})
export class Tests2Service {

  private questionUrl = 'https://api.quantmasters.in/test/progression/paper/new/';
  private competitiveQuestionUrl = 'https://api.quantmasters.in/test/competitive/paper/new/';
  private companyQuestionUrl = 'https://api.quantmasters.in/test/company/paper/new/';
  private paperDataUrl = 'https://api.quantmasters.in/test/paper/';
  private sBaseUrl = 'https://api.quantmasters.in/';
  private uploadExplUrl = 'https://api.quantmasters.in/admin/paper/upload/';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  getQuestions(paperId: string): Observable<ChapterWrapper> {
    this.setSecurityToken();

    const url = this.questionUrl + paperId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<ChapterWrapper>(url, httpOps);
  }

  getCompetitiveQuestions(paperId: string): Observable<CompetitiveWrapper> {
    this.setSecurityToken();

    const url = this.competitiveQuestionUrl + paperId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompetitiveWrapper>(url, httpOps);
  }

  getCompanyQuestions(paperId: string): Observable<CompanyWrapper> {
    this.setSecurityToken();

    const url = this.companyQuestionUrl + paperId;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<CompanyWrapper>(url, httpOps);
  }

  // getPaperAdditionData(paperId: String): Observable<PaperAdditionData[]> {
  //   this.setSecurityToken();

  //   const url = this.paperDataUrl + paperId + '/addition_data';

  //   const httpOps = {
  //     headers: new HttpHeaders({
  //       'Authorization': 'Bearer ' + this.JwtToken
  //     })
  //   };

  //   return this.http.get<PaperAdditionData[]>(url, httpOps);
  // }

  addQuestionExplanation(paperType: string, paperId: string, quesNo: string, explanation: string): Observable<string> {
    this.setSecurityToken();

    const url = this.uploadExplUrl + paperType + '/' + paperId + '/explanation/regular';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const reqBody = {
      ques_no: quesNo,
      explanation
    };

    return this.http.post<string>(url, reqBody, httpOps);
  }

  addImageExplanation(paperType: string, paperId: string, quesNo: string, selectedFile: File): Observable<string> {

    this.setSecurityToken();

    const url = this.uploadExplUrl + paperType + '/' + paperId + '/explanation/image/' + quesNo;

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const uploadData = new FormData();
    uploadData.append('image_upload', selectedFile);

    return this.http.post<string>(url, uploadData, httpOps);
  }

  addImageExplanationForQuestion(paperType: string, paperId: string, quesNo: string, selectedFile: File): Observable<string> {
    this.setSecurityToken();

    const url = this.sBaseUrl + '/v2/admin/test/' + paperType + '/' + paperId + '/' + quesNo + '/image';

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    const uploadData = new FormData();
    uploadData.append('image_upload', selectedFile);

    return this.http.post<string>(url, uploadData, httpOps);

  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
