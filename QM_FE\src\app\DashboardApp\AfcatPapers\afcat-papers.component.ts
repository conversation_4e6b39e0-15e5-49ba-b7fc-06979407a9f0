import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { ChapterPaper } from '../../Models/Dashboard/Chapters/ChapterPaper';
import { SectionWisePapersService } from '../../Services/Dashboard/section-wise-papers.service';

@Component({
  selector: 'app-afcat-papers',
  templateUrl: './afcat-papers.component.html',
  styleUrls: ['./afcat-papers.component.scss']
})
export class AfcatPapersComponent implements OnInit {

  public papers: ChapterPaper[];

  public showIndicator = false;

  constructor(private router: Router,
              private activatedRoute: ActivatedRoute,
              private sectionWisePapersService: SectionWisePapersService) { }

  ngOnInit() {

    const that = this;

    this.showIndicator = true;
    this.sectionWisePapersService.getAfcatPapers().subscribe(response => {
      const respText = JSON.parse(JSON.stringify(response));

      that.papers = respText;
      that.showIndicator = false;
    }, error => {
      that.showIndicator = false;
    });
  }

  takeToLanding() {

    this.router.navigate(['/placement/training/live']);
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {

    this.router.navigate(['../test', '10', paperId, paperName, paperLim], {relativeTo: this.activatedRoute});
  }

}
