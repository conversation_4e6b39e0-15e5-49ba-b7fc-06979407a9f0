import { error } from '@angular/compiler/src/util';
import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { Routes, RouterModule, Router, ActivatedRoute, ChildrenOutletContexts } from '@angular/router';

import * as $ from 'jquery';

import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { PageChangedEvent, PagesModel } from 'ngx-bootstrap/pagination';

import { PaperDetails } from '../../../Models/PaperDetails';
import { ChapterPaper } from '../../../Models/Dashboard/Chapters/ChapterPaper';
import { Paper } from '../../../Models/Paper';

import { TestsService } from '../../../Services/tests.service';
import { PaperService } from '../../../Services/Paper.service';
import { TmcqService } from '../../../Services/Dashboard/TMCQ/tmcq.service';
import { SectionWisePapersService } from '../../../Services/Dashboard/section-wise-papers.service';
import { CompetitiveService } from '../../../Services/Dashboard/competitive.service';
import { ChaptersService } from '../../../Services/Dashboard/chapters.service';
import { ChapterPracticeService } from '../../../Services/Dashboard/chapter-practice.service';
import { OpenTestsService } from '../../../Services/open-tests.service';

@Component({
  selector: 'app-paper-detail',
  templateUrl: './paper-detail.component.html',
  styleUrls: ['./paper-detail.component.scss']
})
export class PaperDetailComponent implements OnInit {

  @ViewChild('successTemplate') public sTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public eTemplate: TemplateRef<any>;
  @ViewChild('validationTemplate') public validationTemplate: TemplateRef<any>;
  @ViewChild('uploadImage') public uploadImage: TemplateRef<any>;

  public routeParams = {};
  public paper_detail: PaperDetails = {
    paper_id: null,
    paper_name: null,
    paper_desc: null,
    status: null,
    time_lim: null,
    created_at: null,
    show_ans: null,
    once_ans: null,
    public: null,
    no_of_questions: null,
    neg_marks: null,
    rand_ques: null
  };
  public showIndicator = false;
  public modalRef: BsModalRef;

  public paperType = '';
  public superPaperType = '';
  public paperSubGrpup = '';
  public paperGroup = '';

  public config = {
    backdrop: true,
    class: 'create-modal',
    isAnimated: 'true'
  };

  public paperQuestion = [];
  public paperQuestionObject = {};
  public displayRecords = [];
  public copyStartItem = 0;  copyEndItem = 10;
  public allQuestionsLegth: number;

  public isCollapsed = [];
  public smallnumPages = 0;

  public validationGood = false;
  public validationTitle = '';
  public validationMsgs = [];

  public uploadImageFor = '1';
  public selectedQuestionIndex = '';
  public selectedQuestionNumber = '';
  public selectedImage: File;
  public sSuccessMsg = 'Data Updated Successfully!';

  public addQuestionBtn_visible = sessionStorage.getItem('paperState') === 'Create' ? true : false;
  constructor(private router: Router,
              private route: ActivatedRoute,
              private chaptersService: ChaptersService,
              private chapterPracticeService: ChapterPracticeService,
              private paperService: PaperService,
              private sectionWiseService: SectionWisePapersService,
              private tmcqService: TmcqService,
              private competitiveService: CompetitiveService,
              private testsService: TestsService,
              private openTestsService: OpenTestsService,
              public modalService: BsModalService) {

    this.route.params.subscribe(params => this.routeParams = JSON.parse(JSON.stringify(params)));

    this.paper_detail.paper_id = this.routeParams['p_id'];
    this.paper_detail.paper_name = this.routeParams['p_name'];
    this.paper_detail.paper_desc = "";
    this.paper_detail.status = this.routeParams['p_status'];
    this.paper_detail.show_ans = this.routeParams['p_show_ans'];
    this.paper_detail.once_ans = this.routeParams['p_once_ans'];
    this.paper_detail.no_of_questions = this.routeParams['p_noofque'];
    this.paper_detail.time_lim = Number(this.routeParams['p_timelim']);
    this.paper_detail.neg_marks = this.routeParams['p_neg_marks'];
    this.paper_detail.rand_ques = this.routeParams['p_rand_ques'];

    this.paperType = sessionStorage.getItem('paperType');
  }

  ngOnInit() {
    this.superPaperType = sessionStorage.getItem('QPaperType');
    this.paperSubGrpup  = sessionStorage.getItem('QPaperGroup');

    if (this.superPaperType === '13' || this.superPaperType === '14') {
      this.paperGroup = sessionStorage.getItem('QPracticePaperGroup');
    }

    if (this.superPaperType === '4') {
      this.paper_detail.paper_desc = sessionStorage.getItem('MockPaperDesc');
    }

    if (this.superPaperType === '1' || this.superPaperType === '6') {
      this.paperGroup = sessionStorage.getItem('QPaperSupGroup');
      this.paperSubGrpup  = sessionStorage.getItem('QPaperGroup');
    }

    this.fnGetPaperQuestions(this.paper_detail.paper_id);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  SubmitNewPaper(isValid: boolean) {
    if (!isValid) {
      return;
    }
    this.showIndicator = true;

    if (this.superPaperType === '1') {

      const paperDetails: ChapterPaper = {
        paper_id    : this.paper_detail.paper_id,
        sub_group_id: this.paperSubGrpup,
        group_id    : this.paperGroup,
        paper_name  : this.paper_detail.paper_name,
        level       : '1',
        no_of_ques  : this.paper_detail.no_of_questions,
        status      : this.paper_detail.status.toString(),
        time_lim    : (this.paper_detail.time_lim * 60000).toString(),
        show_ans    : this.paper_detail.show_ans,
        once_ans    : this.paper_detail.once_ans,
        public      : '0',
        type        : 0,
        created_at  : null,
        neg_marks   : null,
        rand_ques   : null
      };

      this.chaptersService.updatePaper(paperDetails).subscribe(response => {
        this.showIndicator = false;
        this.sSuccessMsg = 'Data Added Successfully!';
        this.openModal(this.sTemplate);
      }, error2 => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.superPaperType === '4') {

      const paperDetails: Paper = {
        paper_id  : this.paper_detail.paper_id,
        paper_name: this.paper_detail.paper_name,
        paper_desc: this.paper_detail.paper_desc,
        no_of_ques: parseInt(this.paper_detail.no_of_questions, 10),
        status    : this.paper_detail.status,
        time_lim  : (this.paper_detail.time_lim * 60000),
        show_ans  : this.paper_detail.show_ans,
        once_ans  : this.paper_detail.once_ans,
        public    : '0',
        created_at: null
      };

      this.testsService.updatePaperDetails(paperDetails).subscribe(response => {
        this.showIndicator = false;
        this.sSuccessMsg = 'Data Added Successfully!';
        this.openModal(this.sTemplate);
      }, error2 => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.superPaperType === '6') {

      const paperDetails: ChapterPaper = {
        paper_id    : this.paper_detail.paper_id,
        sub_group_id: this.paperSubGrpup,
        group_id    : this.paperGroup,
        paper_name  : this.paper_detail.paper_name,
        level       : '1',
        no_of_ques  : this.paper_detail.no_of_questions,
        status      : this.paper_detail.status.toString(),
        time_lim    : (this.paper_detail.time_lim * 60000).toString(),
        show_ans    : this.paper_detail.show_ans,
        once_ans    : this.paper_detail.once_ans,
        public      : '0',
        type        : 1,
        created_at  : null,
        neg_marks   : null,
        rand_ques   : null
      };

      this.chapterPracticeService.updatePaper(paperDetails).subscribe(response => {
        this.showIndicator = false;
        this.sSuccessMsg = 'Data Added Successfully!';
        this.openModal(this.sTemplate);
      }, error2 => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });

    } else if (this.superPaperType === '11') {

      const paperDetail: ChapterPaper = {
        paper_id: this.paper_detail.paper_id,
        sub_group_id: this.paperSubGrpup,
        group_id: '',
        type: 0,
        paper_name: this.paper_detail.paper_name,
        no_of_ques: this.paper_detail.no_of_questions,
        once_ans: this.paper_detail.once_ans,
        show_ans: this.paper_detail.show_ans,
        level: null,
        status: this.paper_detail.status.toString(),
        time_lim: (this.paper_detail.time_lim * 60000).toString(),
        public: '0',
        neg_marks: this.paper_detail.neg_marks,
        rand_ques: this.paper_detail.rand_ques,
        created_at: null
      };

      this.tmcqService.updatePaper(paperDetail).subscribe(response => {
        this.showIndicator = false;
        this.sSuccessMsg = 'Data Added Successfully!';
        this.openModal(this.sTemplate);
      }, error2 => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.superPaperType === '9') {
      const oPayload = {
        paper_name: this.paper_detail.paper_name,
        no_of_ques: this.paper_detail.no_of_questions,
        status: this.paper_detail.status,
        time_lim: this.paper_detail.time_lim * 60000,
        once_ans: this.paper_detail.once_ans,
        show_ans: this.paper_detail.show_ans,
        neg_marks: this.paper_detail.neg_marks,
        rand_ques: this.paper_detail.rand_ques,
        type: Number(0)
      };

      this.sectionWiseService.updateSectionWisePaper(this.paper_detail.paper_id, oPayload).subscribe(response => {
        this.showIndicator = false;
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else if (this.superPaperType === '13' || this.superPaperType === '14') {
      const oPayload = {
        paper_id: this.paper_detail.paper_id,
        group_id: this.paperGroup,
        paper_name: this.paper_detail.paper_name,
        no_of_ques: this.paper_detail.no_of_questions,
        status: this.paper_detail.status.toString(),
        time_lim: `${this.paper_detail.time_lim * 60000}`,
        once_ans: this.paper_detail.once_ans,
        show_ans: this.paper_detail.show_ans,
        public: '0',
        level: null,
        created_at: null
      };

      this.competitiveService.updateCompetitivePaper(oPayload).subscribe(response => {
        this.showIndicator = false;
        this.openModal(this.sTemplate);
      }, error => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
      });
    } else {
      const paperType = sessionStorage.getItem('openPaperType');
      const oPayload = {
        paper_name: this.paper_detail.paper_name,
        no_of_ques: this.paper_detail.no_of_questions,
        status: this.paper_detail.status,
        time_lim: this.paper_detail.time_lim * 60000,
        once_ans: this.paper_detail.once_ans,
        show_ans: this.paper_detail.show_ans,
        neg_marks: this.paper_detail.neg_marks,
        rand_ques: this.paper_detail.rand_ques,
        type: Number(paperType)
      };
      this.paperService.putPaperHeaderDetails(oPayload, this.paper_detail.paper_id).subscribe(response => {

        this.showIndicator = false;
        this.openModal(this.sTemplate);
      }, error1 => {
        this.showIndicator = false;
        this.openModal(this.eTemplate);
        // this.errMsg = 'Looks like something went wrong. Please try again later.';
      });
    }
  }

  fnGetPaperQuestions(paper_id) {

    this.showIndicator = true;

    const paperState = sessionStorage.getItem('paperState');

    if (paperState === 'Edit') {

      if (this.superPaperType === '1') {
        this.paperService.getPaperQuestion(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        });
      } else if (this.superPaperType === '4') {
        this.testsService.getQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        });
      } else if (this.superPaperType === '6') {
        this.chaptersService.getQuestionsForAdmin(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        });
      } else if (this.superPaperType === "9") {
        this.sectionWiseService.getQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        });
      } else if (this.superPaperType == '11') {
        this.tmcqService.getQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        }, error1 => {

        });
      } else if (this.superPaperType === '13' || this.superPaperType === '14') {
        this.competitiveService.getCompetitivePaperQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        });
      } else {
        this.paperService.getPaperQuestion(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        }, error1 => {

        });
      }
    } else if (paperState === 'Create') {

      if (this.superPaperType == '11') {
        this.tmcqService.getQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        }, error1 => {

        });
      } else if (this.superPaperType == '13' || this.superPaperType === '14') {
        this.competitiveService.getCompetitivePaperQuestions(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        }, error1 => {

        });
      } else {
        this.paperService.getPaperQuestion(paper_id).subscribe(response => {
          this.fnPrepareObjectPapersId(response);
          this.paperQuestion = JSON.parse(JSON.stringify(response['questions']));
          this.displayRecords = JSON.parse(JSON.stringify(response['questions'].slice(this.copyStartItem, this.copyEndItem)));
          this.allQuestionsLegth = response['questions'].length;
          for (let i = 0; i < response['questions'].length; i++) {
            this.isCollapsed.push(true);
          }

          this.showIndicator = false;
        }, error1 => {

        });
      }
    }
    this.toMakeCollapseFalse();
  }

  fnPrepareObjectPapersId(response) {
    const oPapers = {};
    if (response.questions && response.questions.length > 0) {
        for (let i = 0; i < response.questions.length; i++) {
          oPapers[response.questions[i].question_no] = response.questions[i];
        }
    } else if (response && response.length > 0) {
      for (let i = 0; i < response.length; i++) {
        oPapers[response[i].question_no] = response[i];
      }
    }
    this.paperQuestionObject = JSON.parse(JSON.stringify(oPapers));
  }

  // To save Question detail
  fnSaveQuestionDeatils(oQuestion) {
    const paperState = sessionStorage.getItem('paperState');
    const that = this;
    if (paperState === 'Edit') {
      this.showIndicator = true;
      const oPayload = {
        'question_no': Number(oQuestion.question_no),
        'question': oQuestion.question,
        'opt_1': oQuestion.opt_1,
        'opt_2': oQuestion.opt_2,
        'opt_3': oQuestion.opt_3,
        'opt_4': oQuestion.opt_4,
        'opt_5': oQuestion.opt_5,
        'correct_opt': Number(oQuestion.correct_opt),
        'explanation': oQuestion.explanation,
        // 'img_expln': oQuestion.img_expln,
        'img_expln': 0,
      };

      const oPayloadMockTests = {
        'paper_id': oQuestion.paper_id,
        'question_no': Number(oQuestion.question_no),
        'question': oQuestion.question,
        'option_1': oQuestion.option_1,
        'option_2': oQuestion.option_2,
        'option_3': oQuestion.option_3,
        'option_4': oQuestion.option_4,
        'correct_opt': Number(oQuestion.correct_opt),
        'explanation': oQuestion.explanation
      };

      const oPayloadChapterTests = {
        'paper_id'   : oQuestion.paper_id,
        'ques_no'    : Number(oQuestion.question_no),
        'question'   : oQuestion.question,
        'opt_1'      : oQuestion.opt_1,
        'opt_2'      : oQuestion.opt_2,
        'opt_3'      : oQuestion.opt_3,
        'opt_4'      : oQuestion.opt_4,
        'opt_5'      : oQuestion.opt_5,
        'correct_opt': Number(oQuestion.correct_opt),
        'explanation': oQuestion.explanation
      };

      if (this.superPaperType === '1') {

        that.chaptersService.updateQuestion(oPayloadChapterTests).subscribe(response => {
          const repObj = JSON.parse(JSON.stringify(response));

          if (repObj.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });

      } else if (this.superPaperType === '4') {

        that.testsService.updateQuestionDetails(oPayloadMockTests).subscribe(response => {
          const repObj = JSON.parse(JSON.stringify(response));

          if (repObj.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });

      } else if (this.superPaperType === '6') {

        that.chapterPracticeService.updateQuestion(oPayloadChapterTests).subscribe(response => {
          const repObj = JSON.parse(JSON.stringify(response));

          if (repObj.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });

      } else if (this.superPaperType === '9') {
        that.sectionWiseService.updateSectionWiseQuestion(oQuestion.paper_id, oPayload).subscribe(response => {
          const repObj = JSON.parse(JSON.stringify(response));

          if (repObj.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else if (this.superPaperType === '11') {
        const oPayload = {
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation,
          'img_expln': 0,
        };
        
        that.tmcqService.postPaperQuestionDetails(oPayload, this.paper_detail.paper_id).subscribe(response => {
          if (response.msg === 'created') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Added Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);

          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else if (this.superPaperType === '13' || this.superPaperType === '14') {

        const oPayload = {
          'paper_id': oQuestion.paper_id,
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation
        };


        this.competitiveService.updateCompetitivePaperQuestion(oPayload).subscribe(response => {
          const repObj = JSON.parse(JSON.stringify(response));

          if (repObj.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else {
        that.paperService.putPaperQuestionDetails(oPayload, oQuestion.paper_id).subscribe(response => {
          if (response.msg === 'updated') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      }
    } else if (paperState === 'Create') {

      this.showIndicator = true;

      if (this.superPaperType === '9') {
        // this.showIndicator = true;
        const oPayload = {
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation,
          // 'img_expln': oQuestion.img_expln,
          'img_expln': 0,
        };
        // return;
        that.tmcqService.postPaperQuestionDetails(oPayload, this.paper_detail.paper_id).subscribe(response => {
          if (response.msg === 'created') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Added Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);

          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else if (this.superPaperType === '11') {
        const oPayload = {
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation,
          'img_expln': 0,
        };
        
        that.tmcqService.postPaperQuestionDetails(oPayload, this.paper_detail.paper_id).subscribe(response => {
          if (response.msg === 'created') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Added Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);

          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else if (this.superPaperType == '13' || this.superPaperType === '14') {

        const oPayload = {
          'paper_id': this.paper_detail.paper_id,
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation,
          'question_origin': ''
          // 'img_expln': oQuestion.img_expln,
          // 'img_expln': 0,
        };

        this.competitiveService.createCompetitivePaperQuestion(oPayload).subscribe(response => {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Added Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      } else {
        const oPayload = {
          'question_no': Number(oQuestion.question_no),
          'question': oQuestion.question,
          'opt_1': oQuestion.opt_1,
          'opt_2': oQuestion.opt_2,
          'opt_3': oQuestion.opt_3,
          'opt_4': oQuestion.opt_4,
          'opt_5': oQuestion.opt_5,
          'correct_opt': Number(oQuestion.correct_opt),
          'explanation': oQuestion.explanation,
          'img_expln': 0,
        };

        that.paperService.postPaperQuestionDetails(oPayload, oQuestion.paper_id).subscribe(response => {
          if (response.msg === 'created') {
            that.showIndicator = false;
            this.sSuccessMsg = 'Question Updated Successfully!';
            that.openModal(that.sTemplate);
            that.fnGetPaperQuestions(oQuestion.paper_id);
          } else {
            that.showIndicator = false;
            that.openModal(that.eTemplate);
          }
        }, error1 => {
          that.showIndicator = false;
          that.openModal(that.eTemplate);
        });
      }
    }


  }
  // To rest the question details
  fnResetQuestionDetails(index, sQuestionNo) {
    this.displayRecords[index] = JSON.parse(JSON.stringify(this.paperQuestionObject[sQuestionNo]));
  }

  onClickUploadButton(index, sQuestionNo) {
    this.uploadImageFor='1';
    this.selectedQuestionIndex = index;
    this.selectedQuestionNumber = sQuestionNo;
    this.openModal(this.uploadImage);
  }

  onSelectFileExplorer(event){
    this.selectedImage = event.target.files[0];
    this.fnUploadImage();
  }

  fnUploadImage(){
    let index = this.selectedQuestionIndex;
    this.showIndicator= true;
    this.paperService.uploadImage(this.selectedImage)
    .subscribe(response => {
      this.closeModel(this.uploadImage);
      if (response.url) {
        if(this.uploadImageFor === '1'){ //1 means Question
          this.displayRecords[index].question = this.displayRecords[index].question + '\n\n' +
          '![enter image description here](' + response.url + ' \"enter image title here\")';
        }else if(this.uploadImageFor === '2') { // 2 means Explanation
          this.displayRecords[index].explanation = this.displayRecords[index].explanation + '\n\n' +
          '![enter image description here](' + response.url + ' \"enter image title here\")';
          // this.displayRecords[index].img_expln = 1;
        }
        // this.fnSaveQuestionDeatils(this.displayRecords[index]);
      }
      this.showIndicator = false;

      this.sSuccessMsg = 'Image Uploded Successfully. Hence,' +
        'image is added to bottom of the content area!\nPlease do rearrage as needed and save it.';
      this.openModal(this.sTemplate);
    }, error => {
      this.showIndicator = false;
    });
  }

  closeModel(template: TemplateRef<any>) {

    this.modalRef.hide();
  }

  questionOrderChanged(event: PageChangedEvent): void {
    this.copyStartItem = (event.page - 1) * event.itemsPerPage;
    this.copyEndItem = event.page * event.itemsPerPage;
    this.displayRecords = this.paperQuestion.slice(this.copyStartItem, this.copyEndItem);
    this.toMakeCollapseFalse();
  }

  takeToPreview() {
    this.router.navigate(['/dashboard/test',
      this.paperType,
      this.paper_detail.paper_id,
      this.paper_detail.paper_name,
      this.paper_detail.time_lim * 60000]);
  }

  selectValue(ev, i, option) {
    const newOptionValue = ev.target.value;
    // this.displayRecords[i]
    switch (option) {
      case 'opt_1':
        this.displayRecords[i].opt_1 = newOptionValue;
        break;
      case 'opt_2':
        this.displayRecords[i].opt_2 = newOptionValue;
          break;
      case 'opt_3':
        this.displayRecords[i].opt_3 = newOptionValue;
        break;
      case 'opt_4':
        this.displayRecords[i].opt_4 = newOptionValue;
        break;
      case 'opt_5':
        this.displayRecords[i].opt_5 = newOptionValue;
        break;
      case 'opt_5 || \'\'':
        this.displayRecords[i].opt_5 = newOptionValue;
        break;
      case 'option_1':
        this.displayRecords[i].option_1 = newOptionValue;
        break;
      case 'option_2':
        this.displayRecords[i].option_2 = newOptionValue;
        break;
      case 'option_3':
        this.displayRecords[i].option_3 = newOptionValue;
        break;
      case 'option_4':
        this.displayRecords[i].option_4 = newOptionValue;
        break;
    }
 }

 validateQuestionContent(question: any) {

  let errorExists = false;
  let errors = [];

  let text = question.question;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  this.validationGood = true;
  this.validationTitle = 'All Good!';

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in question');
  } else {
    this.validationMsgs.push('Invalid Characters found in question: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.opt_1|| question.option_1;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in first option');
  } else {
    this.validationMsgs.push('Invalid Characters found in first option: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.opt_2 || question.option_2;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in second option');
  } else {
    this.validationMsgs.push('Invalid Characters found in second option: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.opt_3|| question.option_3;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in third option');
  } else {
    this.validationMsgs.push('Invalid Characters found in third option: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.opt_4|| question.option_4;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in fourth option');
  } else {
    this.validationMsgs.push('Invalid Characters found in fourth option: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.opt_5 || "";
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in fifth option');
  } else {
    this.validationMsgs.push('Invalid Characters found in fifth option: ' + errors.toString());
    errorExists = true;
  }

  errors = [];
  text = question.explanation;
  for (const i of text) {
    try {
      btoa(i);
    } catch (ex) {
      errors.push(i);
    }
  }

  if (errors.length === 0) {
    this.validationMsgs.push('No invalid characters found in explanation');
  } else {
    this.validationMsgs.push('Invalid Characters found in explanation: ' + errors.toString());
    errorExists = true;
  }

  if (errorExists) {
    this.validationGood = false;
    this.validationTitle = 'Problems exist!';
  }

  this.openModal(this.validationTemplate);
 }

 fnAddNewQuestions() {

   const aQuestion = JSON.parse(JSON.stringify(this.paperQuestion));
   const oNewQuestion = {
    'correct_opt': '1',
    'explanation': '',
    'img_expln': 0,
    'opt_1': '',
    'opt_2': '',
    'opt_3': '',
    'opt_4': '',
    'opt_5': '',
    'paper_id': this.paper_detail.paper_id,
    'question': 'NEW Question..! Please add here',
    'question_no': this.allQuestionsLegth + 1
   };

   this.paperQuestion.push(oNewQuestion);
   this.fnPrepareObjectPapersId(this.paperQuestion);
   this.allQuestionsLegth = this.paperQuestion.length;

   const pageNo = this.paperQuestion.length % 10 === 0 ?
                    Math.trunc(this.paperQuestion.length / 10) :
                    Math.trunc(this.paperQuestion.length / 10) + 1;

   const page: PageChangedEvent = {
    itemsPerPage: 10,
    page: pageNo
   };

   $( '.pagination-page' ).each(index => {
     if (index === pageNo - 1) {
       $( this ).children('a').trigger('pageChange');
     }
   });

   this.questionOrderChanged(page);
   this.displayRecords = this.paperQuestion.slice(this.copyStartItem, this.copyEndItem);
   this.isCollapsed.push(true);
 }

 deleteQuestion(paper_id: string, question_no: string) {

  this.showIndicator = true;

  if (this.superPaperType === '5') {
    this.openTestsService.deleteQuestion(paper_id, question_no).subscribe(response => {
      this.showIndicator = false;
      this.fnGetPaperQuestions(paper_id);
      this.paperQuestion.slice(this.copyStartItem, this.copyEndItem);
      this.sSuccessMsg = 'Question Deleted Successfully!';
      this.openModal(this.sTemplate);
    }, error => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
    });
  } else if (this.superPaperType === '11') {
    this.tmcqService.deleteQuestion(paper_id, question_no).subscribe(response => {
      this.showIndicator = false;
      this.fnGetPaperQuestions(paper_id);
      this.paperQuestion.slice(this.copyStartItem, this.copyEndItem);
      this.sSuccessMsg = 'Question Deleted Successfully!';
      this.openModal(this.sTemplate);
    }, error2 => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
    });
  } else if (this.superPaperType === '13' || this.superPaperType === '14') {
    this.competitiveService.deleteCompetitivePaperQuestion(paper_id, question_no).subscribe(response => {
      this.showIndicator = false;
      this.fnGetPaperQuestions(paper_id);
      this.paperQuestion.slice(this.copyStartItem, this.copyEndItem);
      this.sSuccessMsg = 'Question Deleted Successfully!';
      this.openModal(this.sTemplate);
    }, error2 => {
      this.showIndicator = false;
      this.openModal(this.eTemplate);
    });
  }
 }

 toMakeCollapseFalse() {
   const iCollapsedLength = this.isCollapsed.length;
   for (let i = 0; i < iCollapsedLength ; i++) {
    this.isCollapsed[i] = true;
   }
 }

 hideModal(modalName: string) {
   this.modalRef.hide();

   if (modalName === 'validation') {
     this.validationMsgs = [];
     this.validationTitle = '';
     this.validationGood  = false;
   }
 }
}
