export interface ChapterPaper {
  id: string;
  name: string;
  timeLimit: number;
  companyType: number;
  // Add other relevant fields
}

export interface CompanyType {
  id: number;
  name: string;
  regex: RegExp;
  logo: string;
}

export const COMPANY_TYPES: CompanyType[] = [
  { id: 1, name: 'Infosys Papers', regex: /^INFOSYS*/, logo: '/img/companylogos/infosys.png' },
  { id: 2, name: 'Accenture Papers', regex: /^ACCENTURE*/, logo: '/img/companylogos/accenture.png' },
  { id: 4, name: 'TCS-NQT Papers', regex: /^TCS (NQT||DIGITAL)*/, logo: '/img/companylogos/tcs.png' },
  { id: 5, name: 'Wipro Papers', regex: /^WIPRO MOCK*/, logo: '/img/companylogos/wipro.png' },
  { id: 6, name: 'Cognizant Papers', regex: /^Cognizant*/, logo: '/img/companylogos/cognizant.png' },
  // Add other company types as needed
]; 