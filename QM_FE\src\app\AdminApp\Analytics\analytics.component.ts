import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss']
})
export class AnalyticsComponent implements OnInit {

  constructor(public router: Router,
              public activatedRoute: ActivatedRoute) { }

  ngOnInit() {
  }

  takeToVideoUsage() {
    this.router.navigate(['video-usage'], { relativeTo: this.activatedRoute });
  }
}
