// lib/client-services/user-profile-service.client.ts

import axios from 'axios'
import { NewUser } from '@/types/user-types'
import { LoginRefresh } from '../cookies'
import { ApiAuthService } from './api-auth.service'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

const ENDPOINTS = {
  updateUserProfile: (email: string) =>
    `${V2_BASE_URL}/user/manage/${email}/profile`,
  uploadAvatar: (email: string) =>
    `${API_BASE_URL}/user/manage/${email}/profile/image`,
  getUserAvatar: (email: string) =>
    `${API_BASE_URL}/user/manage/${email}/profile/image`,
  validateCurrentPassword: `${V2_BASE_URL}/user/manage/reset/validate`,
  saveNewPassword: `${V2_BASE_URL}/user/manage/reset/save`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Create form data headers with JWT token
 */
const createFormDataHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`
    }
  }
}

/**
 * Get user email from storage
 */
const getUserEmail = () => {
  return LoginRefresh.getUserEmail() || ''
}

export class UserProfileService {
  /**
   * Update user profile details
   */
  static async updateUserProfile(userInfo: NewUser): Promise<any> {
    try {
      const email = getUserEmail()

      // Create a new user object
      const newUser = {
        email: '', // Set to empty string
        phone_no: userInfo.phone_no,
        f_name: userInfo.f_name,
        l_name: userInfo.l_name,
        inst_name: userInfo.inst_name,
        qual: userInfo.qual,
        usn: userInfo.usn,
        branch: userInfo.branch,
        dob: '' // Will be set below
      }

      // Format date for API
      if (userInfo.dob) {
        try {
          const dob = new Date(userInfo.dob)
          const month =
            dob.getMonth() < 9 ? '0' + (dob.getMonth() + 1) : dob.getMonth() + 1
          const date = dob.getDate() < 10 ? '0' + dob.getDate() : dob.getDate()
          const dobStr = [dob.getFullYear(), month, date].join('-')
          newUser.dob = dobStr
        } catch (error) {
          console.error('Date parsing error:', error)
          newUser.dob = userInfo.dob // fallback to original
        }
      }

      // Generate protected body
      const protectedBody = ApiAuthService.generateAuthedBody(
        'PUT',
        `/v2/user/manage/${email}/profile`,
        newUser
      )

      const response = await axios.put(
        ENDPOINTS.updateUserProfile(email),
        protectedBody, // Send protected body instead of raw data
        createAuthHeaders()
      )
      return response.data
    } catch (error: any) {
      console.error('Error updating user profile:', error)

      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)

        if (error.response.status === 403) {
          throw new Error(
            'Access denied. Please check your authentication or try logging in again.'
          )
        }
      }

      throw error
    }
  }

  /**
   * Upload user avatar
   */
  static async uploadAvatar(file: File): Promise<any> {
    const email = getUserEmail()

    try {
      const formData = new FormData()
      formData.append('image_upload', file)

      const response = await axios.post(
        ENDPOINTS.uploadAvatar(email),
        formData,
        createFormDataHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error uploading avatar:', error)
      throw error
    }
  }

  /**
   * Get user avatar
   */
  static async getUserAvatar(): Promise<string | null> {
    const email = getUserEmail()

    try {
      const response = await axios.get(
        ENDPOINTS.getUserAvatar(email),
        createAuthHeaders()
      )
      return response.data.path
    } catch (error: any) {
      if (error.response?.data?.err === 'No picture uploaded') {
        return null
      }
      console.error('Error fetching avatar:', error)
      throw error
    }
  }

  /**
   * Validate current password
   */
  static async validateCurrentPassword(
    currentPassword: string
  ): Promise<{ key: string }> {
    try {
      const email = getUserEmail()

      const requestBody = { email, password: currentPassword }

      // Generate protected body
      const protectedBody = ApiAuthService.generateAuthedBody(
        'POST',
        '/v2/user/manage/reset/validate',
        requestBody
      )

      const response = await axios.post(
        ENDPOINTS.validateCurrentPassword,
        protectedBody, // Send protected body instead of raw data
        createAuthHeaders()
      )

      if (response.data.msg === 'goahead' && response.data.key) {
        return { key: response.data.key }
      }

      throw new Error('Please enter corrent password. Please try again.')
    } catch (error: any) {
      console.error('Error validating current password:', error)

      if (error.response) {
        console.error('Response data:', error.response.data)

        if (error.response.status === 400) {
          throw new Error('Please enter correct password. Please try again.')
        }
        if (error.response.status === 403) {
          throw new Error(
            'Access denied. Please check your authentication or try logging in again.'
          )
        }
      }

      throw error
    }
  }

  /**
   * Save new password
   */
  static async saveNewPassword(
    newPassword: string,
    resetKey: string
  ): Promise<any> {
    try {
      const email = getUserEmail()

      const requestBody = { email, password: newPassword, reset_key: resetKey }

      // Generate protected body
      const protectedBody = ApiAuthService.generateAuthedBody(
        'POST',
        '/v2/user/manage/reset/save',
        requestBody
      )

      const response = await axios.post(
        ENDPOINTS.saveNewPassword,
        protectedBody, // Send protected body instead of raw data
        createAuthHeaders()
      )

      if (response.data.msg === 'updated') {
        return response.data
      }

      throw new Error('Failed to update password')
    } catch (error: any) {
      console.error('Error saving new password:', error)

      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)

        if (error.response.status === 403) {
          throw new Error(
            'Access denied. Please check your authentication or try logging in again.'
          )
        }
      }

      throw error
    }
  }
}
