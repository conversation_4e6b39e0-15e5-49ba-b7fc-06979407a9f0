// lib/client-services/competitive.service.client.ts
import axios from 'axios'
import {
  CompetitiveGroup,
  CompetitivePapers
} from '@/types/competitive-paper-types'
import { LoginRefresh } from '../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  groupUrl: `${API_BASE_URL}/test/competitive/groups`,
  papersUrl: `${API_BASE_URL}/test/competitive/papers`,
  groupPaperUrl: `${API_BASE_URL}/test/competitive/group`,
  submitUrl: `${API_BASE_URL}/test/competitive/submit/marks`,
  questionUrl: `${API_BASE_URL}/test/competitive/paper/new`,
  vpUrls: `${API_BASE_URL}/v2/verbal/practice`,
  tpUrls: `${API_BASE_URL}/v2/technical/practice`,
  admUrls: `${API_BASE_URL}/admin/paper/competitive`
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()

  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class CompetitiveService {
  /**
   * Get competitive groups
   */
  static async getGroups(): Promise<CompetitiveGroup[]> {
    try {
      const response = await axios.get(ENDPOINTS.groupUrl, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching competitive groups:', error)
      return []
    }
  }

  /**
   * Get details of a specific group
   */
  static async getDetailsOfAGroup(
    groupId: string
  ): Promise<CompetitiveGroup | null> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.groupUrl}/${groupId}/detail`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching group details for ${groupId}:`, error)
      return null
    }
  }

  /**
   * Get all competitive papers
   */
  static async getAllPapers(): Promise<CompetitivePapers[]> {
    try {
      const response = await axios.get(ENDPOINTS.papersUrl, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error fetching competitive papers:', error)
      return []
    }
  }

  /**
   * Get papers of a specific group
   */
  static async getPapersOfAGroup(
    groupId: string
  ): Promise<CompetitivePapers[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.groupPaperUrl}/${groupId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching papers for group ${groupId}:`, error)
      return []
    }
  }

  /**
   * Get marks for a student
   */
  static async getMarksForAStudent(email: string): Promise<string> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.papersUrl}/practice/${email}/marks`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching marks for ${email}:`, error)
      throw error
    }
  }

  /**
   * Submit marks
   */
  static async submitMarks(
    paperId: string,
    email: string,
    marks: number
  ): Promise<string> {
    try {
      const data = {
        email: email,
        paper_id: paperId,
        marks: marks
      }

      const response = await axios.post(
        ENDPOINTS.submitUrl,
        data,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error submitting marks:', error)
      throw error
    }
  }

  /**
   * Get competitive paper questions
   */
  static async getCompetitivePaperQuestions(
    paperId: string
  ): Promise<string[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.questionUrl}/${paperId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching questions for paper ${paperId}:`, error)
      throw error
    }
  }

  /**
   * Get verbal practice groups
   */
  static async getVerbalPracticeGroups(): Promise<string[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.vpUrls}/groups`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching verbal practice groups:', error)
      return []
    }
  }

  /**
   * Get technical practice groups
   */
  static async getTechnicalPracticeGroups(): Promise<string[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.tpUrls}/groups`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error fetching technical practice groups:', error)
      return []
    }
  }

  /**
   * Create new verbal practice group
   */
  static async createNewVerbalPracticeGroup(
    group: CompetitiveGroup
  ): Promise<string> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.vpUrls}/groups`,
        group,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error creating verbal practice group:', error)
      throw error
    }
  }

  /**
   * Create new competitive paper
   */
  static async createNewCompetitivePaper(
    paper: CompetitivePapers
  ): Promise<CompetitivePapers> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.admUrls}/upload/test`,
        paper,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error creating competitive paper:', error)
      throw error
    }
  }

  /**
   * Update competitive paper
   */
  static async updateCompetitivePaper(
    paper: CompetitivePapers
  ): Promise<string> {
    try {
      const response = await axios.put(
        `${ENDPOINTS.admUrls}/update/test`,
        paper,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating competitive paper:', error)
      throw error
    }
  }

  /**
   * Delete competitive paper
   */
  static async deleteCompetitivePaper(paperId: string): Promise<string> {
    try {
      const response = await axios.delete(
        `${ENDPOINTS.admUrls}/update/test/${paperId}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error deleting competitive paper:', error)
      throw error
    }
  }

  /**
   * Create competitive paper question
   */
  static async createCompetitivePaperQuestion(
    question: object
  ): Promise<object> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.admUrls}/upload/question`,
        question,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error creating competitive paper question:', error)
      throw error
    }
  }

  /**
   * Update competitive paper question
   */
  static async updateCompetitivePaperQuestion(
    question: object
  ): Promise<object> {
    try {
      const response = await axios.post(
        `${ENDPOINTS.admUrls}/update/question`,
        question,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error updating competitive paper question:', error)
      throw error
    }
  }

  /**
   * Delete competitive paper question
   */
  static async deleteCompetitivePaperQuestion(
    paperId: string,
    quesNo: string
  ): Promise<string> {
    try {
      const response = await axios.delete(
        `${ENDPOINTS.admUrls}/update/test/${paperId}/${quesNo}`,
        createAuthHeaders()
      )
      return response.data
    } catch (error) {
      console.error('Error deleting competitive paper question:', error)
      throw error
    }
  }
}
