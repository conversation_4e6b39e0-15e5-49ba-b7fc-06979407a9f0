import { Component, OnInit, ViewChild, HostListener, TemplateRef } from '@angular/core';
import { BsDatepickerDirective } from 'ngx-bootstrap/datepicker';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';

import { UserService } from '../../Services/user.service';
import { PasswordService } from '../../Services/password.service';
import { NewUser } from 'src/app/Models/NewUser';
import { Router, NavigationEnd } from '@angular/router';

import { BranchCourse } from '../../Models/Static/BranchCourse';

declare let gtag: Function;

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss']
})
export class UserProfileComponent implements OnInit {

  imagePath: string;
  selectedImage: File;
  imgUploadError: String;

  userInfo = {
    f_name: null,
    l_name: null,
    email: null,
    phone_no: null,
    dob: null,
    yop: null,
    usn: null,
    inst_name: null,
    section: null,
    qual: null,
    branch: null
  };

  newPassword = {
    currentPass: null,
    password: null,
    conf_password: null
  };
  public errorShowText = 'Oops, something went wrong!';
  public visibleFields = false;
  public disableFields = false;
  public key = '';
  public bsValue = new Date();
  public modalRef: BsModalRef;

  public showIndicator = false;

  public selectedView = [1, 0, 0];

  public courses: BranchCourse;
  public branches = { bName: '', subName: [''] };

  @ViewChild(BsDatepickerDirective) datepicker: BsDatepickerDirective;

  @ViewChild('imgUpdlTemplate') public updlTemplate: TemplateRef<any>;
  @ViewChild('successTemplate') public successTemplate: TemplateRef<any>;
  @ViewChild('imgSuccessTemplate') public imgSuccessTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public errorTemplate: TemplateRef<any>;
  @ViewChild('imgErrorTemplate') public imgErrorTemplate: TemplateRef<any>;

  public bsConfig = {
    dateInputFormat: 'DD/MM/YYYY',
    containerClass: 'theme-dark-blue',
  };

  constructor(public router: Router,
    public modalService: BsModalService,
    public userService: UserService,
    public PasswordServices: PasswordService) {
    router.events.subscribe((y: NavigationEnd) => {
      if (y instanceof NavigationEnd) {
        gtag('config', 'UA-163800914-1', { 'page_path': y.url });
      }
    });
  }

  ngOnInit() {
    this.showIndicator = true;
    const email = sessionStorage.getItem('QMail');

    this.courses = new BranchCourse();

    this.userService.getUserProfileDetails(email).subscribe(response => {
      this.userInfo = response;

      this.branches = this.courses.data.find(x => x.bName === this.userInfo.qual);

      const dob = new Date(response.dob);
      const month = (dob.getMonth() + 1) < 10 ? '0' + (dob.getMonth() + 1) : (dob.getMonth() + 1);
      const date = dob.getDate() < 10 ? '0' + dob.getDate() : dob.getDate();
      const dobStr = [date, month, dob.getFullYear()].join('/');

      this.userInfo.dob = dobStr;

      this.userService.getUserProfileAvatar(email).subscribe(response2 => {
        const resp = JSON.parse(JSON.stringify(response2));

        this.imagePath = resp.path;
        this.showIndicator = false;
      }, error => {
        const resp = JSON.parse(JSON.stringify(error));

        if (resp.error.err === 'No pic uploaded') {
          this.imagePath = null;
        } else {
          this.openModal(this.errorTemplate);
        }

        this.showIndicator = false;
      });
    }, error => {
      this.openModal(this.errorTemplate);
      this.showIndicator = false;
    });
  }

  selectView(num: number) {
    this.selectedView = [0, 0, 0];

    if (num === 1) {
      this.selectedView[0] = 1;
    } else if (num === 2) {
      this.selectedView[1] = 1;
    } else if (num === 3) {
      this.selectedView[2] = 1;
    }
  }

  fileSelect(event: any) {
    this.selectedImage = event.target.files[0];

    this.openModal(this.updlTemplate);
  }

  filterSelectedCourse(course: string) {
    this.branches = this.courses.data.find(x => x.bName === course);
  }

  uploadFile() {
    this.modalRef.hide();

    this.showIndicator = true;

    const email = sessionStorage.getItem('QMail');

    this.userService.setUserProfileAvatar(email, this.selectedImage).subscribe(response => {
      this.selectedImage = null;

      this.openModal(this.imgSuccessTemplate);

      this.userService.getUserProfileAvatar(email).subscribe(response2 => {
        const resp = JSON.parse(JSON.stringify(response2));

        this.imagePath = resp.path;
        this.showIndicator = false;
      }, error => {
        const resp = JSON.parse(JSON.stringify(error));

        if (resp.error.err === 'No pic uploaded') {
          this.imagePath = null;
        } else {
          this.openModal(this.errorTemplate);
        }

        this.showIndicator = false;
      });
    }, error => {
      const resp = JSON.parse(JSON.stringify(error));

      this.selectedImage = null;
      this.imgUploadError = resp.error.err;
      this.openModal(this.imgErrorTemplate);

      this.showIndicator = false;
    });
  }

  cancelUpload() {
    this.selectedImage = null;
    this.modalRef.hide();
  }

  updateUserDetails(isValid: boolean) {
    this.showIndicator = true;

    if (!isValid) {
      this.showIndicator = false;
      return;
    }

    const email = sessionStorage.getItem('QMail');

    const newUser = new NewUser();
    newUser.email = '';
    newUser.phone_no = this.userInfo.phone_no;
    newUser.f_name = this.userInfo.f_name;
    newUser.l_name = this.userInfo.l_name;
    newUser.inst_name = this.userInfo.inst_name;
    newUser.section = this.userInfo.section;
    newUser.qual = this.userInfo.qual;
    newUser.usn = this.userInfo.usn;
    newUser.yop = this.userInfo.yop;
    newUser.branch = this.userInfo.branch;

    const dob = new Date(this.userInfo.dob);
    const month = dob.getMonth() < 10 ? '0' + (dob.getMonth() + 1) : (dob.getMonth() + 1);
    const date = dob.getDate() < 10 ? '0' + dob.getDate() : dob.getDate();
    const dobStr = [dob.getFullYear(), month, date].join('-');

    newUser.dob = dobStr;

    this.userService.setUserProfileDetails(email, newUser).subscribe(response => {
      this.showIndicator = false;
      sessionStorage.setItem('QUCmpl', '1');

      this.openModal(this.successTemplate);
    }, error => {
      this.showIndicator = false;
      console.warn(error);
    });
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template);
  }

  checkCurrentPassword() {
    this.showIndicator = true;
    if (!this.newPassword.currentPass) {
      this.showIndicator = false;
      this.visibleFields = false;
      return;
    }
    const email = sessionStorage.getItem('QMail');
    this.PasswordServices.createPasswordChangeRequest(email, this.newPassword.currentPass).subscribe(response => {
      if (response['msg'] === 'goahead' && response['key']) {
        this.visibleFields = true;
        this.disableFields = true;
        this.key = response['key'];
      } else {
        this.visibleFields = false;
      }
      this.showIndicator = false;
    }, error => {
      this.errorShowText = 'Please enter correct password';
      this.openModal(this.errorTemplate);
      this.showIndicator = false;
      this.visibleFields = false;
    });
  }

  updateUserNewPassword(isValid: boolean) {
    const email = sessionStorage.getItem('QMail');
    if (!isValid) {
      return;
    }
    // if (this.newPassword.pa !== this.newPassword.conf_password) {
    //   this.errorShowText = 'Please confirm the correct password';
    //   this.openModal(this.errorTemplate);
    // } else {
    this.PasswordServices.saveChangedPassword(email, this.newPassword.conf_password, this.key).subscribe(response => {
      if (response['msg'] === 'updated') {
        this.newPassword.currentPass = null;
        this.newPassword.password = null;
        this.newPassword.conf_password = null;
        this.visibleFields = false;
        this.disableFields = false;
        this.openModal(this.successTemplate);
      } else {
        this.errorShowText = 'Oops, something went wrong!';
        this.openModal(this.errorTemplate);
      }
      this.showIndicator = false;
    }, error => {
      this.errorShowText = 'Oops, something went wrong!';
      this.openModal(this.errorTemplate);
    });

  }

  @HostListener('window:scroll')
  onScrollEvent() {
    // this.datepicker.hide();
  }
}
