<app-nav></app-nav>
<div class="results-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <h5>My Marks</h5>
  <div class="options-box">
    <button class="custom-btn" (click)="getAllMarks()">All My Marks</button>
    <button class="custom-btn" (click)="getTrialPaperMarks()">Trial Papers</button>
    <button class="custom-btn" (click)="getWeeklyCompetitivePaperMarks()">Weekly Competitive Papers</button>
    <button class="custom-btn" (click)="getSectionWisePaperMarks()">Section Wise Mock Papers</button>
    <button class="custom-btn" (click)="getAfcatPaperMarks()">AFCAT Papers</button>
    <button class="custom-btn" (click)="getCompanyPaperMarks()">Company Papers</button>
    <button class="custom-btn" (click)="getTechnicalMCQMarks()">Technical MCQ Papers</button>
    <button class="custom-btn" (click)="getWPPracticeMarks()">Verbal / Technical Practice Papers</button>
  </div>
  <div class="data-table">
    <div class="headers">
      <h6>Paper Name</h6>
      <h6>Paper Type</h6>
      <h6>Paper Marks</h6>
      <h6>Answered On</h6>
    </div>
    <div class="table-data" *ngFor="let result of displayMarks">
      <p>{{ result.paper_name }}</p>
      <p>{{ result.paper_type }}</p>
      <p>{{ result.marks + "/" + result.total_marks}}</p>
      <p>{{ result.display_date }}</p>
      <button type="button" [style.visibility]="result.sw_exists ? 'visible' : 'hidden'" 
        (click)="showSectionMarks(result.answer_id, result.paper_name, result.display_date)">
        Section-wise Breakdown
      </button>
    </div>
    <pagination *ngIf="numDispMarks > 0" [boundaryLinks]="panginatorConfig.boundaryLinks" [totalItems]="numDispMarks" [rotate]="true" [maxSize]="panginatorConfig.max" [itemsPerPage]="15"
      (pageChanged)="pageChanged($event)"></pagination>
  </div>
  <ng-template #sectionMarksTemplate>
    <div class="modal-header">
      <h4 class="modal-title pull-left text-secondary">{{ paperName }}: Section-Wise Breakdown</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <h6>Asnwered on: {{ answerDate }}</h6>
      <div class="marks-table">
        <div class="headers">
          <p>Section Name</p>
          <p>Marks</p>
          <p>Percentage</p>
        </div>
        <div class="content-row" *ngFor="let marks of sectionMarks">
          <p>{{ marks.section_name }}</p>
          <p>{{ marks.marks }}</p>
          <p>{{ marks.percentage }} %</p>
        </div>
      </div>
    </div>
  </ng-template>
</div>
