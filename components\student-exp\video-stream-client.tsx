'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Skeleton } from '@/components/ui/skeleton'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogDescription
} from '@/components/ui/dialog'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible'
import { SelectedVideoType } from '@/types/video-types'
import {
  Play,
  Pause,
  SkipBack,
  SkipForward,
  Maximize,
  VolumeX,
  Volume2,
  Star,
  StarIcon
} from 'lucide-react'
import { StreamingService } from '@/lib/client-services/streaming.service'
import { useAuth } from '@/lib/hooks/useAuth'
import { UserAvatar } from '../layout/user-avatar'
import { User } from '@/types/user'
import {
  TrackingService,
  VideoUsage
} from '@/lib/client-services/tracking.service'

// Types needed for the component
interface VideoStreamClientProps {
  isModalOpen: boolean
  handleModalClose: () => void
  selectedVideo: SelectedVideoType
  isLoading: boolean
  videoLink: string | null
  videoError?: string
}

interface Comment {
  comment_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user?: User
  replies: CommentReply[]
}

interface CommentReply {
  reply_id: string
  text: string
  created_by: string
  created_at: string
  email: string
  user?: User
}

export default function VideoStreamClient({
  isModalOpen,
  handleModalClose,
  selectedVideo,
  isLoading,
  videoLink,
  videoError
}: VideoStreamClientProps) {
  // State for video player
  const videoRef = useRef<HTMLVideoElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [videoType, setVideoType] = useState<string>('0')

  // State for tracking
  const [totalWatchTime, setTotalWatchTime] = useState(0)
  const [lastWatchTime, setLastWatchTime] = useState(0)
  const [prevSecTick, setPrevSecTick] = useState(0)

  // State for ratings
  const [overallRating, setOverallRating] = useState(0)
  const [userRating, setUserRating] = useState(0)
  const [showRatingModal, setShowRatingModal] = useState(false)

  // State for comments
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [commentReplies, setCommentReplies] = useState<{
    [key: string]: string
  }>({})
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)

  const { user } = useAuth()

  // Format time for display (mm:ss)
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  // Handle video type based on selectedVideo.type
  useEffect(() => {
    if (selectedVideo) {
      setVideoType(selectedVideo.type)
    }
  }, [selectedVideo])

  // Initialize video player
  useEffect(() => {
    if (
      !videoRef.current ||
      !videoLink ||
      videoType === '5' ||
      videoType === '6'
    )
      return

    const video = videoRef.current

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)

      // Track watch time
      const currentSecTick = Math.floor(video.currentTime)
      if (prevSecTick === currentSecTick - 1) {
        setTotalWatchTime((prev) => prev + 1)
        setPrevSecTick(currentSecTick)
        setLastWatchTime(video.currentTime)
      } else {
        setPrevSecTick(currentSecTick)
        setLastWatchTime(video.currentTime)
      }
    }

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
    }

    const handlePlay = () => setIsPlaying(true)
    const handlePause = () => setIsPlaying(false)

    // Add event listeners
    video.addEventListener('timeupdate', handleTimeUpdate)
    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('play', handlePlay)
    video.addEventListener('pause', handlePause)

    return () => {
      // Clean up event listeners
      video.removeEventListener('timeupdate', handleTimeUpdate)
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('play', handlePlay)
      video.removeEventListener('pause', handlePause)
    }
  }, [videoRef, videoLink, videoType, prevSecTick])

  // Track video data on close
  useEffect(() => {
    if (!isModalOpen && selectedVideo && totalWatchTime > 0) {
      const trackData: VideoUsage = {
        email: user!.email,
        videoId: selectedVideo.id,
        userId: user!.id!,
        userName: user!.name || 'Current User',
        videoName: selectedVideo.name || 'Video',
        startTime: new Date(),
        endTime: new Date(),
        watchDuration: totalWatchTime,
        totalDuration: duration,
        percentageWatched: (totalWatchTime / duration) * 100
      }

      TrackingService.submitVideoTrackRecord(trackData)
      console.log('Tracking data:', trackData)

      // Prompt for rating if user hasn't rated
      if (userRating === 0) {
        setShowRatingModal(true)
      }
    }
  }, [
    user,
    isModalOpen,
    selectedVideo,
    totalWatchTime,
    lastWatchTime,
    userRating
  ])

  // Fetch user's rating and comments when modal opens
  useEffect(() => {
    if (isModalOpen && selectedVideo) {
      // Fetch user rating
      const fetchRating = async () => {
        try {
          const response = await StreamingService.getStudentVideoRating(
            selectedVideo.id,
            user!.email
          )
          setUserRating(parseInt(response.rating_given, 10))
          setOverallRating(parseInt(response.rating_given, 10))
        } catch (error) {
          console.error('Error fetching rating:', error)
        }
      }

      // Fetch comments
      const fetchComments = async () => {
        try {
          const response = await StreamingService.getVideoCommentsList(
            selectedVideo.id
          )
          console.log(response)
          setComments(response)
        } catch (error) {
          console.error('Error fetching comments:', error)
        }
      }

      fetchRating()
      fetchComments()
    }
  }, [user, isModalOpen, selectedVideo])

  // Video playback controls
  const togglePlay = () => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  const skipBack = () => {
    if (!videoRef.current) return
    videoRef.current.currentTime -= 10
  }

  const skipAhead = () => {
    if (!videoRef.current) return
    videoRef.current.currentTime += 10
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!videoRef.current) return
    const newTime = parseFloat(e.target.value)
    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }

  const toggleMute = () => {
    if (!videoRef.current) return
    videoRef.current.muted = !isMuted
    setIsMuted(!isMuted)
  }

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!videoRef.current) return
    const newVolume = parseFloat(e.target.value)
    videoRef.current.volume = newVolume
    setVolume(newVolume)
  }

  const toggleFullscreen = () => {
    if (!videoRef.current) return

    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      videoRef.current.requestFullscreen()
    }
  }

  // Rating handlers
  const submitRating = async (rating: number) => {
    try {
      setUserRating(rating)

      await StreamingService.postVideoRating(
        user!.email,
        selectedVideo.id,
        rating
      )

      setShowRatingModal(false)
    } catch (error) {
      console.error('Error submitting rating:', error)
    }
  }

  // Comment handlers
  const submitComment = async () => {
    if (!newComment.trim() || !selectedVideo) return

    try {
      setIsSubmittingComment(true)

      const commentBody = {
        comment_text: newComment,
        email: user!.email,
        name: user!.name || 'Current User'
      }

      await StreamingService.postVideoComment(selectedVideo.id, commentBody)

      const newCommentObj: Comment = {
        comment_id: Date.now().toString(),
        text: newComment,
        created_by: user!.name || 'Current User',
        created_at: new Date().toDateString(),
        email: user!.email,
        replies: []
      }

      setComments((prev) => [newCommentObj, ...prev])
      setNewComment('')
    } catch (error) {
      console.error('Error submitting comment:', error)
    } finally {
      setIsSubmittingComment(false)
    }
  }

  const submitReply = async (commentId: string, index: number) => {
    const replyText = commentReplies[commentId]
    if (!replyText?.trim() || !selectedVideo) return

    try {
      const replyBody = {
        reply_text: replyText,
        email: user!.email,
        name: user!.name || 'Current User'
      }

      await StreamingService.postCommentReply(
        selectedVideo.id,
        commentId,
        replyBody
      )

      const newReply: CommentReply = {
        reply_id: Date.now().toString(),
        text: replyText,
        created_by: user!.name || 'Current User',
        created_at: new Date().toDateString(),
        email: user!.email
      }

      const updatedComments = [...comments]
      updatedComments[index].replies.push(newReply)
      setComments(updatedComments)

      setCommentReplies((prev) => ({ ...prev, [commentId]: '' }))
    } catch (error) {
      console.error('Error submitting reply:', error)
    }
  }

  // Get YouTube embed URL
  const getYoutubeEmbedUrl = (youtubeId: string): string => {
    return `https://www.youtube.com/embed/${youtubeId}?controls=1&autoplay=1`
  }

  // Render star rating component
  const StarRating = ({
    rating,
    setRating,
    readOnly = false
  }: {
    rating: number
    setRating?: (rating: number) => void
    readOnly?: boolean
  }) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => setRating && setRating(star)}
            disabled={readOnly}
            className={`focus:outline-none ${readOnly ? 'cursor-default' : 'cursor-pointer'}`}
          >
            {star <= Math.round(rating) ? (
              <StarIcon className="w-5 h-5 text-yellow-400 fill-yellow-400" />
            ) : (
              <Star className="w-5 h-5 text-gray-300" />
            )}
          </button>
        ))}
      </div>
    )
  }

  return (
    <>
      <Dialog open={isModalOpen} onOpenChange={handleModalClose}>
        <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {videoType === '6'
                ? 'Interview Experience'
                : videoType === '5'
                  ? 'Workshop Video'
                  : videoType === '4'
                    ? 'Trial Video'
                    : videoType === '3'
                      ? 'Review Video'
                      : videoType === '2'
                        ? 'Testimony Video'
                        : 'Course Video'}
            </DialogTitle>
            <DialogDescription>
              {videoType === '6'
                ? 'Watch the interview experience video'
                : videoType === '5'
                  ? 'Watch the workshop video'
                  : videoType === '4'
                    ? 'Watch the trial video'
                    : videoType === '3'
                      ? 'Watch the review video'
                      : videoType === '2'
                        ? 'Watch the testimony video'
                        : 'Watch the course video'}
            </DialogDescription>
          </DialogHeader>

          <div className="relative aspect-video w-full bg-black overflow-hidden rounded-md">
            {videoError ? (
              <Alert variant="destructive" className="my-4">
                <AlertDescription>{videoError}</AlertDescription>
              </Alert>
            ) : isLoading ? (
              <div className="h-full w-full flex items-center justify-center bg-gray-100">
                <Skeleton className="h-full w-full" />
              </div>
            ) : !videoLink ? (
              <div className="h-full w-full flex items-center justify-center bg-gray-100">
                <p className="text-gray-500">Loading video...</p>
              </div>
            ) : videoType === '5' || videoType === '6' ? (
              // YouTube or Vimeo embed
              <iframe
                src={
                  videoType === '6' ? getYoutubeEmbedUrl(videoLink) : videoLink
                }
                className="w-full h-full"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              ></iframe>
            ) : (
              // Native video player with custom controls
              <>
                <video
                  ref={videoRef}
                  src={videoLink}
                  className="w-full h-full"
                  onClick={togglePlay}
                ></video>

                {/* Custom video controls */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white text-sm">
                      {formatTime(currentTime)}
                    </span>
                    <span className="text-white text-sm">
                      {formatTime(duration)}
                    </span>
                  </div>

                  <input
                    type="range"
                    min="0"
                    max={duration || 100}
                    value={currentTime}
                    onChange={handleSeek}
                    className="w-full h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  />

                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-3">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="text-white rounded-full hover:bg-white/20"
                        onClick={skipBack}
                      >
                        <SkipBack className="h-5 w-5" />
                      </Button>

                      <Button
                        size="icon"
                        variant="ghost"
                        className="text-white rounded-full hover:bg-white/20"
                        onClick={togglePlay}
                      >
                        {isPlaying ? (
                          <Pause className="h-5 w-5" />
                        ) : (
                          <Play className="h-5 w-5" />
                        )}
                      </Button>

                      <Button
                        size="icon"
                        variant="ghost"
                        className="text-white rounded-full hover:bg-white/20"
                        onClick={skipAhead}
                      >
                        <SkipForward className="h-5 w-5" />
                      </Button>

                      <div className="flex items-center space-x-2">
                        <Button
                          size="icon"
                          variant="ghost"
                          className="text-white rounded-full hover:bg-white/20"
                          onClick={toggleMute}
                        >
                          {isMuted ? (
                            <VolumeX className="h-5 w-5" />
                          ) : (
                            <Volume2 className="h-5 w-5" />
                          )}
                        </Button>

                        <input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={volume}
                          onChange={handleVolumeChange}
                          className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                        />
                      </div>
                    </div>

                    <Button
                      size="icon"
                      variant="ghost"
                      className="text-white rounded-full hover:bg-white/20"
                      onClick={toggleFullscreen}
                    >
                      <Maximize className="h-5 w-5" />
                    </Button>
                  </div>
                </div>

                {/* Skip overlay buttons (visible on pause) */}
                {!isPlaying && (
                  <div className="absolute inset-0 flex items-center justify-between px-20">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-16 w-16 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20"
                      onClick={skipBack}
                    >
                      <div className="flex flex-col items-center">
                        <SkipBack className="h-8 w-8 text-white" />
                        <span className="text-white text-xs">10s</span>
                      </div>
                    </Button>

                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-20 w-20 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20"
                      onClick={togglePlay}
                    >
                      <Play className="h-10 w-10 text-white" />
                    </Button>

                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-16 w-16 rounded-full bg-white/10 backdrop-blur-sm hover:bg-white/20"
                      onClick={skipAhead}
                    >
                      <div className="flex flex-col items-center">
                        <SkipForward className="h-8 w-8 text-white" />
                        <span className="text-white text-xs">10s</span>
                      </div>
                    </Button>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Video details, ratings and comments */}
          <Tabs defaultValue="ratings" className="mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="ratings">Ratings</TabsTrigger>
              <TabsTrigger value="comments">
                Comments ({comments.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="ratings">
              <Card>
                <CardHeader>
                  <CardTitle>Video Ratings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-col space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        Overall Rating:
                      </span>
                      <div className="flex items-center space-x-2">
                        <StarRating rating={overallRating} readOnly />
                        <span className="text-sm font-medium">
                          {overallRating.toFixed(1)}/5
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">My Rating:</span>
                      <div className="flex items-center space-x-2">
                        <StarRating
                          rating={userRating}
                          setRating={submitRating}
                          readOnly={false}
                        />
                        <span className="text-sm font-medium">
                          {userRating}/5
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comments" className="mt-2">
              <Card>
                <CardHeader>
                  <CardTitle>Comments</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start space-x-4 mb-6">
                    <UserAvatar user={user} />
                    <div className="flex-1">
                      <Textarea
                        placeholder="Add your comment..."
                        value={newComment}
                        onChange={(e: any) => setNewComment(e.target.value)}
                      />
                      <div className="flex justify-end mt-2 space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => setNewComment('')}
                        >
                          Clear
                        </Button>
                        <Button
                          onClick={submitComment}
                          disabled={isSubmittingComment || !newComment.trim()}
                        >
                          Comment
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {comments.map((comment, index) => (
                      <div key={comment.comment_id} className="border-b pb-4">
                        <div className="flex items-start space-x-4">
                          <UserAvatar user={comment.user} />
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <p className="font-medium">
                                {comment.created_by}
                              </p>
                              <p className="text-sm text-gray-500">
                                {comment.created_at}
                              </p>
                            </div>
                            <p className="mt-1">{comment.text}</p>
                            <div className="flex items-center space-x-4 mt-2">
                              <Collapsible>
                                <CollapsibleTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    Reply
                                  </Button>
                                </CollapsibleTrigger>
                                <CollapsibleContent className="mt-2">
                                  <div className="flex items-start space-x-4">
                                    <UserAvatar user={user} />
                                    <div className="flex-1">
                                      <Textarea
                                        placeholder="Add your reply..."
                                        value={
                                          commentReplies[comment.comment_id] ||
                                          ''
                                        }
                                        onChange={(e: any) =>
                                          setCommentReplies({
                                            ...commentReplies,
                                            [comment.comment_id]: e.target.value
                                          })
                                        }
                                        className="min-h-[80px]"
                                      />
                                      <div className="flex justify-end mt-2 space-x-2">
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() =>
                                            setCommentReplies({
                                              ...commentReplies,
                                              [comment.comment_id]: ''
                                            })
                                          }
                                        >
                                          Cancel
                                        </Button>
                                        <Button
                                          size="sm"
                                          onClick={() =>
                                            submitReply(
                                              comment.comment_id,
                                              index
                                            )
                                          }
                                          disabled={
                                            !commentReplies[
                                              comment.comment_id
                                            ]?.trim()
                                          }
                                        >
                                          Submit
                                        </Button>
                                      </div>
                                    </div>
                                  </div>
                                </CollapsibleContent>
                              </Collapsible>

                              {comment.replies.length > 0 && (
                                <Collapsible>
                                  <CollapsibleTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      View {comment.replies.length}{' '}
                                      {comment.replies.length === 1
                                        ? 'Reply'
                                        : 'Replies'}
                                    </Button>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent className="mt-4 space-y-4 pl-12">
                                    {comment.replies.map((reply) => (
                                      <div
                                        key={reply.reply_id}
                                        className="flex items-start space-x-4"
                                      >
                                        <UserAvatar user={reply.user} />
                                        <div className="flex-1">
                                          <div className="flex items-center justify-between">
                                            <p className="font-medium">
                                              {reply.created_by}
                                            </p>
                                            <p className="text-sm text-gray-500">
                                              {reply.created_at}
                                            </p>
                                          </div>
                                          <p className="mt-1">{reply.text}</p>
                                        </div>
                                      </div>
                                    ))}
                                  </CollapsibleContent>
                                </Collapsible>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <div className="mt-4 flex justify-end">
            <DialogClose asChild>
              <Button variant="outline">Close</Button>
            </DialogClose>
          </div>
        </DialogContent>
      </Dialog>

      {/* Rating prompt modal */}
      <Dialog open={showRatingModal} onOpenChange={setShowRatingModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Got a moment?</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4">Please rate your viewing experience.</p>
            <div className="flex items-center justify-center space-x-2">
              <StarRating
                rating={userRating}
                setRating={submitRating}
                readOnly={false}
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button onClick={() => submitRating(userRating || 5)}>
              Submit
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
