@import "src/mixins";

.notes-home-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  min-height: 80vh;
  margin-top: 3em;
  font: "Montserrat", "sans-serif";

  @include for-size(phone-only) {
    width: 100%;
  }

  .super-groups {
    margin-top: 2rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    align-items: center;
    justify-items: center;

    @include for-size(phone-only) {
      grid-template-columns: repeat(2, 1fr);
    }

    .group-1 {
      cursor: pointer;
      width: 150px;
      padding: 15px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);
      transform: scale(1);
      transition: all 0.4s ease-in;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.5);
        transition: all 0.2s ease-out;
      }

      p {
        text-align: center;
        margin: 0;
      }
    }
  }

  .groups {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    @include for-size(phone-only) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0.7em;
    }

    .group-2 {
      cursor: pointer;
      width: 150px;
      margin-left: 1rem;
      padding: 15px 5px;
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);
      transform: scale(1);
      transition: all 0.4s ease-in;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.5);
        transition: all 0.2s ease-out;
      }

      p {
        text-align: center;
        margin: 0;
      }
    }
  }

  .notes-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60%;
    margin-bottom: 6em;

    @include for-size(phone-only) {
      width: 95%;
    }

    .notes-list {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;

      .notes-item {
        display: grid;
        grid-template-columns: 50% 20% 25%;
        gap: 0.5rem;
        align-items: center;
        width: 100%;
        margin-top: 1rem;
        padding: 15px 10px;
        border-radius: 5px;
        box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);
        position: relative;

        @include for-size(phone-only) {
          grid-template-columns: 1fr 1fr;
        }

        p {
          margin: 0;
          margin-right: 15px;

          @include for-size(phone-only) {
            &:nth-of-type(1) {
              grid-column: 1 / span 2;
            }
          }
        }

        .read-btn {
          padding: 7px;
          color: #145a32;
          background-color: #82e0aa;
          border: none;
          border-radius: 5px;
          box-shadow: 2px 3px 10px 0 rgba(121, 124, 125, 0.3);
          transform: scale(1);
          transition: all 0.4s ease;

          &:hover {
            color: #d4efdf;
            background-color: #1e8449;
            transform: scale(1.05);
            box-shadow: 2px 3px 30px 0 rgba(121, 124, 125, 0.3);
            transition: all 0.3s ease;
          }
        }
        .locked-resource {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          margin: auto;
          width: 100%;
          height: 100%;
          background-color: rgba(146, 135, 135, 0.85);
          z-index: 1;
          border-radius: 5px;
          img {
            display: block;
            height: 100%;
            margin: auto;
          }
        }
      }
    }
  }
}
