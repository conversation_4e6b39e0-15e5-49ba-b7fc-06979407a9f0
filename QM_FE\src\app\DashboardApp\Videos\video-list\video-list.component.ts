import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';

import { StreamingService } from '../../../Services/streaming.service';
import { VideoList } from 'src/app/Models/Streaming/VideoList';
import { VideoGroup } from 'src/app/Models/Streaming/VideoGroup';
import { VideoSubGroup } from 'src/app/Models/Streaming/VideoSubGroup';

declare let gtag: Function;

@Component({
  selector: 'app-video-list',
  templateUrl: './video-list.component.html',
  styleUrls: ['./video-list.component.scss']
})
export class VideoListComponent implements OnInit {

  public videoList: VideoList[];
  public videoGroup: VideoGroup[];
  public videoGroups = [];
  public videoSubGroup: VideoSubGroup[];
  public isCollapsed = [];
  public isGroupCollapsed = {};

  public selectedGroup = [];
  public selectedGroupSubGroups = [];

  public showIndicator = false;

  constructor(public router: Router,
    public activatedRoute: ActivatedRoute,
    public streamingService: StreamingService) {

      router.events.subscribe((y: NavigationEnd) => {
        if (y instanceof NavigationEnd) {
          gtag('config', 'UA-163800914-1', { 'page_path': y.url });
        }
      });
    }

  ngOnInit() {

    this.showIndicator = true;

    const that = this;
    setTimeout(() => {
      this.streamingService.getVideoList().subscribe(response => {
        that.showIndicator = false;
        // Added by Gururaj
        const resp = response.sort(function (a, b) {
          const sCurrentSection: number = Number(a.public);
          const sPrevSection: number = Number(b.public);
          return sPrevSection - sCurrentSection;
        });
        for (const video of resp) {
          video.thumbnail = 'https://quantmasters.in' + video.thumbnail;

          if (that.videoGroups.indexOf(video.video_group) === -1) {
            that.isCollapsed.push(true);

            that.videoGroups.push(video.video_group);
          }
        }

        this.streamingService.getVideoGroups().subscribe(response2 => {
          this.videoGroup = response2;

          for (const group of this.videoGroup) {
            that.selectedGroup.push(false);
          }

          this.streamingService.getVideoSubGroups().subscribe(response3 => {
            this.videoSubGroup = response3;

            // TODO: Change to 0
            that.selectGroup(this.videoGroup[0].group_id, 0);
            that.showIndicator = false;
          }, error => {
            that.showIndicator = false;
          });
        }, error => {
          that.showIndicator = false;
        });

        setTimeout(() => {
          that.videoList = response;
        }, 400);
      }, error => {
        that.showIndicator = false;
      });
    });
  }

  selectGroup(groupId: string, groupIdx: number) {

    this.selectedGroup.fill(false);
    this.selectedGroup[groupIdx] = true;

    this.selectedGroupSubGroups = this.videoSubGroup.filter(x => x.group_id === groupId);

    for (const subGroup of this.selectedGroupSubGroups) {
      this.isCollapsed.push(true);
    }
  }

  filterBySubGroup(subGroupId: string) {
    return this.videoList.filter(video => video.video_sub_group === subGroupId);
  }

  getNoOfTopics(groupId: string): number {
    return this.videoList.filter(x => x.video_sub_group === groupId).length;
  }

  startStream(videoId: string, steramType: number, rating: number) {
    // navigate
    this.router.navigate(['stream', videoId, steramType, rating], { relativeTo: this.activatedRoute });
  }

  getVideoNotes(videoId: string) {
    this.streamingService.getVideoNotes(videoId).subscribe(response => {
      const pdfUrl = response;

      window.open(pdfUrl, '_blank');
    }, error => {
      console.warn(error);
    });
  }

  takeToPlans() {
    // navigate
    this.router.navigate(['/plans']);
  }
}
