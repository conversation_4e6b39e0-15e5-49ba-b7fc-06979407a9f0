import { Suspense } from 'react'
import { Skeleton } from '@/components/ui/skeleton'
import { ServerChapterPracticeService } from '@/lib/server-services/chapter-practice-service.server'
import ChapterPracticeList from '@/components/chapter-practice/chapter-practice-list'

export const metadata = {
  title: 'Chapter Practice | Quant Masters',
  description: 'Practice aptitude questions chapter-wise'
}

export default async function ChapterPracticePage() {
  // Get saved group from localStorage (server-side)
  const savedGroupId = ''

  // Fetch initial super groups data
  const superGroups = await ServerChapterPracticeService.getSuperGroups()

  // Get the default selected group based on the first item or saved preference
  const defaultSelectedGroupId =
    savedGroupId || superGroups[0]?.super_group_id || ''

  // Fetch groups for the default selected super group
  const groupsData = defaultSelectedGroupId
    ? await ServerChapterPracticeService.getGroups(defaultSelectedGroupId)
    : []

  return (
    <div className="w-full max-w-7xl mx-auto px-4 py-6">
      <div className="mb-6">
        <div className="title">
          <h1 className="text-2xl font-bold">Chapter Practice</h1>
          <p className="text-gray-600">
            Chapter wise papers covering all core aptitude concepts
          </p>
        </div>
      </div>

      <Suspense fallback={<LoadingSkeleton />}>
        <ChapterPracticeList
          initialSuperGroups={superGroups}
          initialGroups={groupsData}
          initialSelectedGroupId={defaultSelectedGroupId}
        />
      </Suspense>

      <div className="mt-8 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-4">
      <div className="flex space-x-4 overflow-x-auto pb-2">
        {[1, 2, 3, 4].map((i) => (
          <Skeleton key={i} className="h-10 w-32 rounded-md" />
        ))}
      </div>
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-12 w-full rounded-md" />
            <div className="pl-4 space-y-2">
              {[1, 2].map((j) => (
                <Skeleton key={j} className="h-24 w-full rounded-md" />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
