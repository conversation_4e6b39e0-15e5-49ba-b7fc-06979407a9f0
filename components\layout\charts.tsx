'use client'

import React from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Toolt<PERSON>,
  <PERSON>,
  Filler,
  Bar<PERSON>ontroller,
  DoughnutController,
  B<PERSON>bleController,
  RadarController
} from 'chart.js'
import { Line, Bar, Pie, Doughnut, Radar, Bubble } from 'react-chartjs-2'

// Define type for legend position to solve TypeScript errors
// type ChartLegendPosition =
//   | 'top'
//   | 'left'
//   | 'right'
//   | 'bottom'
//   | 'center'
//   | 'chartArea'

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  RadialLinearScale,
  Title,
  Tooltip,
  Legend,
  Filler,
  BarController,
  DoughnutController,
  BubbleController,
  RadarController
)

// Vibrant color palettes
const colorPalettes = {
  primary: [
    'rgba(255, 99, 132, 0.7)',
    'rgba(54, 162, 235, 0.7)',
    'rgba(255, 206, 86, 0.7)',
    'rgba(75, 192, 192, 0.7)',
    'rgba(153, 102, 255, 0.7)',
    'rgba(255, 159, 64, 0.7)'
  ],
  pastel: [
    'rgba(255, 179, 186, 0.7)',
    'rgba(255, 223, 186, 0.7)',
    'rgba(255, 255, 186, 0.7)',
    'rgba(186, 255, 201, 0.7)',
    'rgba(186, 225, 255, 0.7)',
    'rgba(186, 186, 255, 0.7)'
  ],
  neon: [
    'rgba(255, 41, 117, 0.7)',
    'rgba(13, 217, 192, 0.7)',
    'rgba(254, 241, 96, 0.7)',
    'rgba(52, 172, 224, 0.7)',
    'rgba(155, 89, 182, 0.7)',
    'rgba(251, 197, 49, 0.7)'
  ]
}

// Line Chart Component with gradient
export const LineChart = () => {
  const data = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Trends',
        data: [10, 35, 25, 45, 30, 60],
        borderColor: colorPalettes.neon[1],
        backgroundColor: (context: any) => {
          const ctx = context.chart.ctx
          const gradient = ctx.createLinearGradient(0, 0, 0, 300)
          gradient.addColorStop(0, 'rgba(13, 217, 192, 0.8)')
          gradient.addColorStop(1, 'rgba(13, 217, 192, 0.1)')
          return gradient
        },
        borderWidth: 3,
        pointRadius: 6,
        pointBackgroundColor: 'white',
        pointBorderColor: colorPalettes.neon[1],
        pointBorderWidth: 2,
        tension: 0.4,
        fill: true
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        display: false
      },
      y: {
        display: false
      }
    },
    elements: {
      line: {
        cubicInterpolationMode: 'monotone'
      }
    },
    animation: {
      duration: 2000,
      easing: 'easeOutQuart',
      delay: (context: any) => context.dataIndex * 100
    }
  }

  return (
    <div className="w-full h-full">
      <Line data={data} options={options as any} />
    </div>
  )
}

// Curved Bar Chart Component
export const BarChart = () => {
  const data = {
    labels: ['Q1', 'Q2', 'Q3', 'Q4', 'Q5', 'Q6'],
    datasets: [
      {
        label: 'Performance',
        data: [35, 55, 40, 60, 45, 70],
        backgroundColor: colorPalettes.primary.map((color) => color),
        borderRadius: 12,
        borderWidth: 0,
        hoverBackgroundColor: colorPalettes.primary.map((color) =>
          color.replace('0.7', '0.9')
        )
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        display: false
      },
      y: {
        display: false,
        max: 80
      }
    },
    animation: {
      duration: 1800,
      easing: 'easeOutElastic',
      delay: (context: any) => context.dataIndex * 150
    }
  }

  return (
    <div className="w-full h-full">
      <Bar data={data} options={options as any} />
    </div>
  )
}

// Pie Chart Component
export const PieChart = () => {
  const data = {
    labels: ['A', 'B', 'C', 'D', 'E'],
    datasets: [
      {
        data: [25, 20, 15, 30, 10],
        backgroundColor: colorPalettes.pastel,
        borderColor: 'rgba(255, 255, 255, 0.5)',
        borderWidth: 3,
        hoverOffset: 10
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    cutout: '0%',
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2000,
      easing: 'easeOutCirc'
    }
  }

  return (
    <div className="w-full h-full">
      <Pie data={data} options={options as any} />
    </div>
  )
}

// Doughnut Chart Component
export const DoughnutChart = () => {
  const data = {
    labels: ['Red', 'Blue', 'Yellow', 'Green', 'sky'],
    datasets: [
      {
        data: [15, 25, 20, 30, 10],
        backgroundColor: colorPalettes.neon,
        borderColor: 'rgba(255, 255, 255, 0.5)',
        borderWidth: 2,
        hoverOffset: 5
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    cutout: '65%',
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 2200,
      easing: 'easeOutBack'
    }
  }

  return (
    <div className="w-full h-full">
      <Doughnut data={data} options={options as any} />
    </div>
  )
}

// Radar Chart Component
export const RadarChart = () => {
  const data = {
    labels: ['Speed', 'Power', 'Range', 'Agility', 'Strength', 'Stamina'],
    datasets: [
      {
        label: 'Attributes',
        data: [85, 70, 90, 65, 75, 80],
        backgroundColor: 'rgba(155, 89, 182, 0.3)',
        borderColor: 'rgba(155, 89, 182, 0.8)',
        borderWidth: 2,
        pointBackgroundColor: 'rgba(155, 89, 182, 1)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(155, 89, 182, 1)'
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      r: {
        angleLines: {
          display: true,
          color: 'rgba(255, 255, 255, 0.1)'
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)'
        },
        pointLabels: {
          display: false
        },
        ticks: {
          display: false
        }
      }
    },
    animation: {
      duration: 2500,
      easing: 'easeOutQuint'
    }
  }

  return (
    <div className="w-full h-full">
      <Radar data={data} options={options as any} />
    </div>
  )
}

// Bubble Chart Component
export const BubbleChart = () => {
  const data = {
    datasets: [
      {
        label: 'Group A',
        data: [
          { x: 20, y: 30, r: 15 },
          { x: 40, y: 10, r: 10 },
          { x: 30, y: 25, r: 25 },
          { x: 10, y: 40, r: 20 },
          { x: 50, y: 30, r: 15 }
        ],
        backgroundColor: colorPalettes.primary[3]
      },
      {
        label: 'Group B',
        data: [
          { x: 25, y: 15, r: 10 },
          { x: 15, y: 30, r: 15 },
          { x: 35, y: 35, r: 8 },
          { x: 45, y: 20, r: 20 },
          { x: 20, y: 40, r: 12 }
        ],
        backgroundColor: colorPalettes.primary[0]
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        display: false
      },
      y: {
        display: false
      }
    },
    animation: {
      duration: 2000,
      easing: 'easeOutCubic'
    }
  }

  return (
    <div className="w-full h-full">
      <Bubble data={data} options={options as any} />
    </div>
  )
}

// Area Chart Component (similar to line but optimized for filled area)
export const AreaChart = () => {
  const data = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [
      {
        label: 'Activity',
        data: [50, 65, 60, 80, 75, 90, 85],
        borderColor: colorPalettes.pastel[4],
        backgroundColor: (context: any) => {
          const ctx = context.chart.ctx
          const gradient = ctx.createLinearGradient(0, 0, 0, 300)
          gradient.addColorStop(0, 'rgba(186, 225, 255, 0.8)')
          gradient.addColorStop(1, 'rgba(186, 225, 255, 0.1)')
          return gradient
        },
        tension: 0.5,
        fill: true,
        pointRadius: 0
      }
    ]
  }

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    },
    scales: {
      x: {
        display: false
      },
      y: {
        display: false
      }
    }
  }

  return (
    <div className="w-full h-full">
      <Line data={data} options={options as any} />
    </div>
  )
}
