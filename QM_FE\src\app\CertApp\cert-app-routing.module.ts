import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { CertificationComponent } from './certification/certification.component';
import { ViewCertificateComponent } from './view-certificate/view-certificate.component';
import { CertificationFaqComponent } from './certification-faq/certification-faq.component';

const routes: Routes = [
  {
    path: '',
    children: [
      { path: 'view',
        component: CertificationComponent
      },
      {
        path: 'view/:certificateId',
        component: ViewCertificateComponent,
      },
      {
        path: 'faq',
        component: CertificationFaqComponent
      }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CertAppRoutingModule {}
