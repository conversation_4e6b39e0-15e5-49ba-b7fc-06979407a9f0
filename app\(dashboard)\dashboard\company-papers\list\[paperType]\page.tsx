import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { ServerCompanyService } from '@/lib/server-services/company-service.server'
import CompanyPapersDetail from '@/components/company-papers/company-papers-detail'
import { getCompanyHeading } from '@/types/company-paper-types'

interface CompanyPapersPageProps {
  params: Promise<{
    paperType: string
  }>
}

export async function generateMetadata({
  params
}: CompanyPapersPageProps): Promise<Metadata> {
  const { paperType } = await params
  const companyType = parseInt(paperType, 10)

  if (isNaN(companyType)) {
    return {
      title: 'Company Papers | Quant Masters'
    }
  }

  const companyName = getCompanyHeading(companyType)

  return {
    title: `${companyName} Papers | Quant Masters`,
    description: `Practice with ${companyName} test papers and assessments`
  }
}

export default async function CompanyPapersPage({
  params
}: CompanyPapersPageProps) {
  const { paperType } = await params
  const companyType = parseInt(paperType, 10)

  // Validate company type
  if (isNaN(companyType)) {
    notFound()
  }

  const pageHeading = getCompanyHeading(companyType)

  // Fetch papers on the server and filter by company type
  const [allPapers, userPermissions] = await Promise.all([
    ServerCompanyService.getAllPapers(),
    ServerCompanyService.checkUserPermissions()
  ])

  const filteredPapers = ServerCompanyService.filterPapersByCompanyType(
    allPapers,
    companyType
  )

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="page-header mb-8">
        <div className="title">
          <h1 className="text-3xl font-bold text-center text-gray-800">
            {pageHeading}
          </h1>
          <p className="text-center text-gray-600 mt-2">
            Practice papers for {pageHeading}
          </p>
        </div>
      </div>

      {/* Company papers detail component with server-side data */}
      <CompanyPapersDetail
        companyType={companyType}
        pageHeading={pageHeading}
        initialPapers={filteredPapers}
        hasAccess={userPermissions.hasAccess}
      />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
