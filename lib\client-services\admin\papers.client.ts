// lib/client-services/admin/papers.client.ts
import axios from 'axios'
import { Paper, CreatePaperRequest } from '@/types/admin-types'
import { LoginRefresh } from '../../cookies'

const API_BASE_URL = 'https://api.quantmasters.in'
const V2_BASE_URL = 'https://api.quantmasters.in/v2'

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = () => {
  const token = LoginRefresh.getAuthToken()
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

export class AdminPapersClientService {
  /**
   * Get papers by type and optional topic/subtopic
   */
  static async getPapersByType(
    type: number,
    topicId?: string,
    subtopicId?: string
  ): Promise<Paper[]> {
    try {
      let url = ''

      switch (type) {
        case 1: // Chapter-Wise Papers
          if (!subtopicId) return []
          // Use the progression API to match Angular implementation
          url = `${API_BASE_URL}/test/progression/group/${subtopicId}`
          break
        case 4: // Mock Papers
          url = `${API_BASE_URL}/test/question/ret/papers`
          break
        case 5: // Trial Papers
          url = `${API_BASE_URL}/test/open/papers`
          break
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/company/papers`
          break
        case 6: // Chapter-Wise Practice
          if (!topicId) return []
          url = `${API_BASE_URL}/test/progression/practice/group/${topicId}`
          break
        case 8: // Weekly Competitive
          url = `${V2_BASE_URL}/admin/test/open/competitive/papers`
          break
        case 9: // Section-Wise Papers
          url = `${V2_BASE_URL}/test/sectionWise/papers`
          break
        case 11: // Technical MCQs
          if (!subtopicId) return []
          // Use v3 API to match Angular implementation
          url = `${API_BASE_URL}/v3/admin/tmcq/papers/${subtopicId}`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          if (!topicId) return []
          // Use competitive group API to match Angular implementation
          url = `${API_BASE_URL}/test/competitive/group/${topicId}`
          break
        default:
          return []
      }

      const response = await axios.get(url, createAuthHeaders())
      return response.data || []
    } catch (error) {
      console.error(`Error fetching papers for type ${type}:`, error)
      return []
    }
  }

  /**
   * Create a new paper
   */
  static async createPaper(
    paperData: CreatePaperRequest,
    type: number
  ): Promise<Paper> {
    try {
      let url = ''

      switch (type) {
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          // Use competitive create API to match Angular implementation
          url = `${API_BASE_URL}/admin/paper/competitive/upload/test`
          break
        default:
          throw new Error(`Paper creation not supported for type ${type}`)
      }

      const response = await axios.post(url, paperData, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error(`Error creating paper for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Update paper
   */
  static async updatePaper(
    paperId: string,
    updates: Partial<Paper>,
    type: number
  ): Promise<Paper> {
    try {
      let url = ''
      const data = { ...updates, paper_id: paperId }

      switch (type) {
        case 1: // Chapter-Wise Papers
          // Use progression API to match Angular implementation
          url = `${API_BASE_URL}/admin/paper/progression/upload/test`
          break
        case 4: // Mock Papers
          url = `${V2_BASE_URL}/admin/test/model/paper/${paperId}`
          break
        case 5: // Trial Papers
          url = `${V2_BASE_URL}/admin/test/open/paper/${paperId}`
          break
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/company/paper/${paperId}`
          break
        case 8: // Weekly Competitive
          url = `${V2_BASE_URL}/admin/test/competitive/weekly/${paperId}`
          break
        case 9: // Section-Wise Papers
          url = `${V2_BASE_URL}/admin/test/sectionWise/paper/${paperId}`
          break
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          url = `${V2_BASE_URL}/admin/test/competitive/practice/paper`
          break
        default:
          throw new Error(`Paper update not supported for type ${type}`)
      }

      const response = await axios.put(url, data, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error(`Error updating paper for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Delete paper
   */
  static async deletePaper(paperId: string, type: number): Promise<void> {
    try {
      let url = ''

      switch (type) {
        case 5: // Trial Papers
        case 7: // Company Papers
          url = `${API_BASE_URL}/admin/paper/open/update/${paperId}`
          break
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper/${paperId}`
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          // Use competitive delete API to match Angular implementation
          url = `${API_BASE_URL}/admin/paper/competitive/update/test/${paperId}`
          break
        default:
          throw new Error(`Paper deletion not supported for type ${type}`)
      }

      await axios.delete(url, createAuthHeaders())
    } catch (error) {
      console.error(`Error deleting paper for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Toggle paper status
   */
  static async togglePaperStatus(
    paperId: string,
    currentStatus: string,
    type: number
  ): Promise<void> {
    const newStatus = currentStatus === '1' ? '0' : '1'

    try {
      let url = ''
      const data = { status: newStatus }

      switch (type) {
        case 5: // Trial Papers
          url = `${V2_BASE_URL}/admin/test/open/paper/${paperId}`
          break
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/company/paper/${paperId}`
          break
        case 8: // Weekly Competitive
          url = `${V2_BASE_URL}/admin/test/competitive/weekly/${paperId}`
          break
        case 11: // Technical MCQs
          url = `${V2_BASE_URL}/admin/test/tmcq/paper`
          Object.assign(data, { paper_id: paperId })
          break
        case 13: // Verbal Practice
        case 14: // Technical Practice
          // Use competitive update API to match Angular implementation
          url = `${API_BASE_URL}/admin/paper/competitive/update/test`
          break
        default:
          throw new Error(`Status toggle not supported for type ${type}`)
      }

      await axios.put(url, data, createAuthHeaders())
    } catch (error) {
      console.error(`Error toggling paper status for type ${type}:`, error)
      throw error
    }
  }

  /**
   * Refresh competitive cache
   */
  static async refreshCompetitiveCache(): Promise<void> {
    try {
      const url = `${V2_BASE_URL}/admin/test/competitive/weekly/cache/refresh`
      await axios.post(url, {}, createAuthHeaders())
    } catch (error) {
      console.error('Error refreshing competitive cache:', error)
      throw error
    }
  }

  /**
   * Create a new group
   */
  static async createGroup(groupName: string, paperType: number): Promise<any> {
    try {
      let url = ''
      const body = { group_name: groupName }

      switch (paperType) {
        case 11: // Technical MCQs
          url = `${API_BASE_URL}/v3/admin/tmcq/group`
          break
        case 13: // Verbal Practice
          url = `${V2_BASE_URL}/verbal/practice/groups`
          break
        case 14: // Technical Practice
          // Technical practice groups creation endpoint would be similar
          url = `${V2_BASE_URL}/technical/practice/groups`
          break
        default:
          throw new Error(`Group creation not supported for type ${paperType}`)
      }

      const response = await axios.post(url, body, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error(`Error creating group for type ${paperType}:`, error)
      throw error
    }
  }

  /**
   * Create a new subgroup (Technical MCQs only)
   */
  static async createSubGroup(
    groupId: string,
    subGroupName: string
  ): Promise<any> {
    try {
      const url = `${API_BASE_URL}/v3/admin/tmcq/subgroup`
      const body = { group_id: groupId, sub_group_name: subGroupName }

      const response = await axios.post(url, body, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error('Error creating subgroup:', error)
      throw error
    }
  }

  /**
   * Get paper details
   */
  static async getPaperDetails(paperId: string): Promise<Paper | null> {
    try {
      // This is a placeholder - actual implementation would depend on the API
      console.log(`Fetching details for paper ID: ${paperId}`)

      return null
    } catch (error) {
      console.error('Error fetching paper details:', error)
      return null
    }
  }

  /**
   * Get paper questions
   */
  static async getPaperQuestions(
    paperId: string,
    paperType?: number
  ): Promise<any[]> {
    try {
      let url = ''

      // Use different endpoints based on paper type
      if (paperType) {
        switch (paperType) {
          case 1: // Chapter-wise
          case 5: // Trial/Company
          case 8: // Weekly Competitive
            url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
            break
          case 11: // Technical MCQs
            url = `${API_BASE_URL}/v3/admin/tmcq/paper/${paperId}`
            break
          case 13: // Verbal Practice
          case 14: // Technical Practice
            url = `${V2_BASE_URL}/test/competitive/practice/paper/${paperId}`
            break
          default:
            url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
        }
      } else {
        url = `${API_BASE_URL}/test/sample/paper/new/${paperId}`
      }

      const response = await axios.get(url, createAuthHeaders())
      return response.data?.questions || []
    } catch (error) {
      console.error('Error fetching paper questions:', error)
      return []
    }
  }

  /**
   * Add a question to a paper
   */
  static async addQuestion(
    paperId: string,
    questionData: any,
    paperType: number
  ): Promise<any> {
    try {
      let url = ''

      switch (paperType) {
        case 11: // Technical MCQs
          url = `${API_BASE_URL}/v3/admin/tmcq/paper/${paperId}/question`
          break
        case 5: // Trial Papers
        case 7: // Company Papers
          url = `${API_BASE_URL}/admin/paper/open/update/${paperId}/question`
          break
        case 1: // Chapter-wise
          url = `${V2_BASE_URL}/admin/test/chapter/paper/${paperId}/question`
          break
        default:
          throw new Error(
            `Question creation not supported for type ${paperType}`
          )
      }

      const response = await axios.post(url, questionData, createAuthHeaders())
      return response.data
    } catch (error) {
      console.error(`Error adding question for type ${paperType}:`, error)
      throw error
    }
  }

  /**
   * Update a question
   */
  static async updateQuestion(
    paperId: string,
    questionNo: number,
    updates: any,
    paperType: number
  ): Promise<void> {
    try {
      let url = ''

      switch (paperType) {
        case 5: // Trial Papers
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/test/open/paper/${paperId}/question`
          break
        case 11: // Technical MCQs
          url = `${API_BASE_URL}/v3/admin/tmcq/paper/${paperId}/question`
          break
        default:
          throw new Error(`Question update not supported for type ${paperType}`)
      }

      const data = { ...updates, question_no: questionNo }
      await axios.put(url, data, createAuthHeaders())
    } catch (error) {
      console.error(`Error updating question for type ${paperType}:`, error)
      throw error
    }
  }

  /**
   * Delete a question
   */
  static async deleteQuestion(
    paperId: string,
    questionNo: number,
    paperType: number
  ): Promise<void> {
    try {
      let url = ''

      switch (paperType) {
        case 11: // Technical MCQs
          url = `${API_BASE_URL}/v3/admin/tmcq/paper/${paperId}/${questionNo}`
          break
        case 5: // Trial Papers
        case 7: // Company Papers
          url = `${V2_BASE_URL}/admin/test/open/paper/${paperId}/question/${questionNo}`
          break
        default:
          throw new Error(
            `Question deletion not supported for type ${paperType}`
          )
      }

      await axios.delete(url, createAuthHeaders())
    } catch (error) {
      console.error(`Error deleting question for type ${paperType}:`, error)
      throw error
    }
  }
}
