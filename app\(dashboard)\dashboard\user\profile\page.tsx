// app/(dashboard)/dashboard/user/profile/page.tsx
import { Metadata } from 'next'
import { ServerUserProfileService } from '@/lib/server-services/user-profile-service.server'
import UserProfileClient from '@/components/user/user-profile'

export const metadata: Metadata = {
  title: 'User Profile | Quant Masters',
  description: 'Manage your profile details and account settings'
}

export default async function UserProfilePage() {
  // Fetch user profile data on the server
  const { user, avatarPath } = await ServerUserProfileService.getUserProfile()

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Pass the fetched data to the client component */}
      <UserProfileClient initialUser={user} initialAvatarPath={avatarPath} />

      <div className="copy-content mt-16 text-center text-sm text-gray-500">
        <p>
          &copy; {new Date().getFullYear()} Quant Masters. All Rights Reserved.
        </p>
      </div>
    </div>
  )
}
