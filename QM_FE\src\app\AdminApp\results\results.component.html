<div class="results-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>
  <div class="sect-1">
    <div class="info-box">
      <div class="row-1">
        <form #basicSearchForm="ngForm" (submit)="filterResultsList()">
          <h6>Basic Search</h6>
          <div class="form-elem">
            <input type="text" name="basicInput" class="basic-search" [(ngModel)]="basicSearchVal" />
            <button type="submit" class="custom-btn">Search</button>
          </div>
        </form>
        <button type="button" class="custom-btn btn-2" (click)="warnGetAll()"
          title="Warning: This may take a while!">Get all Results</button>
      </div>
      <form #paperSearchForm="ngForm" (submit)="filterResultsListUsingPaper()">
        <h6>Advanced Search</h6>
        <button *ngIf="isMobile" type="button" class="custom-btn" (click)="advOpen = !advOpen">{{ advOpen ? 'Hide' :
          'Show' }}</button>
        <div class="form-fields" *ngIf="advOpen">
          <div class="form-elem">
            <label for="name-inp">Name:</label>
            <input type="text" name="name-inp" [(ngModel)]="userName" />
          </div>
          <div class="form-elem">
            <label for="email-inp">Email:</label>
            <input type="text" name="email-inp" [(ngModel)]="email" />
          </div>
          <!-- <div class="form-elem">
            <label for="paperType">Paper Type:</label>
            <input type="text" name="paperType" [(ngModel)]="paperType" />
          </div> -->
          <div class="form-elem">
            <label for="paper_Type">Paper Type</label>
            <select name="paper_Type" [(ngModel)]="selectedType" (change)="paperTyepSelected()">
              <option value="-1" selected>Select Paper Type</option>
              <option value="2">Company Papers</option>
              <option value="3">Model Papers</option>
              <option value="5">Trial Papers</option>
              <option value="6">Weekly Competitive</option>
            </select>
          </div>
          <!-- <div class="form-elem">
            <label for="paperName">Paper Name:</label>
            <input type="text" name="paperName" [(ngModel)]="paperName" />
          </div> -->
          <div class="form-elem">
            <label for="paperName">Select Paper</label>
            <select name="paperName" [(ngModel)]="paperName" #paper 
              *ngIf="!chapterPapers && !modelPapers && !weeklyCompititive">
              <option selected value="-1">Select Paper</option>
            </select>
            <select name="paperName" [(ngModel)]="paperName" #paper  *ngIf="chapterPapers">
              <option selected value="-1">Select Paper</option>
              <option *ngFor="let paper of chapterPapers" value="{{paper.paper_name}}">{{ paper.paper_name }}</option>
            </select>
            <select name="paperName" [(ngModel)]="paperName" #paper  *ngIf="modelPapers">
              <option selected value="-1">Select Paper</option>
              <option *ngFor="let paper of modelPapers" value="{{paper.paper_name}}">{{ paper.paper_name }}</option>
            </select>
            <select name="paperName" [(ngModel)]="paperId" #paper  *ngIf="weeklyCompititive">
              <option selected value="-1">Select Paper</option>
              <option *ngFor="let paper of weeklyCompititive" value="{{paper.paper_id}}">{{ paper.paper_name }}</option>
            </select>
          </div>
          <div class="form-elem">
            <label for="answerDate">Answer Date:</label>
            <input type="text" name="answerDate" [(ngModel)]="answerDate" />
          </div>
        </div>
        <div class="form-fields" *ngIf="advOpen">
          <button type="submit" class="custom-btn">Filter</button>
          <button type="button" class="custom-btn clr-btn" (click)="clearFilters($event)">Clear All</button>
        </div>
      </form>
    </div>
    <div class="info-boxes">
      <!-- <div class="info-box ops-box">
        <h6>View Settings</h6>
        <div class="icon-wrap" (click)="openViewSettings()">
          <img src="../../../assets/icons/selected.svg" alt="select data icon" />
        </div>
      </div> -->
      <div class="info-box ops-box">
        <h6>Sort Data</h6>
        <div class="icon-wrap" (click)="openSorter()">
          <svg id="Capa_1" enable-background="new 0 0 512 512" height="512" viewBox="0 0 512 512" width="512"
            xmlns="http://www.w3.org/2000/svg">
            <g>
              <path
                d="m487 65.55v380.9c0 36.14-29.41 65.55-65.55 65.55h-330.9c-36.14 0-65.55-29.41-65.55-65.55v-380.9c0-36.14 29.41-65.55 65.55-65.55h330.9c36.14 0 65.55 29.41 65.55 65.55z"
                fill="#2E4053" />
              <path
                d="m487 65.55v380.9c0 36.14-29.41 65.55-65.55 65.55h-165.45v-512h165.45c36.14 0 65.55 29.41 65.55 65.55z"
                fill="#D6DBDF" />
              <path
                d="m343.997 447c-8.623 0-6.845-.635-58.604-52.394-14.024-14.021 7.17-35.256 21.213-21.213l22.394 22.394v-279.574l-22.394 22.394c-14.023 14.023-35.257-7.171-21.213-21.213 53.091-53.091 49.931-51.452 56.787-52.283 9.8-1.2 8.825.682 60.426 52.283 14.02 14.02-7.168 35.257-21.213 21.213l-22.393-22.394v279.574l22.394-22.394c9.478-9.478 25.606-2.634 25.606 10.607 0 8.297.09 6.123-52.394 58.606-1.822 2.187-6.185 4.394-10.609 4.394z"
                fill="#2E4053" />
              <g fill="#ffffff">
                <path
                  d="m185.237 77.591c-5.681-15.22-27.601-15.82-33.919-.38-.118.292 2.305-6.066-53.877 141.45-2.949 7.742.937 16.408 8.679 19.356 7.742 2.951 16.408-.938 19.356-8.679l9.705-25.482h65.642l9.601 25.439c2.267 6.007 7.974 9.708 14.036 9.708 10.413 0 17.751-10.446 14.031-20.3zm-38.63 96.265 21.546-56.57 21.349 56.57z" />
                <path
                  d="m216.819 417h-68.387l78.718-120.812c6.493-9.966-.68-23.188-12.568-23.188h-90.874c-8.284 0-15 6.716-15 15s6.716 15 15 15h63.197l-78.717 120.812c-6.494 9.966.678 23.188 12.567 23.188h96.064c8.284 0 15-6.716 15-15s-6.715-15-15-15z" />
              </g>
            </g>
          </svg>
        </div>
      </div>
      <div class="info-box ops-box">
        <h6>Export Data</h6>
        <div class="icon-wrap" (click)="openExportOptions()">
          <img src="../../../assets/icons/excelexport.svg" alt="export data icon" />
        </div>
      </div>
    </div>
  </div>
  <div class="sect-2">
    <div class="users-headers">
      <h6>Select</h6>
      <h6 *ngIf="showCols[0]">Name</h6>
      <h6 *ngIf="showCols[1]">Email</h6>
      <h6 *ngIf="showCols[2]">Phone Number</h6>
      <h6 *ngIf="showCols[3]">College Name</h6>
      <h6 *ngIf="showCols[4]">Course</h6>
      <h6 *ngIf="showCols[5]">Branch</h6>
      <h6 *ngIf="showCols[6]">YoP</h6>
      <h6 *ngIf="showCols[7]">USN</h6>
      <h6 *ngIf="showCols[8]">Paper Name</h6>
      <h6 *ngIf="showCols[9]">Paper Type</h6>
      <h6 *ngIf="showCols[10]">Marks</h6>
      <h6 *ngIf="showCols[11]">Answered On</h6>
    </div>
    <div class="users-data" *ngFor="let mark of displayMarks">
      <input type="checkbox" (click)="mark.selected = !mark.selected" />
      <p *ngIf="showCols[0]">{{ mark.name }}</p>
      <p *ngIf="showCols[1]">{{ mark.email }}</p>
      <p *ngIf="showCols[2]">{{ mark.phone_no }}</p>
      <p *ngIf="showCols[3]">{{ mark.inst_name }}</p>
      <p *ngIf="showCols[4]">{{ mark.qual }}</p>
      <p *ngIf="showCols[5]">{{ mark.branch }}</p>
      <p *ngIf="showCols[6]">{{ mark.yop }}</p>
      <p *ngIf="showCols[7]">{{ mark.usn }}</p>
      <p *ngIf="showCols[8]">{{ mark.paper_name }}</p>
      <p *ngIf="showCols[9]">{{ mark.paper_type }}</p>
      <p *ngIf="showCols[10]">{{ mark.marks + "/" + mark.total_marks }}</p>
      <p *ngIf="showCols[11]">{{ mark.display_date }}</p>
    </div>
    <pagination *ngIf="numFilteredResults === 0" [boundaryLinks]="true" [totalItems]="numResults" [rotate]="true"
      [maxSize]="pageMax" [itemsPerPage]="pagePer" (pageChanged)="pageChanged($event)"></pagination>
    <pagination *ngIf="numFilteredResults > 0" [boundaryLinks]="true" [totalItems]="numFilteredResults" [rotate]="true"
      [maxSize]="pageMax" [itemsPerPage]="pagePer" (pageChanged)="pageFilterChanged($event)"></pagination>
  </div>
  <ng-template #sortTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-secondary">Sorters</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #sortForm="ngForm">
        <div class="form-row">
          <h6>Sort Field</h6>
          <div class="form-elem" (click)="selectSorter(1)">
            <label for="name">Name</label>
            <input type="radio" id="name" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(2)">
            <label for="email">Email</label>
            <input type="radio" id="email" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(3)">
            <label for="date">Answered On</label>
            <input type="radio" id="date" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectSorter(4)">
            <label for="marks">Marks</label>
            <input type="radio" id="marks" name="sort-field" />
          </div>
        </div>
        <div class="form-row">
          <h6>Sort Order</h6>
          <div class="form-elem" (click)="selectAsc()">
            <label for="asc-btn">Ascending</label>
            <input type="radio" id="asc-btn" name="order" />
          </div>
          <div class="form-elem" (click)="selectDesc()">
            <label for="desc-btn">Descending</label>
            <input type="radio" id="desc-btn" name="order" />
          </div>
        </div>
        <div class="form-row">
          <button (click)="sortList()">Go</button>
        </div>
      </form>
    </div>
  </ng-template>
  <ng-template #viewSettingsTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-secondary">View Settings</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #sortForm="ngForm">
        <div class="form-row">
          <h6>Sort Field</h6>
          <!-- <div class="form-elem" (click)="showCols[0] = !showCols[0]">
            <label for="name2">Name</label>
            <input type="checkbox" id="name2" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[1] = !showCols[1]">
            <label for="email2">Email</label>
            <input type="checkbox" id="email2" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[2] = !showCols[2]">
            <label for="date2">Phone On</label>
            <input type="checkbox" id="date2" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[3] = !showCols[3]">
            <label for="marks">College Name</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[4] = !showCols[4]">
            <label for="marks">Year of Passing</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[5] = !showCols[5]">
            <label for="marks">USN</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[6] = !showCols[6]">
            <label for="marks">Paper Name</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[7] = !showCols[7]">
            <label for="marks">Paper Type</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[8] = !showCols[8]">
            <label for="marks">Marks</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div>
          <div class="form-elem" (click)="showCols[9] = !showCols[9]">
            <label for="marks">Asnwered On</label>
            <input type="checkbox" id="marks" />
            <span class="checkmark"></span>
          </div> -->
        </div>
      </form>
    </div>
  </ng-template>
  <ng-template #exportTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-secondary">Export As Excel</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #sortForm="ngForm">
        <div class="form-row">
          <h6>Export Options</h6>
          <div class="form-elem" (click)="selectExport(1)">
            <label for="all">All Rows</label>
            <input type="radio" id="all" name="sort-field" />
          </div>
          <div class="form-elem" (click)="selectExport(2)">
            <label for="sel">Selected Rows</label>
            <input type="radio" id="sel" name="sort-field" />
          </div>
        </div>
        <div class="form-row">
          <button (click)="exportExcel()">Export</button>
        </div>
      </form>
    </div>
  </ng-template>
  <ng-template #warnAllTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-warning">Fair Warning!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>This will take long, you're better off just filtering for the marks you require.</p>
      <p>If you still insist, hit get all and sit back and relax, maybe get a cup of coffee.</p>
      <div class="form-row">
        <button class="cutom-btn" type="button" (click)="getAllResults()">Get All</button>
      </div>
    </div>
  </ng-template>
  <ng-template #errorTemplate>
    <div class="modal-header">
      <h4 class="modal-title text-danger">Oopsie!</h4>
      <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <p>{{ displayError }}</p>
    </div>
  </ng-template>
</div>