import type { NextConfig } from 'next'

const nextConfig: NextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'edoflip-public.s3.ap-south-1.amazonaws.com',
        port: '',
        pathname: '/quantmasters/testimonials/**',
        search: ''
      },
      {
        protocol: 'https',
        hostname: 'quantmasters.in',
        port: '',
        pathname: '/**',
        search: ''
      },
      {
        protocol: 'https',
        hostname: 'img.youtube.com',
        port: '',
        pathname: '/vi/**',
        search: ''
      }
    ]
  },
  webpack: (config, { isServer }) => {
    // Exclude QM_FE directory from compilation
    config.module.rules.push({
      test: /\.(ts|tsx|js|jsx)$/,
      exclude: /QM_FE/
    })

    return config
  }
}

export default nextConfig
