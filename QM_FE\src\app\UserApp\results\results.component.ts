import { Component, OnInit, TemplateRef, ViewChild, ViewEncapsulation } from '@angular/core';

import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

import { TestsService } from '../../Services/tests.service';

import { StudentMarks } from '../../Models/StudentMarks';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';
import { SectionWisePapersService } from '../../Services/Dashboard/section-wise-papers.service';
import { TmcqService } from '../../Services/Dashboard/TMCQ/tmcq.service';
import { CompetitiveService } from 'src/app/Services/Dashboard/competitive.service';

@Component({
  selector: 'app-results',
  templateUrl: './results.component.html',
  styleUrls: ['./results.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ResultsComponent implements OnInit {

  @ViewChild('sectionMarksTemplate') public sectionMarksTemplate: TemplateRef<any>;

  public modalRef: BsModalRef;
  public modalConfig = {
    animated: true,
    backdrop: true,
    class: 'section-modal'
  };

  private userEmail: string;

  public allMarks: StudentMarks[];
  public displayMarks: StudentMarks[];

  public sectionMarks: { section_name: string, marks: string, total: string, percentage: string }[];

  public numDispMarks: number;

  public showIndicator = false;

  public panginatorConfig = {
    max: 7,
    boundaryLinks: true
  };

  public paperName: string;
  public answerDate: string;

  constructor(private testsService: TestsService,
              private tmcqService: TmcqService,
              private competitiveService: CompetitiveService,
              private sectionWisePaperService: SectionWisePapersService,
              private modalService: BsModalService) { }

  ngOnInit() {
    this.userEmail = sessionStorage.getItem('QMail');
    this.numDispMarks = 0;
    this.setPaginatorConfig();
  }

  getAllMarks() {

    this.showIndicator = true;
    this.testsService.getIndividualResults(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getTrialPaperMarks() {

    this.showIndicator = true;
    this.testsService.getAllOpenResults(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getWeeklyCompetitivePaperMarks() {

    this.showIndicator = true;
    this.testsService.getAllWeeklyCompetitiveResults(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getSectionWisePaperMarks() {

    this.showIndicator = true;
    this.sectionWisePaperService.getSectionWiseMarks(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getAfcatPaperMarks() {

    this.showIndicator = true;
    this.sectionWisePaperService.getAfcatMarks(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getCompanyPaperMarks() {

    this.showIndicator = true;
    this.testsService.getAllOpenCompanyResults(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getTechnicalMCQMarks() {

    this.showIndicator = true;
    this.tmcqService.getMarksForAStudent(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getWPPracticeMarks() {

    this.showIndicator = true;
    this.competitiveService.getMarksForAStudent(this.userEmail).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      aResults.forEach(result => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.allMarks = aResults.sort((a, b) => {
        const aDate = new Date(a.created_at),
              bDate = new Date(b.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });
      // this.allMarks = aResults;
      this.numDispMarks = aResults.length;
      this.displayMarks = this.allMarks.slice(0, 15);
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  showSectionMarks(answerId: string, paperName: string, answerDate: string) {

    this.showIndicator = true;

    this.paperName = paperName;
    this.answerDate = answerDate;
    this.testsService.getOpenPaperSectionWiseMarks(this.userEmail, answerId).subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      this.sectionMarks = aResults;
      for (const marks of this.sectionMarks) {
        marks.section_name = marks.section_name.replace(/[#*]+/g, '');
        marks.marks        = marks.marks + ' / ' + marks.total;
        marks.percentage   = ((parseInt(marks.marks, 10) / parseInt(marks.total, 10)) * 100).toFixed(2);
      }
      this.openModal(this.sectionMarksTemplate);

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  setPaginatorConfig() {
    if (window.innerWidth <= 440) {
      this.panginatorConfig.max = 5;
      this.panginatorConfig.boundaryLinks = false;
    } else {
      this.panginatorConfig.max = 7;
      this.panginatorConfig.boundaryLinks = true;
    }
  }

  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayMarks = this.allMarks.slice(startItem, endItem);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.modalConfig);
  }
}
