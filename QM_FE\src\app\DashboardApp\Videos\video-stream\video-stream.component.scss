.video-wrap {
  width: 100%;
  min-height: 100%;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0px 6px 20px rgba(0, 0, 0, 0.16);
  margin-bottom: 1em;
  padding: 1px;

  .player {
    margin: 2em;

    .play-toggle {
      position: absolute;
      height: 100%;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 80;

      svg {
        height: 80px;
        width: 80px;
        cursor: pointer;

        polygon {
          fill: #adadad;
          transition: all 0.6s ease;
        }
      }

      &:hover svg polygon {
        fill: #fff;
        transition: all 0.3s ease;
      }
    }

    .left-skip,
    .right-skip {
      position: absolute;
      top: calc(50% - 40px);
      height: 80px;
      width: 80px;
      z-index: 100;
      cursor: pointer;

      svg {
        height: 100%;
        width: 100%;

        path,
        text {
          fill: #adadad;
          stroke: #adadad;
          transition: all 0.6s ease;
        }
      }

      &:hover svg path,
      &:hover svg text {
        fill: #fff;
        stroke: #fff;
        transition: all 0.3s ease;
      }
    }

    .left-skip {
      left: 30%;
    }

    .right-skip {
      left: 62%;
    }
  }

  .player-legend {
    margin-left: 2em;
    .btn-warning {
      border-color: #fcfcfc;
    }

    .ratings,
    .ratings-mine {
      display: flex;
      flex-direction: row;
      padding-bottom: 0;
      p {
        font-size: 1.5em;
        padding-right: 10px;

        &:last-of-type {
          padding-left: 10px;
        }
      }
    }

    .ratings-mine {
      p:first-of-type {
        padding-right: 51px;
      }
    }
    h5 {
      padding-top: 1em;
    }
  }
}

.modal-body {
  .btn-warning {
    border-color: #fcfcfc;
  }
  .ratings {
    display: flex;
    justify-content: space-between;
    .ratingSubmitButton {
      color: #fff;
      background-color: #e88224;
      border-radius: 1000px;
      border: none;
      width: 7rem;
      font-family: "Montserrat", sans-serif;
    }
  }
}

.copy-content {
  text-align: right;

  p {
    color: #707070;
    margin: 0;
  }
}

.user-comment-section {
  width: 90%;
  flex-direction: column;
  display: flex;

  h3 {
    margin: 1.5em 0 1em 0;
  }

  .user-new-comment {
    display: flex;
  }

  .image-div {
    // padding-left: 1%;
    width: 7%;
    .user-image {
      border-radius: 50%;
      width: 3rem !important;
      height: 3rem !important;
    }
  }

  .comment-div {
    width: 100%;
    display: flex;
    flex-direction: column;

    .user-comment {
      width: 100% !important;
      // border: none;
      border-bottom-style: ridge;
      outline: none;
    }
    .buttons-div {
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: flex-end;
      padding-top: 1.5%;
      padding-right: 1%;

      button {
        padding: 5px 15px;
        margin-right: 1.5%;
        background-color: #e88224;
        border: none;
        color: #fff;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
        border-radius: 1000px;
        outline: none;
      }
    }
  }
}

.video-comments {
  width: 85%;
  margin-left: 1%;

  flex-direction: row;
  display: flex;
  .image-div {
    padding: 1%;
    width: 6%;
    .user-image {
      border-radius: 50%;
      width: 2rem;
      height: 2rem;
    }
  }
  .comment-text-div {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-top: 1%;
    .comment-heder {
      margin-bottom: 0%;
      width: 100%;
      font: 11px "Montserrat", "serif";
      font-weight: bold;
      color: #0b6fb1;
    }
    .comments {
      word-break: break-all;
      word-break: break-word;
      font: 14px "Montserrat", "serif";
      padding: 4px 0px;
    }
    .comment-reply-section {
      display: flex;
      flex-direction: row;
      padding-bottom: 2%;
      button {
        background-color: #cdcdcde6;
        border: none;
        border-radius: 3px;
        outline: none;
        margin-right: 2%;
        margin-top: 0.5%;
        padding: 5px;
        font: 10px "Montserrat", "serif";
      }
    }
  }

  .reply_section_ExtraCSS {
    display: flex;
    flex-direction: coloumn;
    background-color: #d2d2d2a6;
    border-radius: 10px;
    border-top-left-radius: 0px;
    width: 90%;
    margin: 1% 0% 1% 0%;
  }
}

@media (max-width: 440px) {
  .video-wrap {
    min-height: 0;

    .player-legend {
      margin-left: 1em;
    }

    .player {
      margin-top: 4em;
      margin-left: 1em;
      margin-right: 1em;

      .play-toggle {
        svg {
          width: 40px;
          height: 40px;
        }
      }

      .left-skip,
      .right-skip {
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          width: 50%;
          height: 50%;
        }
      }

      .left-skip {
        left: 15%;
      }

      iframe {
        width: 100%;
        max-height: 20rem;
      }
    }
  }
  .user-comment-section {
    width: 100%;
    .image-div {
      width: 17% !important;
      .user-image {
        width: 2.5rem !important;
        height: 2.5rem !important;
        margin-top: 4%;
      }
    }
    .comment-div {
      width: 80%;
      // .user-comment {
      // margin-left: 1%;
      // }
    }
  }

  .video-comments {
    width: 100%;
    margin-left: 0%;
    margin-bottom: 3%;
    .image-div {
      width: 14% !important;
    }
  }
}
