.main-wrap {
  display: flex;
  flex-direction: column;

  .button-wrap {
    width: 98.5%;
    background-color: #f7f7f7;
    margin-right: auto;
    margin-bottom: 30px;
    padding: 15px;
    box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);
    display: flex;
    flex-direction: row;
  }
  .notes-wrap {
    display: flex;
    flex-direction: column;
    width: 98.5%;
    background-color: #f7f7f7;
    margin-right: auto;
    padding: 15px;
    box-shadow: -3px -5px 23px rgba(0, 0, 0, 0.36);
    p {
      font: normal 25px "Montserrat", sans-serif;
      padding-left: 1.3%;
    }
    .group-wrap {
      display: flex;
      flex-direction: row;

      .group-select-wrap {
        padding: 1%;
        width: 50%;
      }
    }
  }
}
.custom-btn {
  width: max-content;
  height: 40px;
  margin: auto;
  font-size: 17px;
  font-weight: normal;
  border: none;
  border-radius: 4px;
  color: #fff;
  background-color: #2e4053;
  margin-bottom: 1em;
  padding: 5px 10px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

.custom-btn:active:after {
  transition: 0s;
  opacity: 0.7;
  clip-path: circle(0% at 0% 0%);
}

.custom-btn::after {
  content: "";
  display: block;
  position: relative;
  top: -30px;
  height: 40px;
  background-color: #9fa8da;
  opacity: 0;
  clip-path: circle(150% at 0% 0%);
  transition: all 0.4s ease-in;
}

form {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;

  .elem-custom-cta {
    display: flex;
    align-items: center;
  }

  .form-row--1 {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-top: 1em;

    .form-elem {
      width: 98%;
      padding: 1%;
    }
  }
  .Note-Detail-Top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    margin: 1em 0;
    width: 100%;

    .form-elem {
      width: 40%;
      padding: 1%;
    }

    .updl-p {
      font-size: 1em;
      margin: 0;
      width: 100%;
      text-align: right;
    }
  }

  input,
  select {
    display: block;
    height: 50px;
    width: 100%;
    padding: 3px 5px;
    border: solid 1.5px #707070;
    border-radius: 5px;
    transition: all 0.3s ease;

    &:focus {
      border: solid 1.5px #0b6fb1;
      transition: all 0.3s ease;
    }

    &:focus + .placeholder-text {
      top: -75px;
      font-size: 13px;
      transition: all 0.3s ease;
    }
  }

  .placeholder-text {
    position: relative;
    top: -56px;
    left: 10px;
    padding: 3px;
    font-size: 17px;
    background-color: #fff;
    transition: all 0.4s ease;
  }

  .custom-btn {
    width: 150px;
    height: 40px;
    background-color: #e88224;
    border: none;
    border-radius: 4px;
    color: #fff;
    padding: 8px;
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
  }

  .custom-btn:active:after {
    transition: 0s;
    opacity: 0.7;
    clip-path: circle(0% at 0% 0%);
  }

  .custom-btn::after {
    content: "";
    display: block;
    position: relative;
    top: -32px;
    left: -8px;
    height: 40px;
    width: 150px;
    background-color: #e88224;
    opacity: 0;
    clip-path: circle(150% at 0% 0%);
    transition: all 0.4s ease-in;
  }

  .validate-btn {
    background-color: #58d68d;
  }

  .clear-btn {
    background-color: #f25252;
  }
  .img-updl--btn1 {
    border: none;
    background-color: rgba(0, 0, 0, 0);
    justify-self: start;
    margin-left: 0;

    img.del-img {
      height: 2.5em;
      width: 2.5em;
    }
  }
  #passwordHelpBlock,
  .form-error--text {
    font-size: 80%;
    color: #dc3545;
  }

  .is-inv {
    background-color: #dc3545;
  }

  .hidden-xs-up {
    display: none;
  }

  .cntr {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    text-align: center;
  }
}

.embed-settings {
  margin-top: 3em;
  font: normal 1rem "Montserrat", sans-serif;

  input {
    padding: 3px 5px;
    margin-left: 0.3rem;
  }
}

.common-div {
  margin-left: 1%;
  padding: 1%;
}

.ButtonWidthNoteDetail {
  margin: 0;  
}

.Ng-temp-Main {
  align-items: center;
  margin-left: 1.2%;
  .Ng-temp-Button {
    display: flex;
    flex-direction: row;
    justify-content: center;
  }
}

.img-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 45%;

  button {
    border: none;
    border-radius: 50%;
    margin-left: 0.5em;
    height: 3em;
    width: 3em;

    img {
      height: 100%;
      width: 100%;
    }
  }
}

.create-modal {
  .setting-btn {
    cursor: pointer;
    padding: 0.7em;
    margin-bottom: 0.7em;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 2px 3px 15px rgba(0, 0, 0, 0.25);

    &:hover {
      background-color: rgb(206, 206, 206);
      transition: all 0.2s ease;
    }
  }

  .settings-table-super {

    .setting {
      padding: 0.5em;
      background-color:#D5D8DC;

      .group-name {
        cursor: pointer;
        padding: 0.3em;

        &:hover {
          background-color: #eee;
        }
      }

      .isSelected {
        background-color: #eee;
      }
    }
  }

  .group-btns {
    display: flex;
    justify-content: flex-start;
    margin-top: 1.5em;

    .group-btn {
      cursor: pointer;
      padding: 0.5em;
      margin-right: 0.5em;
      border-radius: 5px;
      color: #fff;
      background-color: #2C3E50;
      box-shadow: 2px 3px 15px rgba(0, 0, 0, 0.25);

      &:hover {
        color: #000;
        background-color: #ABB2B9;
        transition: all 0.2s ease;
      }
    }
  }
}

@media (max-width: 440px) {
  .main-wrap {
    .notes-wrap {
      .group-wrap {
        display: flex;
        flex-direction: column;
        .group-select-wrap {
          width: 100%;
        }
      }
    }
  }
  .custom-btn {
    font-size: 15px;
    width: 95% !important;
    height: 50px;
  }
  .common-div {
    width: 104%;
    // text-align: center;
    margin-left: 4%;
  }
  form {
    .Note-Detail-Top {
      display: flex;
      flex-direction: column;
      width: 100%;

      .form-elem {
        width: 100%;
        padding: 1%;
      }

      .img-box{
        width: 100%;
        p {
          font: normal 17px "Montserrat", sans-serif;
          padding-left: 1.3%;
          // text-align: left;
        }
      }
    }
    
  }

  .embed-settings {
    padding: 0.1em;
  
    input {
      display: block;
      margin-left: 0;
      width: 100%;
    }
  }
}
