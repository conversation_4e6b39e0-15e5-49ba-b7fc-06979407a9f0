.qm-ml--wrap {
  --clr-blue-primary: #0A6FB0;
  --clr-ylw-primary: #FBBC05;
  --clr-ylw-secondary: #F59920;
  --clr-grey-1: #707070;
  --clr-grey-2: #686868;

  --star-size: 60px;
  --star-color: #eaecee;
  --star-background: #ffcc00;

  width: 100vw;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2000 1500'%3E%3Cdefs%3E%3Crect stroke='%23ffffff' stroke-width='0.82' width='1' height='1' id='s'/%3E%3Cpattern id='a' width='3' height='3' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cuse fill='%23fafafa' href='%23s' y='2'/%3E%3Cuse fill='%23fafafa' href='%23s' x='1' y='2'/%3E%3Cuse fill='%23f5f5f5' href='%23s' x='2' y='2'/%3E%3Cuse fill='%23f5f5f5' href='%23s'/%3E%3Cuse fill='%23f0f0f0' href='%23s' x='2'/%3E%3Cuse fill='%23f0f0f0' href='%23s' x='1' y='1'/%3E%3C/pattern%3E%3Cpattern id='b' width='7' height='11' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ebebeb'%3E%3Cuse href='%23s'/%3E%3Cuse href='%23s' y='5' /%3E%3Cuse href='%23s' x='1' y='10'/%3E%3Cuse href='%23s' x='2' y='1'/%3E%3Cuse href='%23s' x='2' y='4'/%3E%3Cuse href='%23s' x='3' y='8'/%3E%3Cuse href='%23s' x='4' y='3'/%3E%3Cuse href='%23s' x='4' y='7'/%3E%3Cuse href='%23s' x='5' y='2'/%3E%3Cuse href='%23s' x='5' y='6'/%3E%3Cuse href='%23s' x='6' y='9'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='h' width='5' height='13' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ebebeb'%3E%3Cuse href='%23s' y='5'/%3E%3Cuse href='%23s' y='8'/%3E%3Cuse href='%23s' x='1' y='1'/%3E%3Cuse href='%23s' x='1' y='9'/%3E%3Cuse href='%23s' x='1' y='12'/%3E%3Cuse href='%23s' x='2'/%3E%3Cuse href='%23s' x='2' y='4'/%3E%3Cuse href='%23s' x='3' y='2'/%3E%3Cuse href='%23s' x='3' y='6'/%3E%3Cuse href='%23s' x='3' y='11'/%3E%3Cuse href='%23s' x='4' y='3'/%3E%3Cuse href='%23s' x='4' y='7'/%3E%3Cuse href='%23s' x='4' y='10'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='c' width='17' height='13' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23e5e5e5'%3E%3Cuse href='%23s' y='11'/%3E%3Cuse href='%23s' x='2' y='9'/%3E%3Cuse href='%23s' x='5' y='12'/%3E%3Cuse href='%23s' x='9' y='4'/%3E%3Cuse href='%23s' x='12' y='1'/%3E%3Cuse href='%23s' x='16' y='6'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='d' width='19' height='17' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ffffff'%3E%3Cuse href='%23s' y='9'/%3E%3Cuse href='%23s' x='16' y='5'/%3E%3Cuse href='%23s' x='14' y='2'/%3E%3Cuse href='%23s' x='11' y='11'/%3E%3Cuse href='%23s' x='6' y='14'/%3E%3C/g%3E%3Cg fill='%23e0e0e0'%3E%3Cuse href='%23s' x='3' y='13'/%3E%3Cuse href='%23s' x='9' y='7'/%3E%3Cuse href='%23s' x='13' y='10'/%3E%3Cuse href='%23s' x='15' y='4'/%3E%3Cuse href='%23s' x='18' y='1'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='e' width='47' height='53' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ff8200'%3E%3Cuse href='%23s' x='2' y='5'/%3E%3Cuse href='%23s' x='16' y='38'/%3E%3Cuse href='%23s' x='46' y='42'/%3E%3Cuse href='%23s' x='29' y='20'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='f' width='59' height='71' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ff8200'%3E%3Cuse href='%23s' x='33' y='13'/%3E%3Cuse href='%23s' x='27' y='54'/%3E%3Cuse href='%23s' x='55' y='55'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='g' width='139' height='97' patternUnits='userSpaceOnUse' patternTransform='scale(20) translate(-950 -712.5)'%3E%3Cg fill='%23ff8200'%3E%3Cuse href='%23s' x='11' y='8'/%3E%3Cuse href='%23s' x='51' y='13'/%3E%3Cuse href='%23s' x='17' y='73'/%3E%3Cuse href='%23s' x='99' y='57'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect fill='url(%23a)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23b)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23h)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23c)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23d)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23e)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23f)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23g)' width='100%25' height='100%25'/%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;

  .qm-ml--landing {
    position: relative;
    width: 100%;
    max-width: 1220px;
    height: 100%;
    margin: auto;
    font: normal normal 16px 'Product Sans', sans-serif;
  
    .qm-logo {
      width: 20%;
      height: 80px;
  
      a {
        height: 40px;
        color: #0B6FB1;
        text-decoration: none;
        position: relative;
        font: bold normal 1.5rem 'Product Sans', sans-serif;;
        z-index: 2;
  
        img {
          height: 100%;
          width: 35px;
          margin-bottom: 8px;
        }
      }
    }
  
    .qm-mode--switch {
      position: absolute;
      top: 1rem;
      right: 0;
      width: 10%;
      height: 30px;
  
      label, .qm-toggle {
        height: 2rem;
        border-radius: 100px;
      }
  
      label {
        width: 120px;
        background-color: rgba(0,0,0,.1);
        border-radius: 100px;
        position: relative;
        cursor: pointer;
      }
  
      .qm-toggle {
        position: absolute;
        width: 50%;
        background-color: #F49820;
        box-shadow: 0 2px 15px rgba(0,0,0,.15);
        transition: transform .3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
  
      .qm-names {
        font-size: 90%;
        font-weight: bolder;
        width: 100%;
        height: 100%;
        position: absolute;
        display: flex;
        justify-content: space-around;
        align-items: center;
        user-select: none;
  
        p {
          margin: 0;
        }
      }
    }

    .qm-ratings {
      margin-top: 3em;
      width: 80%;
      display: flex;
      justify-content: center;
      align-items: center;

      .rm-data--box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 50%;

        img {
          height: 5.625rem;
          width: 5.625rem;
        }
  
        .rating-stars {
          --percent: calc(var(--rating) / 5 * 100%);
          
          display: inline-block;
          font-size: var(--star-size);
          font-family: Times;
          line-height: 1;
          
          &::before {
            content: '★★★★★';
            letter-spacing: 3px;
            background: linear-gradient(90deg, var(--star-background) var(--percent), var(--star-color) var(--percent));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }

        p {
          text-align: center;
          line-height: 1.5;
        }
  
        h4 {
          font-weight: 700;
        }
  
        span {
          padding: 3px;
          background-color: #ffcc00;
        }
  
        h1 {
          font-size: 50px;
          font-weight: 700;
          color: #ffcc00;
          text-align: center;
  
          small {
            font-size: 20px;
            color: #2C3E50;
          }
        }
      }
    }
  
    .qm-sect--reg {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 2em;
  
      h2 {
        font-size: 3rem;
        color: var(--clr-blue-primary);
        margin-bottom: 0;
        font-weight: 900;
        text-align: center;
      }
  
      h1, h3 {
        font-size: 2.5rem;
        color: #565656;
        margin-bottom: 0;
        font-weight: 900;
      }

      h1 {
        margin-top: 3rem;
      }

      h3 {
        background: #FDC830;
        background: -webkit-linear-gradient(to right, #F37335, #FDC830);
        background: linear-gradient(to right, #F37335, #FDC830);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-top: 0.5em;
      }

      .offer-box {
        margin-top: 4rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
          height: 475px;
          width: 450px;
          margin-bottom: 2em;
        }

        p {
          text-align: center;
          font-size: 1rem;
          text-align: center;
          font-weight: 900;
          color: var(--clr-grey-1);

          span {
            padding: 2px;
            border-radius: 5px;
            cursor: pointer;
            color: #0B5345;
            background-color: #2BE7C2;
            font-weight: 900;
          }
        }
      }
    }

    .qm-sect--intro {

      h4 {
        font-size: 2.5rem;
        color: #565656;
        margin-top: 1rem;
        margin-bottom: 0;
        font-weight: 900;

        &:last-of-type {
          background: #FDC830;
          background: -webkit-linear-gradient(to right, #F37335, #FDC830);
          background: linear-gradient(to right, #F37335, #FDC830);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .qm-features--grid {
        width: 100%;
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2em;
        place-items: center;
        margin-top: 4em;

        .qm-feature--card {
          width: 16.875rem;
          padding: 1.5rem;
          border-radius: 1rem;
          box-shadow: 0 5px 15px #0000002c;
          background-color: #fff;

          img {
            display: block;
            height: 150px;
            width: 100%;
            margin: auto;
          }

          p {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 900;
            margin-top: 0.5em;
          }

          &:nth-of-type(5) {
            grid-column: 2;
          }

          &:last-of-type {
            grid-column: 3;
          }
        }
      }

      .qm-upcoming--info {
        width: 100%;
        margin-top: 2.5rem;

        p {
          margin-bottom: 0;
          font-size: 1.5rem;
          font-weight: 900;
          color: var(--clr-blue-primary);
          text-align: center;
        }

        .qm-info--card {
          max-width: 25em;
          display: flex;
          align-items: center;
          justify-content: space-around;
          padding: 1.4rem 1rem;
          margin: auto;
          margin-top: 0.5rem;
          border-radius: 1rem;
          box-shadow: 0 5px 15px #0000002c;
          background-color: #fff;

          p {
            margin-right: 2em;
            font-size: 1.125rem;
            color: var(--clr-grey-2);
          }
        }
      }
    }

    .qm-sect--detail {

      h2 {
        font-size: 2.5rem;
        margin-top: 3rem;
        margin-bottom: 0;
        font-weight: 900;
      }

      .qm-detail--lists {
        width: 100%;

        .qm-detail--list {
          width: 60%;
          margin: auto;
          margin-top: 2em;

          h4 {
            text-align: center;
            font-size: 2.3rem;
            color: var(--clr-ylw-primary);
            margin-top: 1rem;
          }

          ul {
            padding: 1rem 2rem;
            margin-top: 1em;
            background: rgba(0, 0, 0, 0.05) 0% 0% no-repeat padding-box;
            box-shadow: 0px 0px 18px #0000003f;
            border-radius: 15px;
            opacity: 1;
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);

            li {
              font-size: 1.5rem;
              color: var(--clr-ylw-primary);
              position: relative;
              padding: 1rem;
              margin-bottom: 0.5em;
              border-radius: 0.5rem;
              background-color: var(--clr-blue-primary);
              list-style: none;
              font-weight: 900;

              span {
                font-size: 1.7rem;
              }
            }
          }
        }
      }

      .qm-card--grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        justify-items: center;
        margin-top: 3rem;
        width: 100%;

        .qm-student-rev--card {
          padding: 1rem;
          background: rgba(0, 0, 0, 0.05) 0% 0% no-repeat padding-box;
          box-shadow: 0px 0px 18px #0000003f;
          border-radius: 15px;
          opacity: 1;
          backdrop-filter: blur(5px);
          -webkit-backdrop-filter: blur(5px);
        }

        .student-avatar {
          height: 6.35rem;
          width: 6.25rem;

          img {
            height: 100%;
            width: 100%;
            border-radius: 50%;
          }
        }

        .student-review {
          line-height: 1.5rem;

          i {
            font-weight: bold;
            font-size: 1.5rem;
          }
        }

        .student-name {
          font-size: 1rem;
          font-weight: 900;
        }
      }

      .video-grid--1 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        justify-items: center;
        margin-top: 3rem;
        width: 100%;
  
        .qm-video {
          position: relative;
  
          iframe {
            border-radius: 1rem;
          }
  
          .video-title {
            width: 70%;
            position: relative;
            left: 15%;
            padding: 0.5rem;
            color: #fff;
            text-align: center;
            font-weight: 900;
            font-size: 1.1rem;
            border-radius: 2.5rem;
            background: transparent linear-gradient(101deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
            box-shadow: 0px 0px 18px #00000017;
          }
  
          .video-text {
            font-size: 1.5rem;
            color: var(--clr-grey-1);
            text-align: center;
          }
        }
      }
    }

    .qm-sect--enroll {
      
      h2 {
        font-size: 2rem;

        span {
          font-size: 2.5rem;
          color: var(--clr-ylw-primary);
        }

        &:first-of-type span,
        &:first-of-type span sup,
        &:nth-of-type(2) span {
          background: #000046;
          background: -webkit-linear-gradient(to right, #1CB5E0, #000046);
          background: linear-gradient(to right, #1CB5E0, #000046);
          -webkit-background-clip: text;
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        &:nth-of-type(2) span {
          font-size: 2rem;
        }

        // &:last-of-type {
        //   margin-top: 2em;
        // }
      }

      p {
        width: 80%;
        text-align: left;
        font-size: 1.4rem;
        margin-top: 1em;
        text-align: center;

        span {
          font-weight: 900;
          color: var(--clr-blue-primary);
        }

        &:last-of-type {
          text-align: center;
        }
      }
    }

    .qm-grid--1 {
      margin-top: 6em;

      img {
        display: block;
        margin: auto;
        height: 100%;
        width: 35%;
      }
    }

    .qm-prim--btn {
      height: 3.5rem;
      min-width: 12.5rem;
      margin-top: 1em;
      font-size: 1.5rem;
      border: none;
      border-radius: 0.625rem;
      background: transparent linear-gradient(110deg, #F8DB1F 0%, #E28A26 100%) 0% 0% no-repeat padding-box;
      transform: scale(1);
      transition: all 0.4s ease-out;

      &:hover {
        transform: scale(1.1);
        transition: all 0.2s ease-in;
      }
    }

    .qm-sec--btn {
      height: 3.5rem;
      min-width: 12.5rem;
      border: none;
      border-radius: 0.625rem;
      font-size: 1.5rem;
      color: #fff;
      background: transparent linear-gradient(225deg, var(--unnamed-color-0a6fb0) 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      background: transparent linear-gradient(225deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      transform: scale(1);
      transition: all 0.4s ease-out;
  
      &:hover {
        transform: scale(1.1);
        transition: all 0.2s ease-in;
      }
    }

    .qm-ftr {
      margin-top: 4em;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      p {
        font-size: 2em;
      }
  
      .icons {
        margin-top: 2em;
        display: flex;
        justify-content: space-between;
        width: 20%;
  
        .icon {
          display: grid;
          place-items: center;
          padding: 0.1rem;
          background: transparent linear-gradient(111deg, #0C7DA9 0%, #16366D 100%) 0% 0% no-repeat padding-box;
          border-radius: 50%;
          height: 40px;
          width: 40px;
  
          a {
            img {
              height: 20px;
              width: 20px;
            }
          }
        }
      }
  
      .copy-text {
        font-size: 1em;
        font-weight: lighter;
        margin-top: 2em;
        margin-bottom: 0.5em;
        color: var(--clr-grey-1);
      }
    }
  }

  .qm-contact--box {
    cursor: pointer;
    max-width: 17rem;
    position: fixed;
    display: flex;
    top: 85%;
    left: 90%;
    align-items: center;
    justify-content: center;
    padding: 0.5em 1em;
    background: rgba(0, 0, 0, 0.05) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 18px #0000003f;
    border-radius: 15px;
    opacity: 1;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);

    &:hover {
      transform: scale(1.05);
      transition: all 0.2s ease;
    }

    p {
      margin: 0;
      font-weight: 900;
    }

    img {
      height: 3rem;
      width: 3rem;
    }
  }
}

.reg-modal {
  font: 'Product Sans' 'sans-serif';

  .modal-body {
    .form-error--text {
      display: block;
      font-size: 12px;
      color: #dc3545;
    }

    .secondary-btn {
      height: 2.5rem;
      min-width: 7.5rem;
      border: none;
      border-radius: 0.625rem;
      color: #fff;
      background: transparent linear-gradient(225deg, var(--unnamed-color-0a6fb0) 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      background: transparent linear-gradient(225deg, #0A6FB0 0%, #1B1749 100%) 0% 0% no-repeat padding-box;
      transform: scale(1);
      transition: all 0.4s ease-out;
  
      &:hover {
        transform: scale(1.1);
        transition: all 0.2s ease-in;
      }
    }

    input,
    select {
      display: block;
      height: 40px;
      width: 100%;
      padding: 3px 5px;
      border: solid 1.5px #707070;
      font-size: 18px;
      border-radius: 5px;
      transition: all 0.3s ease;

      &:focus {
        border: solid 1.5px #0B6FB1;
        transition: all 0.3s ease;
      }

      &:focus + .placeholder-text {
          top: -75px;
          font-size: 13px;
          transition: all 0.3s ease;
      }
    }

    .form-elem {
      margin-bottom: 1em;
    }

    .placeholder-text {
      position: relative;
      top: -56px;
      left: 10px;
      padding: 3px;
      font-size: 17px;
      background-color: #fff;
      transition: all 0.4s ease;
    }

    #passwordHelpBlock,
    #emailHelpBlock {
      margin-top: 10px;
      font-size: 12px;
      color: #6c757d;
      width: 90%;
    }

    .item-tab {
      td:last-of-type {
        width: 100px;
      }
    }
  }
}

/** DARK STYLES */
.dark-theme {
  background-color: #060a24;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 2000 1500'%3E%3Cdefs%3E%3Crect stroke='%23060a24' stroke-width='0.7' width='1' height='1' id='s'/%3E%3Cpattern id='a' width='3' height='3' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cuse fill='%230b0f28' href='%23s' y='2'/%3E%3Cuse fill='%230b0f28' href='%23s' x='1' y='2'/%3E%3Cuse fill='%2310142d' href='%23s' x='2' y='2'/%3E%3Cuse fill='%2310142d' href='%23s'/%3E%3Cuse fill='%23151931' href='%23s' x='2'/%3E%3Cuse fill='%23151931' href='%23s' x='1' y='1'/%3E%3C/pattern%3E%3Cpattern id='b' width='7' height='11' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%231a1e36'%3E%3Cuse href='%23s'/%3E%3Cuse href='%23s' y='5' /%3E%3Cuse href='%23s' x='1' y='10'/%3E%3Cuse href='%23s' x='2' y='1'/%3E%3Cuse href='%23s' x='2' y='4'/%3E%3Cuse href='%23s' x='3' y='8'/%3E%3Cuse href='%23s' x='4' y='3'/%3E%3Cuse href='%23s' x='4' y='7'/%3E%3Cuse href='%23s' x='5' y='2'/%3E%3Cuse href='%23s' x='5' y='6'/%3E%3Cuse href='%23s' x='6' y='9'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='h' width='5' height='13' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%231a1e36'%3E%3Cuse href='%23s' y='5'/%3E%3Cuse href='%23s' y='8'/%3E%3Cuse href='%23s' x='1' y='1'/%3E%3Cuse href='%23s' x='1' y='9'/%3E%3Cuse href='%23s' x='1' y='12'/%3E%3Cuse href='%23s' x='2'/%3E%3Cuse href='%23s' x='2' y='4'/%3E%3Cuse href='%23s' x='3' y='2'/%3E%3Cuse href='%23s' x='3' y='6'/%3E%3Cuse href='%23s' x='3' y='11'/%3E%3Cuse href='%23s' x='4' y='3'/%3E%3Cuse href='%23s' x='4' y='7'/%3E%3Cuse href='%23s' x='4' y='10'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='c' width='17' height='13' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%231f233a'%3E%3Cuse href='%23s' y='11'/%3E%3Cuse href='%23s' x='2' y='9'/%3E%3Cuse href='%23s' x='5' y='12'/%3E%3Cuse href='%23s' x='9' y='4'/%3E%3Cuse href='%23s' x='12' y='1'/%3E%3Cuse href='%23s' x='16' y='6'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='d' width='19' height='17' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%23060a24'%3E%3Cuse href='%23s' y='9'/%3E%3Cuse href='%23s' x='16' y='5'/%3E%3Cuse href='%23s' x='14' y='2'/%3E%3Cuse href='%23s' x='11' y='11'/%3E%3Cuse href='%23s' x='6' y='14'/%3E%3C/g%3E%3Cg fill='%2324273e'%3E%3Cuse href='%23s' x='3' y='13'/%3E%3Cuse href='%23s' x='9' y='7'/%3E%3Cuse href='%23s' x='13' y='10'/%3E%3Cuse href='%23s' x='15' y='4'/%3E%3Cuse href='%23s' x='18' y='1'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='e' width='47' height='53' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%2336576c'%3E%3Cuse href='%23s' x='2' y='5'/%3E%3Cuse href='%23s' x='16' y='38'/%3E%3Cuse href='%23s' x='46' y='42'/%3E%3Cuse href='%23s' x='29' y='20'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='f' width='59' height='71' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%2336576c'%3E%3Cuse href='%23s' x='33' y='13'/%3E%3Cuse href='%23s' x='27' y='54'/%3E%3Cuse href='%23s' x='55' y='55'/%3E%3C/g%3E%3C/pattern%3E%3Cpattern id='g' width='139' height='97' patternUnits='userSpaceOnUse' patternTransform='rotate(45 1000 750) scale(13.55) translate(-926.2 -694.65)'%3E%3Cg fill='%2336576c'%3E%3Cuse href='%23s' x='11' y='8'/%3E%3Cuse href='%23s' x='51' y='13'/%3E%3Cuse href='%23s' x='17' y='73'/%3E%3Cuse href='%23s' x='99' y='57'/%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect fill='url(%23a)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23b)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23h)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23c)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23d)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23e)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23f)' width='100%25' height='100%25'/%3E%3Crect fill='url(%23g)' width='100%25' height='100%25'/%3E%3C/svg%3E");
  background-attachment: fixed;
  background-size: cover;

  .qm-ml--landing {

    .qm-mode--switch p {
      color: #E5E7E9;
    }

    .qm-sect--intro {
      .qm-features--grid .qm-feature--card,
      .qm-upcoming--info .qm-info--card {
        background-color: #34323d;

        p {
          color: #E5E7E9;
        }
      }

      .qm-ratings .rm-data--box {
        p {
          color: #E5E7E9;

          span {
            color: #000;
          }
        }

        h1 small {
          color: #E5E7E9;
        }
      }
    }

    .qm-sect--detail {
      .qm-card--grid .qm-student-rev--card,
      .qm-detail--lists .qm-detail--list {
        background-color:#34323d;

        p {
          color: #E5E7E9;
        }
      }
    }

    .qm-sect--enroll p,
    .qm-sect--enroll h4,
    .qm-ftr h5,
    .qm-ftr .copy-text {
      color: #E5E7E9;
    }

    .qm-sect--enroll h4:first-of-type span,
    .qm-sect--enroll h4:first-of-type span sup {
      background: #2193b0;
      background: -webkit-linear-gradient(to right, #6dd5ed, #2193b0);
      background: linear-gradient(to right, #6dd5ed, #2193b0);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .qm-contact--box {
    background-color: #34323d8e;

    p {
      color: #E5E7E9;
    }
  }
}

@media (max-width: 440px) {

  .qm-ml--wrap {
    .qm-ml--landing {
      .qm-mode--switch {
        position: initial;
        width: initial;
        text-align: center;
      }
  
      .qm-logo {
        width: 100%;
        text-align: center;
      }

      .qm-ratings {
        width: 95%;
        flex-direction: column;

        .rm-data--box {
          margin-top: 2em;
        }
      }

      .qm-sect--reg {
        width: 99%;

        h3, h1 {
          text-align: center;
        }
      }

      .qm-sect--intro {

        h2, h3, h4 {
          text-align: center;
        }

        .qm-features--grid {
          grid-template-columns: 1fr;

          .qm-feature--card:last-of-type,
          .qm-feature--card:nth-of-type(5) {
            grid-column: initial;
          }
        }

        .qm-upcoming--info {
          width: 85%;
        }
      }

      .qm-sect--detail { 
      
        .qm-card--grid,
        .video-grid--1 {
          grid-template-columns: 1fr;

          .qm-student-rev--card {
            width: 95%;
          }

          .qm-video {
            width: 95%;

            iframe {
              width: 100%;
            }
          }
        }

        .qm-detail--lists .qm-detail--list {
          width: 95%;

          ul li {
            font-size: 1em;
          }
        }
      }

      .qm-sect--enroll h4 {
        text-align: center;

        span {
          display: block;
        }
      }

      .qm-ftr {
        margin-top: 2em;
  
        h5 {
          text-align: center;
        }
  
        .icons {
          margin-top: 0.5em;
          width: 70%;
        }
  
        .copy-text {
          margin-top: 1em;
          text-align: center;
          font-size: 15px;
        }
      }
    }

    .qm-contact--box {
      top: 90%;
      left: 10%;
    }

    .qm-grid--1 img {
      width: 95%;
    }
  }
}