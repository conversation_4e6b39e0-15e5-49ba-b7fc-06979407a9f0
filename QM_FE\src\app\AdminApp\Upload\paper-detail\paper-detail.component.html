<div class="main-wrap">
  <app-loading-indicator *ngIf="showIndicator"></app-loading-indicator>

  <div class="paper-detail">
    <form #EditPaperDetail="ngForm" (ngSubmit)="SubmitNewPaper(EditPaperDetail.valid)" autocomplete="off">
      <div class="Paper_header">
        <span>Paper Details</span>
        <div class="btn-ctrl--wrap">
          <button type="button" class="custom-btn preview-btn" (click)="takeToPreview()">Student Preview</button>
          <button type="submit" value="submit" class="custom-btn register-btn">Save</button>
        </div>
      </div>
      <div class="form-row--1">
        <div class="form-elem">
          <label>Paper name</label>
          <input type="text" name="PaperName" placeholder="Paper Name *"
            [class.is-inv--input]="PaperName.invalid && PaperName.touched || (PaperName.pristine && EditPaperDetail.submitted)"
            [(ngModel)]="paper_detail.paper_name" #PaperName="ngModel" required />
          <small class="form-error--text"
            *ngIf="(PaperName.touched || EditPaperDetail.submitted) && PaperName.errors?.required">
            Paper Name is Required
          </small>
        </div>
        <div class="form-elem" *ngIf="superPaperType == '4'">
          <label>Paper Description</label>
          <input type="text" name="PaperDesc" placeholder="Paper Desc *"
            [class.is-inv--input]="PaperDesc.invalid && PaperDesc.touched || (PaperDesc.pristine && EditPaperDetail.submitted)"
            [(ngModel)]="paper_detail.paper_desc" #PaperDesc="ngModel" required />
          <small class="form-error--text"
            *ngIf="(PaperDesc.touched || EditPaperDetail.submitted) && PaperDesc.errors?.required">
            Paper Description is Required
          </small>
        </div>
        <div class="form-elem">
          <label>Time Limit (Minutes)</label>
          <input type="number" name="PaperTimeLim" placeholder="Time Limit *"
            [class.is-inv--input]="PaperTimeLim.invalid && PaperTimeLim.touched || (PaperTimeLim.pristine && EditPaperDetail.submitted)"
            [(ngModel)]="paper_detail.time_lim" #PaperTimeLim="ngModel" required pattern="^[0-9]{1,3}$" />
          <small class="form-error--text"
            *ngIf="(PaperTimeLim.touched || EditPaperDetail.submitted) && PaperTimeLim.errors?.required">
            Paper Time limit is Required
          </small>
          <small class="form-error--text" *ngIf="PaperTimeLim.errors?.pattern && (PaperTimeLim.touched || EditPaperDetail.submitted)">
            Paper Time limit got exceeded
          </small>
        </div>
        <div class="form-elem" *ngIf="superPaperType != '13' && superPaperType != '14' && superPaperType != '4' && superPaperType != '1'">
          <label title="">Status</label>
          <select name="Status" [(ngModel)]="paper_detail.status" #Status="ngModel" class="custom-select form-control"
            required>
            <option [ngValue]="0" value="0">Hide paper</option>
            <option [ngValue]="1" value="1">Show paper</option>
          </select>
          <small class="form-error--text"
            *ngIf="(Status.touched || EditPaperDetail.submitted) && Status.errors?.required">
            Please choose paper status
          </small>
          <!-- <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input" id="customSwitch1" [value]="paper_detail.p_status">
            <label class="custom-control-label" for="customSwitch1">Toggle this switch element</label>
          </div> -->
        </div>
      </div>
      <div class="form-row--1">
        <div class="form-elem">
          <label>Show Answer</label>
          <select name="ShowAns" [(ngModel)]="paper_detail.show_ans" #ShowAns="ngModel"
            class="custom-select form-control" required>
            <option [ngValue]="0" value="0">Dont' show the answer</option>
            <option [ngValue]="1" value="1">Show the answer</option>
          </select>
          <small class="form-error--text"
            *ngIf="(ShowAns.touched || EditPaperDetail.submitted) && ShowAns.errors?.required">
            Please choose any one
          </small>
        </div>
        <div class="form-elem" *ngIf="superPaperType != '13' && superPaperType != '14' && superPaperType != '4' && superPaperType != '1'">
          <label>Shuffle Questions</label>
          <select name="ShuffleQues" [(ngModel)]="paper_detail.rand_ques" #ShuffleQues="ngModel"
            class="custom-select form-control" required>
            <option [ngValue]="0" value="0">No</option>
            <option [ngValue]="1" value="1">Yes</option>
          </select>
          <small class="form-error--text"
            *ngIf="(ShuffleQues.touched || EditPaperDetail.submitted) && ShuffleQues.errors?.required">
            Please choose any one
          </small>
        </div>
        <div class="form-elem" *ngIf="superPaperType != '13' && superPaperType != '14' && superPaperType != '4' && superPaperType != '1'">
          <label>Negative Marking</label>
          <select name="NegMarks" [(ngModel)]="paper_detail.neg_marks" #NegMarks="ngModel"
            class="custom-select form-control" required>
            <option [ngValue]="0" value="0">No</option>
            <option [ngValue]="1" value="1">Yes</option>
          </select>
          <small class="form-error--text"
            *ngIf="(NegMarks.touched || EditPaperDetail.submitted) && NegMarks.errors?.required">
            Please choose any one
          </small>
        </div>
        <div class="form-elem">
          <label>Answer Once</label>
          <select name="AnswerOnce" [(ngModel)]="paper_detail.once_ans" #AnswerOnce="ngModel" disabled
            class="custom-select form-control" required>
            <option [ngValue]="0" value="0">No</option>
            <option [ngValue]="1" value="1">Yes</option>
          </select>
          <small class="form-error--text"
            *ngIf="(AnswerOnce.touched || EditPaperDetail.submitted) && AnswerOnce.errors?.required">
            Please choose any one
          </small>
        </div>
      </div>
    </form>
  </div>

  <!-- Paper question section -->
  <div class="pt-3" style="width: 98%;">
    <div class="d-flex flex-row justify-content-between">
      <span class="Header-class-font">Questions ({{allQuestionsLegth}})</span>
      <!-- <button class="btn btn-secondary text-left" type="button" (click)="fnAddNewQuestions()"
      *ngIf = 'addQuestionBtn_visible'> Add </button>  -->
    </div>
    <div *ngFor="let que of displayRecords; let i = index" class="py-3 pr-4 d-flex flex-row">
      <div class="mr-1">
        <button class="btn btn-info text-left">
          {{que.question_no}}
        </button>
      </div>
      <div class="d-flex flex-column overflow-hidden w-100">
        <div class="w-100 overflow-hidden">
          <button class="btn btn-secondary text-left w-100" type="button" data-toggle="collapse"
            (click)="isCollapsed[i] = !isCollapsed[i]" [class.is-open]="!isCollapsed[i]"
            [attr.aria-expanded]="!isCollapsed" aria-controls="collapseVideoList">
            <p class="mb-0 text-nowrap text-truncate">{{que.question}} </p>
          </button>
          <!-- <button type="button" class="btn btn-outline-success" aria-label="Close">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-save2"
              viewBox="0 0 16 16">
              <path
                d="M2 1a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H9.5a1 1 0 0 0-1 1v4.5h2a.5.5 0 0 1 .354.854l-2.5 2.5a.5.5 0 0 1-.708 0l-2.5-2.5A.5.5 0 0 1 5.5 6.5h2V2a2 2 0 0 1 2-2H14a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2a2 2 0 0 1 2-2h2.5a.5.5 0 0 1 0 1H2z" />
            </svg>
          </button> -->
        </div>
        <div [collapse]="isCollapsed[i]" [isAnimated]="true" class="question-detail">
          <div class="question_header">
            <div class="d-flex justify-content-end mr-5 mt-2">
              <button class="custom-btn btn-green" type="button" (click)="validateQuestionContent(que)">Validate</button>
              <button class="custom-btn mr-2" type="button" (click)="fnSaveQuestionDeatils(que)">Save</button>
              <button class="custom-btn mr-2" type="button" (click)="fnResetQuestionDetails(i,que.question_no)">Clear</button>
              <button class="custom-btn" type="button" *ngIf="superPaperType != '13' && superPaperType != '14'" (click)="onClickUploadButton(i,que.question_no)">Upload Image</button>
              <img src="../../../../assets/push-icons/trash.svg" alt="Delete Question" title="Delete this Question"
                (click)="deleteQuestion(que.paper_id, que.question_no)" *ngIf="superPaperType != '4'" />
            </div>
            <div class="container">
              <div class="row p-2">
                <div class="col">
                  <angular-markdown-editor [textareaId]="que.question_no" rows="3" name="markdownText"
                    [(ngModel)]="que.question" rows="15">
                  </angular-markdown-editor>
                </div>
              </div>
              <div class="row row-cols-2 p-2">
                <div class="col">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">
                        <input type="radio" aria-label="Radio button for following text input" [name]="que.question_no"
                          [value]="1" [(ngModel)]="que.correct_opt">
                      </div>
                    </div>
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType != '4'"
                      (change)="selectValue($event,i,'opt_1')"
                      [value]="que.opt_1">
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType == '4'"
                      (change)="selectValue($event,i,'option_1')"
                      [value]="que.option_1">
                  </div>
                </div>
                <div class="col">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">
                        <input type="radio" aria-label="Radio button for following text input" [name]="que.question_no"
                          [value]="2" [(ngModel)]="que.correct_opt" >
                      </div>
                    </div>
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType != '4'"
                      (change)="selectValue($event,i,'opt_2')"
                      [value]="que.opt_2">
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType == '4'"
                      (change)="selectValue($event,i,'option_2')"
                      [value]="que.option_2">
                  </div>
                </div>
                <div class="col mt-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">
                        <input type="radio" aria-label="Radio button for following text input" [name]="que.question_no"
                          [value]="3" [(ngModel)]="que.correct_opt">
                      </div>
                    </div>
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType != '4'"
                      (change)="selectValue($event,i,'opt_3')"
                      [value]="que.opt_3">
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType == '4'"
                      (change)="selectValue($event,i,'option_3')"
                      [value]="que.option_3">
                  </div>
                </div>
                <div class="col mt-2">
                  <div class="input-group">
                    <div class="input-group-prepend">
                      <div class="input-group-text">
                        <input type="radio" aria-label="Radio button for following text input" [name]="que.question_no"
                          [value]="4" [(ngModel)]="que.correct_opt">
                      </div>
                    </div>
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType != '4'"
                      (change)="selectValue($event,i,'opt_4')"
                      [value]="que.opt_4">
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      *ngIf="superPaperType == '4'"
                      (change)="selectValue($event,i,'option_4')"
                      [value]="que.option_4">
                  </div>
                </div>
                <div class="col m-auto" *ngIf="superPaperType != '4'">
                  <div class="input-group mt-2">
                    <div class="input-group-prepend">
                      <div class="input-group-text">
                        <input type="radio" aria-label="Radio button for following text input" [name]="que.question_no"
                          [value]="5" [(ngModel)]="que.correct_opt">
                      </div>
                    </div>
                    <input type="text" class="form-control" aria-label="Text input with radio button"
                      (change)="selectValue($event,i,'opt_5 || \'\'')"
                      [value]="que.opt_5 || ''">
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col">
                  <p>Explanation:</p>
                  <angular-markdown-editor [textareaId]="'e' + que.question_no" rows="3" name="markdownText"
                    [(ngModel)]="que.explanation" rows="15">
                  </angular-markdown-editor>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <p>Having trouble updating the paginator below expecially when adding questions :P, so actual page is: <b>{{copyStartItem / 10 + 1}}</b></p>
    <div class="pr-4 d-flex flex-row justify-content-between" >
      <pagination [boundaryLinks]="true" [totalItems]="allQuestionsLegth" class="job-list-pagination" [rotate]="true"
        [maxSize]="7" [itemsPerPage]="10" (pageChanged)="questionOrderChanged($event)" (numPages)="smallnumPages = $event"></pagination>
        <div>
          <button class="btn btn-secondary text-left" style="height: fit-content;" type="button" (click)="fnAddNewQuestions()"
          *ngIf = 'addQuestionBtn_visible'>Add</button>
        </div>
    </div>
  </div>
</div>

<ng-template #successTemplate>
  <div class="modal-header">
    <h4 class="modal-title text-success">Success!</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>{{sSuccessMsg}}</p>
  </div>
</ng-template>
<ng-template #errorTemplate>

  <div class="modal-header">
    <h4 class="modal-title text-danger">Error!</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <p>Please try doing again!</p>
  </div>
</ng-template>

<ng-template #validationTemplate>
  <div class="modal-header">
    <h4 class="modal-title pull-left" [class.text-success]="validationGood" [class.text-warning]="!validationGood">{{ validationTitle }}</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="hideModal('validation')">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="msg-wrap" *ngFor="let msg of validationMsgs">
      <p>{{ msg }}</p>
    </div>
  </div>
</ng-template>

<ng-template #uploadImage>
  <div class="modal-header">
    <h4 class="modal-title text-success">Upload Image</h4>
    <button type="button" class="close pull-right" aria-label="Close" (click)="modalRef.hide()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="paper-detail">
    <form (ngSubmit)="ngbtnUploadImage.click()" autocomplete="off">
      <div class="form-row--1 ml-3 mr-2 mb-2">
        <div class="form-elem">
          <label>Image for:</label>
          <select name="UploadImage" [(ngModel)]="uploadImageFor" #ngUploadImage="ngModel"
            class="custom-select form-control" required>
            <!-- <option [ngValue]="0" value="0">Select one option</option> -->
            <option [ngValue]="1" value="1">Question</option>
            <option [ngValue]="2" value="2">Explanation</option>
          </select>
          <!-- <small class="form-error--text"
            *ngIf="(ngUploadImage.touched || ngFormUploadImage.submitted) && ngUploadImage.errors?.required">
            Please choose any one
          </small> -->
        </div>
      </div>
      <div class="img-box pt-3 pb-3">
        <p class="updl-p">Upload Image</p>
          <input style="display: none" type="file" (change)="onSelectFileExplorer($event)" #ngbtnUploadImage />
          <button type="submit" title="Select Image">
            <img src="../../../../assets/push-icons/edit.svg" />
          </button>
      </div>
    </form>
  </div>
</ng-template>

