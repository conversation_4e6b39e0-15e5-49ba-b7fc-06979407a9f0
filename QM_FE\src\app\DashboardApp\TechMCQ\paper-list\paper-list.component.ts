import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { getgroups } from 'process';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';
import { TMCQSubGroup } from 'src/app/Models/Dashboard/TMCQ/TMCQSubGroup';
import { TMCQGroup } from '../../../Models/Dashboard/TMCQ/TMCQGroup';
import { TmcqService } from '../../../Services/Dashboard/TMCQ/tmcq.service';

@Component({
  selector: 'app-paper-list',
  templateUrl: './paper-list.component.html',
  styleUrls: ['./paper-list.component.scss']
})
export class PaperListComponent implements OnInit {

  public showIndicator = false;

  public selectedTopic: string;
  public selectedSubTopic: string;

  public groups: TMCQGroup[];
  public subGroups: TMCQSubGroup[];
  public papers: ChapterPaper[];

  constructor(private router: Router,
              private tmcqService: TmcqService) { }

  ngOnInit() {

    this.getGroups();
  }

  getGroups() {
    this.showIndicator = true;
    this.tmcqService.getTMCQGroups().subscribe(response => {
      this.groups = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getSubGroups(groupId: string, groupName: string) {
    this.showIndicator = true;

    this.selectedTopic = groupName;
    this.tmcqService.getTMCQSubGroups(groupId).subscribe(response => {
      this.subGroups = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  getPapers(subGroupId: string, subGroupName: string) {
    this.showIndicator = true;

    this.selectedSubTopic = subGroupName;
    this.tmcqService.getPapersOfAGroup(subGroupId).subscribe(response => {
      this.papers = response;
      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  beginTest(paperId: string, paperName: string, paperLim: number) {
    this.router.navigate(['/dashboard/test/', '11', paperId, paperName, paperLim]);
  }
}
