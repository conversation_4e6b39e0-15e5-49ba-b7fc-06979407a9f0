// lib/server-services/notes-service.server.ts
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { NotesGroup, Notes, Comment } from '@/types/notes-types'

const API_BASE_URL = 'https://api.quantmasters.in'
const ENDPOINTS = {
  groupsUrl: `${API_BASE_URL}/v2/notes/groups`,
  notesUrl: `${API_BASE_URL}/v3/notes`,
  commentsUrl: `${API_BASE_URL}/v2/notes`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerNotesService {
  /**
   * Get super groups and groups
   */
  static async getSuperGroups(): Promise<NotesGroup[]> {
    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        ENDPOINTS.groupsUrl,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error('Error fetching super groups:', error)
      return []
    }
  }

  /**
   * Get notes for a specific group
   */
  static async getNotesOfGroup(groupId: string): Promise<Notes[]> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.notesUrl}/${groupId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching notes for group ${groupId}:`, error)
      return []
    }
  }

  /**
   * Get note detail for reading
   */
  static async getNotesForReading(noteId: string): Promise<Notes | null> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()

    try {
      const response = await axios.get(
        `${ENDPOINTS.notesUrl}/detail/${noteId}`,
        createAuthHeaders(token)
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching note detail ${noteId}:`, error)
      return null
    }
  }

  /**
   * Get comments for a note
   */
  static async getNotesComments(noteId: string): Promise<Comment[]> {
    try {
      const response = await axios.get(
        `${ENDPOINTS.commentsUrl}/${noteId}/comments`
      )
      return response.data
    } catch (error) {
      console.error(`Error fetching comments for note ${noteId}:`, error)
      return []
    }
  }
}
