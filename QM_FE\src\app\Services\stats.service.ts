import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class StatsService {
  private statUrlReg    = 'https://api.quantmasters.in/stats/registered';
  private statUrlPur    = 'https://api.quantmasters.in/stats/purchased';
  private statUrlNotPur = 'https://api.quantmasters.in/stats/not/purchased';
  private statUrlLwk    = 'https://api.quantmasters.in/stats/week/before';
  private statUrlTests  = 'https://api.quantmasters.in/stats/tests';
  private statUrlAns    = 'https://api.quantmasters.in/stats/answered';

  private JwtToken: string;

  constructor(private http: HttpClient) {
    this.JwtToken = sessionStorage.getItem('QMA_TOK');
  }

  retStatsRegistered(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.statUrlReg, httpOps);
  }

  retStatsPurchased(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.statUrlPur, httpOps);
  }

  retStatsNotPurchased(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.statUrlNotPur, httpOps);
  }

  retStatsLastWeek(): Observable<string> {
    this.setSecurityToken();

    const httpOps = {
      headers: new HttpHeaders({
        'Authorization': 'Bearer ' + this.JwtToken
      })
    };

    return this.http.get<string>(this.statUrlLwk, httpOps);
  }

  retStatsTests(): Observable<string> {
    return this.http.get<string>(this.statUrlTests);
  }

  retStatsAnswers(): Observable<string> {
    return this.http.get<string>(this.statUrlAns);
  }

  setSecurityToken() {
    if (!sessionStorage.getItem('QMA_TOK')) {
      this.JwtToken = null;
    } else {
      this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }
  }
}
