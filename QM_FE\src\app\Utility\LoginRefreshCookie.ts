export class LoginRefresh {
    getCookie(name) {
        name = encodeURIComponent(name);
        var /** @type {?} */ regExp = this.getCookieRegExp(name);
        var /** @type {?} */ result = regExp.exec(document.cookie);
        return decodeURIComponent(result ? result[1] : '');
}
    getCookieRegExp(name) {
  var /** @type {?} */ escapedName = name.replace(/([\[\]\{\}\(\)\|\=\;\+\?\,\.\*\^\$])/ig, '\\$1');
  return new RegExp('(?:^' + escapedName + '|;\\s*' + escapedName + ')=(.*?)(?:;|$)', 'g');
}
  /**
 * Setting up the cookies
 * @param template Login deatils
 */
   fnSetCookiesAndSession(oData, email, user, Cmlp, key){
    let expiredTime = new Date();
    expiredTime.setHours( expiredTime.getHours() + 12 ).toString(); 

    document.cookie = `Login_Email=${email}; path=/`
    document.cookie = `Login_SessionId=${oData.sess}; path=/`;
    document.cookie = `Login_Token=${oData.token}; path=/`;
    document.cookie = `Login_Expires=${expiredTime.toString()}; path=/`;

    sessionStorage.setItem('logged', 'true');
    sessionStorage.setItem('QUser', user);
    sessionStorage.setItem('QMSESS_ID', oData.sess);
    sessionStorage.setItem('QMA_TOK', oData.token);
    sessionStorage.setItem('QMail', email);
    sessionStorage.setItem('QUCmpl',  Cmlp);
    sessionStorage.setItem('QMA_KEY', key);

  }
}