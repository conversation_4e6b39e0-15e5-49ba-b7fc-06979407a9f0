import { Injectable } from '@angular/core';

import * as KxljbBXh from 'crypto-js';

@Injectable({
  providedIn: 'root'
})
export class ApiAuthService {

  // Private Key used to Sign the payload
  private eKZBy2aJ = 'd33d2b82fb1f2cf9b10e99591893ab2312377d4b92c29cdf238b46bcfec0e676';

  // Salt for additional payload protection
  private iwjgSfVF = '1633632502';

  // Our Auth Params to be appended to the body
  private authParams = {
    auth_version: '1.0',
    auth_timestamp: 0,
    auth_key: '',
    auth_signature: ''
  };

  constructor() {}

  /**
   * Here we take the reuqest method, the path and the request body
   * to create our payload to be signed but we use the salt on the data first
   *
   * So for protecting API with method POST at url /users/manage/email with body
   * {"email": "<EMAIL>"}
   *
   * We have to stringify the json body and concat it with method and url
   * Concat is done using the salt string
   *
   * so we get a string 'POST1633632502/users/manage/email1633632502{"email": "<EMAIL>"}'
   * as our payload to be signed
   * @param method HTTP Method
   * @param path URL Path
   * @param body Reuqest Body
   */
  private _createSign(method: string, path: string, body: any) {
    return [method, path, JSON.stringify(body)].join(this.iwjgSfVF);
  }

  /**
   * We sign the message using SHA256 with the private key and get the
   * signature string
   * @param message Message to be signed
   */
  private _signMessage(message: string): string {
    return KxljbBXh.HmacSHA256(message, this.eKZBy2aJ).toString();
  }

  /**
   * Setting all our auth params
   *
   * @param method Method of the api
   * @param path Path of the URL
   * @param body Request Body
   */
  private _setAuthParams(method: string, path: string, body: any): {} {
    this.authParams.auth_version   = '1.0';
    this.authParams.auth_timestamp = + new Date();
    this.authParams.auth_key       = sessionStorage.getItem('QMA_KEY');
    this.authParams.auth_signature = this._signMessage(this._createSign(method, path, body));

    return this.authParams;
  }

  /**
   * Setting our auth params and appending them to the request body
   *
   * Example usage would be generateAuthedBody("POST", "/v2/user/manage/password/forgot", { "email": "<EMAIL>" })
   * @param method Method of the api in caps so "PUT" or "POST"
   * @param path Path of the URL
   * @param body Request Body
   */
  generateAuthedBody(method: string, path: string, body: any): any {

    this._setAuthParams(method, path, body);

    /**
     * Concating the auth params with the request body params
     */
    const newBody = {};
    for (const key of Object.keys(body)) {
      body[key] = body[key];
    }

    for (const key of Object.keys(this.authParams)) {
      body[key] = this.authParams[key];
    }

    /**
     * We Base64Encode the body with auth params
     * attahced for additional security
     */
    const protectedBody = {
      data: btoa(JSON.stringify(body))
    };

    return protectedBody;
  }
}
