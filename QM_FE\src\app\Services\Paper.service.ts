import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';

import { ApiAuthService } from './api-auth.service';
import { PaperDetails } from './../Models/PaperDetails';
@Injectable({
    providedIn: 'root'
})

export class PaperService {
    private BaseUrl = 'https://api.quantmasters.in';

    private JwtToken: string;

    constructor(private http: HttpClient,
        private apiAuthService: ApiAuthService) {
        this.JwtToken = sessionStorage.getItem('QMA_TOK');
    }

    // Put call for paper header
    putPaperHeaderDetails(paperDetails: object, paper_id: string): Observable<any> {
        const httpOps = {
            headers: new HttpHeaders({
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + this.JwtToken
            })
        };

        const protectedBody = this.apiAuthService.generateAuthedBody('PUT', '/v2/admin/test/open/paper/' + paper_id, paperDetails);

        const Url = this.BaseUrl + '/v2/admin/test/open/paper/' + paper_id;
        return this.http.put<PaperDetails>(Url, protectedBody, httpOps);
    }

    getPaperQuestion(paper_id) {
        const url = this.BaseUrl + '/test/sample/paper/new/' + paper_id;
        return this.http.get<any>(url);
    }

    // Create open paper question
    postPaperQuestionDetails(QuestionDetails: object, paper_id: string): Observable<any> {
        const httpOps = {
            headers: new HttpHeaders({
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + this.JwtToken
            })
        };

        const protectedBody = this.apiAuthService.generateAuthedBody('POST', '/admin/paper/open/update/' + paper_id + '/question', QuestionDetails);

        const Url = this.BaseUrl + '/admin/paper/open/update/' + paper_id + '/question';
        return this.http.post<PaperDetails>(Url, protectedBody, httpOps);
    }

    // put call for question
    putPaperQuestionDetails(QuestionDetails: object, paper_id: string): Observable<any> {
        const httpOps = {
            headers: new HttpHeaders({
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + this.JwtToken
            })
        };

        const protectedBody = this.apiAuthService.generateAuthedBody('PUT', '/v2/admin/test/open/paper/' + paper_id + '/question', QuestionDetails);

        const Url = this.BaseUrl + '/v2/admin/test/open/paper/' + paper_id + '/question';
        return this.http.put<PaperDetails>(Url, protectedBody, httpOps);
    }

    // Delete entire open paper
    deleteOpenPaperDetails(paper_id: string): Observable<string> {
        const httpOps = {
            headers: new HttpHeaders({
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + this.JwtToken
            })
        };

        const Url = this.BaseUrl + '/admin/paper/open/update/' + paper_id;
        return this.http.delete<string>(Url, httpOps);
    }
    
    uploadImage(imgPath: File): Observable<any> {

        const uploadData = new FormData();
        uploadData.append('image_upload', imgPath);
    
        const detailUrl = this.BaseUrl + '/v2/admin/notes/image';
    
        const httpOps = {
          headers: new HttpHeaders({
            'Authorization': 'Bearer ' + this.JwtToken
          })
        };
        return this.http.post<any>(detailUrl, uploadData, httpOps);
      }
}
