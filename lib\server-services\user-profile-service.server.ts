// lib/server-services/user-profile-service.server.ts

import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import axios from 'axios'
import { NewUser } from '@/types/user-types'

const API_BASE_URL = 'https://api.quantmasters.in'
// const V2_BASE_URL = 'https://api.quantmasters.in/v2'

const ENDPOINTS = {
  getUserProfile: (email: string) =>
    `${API_BASE_URL}/user/manage/${email}/profile`,
  getUserAvatar: (email: string) =>
    `${API_BASE_URL}/user/manage/${email}/profile/image`
}

/**
 * Get server-side JWT token from cookies
 */
const getServerSideToken = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_TOK')?.value || ''
}

/**
 * Get server-side user email from cookies
 */
const getServerSideEmail = async () => {
  const cookieStore = await cookies()
  return cookieStore.get('QMA_USR')?.value || ''
}

/**
 * Create auth headers with JWT token
 */
const createAuthHeaders = (token: string) => {
  return {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  }
}

/**
 * Check login status server-side
 */
const checkServerSideLoginStatus = async () => {
  const cookieStore = await cookies()
  const token = cookieStore.get('QMA_TOK')?.value
  const email = cookieStore.get('QMA_USR')?.value

  return !!(token && email)
}

export class ServerUserProfileService {
  /**
   * Get user profile details
   */
  static async getUserProfile(): Promise<{
    user: NewUser
    avatarPath: string | null
  }> {
    if (!(await checkServerSideLoginStatus())) {
      redirect('/user/login')
    }

    const token = await getServerSideToken()
    const email = await getServerSideEmail()

    try {
      // Get user profile details
      const profileResponse = await axios.get(
        ENDPOINTS.getUserProfile(email),
        createAuthHeaders(token)
      )

      const userProfile = profileResponse.data

      // Format date of birth
      if (userProfile.dob) {
        const dob = new Date(userProfile.dob)
        const month = (dob.getMonth() + 1).toString().padStart(2, '0')
        const date = dob.getDate().toString().padStart(2, '0')
        userProfile.dob = `${date}/${month}/${dob.getFullYear()}`
      }

      // Get user avatar
      let avatarPath: string | null = null
      try {
        const avatarResponse = await axios.get(
          ENDPOINTS.getUserAvatar(email),
          createAuthHeaders(token)
        )
        avatarPath = avatarResponse.data.path
      } catch (avatarError: any) {
        // Avatar might not exist, which is fine
        if (avatarError.response?.data?.err !== 'No pic uploaded') {
          console.error('Error fetching avatar:', avatarError)
        }
      }

      return {
        user: userProfile,
        avatarPath
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
      throw error
    }
  }
}
