import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { CarouselModule } from 'ngx-bootstrap/carousel';
import { CollapseModule } from 'ngx-bootstrap/collapse';

import { CampaignsAppRoutingModule } from './campaigns-app-routing.module';

import { SharedIndicatorModule } from '../Components/Utilities/shared-indicator/shared-indicator.module';

import { MlLandingComponent } from './ml-landing/ml-landing.component';
import { WorkshopLandingComponent } from './workshop-landing/workshop-landing.component';
import { RecordedLandingComponent } from './recorded-landing/recorded-landing.component';

@NgModule({
  declarations: [
    MlLandingComponent,
    WorkshopLandingComponent,
    RecordedLandingComponent
  ],
  imports: [
    CommonModule,
    CampaignsAppRoutingModule,
    SharedIndicatorModule,
    FormsModule,
    CarouselModule.forRoot(),
    CollapseModule.forRoot()
  ]
})
export class CampaignsAppModule { }
