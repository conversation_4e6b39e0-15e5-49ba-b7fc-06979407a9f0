import { Component, OnInit, ViewChild, TemplateRef, ViewEncapsulation } from '@angular/core';

import { TestsService } from '../../Services/tests.service';
import { AdminViewService } from '../../Services/admin-view.service';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PageChangedEvent } from 'ngx-bootstrap/pagination';

import * as $ from 'jquery';
import * as XLSX from 'xlsx';

import { Marks } from '../../Models/Marks';
import { ChaptersService } from '../../Services/Dashboard/chapters.service';
import { ChapterPracticeService } from '../../Services/Dashboard/chapter-practice.service';
import { OpenTestsService } from '../../Services/open-tests.service';
import { CompanyService } from 'src/app/Services/Dashboard/company.service';

import { Paper } from '../../Models/Paper';
import { ChapterPaper } from 'src/app/Models/Dashboard/Chapters/ChapterPaper';

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

@Component({
  selector: 'app-results',
  templateUrl: './results.component.html',
  styleUrls: ['./results.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ResultsComponent implements OnInit {

  @ViewChild('sortTemplate') public sortTemplate: TemplateRef<any>;
  @ViewChild('viewSettingsTemplate') public viewSettingsTemplate: TemplateRef<any>;
  @ViewChild('exportTemplate') public exportTemplate: TemplateRef<any>;
  @ViewChild('warnAllTemplate') public warnAllTemplate: TemplateRef<any>;
  @ViewChild('errorTemplate') public errorTemplate: TemplateRef<any>;

  public showIndicator: boolean;

  public allMarks: Marks[];
  public displayMarks: Marks[];
  public filteredMarks: Marks[];

  public basicSearchVal = '';
  public userName = '';
  public email = '';
  public paperType = '-1';
  public paperName = '-1';
  public paperId = '-1';
  public answerDate = '';

  public numResults: number;
  public numFilteredResults: number;
  public pageMax: number;
  public pagePer: number;

  public sortField: number;
  public sortAsc: boolean;

  public exportAll: boolean;
  public exportSelected: boolean;

  public modalRef: BsModalRef;
  public config = {
    animated: true,
    backdrop: true,
    ignoreBackdropClick: false,
    keyboard: true,
    class: 'sorter-model'
  };
  public isMobile = false;
  public advOpen = true;

  public displayError = '';

  public showCols = [
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true
  ];
  public selectedType = '-1';
  public selectedPaper = '-1';

  public chapterPapers: ChapterPaper[];
  public modelPapers: Paper[];
  public weeklyCompititive: any[];

  constructor(private testsService: TestsService,
              private modalService: BsModalService,
              private adminViewService: AdminViewService,
              private chaptersService: ChaptersService,
    private modelTestsService: TestsService,
    private chapterPracticeService: ChapterPracticeService,
    private openTestsService: OpenTestsService,
    private comanyService: CompanyService,) { }

  ngOnInit() {

    if ($(window).width() <= 440) {
      this.pageMax = 3;
      this.pagePer = 5;
      this.isMobile = true;
      this.advOpen = false;
    } else {
      this.pagePer = 15;
      this.pageMax = 7;
    }

    this.exportAll = false;
    this.exportSelected = false;
  }

  warnGetAll() {
    this.openModal(this.warnAllTemplate);
  }

  getAllResults() {

    this.modalRef.hide();
    this.showIndicator = true;
    this.testsService.getAllResults().subscribe(results => {
      const aResults = JSON.parse(JSON.stringify(results));

      // const aResults: Marks[] = [
      //   {
      //     created_at: '2020-06-07T12:37:45.000000Z',
      //     email: '<EMAIL>',
      //     marks: 0,
      //     name: 'Shreyas Sanil',
      //     paper_id: '0c75fa6f-e3ab-3c4c-afa8-e70d320c4425',
      //     paper_name: 'Average Level 1',
      //     paper_type: 'Chapter Paper',
      //     total_marks: 25,
      //     display_date: ''
      //   }
      // ];

      aResults.forEach((result: Marks) => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
        result.selected = false;
      });

      this.allMarks = aResults.sort((x, y) => {
        const aDate = new Date(x.created_at),
              bDate = new Date(y.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });

      this.sortField = 3;
      this.sortAsc = true;

      this.displayMarks = this.allMarks.slice(0, this.pagePer);
      this.numResults = aResults.length;
      this.numFilteredResults = 0;

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  // filterResultsList() {
  //   this.showIndicator = true;

  //   if (this.basicSearchVal !== '') {
  //     const searchVal = this.basicSearchVal;
  //     let intFiltered = [];

  //     this.filteredMarks = this.customFilter(this.allMarks, 'name', searchVal);

  //     if (this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'email', searchVal);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'email', searchVal);
  //     }

  //     if (this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'paper_name', searchVal);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'paper_name', searchVal);
  //     }

  //     if (this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'paper_type', searchVal);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'paper_type', searchVal);
  //     }

  //     if (this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'created_at', searchVal);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'created_at', searchVal);
  //     }

  //     this.numFilteredResults = this.filteredMarks.length;
  //     this.displayMarks = this.filteredMarks.slice(0, this.pagePer);
  //   }

  //   this.showIndicator = false;
  // }

  filterResultsList() {
    this.showIndicator = true;

    const searchVal = this.basicSearchVal;
    this.adminViewService.filterResultsBasics(searchVal).subscribe(response => {

      this.filteredMarks = response;

      this.filteredMarks.forEach((result: Marks) => {
        const date = new Date(result.created_at);
        result.display_date = date.toLocaleString();
      });

      this.filteredMarks = this.filteredMarks.sort((x, y) => {
        const aDate = new Date(x.created_at),
              bDate = new Date(y.created_at);

        return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
      });

      this.numFilteredResults = this.filteredMarks.length;
      this.displayMarks = this.filteredMarks.slice(0, this.pagePer);

      this.showIndicator = false;
    }, error => {
      this.showIndicator = false;
    });
  }

  filterResultsListUsingPaper() {
    this.showIndicator = true;
    if(this.selectedType != '6') { //6 is weekly comititive
      const searchVal = this.paperName;
      this.adminViewService.filterResultsByPaperName(searchVal).subscribe(response => {
  
        this.filteredMarks = response;
  
        this.filteredMarks.forEach((result: Marks) => {
          const date = new Date(result.created_at);
          result.display_date = date.toLocaleString();
        });
  
        this.filteredMarks = this.filteredMarks.sort((x, y) => {
          const aDate = new Date(x.created_at),
                bDate = new Date(y.created_at);
  
          return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
        });
  
        this.numFilteredResults = this.filteredMarks.length;
        this.displayMarks = this.filteredMarks.slice(0, this.pagePer);
  
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
      });
    } else if(this.selectedType == '6' && this.paperId != '-1') {
      const searchVal = this.paperId;
      this.adminViewService.filterResultsByPaperID(searchVal).subscribe(response => {
  
        this.filteredMarks = response;
  
        this.filteredMarks.forEach((result: Marks) => {
          const date = new Date(result.created_at);
          result.display_date = date.toLocaleString();
        });
  
        this.filteredMarks = this.filteredMarks.sort((x, y) => {
          const aDate = new Date(x.created_at),
                bDate = new Date(y.created_at);
  
          return aDate > bDate ? -1 : aDate < bDate ? 1 : 0;
        });
  
        this.numFilteredResults = this.filteredMarks.length;
        this.displayMarks = this.filteredMarks.slice(0, this.pagePer);
  
        this.showIndicator = false;
      }, error => {
        this.showIndicator = false;
      });
    }
  }

  // filterResultsListUsingPaper() {
  //   this.showIndicator = true;
  //   let intFiltered = [];

  //   if (this.userName !== '') {
  //     this.filteredMarks = this.customFilter(this.allMarks, 'name', this.userName);
  //   }

  //   if (this.email !== '') {
  //     if (this.filteredMarks && this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'email', this.email);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'email', this.email);
  //     }
  //   }

  //   if (this.paperName !== '') {
  //     if ( this.filteredMarks && this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'paper_name', this.paperName);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'paper_name', this.paperName);
  //     }
  //   }

  //   if (this.paperType !== '') {
  //     if (this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'paper_type', this.paperType);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'paper_type', this.paperType);
  //     }
  //   }

  //   if (this.answerDate !== '') {
  //     if (this.filteredMarks && this.filteredMarks.length > 0) {
  //       intFiltered = this.customFilter(this.filteredMarks, 'created_at', this.answerDate);

  //       if (intFiltered.length > 0) {
  //         this.filteredMarks = intFiltered;
  //       }
  //     } else {
  //       this.filteredMarks = this.customFilter(this.allMarks, 'created_at', this.answerDate);
  //     }
  //   }

  //   this.numFilteredResults = this.filteredMarks.length;
  //   this.displayMarks = this.filteredMarks.slice(0, this.pagePer);
  //   this.showIndicator = false;
  // }

  customFilter(targetArray: any[], objProp: string, compareStr: string) {
    return targetArray.filter((x) => {
      if (x.hasOwnProperty(objProp)) {
        const i = x[objProp].search(compareStr);
        return i !== -1 ? true : false;
      }

      return false;
    });
  }

  clearFilters($event: Event) {
    $event.preventDefault();
    $event.stopPropagation();

    this.basicSearchVal = '';
    this.userName = '';
    this.email = '';
    this.paperName = '';
    this.answerDate = '';
    this.selectedType = '-1';
    this.paperName = '-1';

    this.numFilteredResults = 0;
    this.filteredMarks = [];
    if(this.allMarks && this.allMarks.length > 0)
      this.displayMarks = this.allMarks.slice(0, this.pagePer);
  }

  openViewSettings() {
    this.openModal(this.viewSettingsTemplate);
  }

  openSorter() {
    this.openModal(this.sortTemplate);

    switch (this.sortField) {
      case 1:
        $('#name').attr('checked', true).trigger('click');
        break;
      case 2:
        $('#email').attr('checked', true).trigger('click');
        break;
      case 3:
        $('#date').attr('checked', true).trigger('click');
        break;
      case 4:
        $('#marks').attr('checked', true).trigger('click');
        break;
    }

    this.sortAsc ? $('#asc-btn').attr('checked', true).trigger('click') : $('#desc-btn').attr('checked', true).trigger('click');
  }

  openExportOptions() {
    this.openModal(this.exportTemplate);
  }

  selectSorter(num: number) {
    switch (num) {
      case 1:
        $('#name').attr('checked', true).trigger('click');
        this.sortField = 1;
        break;
      case 2:
        $('#email').attr('checked', true).trigger('click');
        this.sortField = 2;
        break;
      case 3:
        $('#date').attr('checked', true).trigger('click');
        this.sortField = 3;
        break;
      case 4:
        $('#marks').attr('checked', true).trigger('click');
        this.sortField = 4;
        break;
      default:
        break;
    }
  }

  selectAsc() {
    $('#asc-btn').attr('checked', true).trigger('click');
    this.sortAsc = true;
  }

  selectDesc() {
    $('#desc-btn').attr('checked', true).trigger('click');
    this.sortAsc = false;
  }

  selectExport(num: number) {
    switch (num) {
      case 1:
        $('#all').attr('checked', true).trigger('click');
        this.exportAll = true;
        this.exportSelected = false;
        break;
      case 2:
        $('#sel').attr('checked', true).trigger('click');
        this.exportSelected = true;
        this.exportAll = false;
    }
  }

  sortList() {
    let filterField = '';
    switch (this.sortField) {
      case 1:
        filterField = 'name';
        break;
      case 2:
        filterField = 'email';
        break;
      case 3:
        filterField = 'created_at';
        break;
      case 4:
        filterField = 'marks';
    }

    if (this.numFilteredResults > 0) {
      if (filterField === 'created_at') {
        this.filteredMarks.sort((a, b) => {
          const aDate = new Date(a.created_at),
                bDate = new Date(b.created_at);

          return this.sortAsc ? aDate > bDate ? -1 : aDate < bDate ? 1 : 0 : aDate > bDate ? 1 : aDate < bDate ? -1 : 0;
        });
      } else {
        this.filteredMarks.sort((a, b) => {
          return this.sortAsc ? a[filterField] > b[filterField] ? -1 :
            a[filterField] < b[filterField] ? 1 : 0 : a[filterField] > b[filterField] ? 1 :
            a[filterField] < b[filterField] ? -1 : 0;
        });
      }

      this.numFilteredResults = this.filteredMarks.length;
      this.displayMarks = this.filteredMarks.slice(0, this.pagePer);
    } else {
      if (filterField === 'created_at') {
        this.allMarks.sort((a, b) => {
          const aDate = new Date(a.created_at),
                bDate = new Date(b.created_at);

          return this.sortAsc ? aDate > bDate ? -1 : aDate < bDate ? 1 : 0 : aDate > bDate ? 1 : aDate < bDate ? -1 : 0;
        });
      } else {
        this.allMarks.sort((a, b) => {
          return this.sortAsc ? a[filterField] > b[filterField] ? 1 :
            a[filterField] < b[filterField] ? -1 : 0 : a[filterField] > b[filterField] ? -1 :
            a[filterField] < b[filterField] ? 1 : 0;
        });
      }

      this.numResults = this.allMarks.length;
      this.displayMarks = this.allMarks.slice(0, this.pagePer);
    }

    this.modalRef.hide();
  }

  exportExcel() {
    if (!this.filteredMarks) {
      this.modalRef.hide();

      this.displayError = 'No data is loaded to export :C';
      this.openModal(this.errorTemplate);
      return;
    }

    if (this.exportAll) {
      const exportRecords = this.filteredMarks;

      exportRecords.map(x => {
        delete x.paper_id;
        delete x.created_at;
        delete x.selected;

        return x;
      });

      this.exportAsExcelFile(exportRecords, 'All-Data');
    } else if (this.exportSelected) {
      const exportRecords = this.filteredMarks.filter(x => x.selected);

      if (exportRecords && exportRecords.length === 0) {
        this.modalRef.hide();

        this.displayError = 'No data is selected to export :C';
        this.openModal(this.errorTemplate);
        return;
      } else {
        exportRecords.map(x => {
          delete x.paper_id;
          delete x.created_at;
          delete x.selected;

          return x;
        });

        this.exportAsExcelFile(exportRecords, 'Selected-Data');
      }
    }
  }

  pageChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayMarks = this.allMarks.slice(startItem, endItem);
  }

  pageFilterChanged(event: PageChangedEvent): void {
    const startItem = (event.page - 1) * event.itemsPerPage;
    const endItem = event.page * event.itemsPerPage;
    this.displayMarks = this.filteredMarks.slice(startItem, endItem);
  }

  openModal(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, this.config);
  }

  public exportAsExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    const workbook: XLSX.WorkBook = {Sheets: {'data': worksheet}, SheetNames: ['data']};
    XLSX.writeFile(workbook, this.toExportFileName(excelFileName));
  }

  public toExportFileName(excelFileName: string): string {
    return `${excelFileName}_export_${new Date().getTime()}.xlsx`;
  }

    paperTyepSelected() {
    this.showIndicator = true;
    switch (parseInt(this.selectedType, 10)) {
      case 0:
        this.chaptersService.getSuperGroups().subscribe((response) => {
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 2:
          this.openTestsService.getFreeCompanyPapers().subscribe((response) => {
            const respObj = JSON.parse(JSON.stringify(response));

            this.modelPapers = respObj;
            this.chapterPapers = null;
            this.weeklyCompititive = null;
            this.showIndicator = false;
          }, error => {
            this.showIndicator = false;
          });
        break;
      case 3:
        this.modelTestsService.getPapers().subscribe((response) => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelPapers = respObj;
          this.chapterPapers = null;
          this.weeklyCompititive= null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 4:
        this.chapterPracticeService.getSuperGroups().subscribe(response => {
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 5:
        this.openTestsService.getPapers().subscribe(response => {
          const respObj = JSON.parse(JSON.stringify(response));

          this.modelPapers = respObj;
          this.chapterPapers = null;
          this.weeklyCompititive = null;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      case 6:
        this.adminViewService.getWeeklyCompetitivePapers().subscribe(response => {
          const resp = JSON.parse(JSON.stringify(response));
          this.modelPapers = null;
          this.chapterPapers = null;
          this.weeklyCompititive = resp;
          this.showIndicator = false;
        }, error => {
          this.showIndicator = false;
        });
        break;
      default:
        this.showIndicator = false;

    }
  }
}
