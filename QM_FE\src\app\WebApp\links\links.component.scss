@import "src/mixins";

.mainClass {
  a {
    text-align: center;

    .subImageClass {
      margin-top: 20px;
      width: 10%;
    }
  }
  .item {
    width: 55%;
    display: grid;
    grid-template-columns: 100%;
    gap: 0.8rem;
    align-items: center;
    margin-top: 1rem;
    padding: 15px 10px;
    border-radius: 5px;
    box-shadow: 0 5px 30px 0 rgba(121, 124, 125, 0.3);
    position: relative;

    @include for-size(phone-only) {
      grid-template-columns: 1fr;
    }

    p {
      margin: 0;
      margin-right: 15px;

      @include for-size(phone-only) {
        &:nth-of-type(1) {
          grid-column: 1 / span 2;
        }
      }
    }

    .read-btn {
      padding: 7px;
      color: #145a32;
      background-color: #82e0aa;
      border: none;
      border-radius: 5px;
      box-shadow: 2px 3px 10px 0 rgba(121, 124, 125, 0.3);
      transform: scale(1);
      transition: all 0.4s ease;

      &:hover {
        color: #d4efdf;
        background-color: #1e8449;
        transform: scale(1.05);
        box-shadow: 2px 3px 30px 0 rgba(121, 124, 125, 0.3);
        transition: all 0.3s ease;
      }
    }
  }
  .copy-text {
    position: absolute;
    bottom: 0;
    font-size: 17px;
    font-weight: lighter;
    margin-top: 2em;
    margin-bottom: .5em;
    color: var(--clr-grey-1);
}
}

@media (max-width: 440px) {
  .mainClass {
    .subImageClass {
      width: 30%;
    }
    .item {
      width: 90%;
    }
  }
}
